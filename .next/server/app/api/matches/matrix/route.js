/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/matches/matrix/route";
exports.ids = ["app/api/matches/matrix/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_ubt22_workspace_indie_lingxiai_gemini_src_app_api_matches_matrix_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/matches/matrix/route.ts */ \"(rsc)/./src/app/api/matches/matrix/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/matches/matrix/route\",\n        pathname: \"/api/matches/matrix\",\n        filename: \"route\",\n        bundlePath: \"app/api/matches/matrix/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/api/matches/matrix/route.ts\",\n    nextConfigOutput,\n    userland: _home_ubt22_workspace_indie_lingxiai_gemini_src_app_api_matches_matrix_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/matches/matrix/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/matches/matrix/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var _lib_services_matching_v2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/matching-v2 */ \"(rsc)/./src/lib/services/matching-v2.ts\");\n/* harmony import */ var _trigger_dev_sdk_v3__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @trigger.dev/sdk/v3 */ \"(rsc)/./node_modules/@trigger.dev/sdk/dist/esm/v3/index.js\");\n// V2.0 智能候选人矩阵 API\n\n\n\n\n\n\n\n\n// 异步处理匹配请求\nasync function processMatchRequestAsync(requestId, requesterId) {\n    try {\n        console.log(`🚀 开始异步处理匹配请求: ${requestId}`);\n        await _lib_services_matching_v2__WEBPACK_IMPORTED_MODULE_4__.MatchingServiceV2.processSingleRequest(requestId, requesterId);\n        console.log(`✅ 异步处理完成: ${requestId}`);\n    } catch (error) {\n        console.error(`❌ 异步处理失败: ${requestId}`, error);\n    }\n}\n// 生成智能候选人矩阵请求\nasync function POST(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createRouteClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // 检查每日限制（复用 v1.0 的限制逻辑）\n        const { MatchingService } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_services_matching_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/services/matching */ \"(rsc)/./src/lib/services/matching.ts\"));\n        const limitCheck = await MatchingService.checkDailyMatchLimit(user.id);\n        if (!limitCheck.canMatch) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'DAILY_LIMIT_EXCEEDED',\n                message: '今日匹配次数已用完',\n                limitInfo: limitCheck\n            }, {\n                status: 429\n            });\n        }\n        // 生成请求ID\n        const requestId = (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n        // 创建匹配请求记录\n        const matchRequestData = {\n            id: requestId,\n            requesterId: user.id,\n            status: 'processing'\n        };\n        const [matchRequest] = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests).values(matchRequestData).returning();\n        // 创建任务队列记录\n        const queueData = {\n            matchRequestId: requestId,\n            requesterId: user.id,\n            status: 'pending'\n        };\n        await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchQueue).values(queueData);\n        console.log(`📝 创建匹配矩阵请求: ${requestId} for user: ${user.id}`);\n        // 立即触发异步处理\n        // processMatchRequestAsync(requestId, user.id).catch(error => {\n        //   console.error(`异步处理失败 ${requestId}:`, error);\n        // });\n        // 使用 Trigger.dev 触发后台任务\n        const handle = await _trigger_dev_sdk_v3__WEBPACK_IMPORTED_MODULE_5__.tasks.trigger(\"process-match-request\", {\n            matchRequestId: requestId\n        });\n        console.log(`🚀 Trigger.dev 任务已启动: ${handle.id}`);\n        // 立即返回请求ID，实际处理将异步进行\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            requestId: requestId,\n            status: 'processing',\n            message: '专属红娘正在为您筛选候选人，请稍候...',\n            estimatedTime: '2-3分钟'\n        }, {\n            status: 202\n        });\n    } catch (error) {\n        console.error('创建匹配矩阵请求失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'INTERNAL_SERVER_ERROR',\n            message: '服务器内部错误'\n        }, {\n            status: 500\n        });\n    }\n}\n// 获取匹配矩阵结果\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createRouteClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const url = new URL(request.url);\n        const requestId = url.searchParams.get('requestId');\n        if (!requestId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'BAD_REQUEST',\n                message: '缺少 requestId 参数'\n            }, {\n                status: 400\n            });\n        }\n        // 查询匹配请求状态\n        const matchRequest = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests.id, requestId)).limit(1);\n        if (matchRequest.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'NOT_FOUND',\n                message: '匹配请求不存在'\n            }, {\n                status: 404\n            });\n        }\n        const request_data = matchRequest[0];\n        // 检查权限\n        if (request_data.requesterId !== user.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'FORBIDDEN',\n                message: '无权访问此匹配请求'\n            }, {\n                status: 403\n            });\n        }\n        // 根据状态返回不同响应\n        switch(request_data.status){\n            case 'processing':\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    status: 'processing',\n                    message: '正在生成匹配矩阵，请稍候...',\n                    requestId: requestId\n                });\n            case 'completed':\n                // 从 match_candidates 表获取候选人数据\n                const candidates = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchCandidates).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchCandidates.requestId, requestId)).orderBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchCandidates.rank);\n                if (candidates.length === 0) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'DATA_ERROR',\n                        message: '候选人数据不存在，请重新生成'\n                    }, {\n                        status: 500\n                    });\n                }\n                // 重构候选人矩阵数据\n                const topMatch = candidates.find((c)=>c.rank === 1);\n                const potentialMatches = candidates.filter((c)=>c.rank > 1);\n                if (!topMatch) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'DATA_ERROR',\n                        message: '首席推荐数据不存在，请重新生成'\n                    }, {\n                        status: 500\n                    });\n                }\n                const matrix = {\n                    topMatch: {\n                        candidate: {\n                            candidateId: topMatch.candidateId,\n                            compatibilityScore: topMatch.compatibilityScore,\n                            reasoning: topMatch.reasoning,\n                            highlights: topMatch.highlights,\n                            challenges: topMatch.challenges,\n                            personalitySummary: topMatch.personalitySummary\n                        },\n                        relationshipInsight: topMatch.relationshipInsight,\n                        conversationSimulation: topMatch.conversationSimulation,\n                        datePlan: topMatch.datePlan\n                    },\n                    potentialMatches: potentialMatches.map((match)=>({\n                            candidate: {\n                                candidateId: match.candidateId,\n                                compatibilityScore: match.compatibilityScore,\n                                reasoning: match.reasoning,\n                                highlights: match.highlights,\n                                challenges: match.challenges,\n                                personalitySummary: match.personalitySummary\n                            },\n                            compatibilityReason: match.reasoning\n                        }))\n                };\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    status: 'completed',\n                    requestId: requestId,\n                    matrix: matrix,\n                    generatedAt: request_data.createdAt\n                });\n            case 'failed':\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    status: 'failed',\n                    message: request_data.errorMessage || '匹配生成失败',\n                    requestId: requestId\n                }, {\n                    status: 500\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'UNKNOWN_STATUS',\n                    message: '未知的请求状态'\n                }, {\n                    status: 500\n                });\n        }\n    } catch (error) {\n        console.error('获取匹配矩阵结果失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'INTERNAL_SERVER_ERROR',\n            message: '服务器内部错误'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/matches/matrix/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   agentExecutionLogs: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs),\n/* harmony export */   aiAgentFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiAgentFeedback),\n/* harmony export */   conversations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.conversations),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   matchCandidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matchCandidates),\n/* harmony export */   matchQueue: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matchQueue),\n/* harmony export */   matchRequests: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matchRequests),\n/* harmony export */   matches: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matches),\n/* harmony export */   messages: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.messages),\n/* harmony export */   userProfiles: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userProfiles),\n/* harmony export */   userSessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userSessions),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"(rsc)/./node_modules/postgres/src/index.js\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var _utils_env__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/env */ \"(rsc)/./src/lib/utils/env.ts\");\n\n\n\n\n// 确保环境变量被加载\n(0,_utils_env__WEBPACK_IMPORTED_MODULE_2__.ensureEnvLoaded)();\n// Create the connection\nconst connectionString = (0,_utils_env__WEBPACK_IMPORTED_MODULE_2__.getOptionalEnv)('DATABASE_URL');\nlet db;\nif (connectionString) {\n    // Create postgres client with better configuration for Supabase\n    const client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n        max: 1,\n        idle_timeout: 20,\n        connect_timeout: 10,\n        ssl: 'require',\n        prepare: false\n    });\n    // Create drizzle instance\n    db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_3__.drizzle)(client, {\n        schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n    });\n} else {\n    // Mock database for build time - create a minimal mock\n    const mockClient = {};\n    db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_3__.drizzle)(mockClient, {\n        schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n    });\n}\n\n// Export schema for use in other files\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   agentExecutionLogs: () => (/* binding */ agentExecutionLogs),\n/* harmony export */   aiAgentFeedback: () => (/* binding */ aiAgentFeedback),\n/* harmony export */   conversations: () => (/* binding */ conversations),\n/* harmony export */   matchCandidates: () => (/* binding */ matchCandidates),\n/* harmony export */   matchQueue: () => (/* binding */ matchQueue),\n/* harmony export */   matchRequests: () => (/* binding */ matchRequests),\n/* harmony export */   matches: () => (/* binding */ matches),\n/* harmony export */   messages: () => (/* binding */ messages),\n/* harmony export */   userProfiles: () => (/* binding */ userProfiles),\n/* harmony export */   userSessions: () => (/* binding */ userSessions),\n/* harmony export */   users: () => (/* binding */ users)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/uuid.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/jsonb.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n\n// Users table\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    avatar: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('avatar'),\n    bio: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('bio'),\n    age: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('age'),\n    gender: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('gender'),\n    location: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('location'),\n    interests: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('interests').$type().default([]),\n    personalityTraits: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('personality_traits').$type(),\n    personalitySummary: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('personality_summary'),\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_active').default(true),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// User profiles table for additional profile information\nconst userProfiles = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_profiles', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    selfDescription: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('self_description'),\n    lookingFor: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('looking_for'),\n    relationshipGoals: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('relationship_goals'),\n    lifestyle: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('lifestyle').$type(),\n    values: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('values').$type().default([]),\n    photos: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('photos').$type().default([]),\n    preferences: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('preferences').$type(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// Matches table\nconst matches = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('matches', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    user1Id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user1_id').references(()=>users.id).notNull(),\n    user2Id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user2_id').references(()=>users.id).notNull(),\n    compatibilityScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('compatibility_score'),\n    aiAnalysis: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('ai_analysis').$type(),\n    conversationSimulation: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('conversation_simulation').$type(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('pending'),\n    user1Liked: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user1_liked').default(false),\n    user2Liked: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user2_liked').default(false),\n    user1Viewed: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user1_viewed').default(false),\n    user2Viewed: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user2_viewed').default(false),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// AI Agent Feedback table\nconst aiAgentFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('ai_agent_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_id').references(()=>matches.id).notNull(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    feedbackType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feedback_type').notNull(),\n    feedbackText: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feedback_text'),\n    rating: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('rating'),\n    aspectRated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('aspect_rated'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// Conversations table for storing chat messages\nconst conversations = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('conversations', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_id').references(()=>matches.id).notNull(),\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_active').default(true),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// Messages table\nconst messages = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('messages', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    conversationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('conversation_id').references(()=>conversations.id).notNull(),\n    senderId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('sender_id').references(()=>users.id).notNull(),\n    content: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('content').notNull(),\n    messageType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('message_type').default('text'),\n    isRead: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_read').default(false),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// User sessions for tracking activity\nconst userSessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_sessions', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_token').notNull().unique(),\n    expiresAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('expires_at').notNull(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// ===== V2.0 新增表 =====\n// 1. 任务队列，用于异步处理匹配请求\nconst matchQueue = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('match_queue', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchRequestId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_request_id').notNull().unique(),\n    requesterId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('requester_id').references(()=>users.id).notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('pending'),\n    attempts: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('attempts').default(0),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// 2. 匹配请求的总记录\nconst matchRequests = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('match_requests', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    requesterId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('requester_id').references(()=>users.id).notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('processing'),\n    errorMessage: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('error_message'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// 3. 用于记录用户对每个候选人的决策和详细分析\nconst matchCandidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('match_candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    requestId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('request_id').references(()=>matchRequests.id).notNull(),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('candidate_id').references(()=>users.id).notNull(),\n    rank: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('rank').notNull(),\n    // 兼容性分析数据\n    compatibilityScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('compatibility_score').notNull().default(0),\n    reasoning: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reasoning'),\n    highlights: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('highlights'),\n    challenges: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('challenges'),\n    personalitySummary: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('personality_summary'),\n    // 首席推荐的额外数据 (rank = 1 时才有)\n    relationshipInsight: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('relationship_insight'),\n    conversationSimulation: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('conversation_simulation'),\n    datePlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('date_plan'),\n    // 用户决策\n    userDecision: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('user_decision').default('pending'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// 4. Agent执行日志表 - 记录ARAG-Soul框架每个Agent的执行状态和结果\nconst agentExecutionLogs = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('agent_execution_logs', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchRequestId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_request_id').references(()=>matchRequests.id).notNull(),\n    // Agent信息\n    agentName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('agent_name').notNull(),\n    stepOrder: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('step_order').notNull(),\n    // 执行状态\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('pending'),\n    // 时间信息\n    startedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('started_at'),\n    completedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('completed_at'),\n    // 结果数据\n    resultData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('result_data'),\n    resultSummary: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('result_summary'),\n    errorMessage: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('error_message'),\n    // 性能指标\n    executionTimeMs: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('execution_time_ms'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/agent-logger.ts":
/*!****************************************************!*\
  !*** ./src/lib/services/arag-soul/agent-logger.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AGENT_DISPLAY_NAMES: () => (/* binding */ AGENT_DISPLAY_NAMES),\n/* harmony export */   AGENT_NAMES: () => (/* binding */ AGENT_NAMES),\n/* harmony export */   AGENT_STEP_ORDER: () => (/* binding */ AGENT_STEP_ORDER),\n/* harmony export */   AgentLogger: () => (/* binding */ AgentLogger),\n/* harmony export */   generateResultSummary: () => (/* binding */ generateResultSummary)\n/* harmony export */ });\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n// Agent执行日志记录服务\n\n\n\n// Agent名称枚举\nconst AGENT_NAMES = {\n    RETRIEVE_CANDIDATES: 'retrieveCandidates',\n    GENERATE_USER_SOUL_PROFILE: 'generateUserSoulProfile',\n    RUN_COMPATIBILITY_INFERENCE: 'runCompatibilityInference',\n    RANK_AND_FINALIZE: 'rankAndFinalize',\n    GENERATE_FULL_REPORT: 'generateFullReport'\n};\n// Agent步骤顺序\nconst AGENT_STEP_ORDER = {\n    [AGENT_NAMES.RETRIEVE_CANDIDATES]: 1,\n    [AGENT_NAMES.GENERATE_USER_SOUL_PROFILE]: 2,\n    [AGENT_NAMES.RUN_COMPATIBILITY_INFERENCE]: 3,\n    [AGENT_NAMES.RANK_AND_FINALIZE]: 4,\n    [AGENT_NAMES.GENERATE_FULL_REPORT]: 5\n};\n// Agent友好名称\nconst AGENT_DISPLAY_NAMES = {\n    [AGENT_NAMES.RETRIEVE_CANDIDATES]: '检索候选人',\n    [AGENT_NAMES.GENERATE_USER_SOUL_PROFILE]: '分析用户人格',\n    [AGENT_NAMES.RUN_COMPATIBILITY_INFERENCE]: '兼容性推理',\n    [AGENT_NAMES.RANK_AND_FINALIZE]: '排序决策',\n    [AGENT_NAMES.GENERATE_FULL_REPORT]: '生成完整报告'\n};\n// Agent日志记录器类\nclass AgentLogger {\n    constructor(matchRequestId){\n        this.matchRequestId = matchRequestId;\n    }\n    // 初始化所有Agent日志记录\n    async initializeAgentLogs() {\n        try {\n            const logs = Object.values(AGENT_NAMES).map((agentName)=>({\n                    matchRequestId: this.matchRequestId,\n                    agentName,\n                    stepOrder: AGENT_STEP_ORDER[agentName],\n                    status: 'pending',\n                    resultSummary: `等待执行 ${AGENT_DISPLAY_NAMES[agentName]}...`\n                }));\n            await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs).values(logs);\n            console.log(`✅ 初始化了 ${logs.length} 个Agent日志记录`);\n        } catch (error) {\n            console.error('❌ 初始化Agent日志失败:', error);\n            throw error;\n        }\n    }\n    // 开始执行Agent\n    async startAgent(agentName) {\n        try {\n            const startTime = new Date();\n            await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs).set({\n                status: 'running',\n                startedAt: startTime,\n                resultSummary: `正在执行 ${AGENT_DISPLAY_NAMES[agentName]}...`,\n                updatedAt: startTime\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs.matchRequestId, this.matchRequestId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs.agentName, agentName)));\n            console.log(`🚀 开始执行 ${agentName}`);\n        } catch (error) {\n            console.error(`❌ 开始执行 ${agentName} 失败:`, error);\n            throw error;\n        }\n    }\n    // 完成Agent执行\n    async completeAgent(agentName, resultData, resultSummary) {\n        try {\n            const completedTime = new Date();\n            // 获取开始时间以计算执行耗时\n            const existingLog = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs.matchRequestId, this.matchRequestId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs.agentName, agentName))).limit(1);\n            const executionTimeMs = existingLog[0]?.startedAt ? completedTime.getTime() - existingLog[0].startedAt.getTime() : null;\n            await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs).set({\n                status: 'completed',\n                completedAt: completedTime,\n                resultData,\n                resultSummary,\n                executionTimeMs,\n                updatedAt: completedTime\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs.matchRequestId, this.matchRequestId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs.agentName, agentName)));\n            console.log(`✅ 完成执行 ${agentName}, 耗时: ${executionTimeMs}ms`);\n        } catch (error) {\n            console.error(`❌ 完成执行 ${agentName} 失败:`, error);\n            throw error;\n        }\n    }\n    // Agent执行失败\n    async failAgent(agentName, errorMessage) {\n        try {\n            const failedTime = new Date();\n            // 获取开始时间以计算执行耗时\n            const existingLog = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs.matchRequestId, this.matchRequestId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs.agentName, agentName))).limit(1);\n            const executionTimeMs = existingLog[0]?.startedAt ? failedTime.getTime() - existingLog[0].startedAt.getTime() : null;\n            await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs).set({\n                status: 'failed',\n                completedAt: failedTime,\n                errorMessage,\n                resultSummary: `${AGENT_DISPLAY_NAMES[agentName]} 执行失败`,\n                executionTimeMs,\n                updatedAt: failedTime\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs.matchRequestId, this.matchRequestId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs.agentName, agentName)));\n            console.log(`❌ ${agentName} 执行失败: ${errorMessage}`);\n        } catch (error) {\n            console.error(`❌ 记录 ${agentName} 失败状态失败:`, error);\n            throw error;\n        }\n    }\n    // 获取所有Agent日志\n    async getAllAgentLogs() {\n        try {\n            return await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs.matchRequestId, this.matchRequestId)).orderBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.agentExecutionLogs.stepOrder);\n        } catch (error) {\n            console.error('❌ 获取Agent日志失败:', error);\n            throw error;\n        }\n    }\n}\n// 工具函数：生成结果摘要\nconst generateResultSummary = {\n    [AGENT_NAMES.RETRIEVE_CANDIDATES]: (data)=>{\n        const count = data?.candidatePoolIds?.length || 0;\n        return `找到 ${count} 个候选人`;\n    },\n    [AGENT_NAMES.GENERATE_USER_SOUL_PROFILE]: (data)=>{\n        const traits = data?.userSoulProfile?.personalityTraits;\n        const traitCount = traits ? Object.keys(traits).length : 0;\n        return `分析了 ${traitCount} 个人格特质`;\n    },\n    [AGENT_NAMES.RUN_COMPATIBILITY_INFERENCE]: (data)=>{\n        const count = data?.candidatesWithAnalysis?.length || 0;\n        return `完成 ${count} 个候选人的兼容性分析`;\n    },\n    [AGENT_NAMES.RANK_AND_FINALIZE]: (data)=>{\n        const count = data?.rankedCandidates?.length || 0;\n        return `排序完成，选出前 ${count} 名候选人`;\n    },\n    [AGENT_NAMES.GENERATE_FULL_REPORT]: (data)=>{\n        const hasTopMatch = !!data?.finalMatrix?.topMatch;\n        const potentialCount = data?.finalMatrix?.potentialMatches?.length || 0;\n        return `生成完整报告：1个首席推荐 + ${potentialCount}个潜力候选人`;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/agent-logger.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/agents.ts":
/*!**********************************************!*\
  !*** ./src/lib/services/arag-soul/agents.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateFullReportNode: () => (/* binding */ generateFullReportNode),\n/* harmony export */   generateUserSoulProfileNode: () => (/* binding */ generateUserSoulProfileNode),\n/* harmony export */   rankAndFinalizeNode: () => (/* binding */ rankAndFinalizeNode),\n/* harmony export */   runCompatibilityInferenceNode: () => (/* binding */ runCompatibilityInferenceNode)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n/* harmony import */ var _prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prompts */ \"(rsc)/./src/lib/services/arag-soul/prompts.ts\");\n/* harmony import */ var _utils_env__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/env */ \"(rsc)/./src/lib/utils/env.ts\");\n/* harmony import */ var _agent_logger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./agent-logger */ \"(rsc)/./src/lib/services/arag-soul/agent-logger.ts\");\n// ARAG-Soul 框架的 Agent 实现\n\n\n\n\n// 模型配置\nconst MODEL_NAME = 'google/gemini-2.5-flash-preview-05-20';\n// 创建 AI 调用函数\nconst createModelCall = async (prompt, systemPrompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.default)=>{\n    // 确保环境变量被加载并获取 API 密钥\n    (0,_utils_env__WEBPACK_IMPORTED_MODULE_2__.ensureEnvLoaded)();\n    const apiKey = (0,_utils_env__WEBPACK_IMPORTED_MODULE_2__.getRequiredEnv)('OPENROUTER_API_KEY');\n    // 初始化 OpenRouter 客户端\n    const openrouter = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n        baseURL: 'https://openrouter.ai/api/v1',\n        apiKey: apiKey\n    });\n    try {\n        const completion = await openrouter.chat.completions.create({\n            model: MODEL_NAME,\n            messages: [\n                {\n                    role: 'system',\n                    content: systemPrompt\n                },\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 2048,\n            response_format: {\n                type: 'json_object'\n            }\n        });\n        return completion.choices[0]?.message?.content || '';\n    } catch (error) {\n        console.error('OpenRouter API 调用失败:', error);\n        throw error;\n    }\n};\n// 人格洞察 Agent\nasync function generateUserSoulProfileNode(state) {\n    const agentName = _agent_logger__WEBPACK_IMPORTED_MODULE_3__.AGENT_NAMES.GENERATE_USER_SOUL_PROFILE;\n    try {\n        console.log('🧠 执行人格洞察 Agent...');\n        // 记录开始执行\n        if (state.logger) {\n            await state.logger.startAgent(agentName);\n        }\n        const { userProfile } = state;\n        if (!userProfile) {\n            throw new Error('用户资料不存在');\n        }\n        const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.personalityInsight.replace('{name}', userProfile.name || '未知').replace('{age}', userProfile.age?.toString() || '未知').replace('{gender}', userProfile.gender || '未知').replace('{selfDescription}', userProfile.selfDescription || '').replace('{interests}', JSON.stringify(userProfile.interests || [])).replace('{values}', JSON.stringify(userProfile.values || [])).replace('{lifestyle}', JSON.stringify(userProfile.lifestyle || {})).replace('{relationshipGoals}', userProfile.relationshipGoals || '');\n        const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.personality);\n        console.log('🔍 AI 返回的原始内容:', responseContent);\n        let result;\n        try {\n            result = JSON.parse(responseContent);\n        } catch (parseError) {\n            console.error('❌ JSON 解析失败:', parseError);\n            console.error('原始内容:', responseContent);\n            throw new Error(`人格洞察 JSON 解析失败: ${parseError}`);\n        }\n        console.log('🔍 解析后的结果:', result);\n        // 添加安全检查和默认值\n        const userSoulProfile = {\n            userId: userProfile.userId,\n            personalityTraits: result.personalityTraits || {},\n            coreValues: Array.isArray(result.coreValues) ? result.coreValues : [],\n            communicationStyle: result.communicationStyle || '未知',\n            relationshipGoals: userProfile.relationshipGoals || '寻找合适的伴侣',\n            lifestyle: userProfile.lifestyle || {},\n            summary: result.summary || '正在分析中...'\n        };\n        console.log('🔍 生成的用户灵魂档案:', {\n            userId: userSoulProfile.userId,\n            coreValuesCount: userSoulProfile.coreValues.length,\n            hasSummary: !!userSoulProfile.summary\n        });\n        console.log('✅ 人格洞察完成');\n        // 记录完成执行\n        if (state.logger) {\n            const summary = _agent_logger__WEBPACK_IMPORTED_MODULE_3__.generateResultSummary[agentName](userSoulProfile);\n            await state.logger.completeAgent(agentName, userSoulProfile, summary);\n        }\n        return {\n            userSoulProfile,\n            step: 'personality_insight_completed'\n        };\n    } catch (error) {\n        console.error('❌ 人格洞察失败:', error);\n        // 记录失败状态\n        if (state.logger) {\n            const errorMessage = error instanceof Error ? error.message : '未知错误';\n            await state.logger.failAgent(agentName, errorMessage);\n        }\n        return {\n            error: `人格洞察失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'personality_insight_failed'\n        };\n    }\n}\n// 深度兼容性推理 Agent\nasync function runCompatibilityInferenceNode(state) {\n    const agentName = _agent_logger__WEBPACK_IMPORTED_MODULE_3__.AGENT_NAMES.RUN_COMPATIBILITY_INFERENCE;\n    try {\n        console.log('🔍 执行兼容性推理 Agent...');\n        // 记录开始执行\n        if (state.logger) {\n            await state.logger.startAgent(agentName);\n        }\n        const { userSoulProfile, candidatePoolIds } = state;\n        if (!userSoulProfile || !candidatePoolIds) {\n            throw new Error('缺少必要的状态数据');\n        }\n        // 这里需要从数据库获取候选人资料\n        // 为了演示，我们先创建一个模拟的候选人分析函数\n        const candidatesWithAnalysis = [];\n        // 并行处理所有候选人\n        const analysisPromises = candidatePoolIds.map(async (candidateId)=>{\n            // TODO: 从数据库获取候选人详细资料\n            const candidateProfile = await getCandidateProfile(candidateId);\n            const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.compatibilityInference.replace('{userSoulProfile}', JSON.stringify(userSoulProfile)).replace('{candidateName}', candidateProfile.name || '未知').replace('{candidateAge}', candidateProfile.age?.toString() || '未知').replace('{candidateSelfDescription}', candidateProfile.selfDescription || '').replace('{candidateInterests}', JSON.stringify(candidateProfile.interests || [])).replace('{candidateValues}', JSON.stringify(candidateProfile.values || [])).replace('{candidateLifestyle}', JSON.stringify(candidateProfile.lifestyle || {}));\n            const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.compatibility);\n            const result = JSON.parse(responseContent);\n            return {\n                candidateId,\n                compatibilityScore: result.compatibilityScore || 0,\n                reasoning: result.reasoning || '分析中...',\n                highlights: Array.isArray(result.highlights) ? result.highlights : [],\n                challenges: Array.isArray(result.challenges) ? result.challenges : [],\n                personalitySummary: result.personalitySummary || '候选人分析中...'\n            };\n        });\n        const analysisResults = await Promise.all(analysisPromises);\n        candidatesWithAnalysis.push(...analysisResults);\n        console.log(`✅ 兼容性推理完成，分析了 ${candidatesWithAnalysis.length} 个候选人`);\n        // 记录完成执行\n        if (state.logger) {\n            const summary = _agent_logger__WEBPACK_IMPORTED_MODULE_3__.generateResultSummary[agentName]({\n                candidatesWithAnalysis\n            });\n            await state.logger.completeAgent(agentName, {\n                candidatesWithAnalysis\n            }, summary);\n        }\n        return {\n            candidatesWithAnalysis,\n            step: 'compatibility_inference_completed'\n        };\n    } catch (error) {\n        console.error('❌ 兼容性推理失败:', error);\n        // 记录失败状态\n        if (state.logger) {\n            const errorMessage = error instanceof Error ? error.message : '未知错误';\n            await state.logger.failAgent(agentName, errorMessage);\n        }\n        return {\n            error: `兼容性推理失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'compatibility_inference_failed'\n        };\n    }\n}\n// 从数据库获取候选人资料\nasync function getCandidateProfile(candidateId) {\n    const { db } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\"));\n    const { users, userProfiles } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\"));\n    const { eq } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/drizzle-orm\").then(__webpack_require__.bind(__webpack_require__, /*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/index.js\"));\n    const candidateWithProfile = await db.select({\n        user: users,\n        profile: userProfiles\n    }).from(users).leftJoin(userProfiles, eq(users.id, userProfiles.userId)).where(eq(users.id, candidateId)).limit(1);\n    if (candidateWithProfile.length === 0) {\n        throw new Error(`候选人 ${candidateId} 不存在`);\n    }\n    const candidate = candidateWithProfile[0];\n    return {\n        userId: candidate.user.id,\n        name: candidate.user.name || '未知',\n        age: candidate.user.age || 0,\n        selfDescription: candidate.profile?.selfDescription || '',\n        interests: candidate.user.interests || [],\n        values: candidate.profile?.values || [],\n        lifestyle: candidate.profile?.lifestyle || {}\n    };\n}\n// 排序和最终决策 Agent\nasync function rankAndFinalizeNode(state) {\n    const agentName = _agent_logger__WEBPACK_IMPORTED_MODULE_3__.AGENT_NAMES.RANK_AND_FINALIZE;\n    try {\n        console.log('🏆 执行排序和最终决策 Agent...');\n        // 记录开始执行\n        if (state.logger) {\n            await state.logger.startAgent(agentName);\n        }\n        const { candidatesWithAnalysis } = state;\n        if (!candidatesWithAnalysis || candidatesWithAnalysis.length === 0) {\n            throw new Error('没有候选人分析数据');\n        }\n        // 按兼容性分数排序\n        const rankedCandidates = candidatesWithAnalysis.sort((a, b)=>b.compatibilityScore - a.compatibilityScore).slice(0, 5); // 取前5名\n        console.log(`✅ 排序完成，选出前 ${rankedCandidates.length} 名候选人`);\n        // 记录完成执行\n        if (state.logger) {\n            const summary = _agent_logger__WEBPACK_IMPORTED_MODULE_3__.generateResultSummary[agentName]({\n                rankedCandidates\n            });\n            await state.logger.completeAgent(agentName, {\n                rankedCandidates\n            }, summary);\n        }\n        return {\n            rankedCandidates,\n            step: 'ranking_completed'\n        };\n    } catch (error) {\n        console.error('❌ 排序失败:', error);\n        // 记录失败状态\n        if (state.logger) {\n            const errorMessage = error instanceof Error ? error.message : '未知错误';\n            await state.logger.failAgent(agentName, errorMessage);\n        }\n        return {\n            error: `排序失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'ranking_failed'\n        };\n    }\n}\n// 生成完整报告 Agent\nasync function generateFullReportNode(state) {\n    const agentName = _agent_logger__WEBPACK_IMPORTED_MODULE_3__.AGENT_NAMES.GENERATE_FULL_REPORT;\n    try {\n        console.log('📝 生成完整报告...');\n        // 记录开始执行\n        if (state.logger) {\n            await state.logger.startAgent(agentName);\n        }\n        const { rankedCandidates, userSoulProfile } = state;\n        if (!rankedCandidates || !userSoulProfile) {\n            throw new Error('缺少必要数据');\n        }\n        // 为首席推荐生成完整报告\n        const topCandidate = rankedCandidates[0];\n        // 生成关系洞察、对话模拟和约会计划\n        const [relationshipInsight, conversationSimulation, datePlan] = await Promise.all([\n            generateRelationshipInsight(userSoulProfile, topCandidate),\n            generateConversationSimulation(userSoulProfile, topCandidate),\n            generateDatePlan(userSoulProfile, topCandidate)\n        ]);\n        const finalMatrix = {\n            topMatch: {\n                candidate: topCandidate,\n                relationshipInsight,\n                conversationSimulation,\n                datePlan\n            },\n            potentialMatches: rankedCandidates.slice(1, 5).map((candidate)=>({\n                    candidate,\n                    highlights: candidate.highlights,\n                    compatibilityReason: candidate.reasoning\n                })),\n            generatedAt: new Date(),\n            requestId: state.requesterId\n        };\n        console.log('✅ 完整报告生成完成');\n        // 记录完成执行\n        if (state.logger) {\n            const summary = _agent_logger__WEBPACK_IMPORTED_MODULE_3__.generateResultSummary[agentName]({\n                finalMatrix\n            });\n            await state.logger.completeAgent(agentName, {\n                finalMatrix\n            }, summary);\n        }\n        return {\n            finalMatrix,\n            step: 'report_generation_completed'\n        };\n    } catch (error) {\n        console.error('❌ 报告生成失败:', error);\n        // 记录失败状态\n        if (state.logger) {\n            const errorMessage = error instanceof Error ? error.message : '未知错误';\n            await state.logger.failAgent(agentName, errorMessage);\n        }\n        return {\n            error: `报告生成失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'report_generation_failed'\n        };\n    }\n}\n// 辅助函数：生成关系洞察\nasync function generateRelationshipInsight(userProfile, candidate) {\n    const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.relationshipHighlight.replace('{userSoulProfile}', JSON.stringify(userProfile)).replace('{candidateAnalysis}', JSON.stringify(candidate));\n    const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.compatibility);\n    return JSON.parse(responseContent);\n}\n// 辅助函数：生成对话模拟\nasync function generateConversationSimulation(userProfile, candidate) {\n    const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.conversationSimulation.replace('{userProfile}', JSON.stringify(userProfile)).replace('{candidateProfile}', JSON.stringify(candidate)).replace('{scenario}', '咖啡厅初次见面');\n    const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.conversation);\n    return JSON.parse(responseContent);\n}\n// 辅助函数：生成约会计划\nasync function generateDatePlan(userProfile, candidate) {\n    // 安全地找出共同兴趣\n    const userCoreValues = userProfile.coreValues || [];\n    const candidateHighlights = candidate.highlights || [];\n    const commonInterests = userCoreValues.filter((value)=>candidateHighlights.some((highlight)=>highlight.includes(value)));\n    const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.datePlanGeneration.replace('{userProfile}', JSON.stringify(userProfile)).replace('{candidateProfile}', JSON.stringify(candidate)).replace('{commonInterests}', JSON.stringify(commonInterests));\n    const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.dating);\n    return JSON.parse(responseContent);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/agents.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/index.ts":
/*!*********************************************!*\
  !*** ./src/lib/services/arag-soul/index.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AragSoulService: () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.AragSoulService),\n/* harmony export */   AragSoulStateAnnotation: () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.AragSoulStateAnnotation),\n/* harmony export */   PROMPTS: () => (/* reexport safe */ _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS),\n/* harmony export */   SYSTEM_PROMPTS: () => (/* reexport safe */ _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS),\n/* harmony export */   aragSoulService: () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.aragSoulService),\n/* harmony export */   createAragSoulWorkflow: () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.createAragSoulWorkflow),\n/* harmony export */   \"default\": () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.aragSoulService),\n/* harmony export */   generateFullReportNode: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_2__.generateFullReportNode),\n/* harmony export */   generateUserSoulProfileNode: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_2__.generateUserSoulProfileNode),\n/* harmony export */   rankAndFinalizeNode: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_2__.rankAndFinalizeNode),\n/* harmony export */   runCompatibilityInferenceNode: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_2__.runCompatibilityInferenceNode)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(rsc)/./src/lib/services/arag-soul/types.ts\");\n/* harmony import */ var _prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prompts */ \"(rsc)/./src/lib/services/arag-soul/prompts.ts\");\n/* harmony import */ var _agents__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./agents */ \"(rsc)/./src/lib/services/arag-soul/agents.ts\");\n/* harmony import */ var _workflow__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./workflow */ \"(rsc)/./src/lib/services/arag-soul/workflow.ts\");\n// ARAG-Soul 框架主入口\n\n\n\n\n// 便捷导出\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3NlcnZpY2VzL2FyYWctc291bC9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxrQkFBa0I7QUFFTTtBQUNFO0FBQ0Q7QUFDRTtBQUUzQixPQUFPO0FBQ2lEIiwic291cmNlcyI6WyIvaG9tZS91YnQyMi93b3Jrc3BhY2UvaW5kaWUvbGluZ3hpYWktZ2VtaW5pL3NyYy9saWIvc2VydmljZXMvYXJhZy1zb3VsL2luZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEFSQUctU291bCDmoYbmnrbkuLvlhaXlj6NcblxuZXhwb3J0ICogZnJvbSAnLi90eXBlcyc7XG5leHBvcnQgKiBmcm9tICcuL3Byb21wdHMnO1xuZXhwb3J0ICogZnJvbSAnLi9hZ2VudHMnO1xuZXhwb3J0ICogZnJvbSAnLi93b3JrZmxvdyc7XG5cbi8vIOS+v+aNt+WvvOWHulxuZXhwb3J0IHsgYXJhZ1NvdWxTZXJ2aWNlIGFzIGRlZmF1bHQgfSBmcm9tICcuL3dvcmtmbG93JztcbiJdLCJuYW1lcyI6WyJhcmFnU291bFNlcnZpY2UiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/prompts.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/arag-soul/prompts.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROMPTS: () => (/* binding */ PROMPTS),\n/* harmony export */   SYSTEM_PROMPTS: () => (/* binding */ SYSTEM_PROMPTS)\n/* harmony export */ });\n// ARAG-Soul 框架的 Prompt 模板\nconst PROMPTS = {\n    // 人格洞察 Agent\n    personalityInsight: `\n你是一位专业的心理学家和人格分析师。请基于用户的详细资料，生成一份深度的\"灵魂画像\"。\n\n用户资料：\n姓名: {name}\n年龄: {age}\n性别: {gender}\n自我描述: {selfDescription}\n兴趣爱好: {interests}\n价值观: {values}\n生活方式: {lifestyle}\n感情目标: {relationshipGoals}\n\n请分析并生成：\n1. 核心人格特质（5-7个关键词）\n2. 沟通风格描述\n3. 价值观体系\n4. 情感需求\n5. 生活态度\n6. 一句话人格总结\n\n请以JSON格式返回：\n{\n  \"personalityTraits\": {\n    \"openness\": 0.8,\n    \"conscientiousness\": 0.7,\n    \"extraversion\": 0.6,\n    \"agreeableness\": 0.9,\n    \"neuroticism\": 0.3\n  },\n  \"coreValues\": [\"诚实\", \"成长\", \"家庭\"],\n  \"communicationStyle\": \"温和而深度的交流者\",\n  \"emotionalNeeds\": [\"理解\", \"支持\", \"共同成长\"],\n  \"lifeAttitude\": \"积极向上，注重内在成长\",\n  \"summary\": \"一个温暖、有深度、追求真实连接的灵魂\"\n}\n`,\n    // 深度兼容性推理 Agent\n    compatibilityInference: `\n你是一位资深的情感匹配专家。请分析两个人的兼容性。\n\n用户A的灵魂画像：\n{userSoulProfile}\n\n候选人B的资料：\n姓名: {candidateName}\n年龄: {candidateAge}\n自我描述: {candidateSelfDescription}\n兴趣爱好: {candidateInterests}\n价值观: {candidateValues}\n生活方式: {candidateLifestyle}\n\n请进行深度分析并给出：\n1. 兼容性分数 (0-100)\n2. 详细推理过程\n3. 关系亮点 (3-5个)\n4. 潜在挑战 (2-3个)\n5. 候选人人格摘要\n\n请以JSON格式返回：\n{\n  \"compatibilityScore\": 85,\n  \"reasoning\": \"详细的兼容性分析...\",\n  \"highlights\": [\"共同的价值观\", \"互补的性格\", \"相似的生活目标\"],\n  \"challenges\": [\"沟通方式差异\", \"生活节奏不同\"],\n  \"personalitySummary\": \"一个温暖、独立、有创造力的人\"\n}\n`,\n    // 关系亮点提炼 Agent\n    relationshipHighlight: `\n基于兼容性分析，请深入挖掘这段关系的潜力。\n\n用户A: {userSoulProfile}\n候选人B: {candidateAnalysis}\n\n请提供：\n1. 关系优势 (3-4个具体方面)\n2. 成长机会 (2-3个)\n3. 相处建议 (3-4条实用建议)\n4. 沟通技巧 (2-3个针对性建议)\n\n请以JSON格式返回：\n{\n  \"strengths\": [\"深度的精神连接\", \"互补的技能组合\"],\n  \"growthOpportunities\": [\"共同探索新兴趣\", \"相互学习不同视角\"],\n  \"suggestions\": [\"定期深度对话\", \"尊重彼此的独立空间\"],\n  \"communicationTips\": [\"使用'我'语句表达感受\", \"积极倾听对方观点\"]\n}\n`,\n    // 对话模拟 Agent\n    conversationSimulation: `\n请模拟用户A和候选人B的一段自然对话。\n\n用户A资料: {userProfile}\n候选人B资料: {candidateProfile}\n对话场景: {scenario}\n\n要求：\n1. 对话要体现双方的性格特点\n2. 包含6-8轮对话\n3. 展现自然的互动和化学反应\n4. 体现共同兴趣或价值观\n\n请以JSON格式返回：\n{\n  \"scenario\": \"根据双方特点选择的地点，如咖啡厅\",\n  \"messages\": [\n    {\"speaker\": \"user\", \"content\": \"...\", \"emotion\": \"好奇\"},\n    {\"speaker\": \"candidate\", \"content\": \"...\", \"emotion\": \"友善\"}\n  ],\n  \"analysis\": \"这段对话展现了双方的...\"\n}\n`,\n    // 约会计划生成 Agent\n    datePlanGeneration: `\n基于两人的共同兴趣和性格特点，设计一个完美的初次约会计划。\n\n用户A: {userProfile}\n候选人B: {candidateProfile}\n共同兴趣: {commonInterests}\n\n请设计：\n1. 约会主题和地点\n2. 具体活动安排\n3. 时间规划\n4. 预算建议\n5. 为什么这个计划适合他们\n\n请以JSON格式返回：\n{\n  \"title\": \"艺术与咖啡的邂逅\",\n  \"description\": \"结合艺术欣赏和深度交流的约会\",\n  \"location\": \"市中心艺术区\",\n  \"activities\": [\"参观画廊\", \"咖啡厅聊天\", \"街头艺术漫步\"],\n  \"duration\": \"3-4小时\",\n  \"budget\": \"200-300元\",\n  \"reasoning\": \"这个计划结合了双方对艺术的热爱...\"\n}\n`\n};\n// 系统提示词\nconst SYSTEM_PROMPTS = {\n    default: `你是寡佬AI的专业红娘助手，专门负责深度的情感匹配分析。你的分析要准确、深入、有洞察力，同时保持温暖和专业的语调。请始终以JSON格式返回结构化的结果。`,\n    personality: `你是一位专业的心理学家，擅长人格分析和深度洞察。你的分析要基于心理学理论，同时具有实用性。`,\n    compatibility: `你是一位资深的情感匹配专家，拥有丰富的关系咨询经验。你的分析要客观、全面，既看到优势也识别挑战。`,\n    conversation: `你是一位对话专家，擅长模拟真实的人际互动。你的对话要自然、有趣，体现人物的真实性格。`,\n    dating: `你是一位约会策划专家，了解各种约会形式和场所。你的建议要实用、有创意，适合不同性格的人。`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/prompts.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/types.ts":
/*!*********************************************!*\
  !*** ./src/lib/services/arag-soul/types.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n// ARAG-Soul 框架的类型定义\n// Prompt 模板\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/types.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/workflow.ts":
/*!************************************************!*\
  !*** ./src/lib/services/arag-soul/workflow.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AragSoulService: () => (/* binding */ AragSoulService),\n/* harmony export */   AragSoulStateAnnotation: () => (/* binding */ AragSoulStateAnnotation),\n/* harmony export */   aragSoulService: () => (/* binding */ aragSoulService),\n/* harmony export */   createAragSoulWorkflow: () => (/* binding */ createAragSoulWorkflow)\n/* harmony export */ });\n/* harmony import */ var _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @langchain/langgraph */ \"(rsc)/./node_modules/@langchain/langgraph/index.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var _agent_logger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./agent-logger */ \"(rsc)/./src/lib/services/arag-soul/agent-logger.ts\");\n/* harmony import */ var _agents__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./agents */ \"(rsc)/./src/lib/services/arag-soul/agents.ts\");\n// ARAG-Soul 工作流编排器\n // 暂时不使用\n\n\n\n\n\n// 定义 ARAG-Soul 状态注解\nconst AragSoulStateAnnotation = _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation.Root({\n    // 输入\n    requesterId: _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation,\n    userProfile: _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation,\n    candidatePoolIds: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x,\n        default: ()=>[]\n    }),\n    // 中间状态\n    userSoulProfile: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x\n    }),\n    candidatesWithAnalysis: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x,\n        default: ()=>[]\n    }),\n    rankedCandidates: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x,\n        default: ()=>[]\n    }),\n    // 输出\n    finalMatrix: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x\n    }),\n    // 错误处理\n    error: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x\n    }),\n    step: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x,\n        default: ()=>'initialized'\n    }),\n    // 日志记录器\n    logger: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x\n    })\n});\n// 候选人检索节点\nasync function retrieveCandidatesNode(state) {\n    const agentName = _agent_logger__WEBPACK_IMPORTED_MODULE_3__.AGENT_NAMES.RETRIEVE_CANDIDATES;\n    try {\n        console.log('🔍 检索候选人池...');\n        // 记录开始执行\n        if (state.logger) {\n            await state.logger.startAgent(agentName);\n        }\n        const { requesterId } = state;\n        if (!requesterId) {\n            throw new Error('缺少请求者ID');\n        }\n        // 获取用户资料\n        const userWithProfile = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n            user: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users,\n            profile: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles.userId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, requesterId)).limit(1);\n        if (userWithProfile.length === 0) {\n            throw new Error('用户不存在');\n        }\n        const userProfile = {\n            userId: userWithProfile[0].user.id,\n            name: userWithProfile[0].user.name,\n            age: userWithProfile[0].user.age,\n            gender: userWithProfile[0].user.gender,\n            interests: userWithProfile[0].user.interests,\n            selfDescription: userWithProfile[0].profile?.selfDescription,\n            values: userWithProfile[0].profile?.values,\n            lifestyle: userWithProfile[0].profile?.lifestyle,\n            relationshipGoals: userWithProfile[0].profile?.relationshipGoals\n        };\n        // 检索候选人池（排除自己，选择异性，活跃用户）\n        const candidatePool = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n            id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles.userId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.ne)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, requesterId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.isActive, true), // 异性匹配逻辑\n        userProfile.gender === 'male' ? (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.gender, 'female') : (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.gender, 'male'))).limit(8); // 限制候选人池大小\n        const candidatePoolIds = candidatePool.map((c)=>c.id);\n        console.log(`✅ 检索到 ${candidatePoolIds.length} 个候选人`);\n        // 记录完成执行\n        if (state.logger) {\n            const summary = `找到 ${candidatePoolIds.length} 个候选人`;\n            await state.logger.completeAgent(agentName, {\n                candidatePoolIds,\n                userProfile\n            }, summary);\n        }\n        return {\n            userProfile,\n            candidatePoolIds,\n            step: 'candidates_retrieved'\n        };\n    } catch (error) {\n        console.error('❌ 候选人检索失败:', error);\n        // 记录失败状态\n        if (state.logger) {\n            const errorMessage = error instanceof Error ? error.message : '未知错误';\n            await state.logger.failAgent(agentName, errorMessage);\n        }\n        return {\n            error: `候选人检索失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'candidates_retrieval_failed'\n        };\n    }\n}\n// 条件路由函数\nfunction shouldContinue(state) {\n    if (state.error) {\n        return '__end__';\n    }\n    switch(state.step){\n        case 'candidates_retrieved':\n            return 'generateUserSoulProfile';\n        case 'personality_insight_completed':\n            return 'runCompatibilityInference';\n        case 'compatibility_inference_completed':\n            return 'rankAndFinalize';\n        case 'ranking_completed':\n            return 'generateFullReport';\n        case 'report_generation_completed':\n            return '__end__';\n        default:\n            return '__end__';\n    }\n}\n// 创建 ARAG-Soul 工作流\nfunction createAragSoulWorkflow() {\n    const workflow = new _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.StateGraph(AragSoulStateAnnotation)// 添加节点\n    .addNode('retrieveCandidates', retrieveCandidatesNode).addNode('generateUserSoulProfile', _agents__WEBPACK_IMPORTED_MODULE_4__.generateUserSoulProfileNode).addNode('runCompatibilityInference', _agents__WEBPACK_IMPORTED_MODULE_4__.runCompatibilityInferenceNode).addNode('rankAndFinalize', _agents__WEBPACK_IMPORTED_MODULE_4__.rankAndFinalizeNode).addNode('generateFullReport', _agents__WEBPACK_IMPORTED_MODULE_4__.generateFullReportNode)// 设置入口点\n    .addEdge(_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.START, 'retrieveCandidates')// 添加条件边\n    .addConditionalEdges('retrieveCandidates', shouldContinue, {\n        'generateUserSoulProfile': 'generateUserSoulProfile',\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    }).addConditionalEdges('generateUserSoulProfile', shouldContinue, {\n        'runCompatibilityInference': 'runCompatibilityInference',\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    }).addConditionalEdges('runCompatibilityInference', shouldContinue, {\n        'rankAndFinalize': 'rankAndFinalize',\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    }).addConditionalEdges('rankAndFinalize', shouldContinue, {\n        'generateFullReport': 'generateFullReport',\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    }).addConditionalEdges('generateFullReport', shouldContinue, {\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    });\n    return workflow.compile();\n}\n// ARAG-Soul 服务主入口\nclass AragSoulService {\n    constructor(){\n        this.workflow = createAragSoulWorkflow();\n    }\n    async generateCandidateMatrix(requesterId, matchRequestId) {\n        try {\n            console.log(`🚀 启动 ARAG-Soul 工作流，请求者: ${requesterId}`);\n            // 创建并初始化logger\n            let logger;\n            if (matchRequestId) {\n                logger = new _agent_logger__WEBPACK_IMPORTED_MODULE_3__.AgentLogger(matchRequestId);\n                await logger.initializeAgentLogs();\n            }\n            const initialState = {\n                requesterId,\n                step: 'initialized',\n                logger\n            };\n            // 执行工作流\n            const result = await this.workflow.invoke(initialState);\n            if (result.error) {\n                throw new Error(result.error);\n            }\n            if (!result.finalMatrix) {\n                throw new Error('工作流未生成最终结果');\n            }\n            console.log('✅ ARAG-Soul 工作流执行完成');\n            return result.finalMatrix;\n        } catch (error) {\n            console.error('❌ ARAG-Soul 工作流执行失败:', error);\n            throw error;\n        }\n    }\n    // 流式执行（用于实时监控）\n    async *generateCandidateMatrixStream(requesterId, matchRequestId) {\n        // 创建并初始化logger\n        let logger;\n        if (matchRequestId) {\n            logger = new _agent_logger__WEBPACK_IMPORTED_MODULE_3__.AgentLogger(matchRequestId);\n            await logger.initializeAgentLogs();\n        }\n        const initialState = {\n            requesterId,\n            step: 'initialized',\n            logger\n        };\n        try {\n            for await (const step of this.workflow.stream(initialState)){\n                yield {\n                    step: step.step || 'processing',\n                    data: step,\n                    timestamp: new Date()\n                };\n            }\n        } catch (error) {\n            yield {\n                step: 'error',\n                error: error instanceof Error ? error.message : '未知错误',\n                timestamp: new Date()\n            };\n        }\n    }\n}\n// 导出单例实例\nconst aragSoulService = new AragSoulService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/workflow.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/matching-v2.ts":
/*!*****************************************!*\
  !*** ./src/lib/services/matching-v2.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MatchingServiceV2: () => (/* binding */ MatchingServiceV2)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/env */ \"(rsc)/./src/lib/utils/env.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var _arag_soul__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./arag-soul */ \"(rsc)/./src/lib/services/arag-soul/index.ts\");\n// V2.0 匹配服务 - 智能候选人矩阵\n\n\n\n\n\n// 确保环境变量被加载\n(0,_lib_utils_env__WEBPACK_IMPORTED_MODULE_0__.ensureEnvLoaded)();\nclass MatchingServiceV2 {\n    /**\n   * 处理队列中的匹配请求\n   */ static async processQueuedRequests() {\n        try {\n            console.log('🔄 开始处理匹配队列...');\n            // 获取待处理的任务\n            const pendingTasks = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue.status, 'pending')).limit(5); // 一次处理5个任务\n            if (pendingTasks.length === 0) {\n                console.log('📭 队列为空，无待处理任务');\n                return {\n                    processedCount: 0\n                };\n            }\n            console.log(`📋 发现 ${pendingTasks.length} 个待处理任务`);\n            // 并行处理任务\n            const processingPromises = pendingTasks.map((task)=>this.processSingleRequest(task.matchRequestId, task.requesterId));\n            const results = await Promise.allSettled(processingPromises);\n            const successCount = results.filter((result)=>result.status === 'fulfilled').length;\n            console.log(`✅ 队列处理完成，成功处理 ${successCount}/${pendingTasks.length} 个任务`);\n            return {\n                processedCount: successCount\n            };\n        } catch (error) {\n            console.error('❌ 队列处理失败:', error);\n            return {\n                processedCount: 0\n            };\n        }\n    }\n    /**\n   * 处理单个匹配请求\n   */ static async processSingleRequest(requestId, requesterId) {\n        try {\n            console.log(`🎯 开始处理请求: ${requestId}`);\n            // 更新队列状态为处理中\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue).set({\n                status: 'processing'\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue.matchRequestId, requestId));\n            // 更新请求状态为处理中\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).set({\n                status: 'processing'\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, requestId));\n            // 调用 ARAG-Soul 生成候选人矩阵\n            const candidateMatrix = await _arag_soul__WEBPACK_IMPORTED_MODULE_3__.aragSoulService.generateCandidateMatrix(requesterId, requestId);\n            // 保存结果到数据库\n            await this.saveCandidateMatrix(requestId, candidateMatrix);\n            // 更新状态为完成\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).set({\n                status: 'completed'\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, requestId));\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue).set({\n                status: 'completed'\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue.matchRequestId, requestId));\n            console.log(`✅ 请求处理完成: ${requestId}`);\n        } catch (error) {\n            console.error(`❌ 请求处理失败: ${requestId}`, error);\n            // 更新状态为失败\n            const errorMessage = error instanceof Error ? error.message : '未知错误';\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).set({\n                status: 'failed',\n                errorMessage\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, requestId));\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue).set({\n                status: 'failed'\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue.matchRequestId, requestId));\n        }\n    }\n    /**\n   * 保存候选人矩阵到数据库\n   */ static async saveCandidateMatrix(requestId, matrix) {\n        try {\n            // 保存首席推荐（包含完整数据）\n            const topCandidateData = {\n                requestId,\n                candidateId: matrix.topMatch.candidate.candidateId,\n                rank: 1,\n                compatibilityScore: matrix.topMatch.candidate.compatibilityScore,\n                reasoning: matrix.topMatch.candidate.reasoning,\n                highlights: matrix.topMatch.candidate.highlights,\n                challenges: matrix.topMatch.candidate.challenges,\n                personalitySummary: matrix.topMatch.candidate.personalitySummary,\n                // 首席推荐的额外数据\n                relationshipInsight: matrix.topMatch.relationshipInsight,\n                conversationSimulation: matrix.topMatch.conversationSimulation,\n                datePlan: matrix.topMatch.datePlan,\n                userDecision: 'pending'\n            };\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).values(topCandidateData);\n            // 保存潜力候选人（简化数据）\n            const potentialCandidatesData = matrix.potentialMatches.map((match, index)=>({\n                    requestId,\n                    candidateId: match.candidate.candidateId,\n                    rank: index + 2,\n                    compatibilityScore: match.candidate.compatibilityScore,\n                    reasoning: match.candidate.reasoning,\n                    highlights: match.candidate.highlights,\n                    challenges: match.candidate.challenges,\n                    personalitySummary: match.candidate.personalitySummary,\n                    // 潜力候选人不需要额外数据\n                    relationshipInsight: null,\n                    conversationSimulation: null,\n                    datePlan: null,\n                    userDecision: 'pending'\n                }));\n            if (potentialCandidatesData.length > 0) {\n                await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).values(potentialCandidatesData);\n            }\n            console.log(`💾 候选人矩阵已保存: ${requestId}`);\n        } catch (error) {\n            console.error('保存候选人矩阵失败:', error);\n            throw error;\n        }\n    }\n    /**\n   * 更新用户对候选人的决策（单向匹配逻辑）\n   */ static async updateCandidateDecision(requestId, candidateId, userId, decision) {\n        try {\n            // 验证权限\n            const request = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, requestId)).limit(1);\n            if (request.length === 0 || request[0].requesterId !== userId) {\n                throw new Error('无权限操作此请求');\n            }\n            // 更新决策\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).set({\n                userDecision: decision\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId, requestId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.candidateId, candidateId)));\n            // 如果是喜欢，检查候选人是否已经回应了这个喜欢\n            let mutualMatch = false;\n            let contactInfo = null;\n            if (decision === 'liked') {\n                // 检查候选人是否也喜欢了发起者\n                const candidateResponse = await this.checkCandidateResponse(userId, candidateId);\n                if (candidateResponse.hasLiked) {\n                    // 互相喜欢，更新状态并获取联系信息\n                    mutualMatch = true;\n                    // 更新双方的状态为 mutual_liked\n                    await this.updateMutualMatchStatus(userId, candidateId);\n                    // 获取联系信息\n                    contactInfo = await this.getContactInfo(userId, candidateId);\n                }\n            }\n            return {\n                success: true,\n                mutualMatch,\n                contactInfo\n            };\n        } catch (error) {\n            console.error('更新候选人决策失败:', error);\n            throw error;\n        }\n    }\n    /**\n   * 检查候选人是否已经回应了发起者的喜欢\n   */ static async checkCandidateResponse(requesterId, candidateId) {\n        try {\n            // 查找候选人是否在任何匹配请求中喜欢了发起者\n            const candidateResponse = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, candidateId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.candidateId, requesterId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.userDecision, 'liked')));\n            return {\n                hasLiked: candidateResponse.length > 0\n            };\n        } catch (error) {\n            console.error('检查候选人回应失败:', error);\n            return {\n                hasLiked: false\n            };\n        }\n    }\n    /**\n   * 更新互相匹配的状态\n   */ static async updateMutualMatchStatus(userId, candidateId) {\n        try {\n            // 查找并更新用户对候选人的记录\n            const userToCandidate = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n                id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.id\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.candidateId, candidateId)));\n            if (userToCandidate.length > 0) {\n                await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).set({\n                    userDecision: 'mutual_liked'\n                }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.id, userToCandidate[0].id));\n            }\n            // 查找并更新候选人对用户的记录\n            const candidateToUser = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n                id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.id\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, candidateId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.candidateId, userId)));\n            if (candidateToUser.length > 0) {\n                await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).set({\n                    userDecision: 'mutual_liked'\n                }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.id, candidateToUser[0].id));\n            }\n        } catch (error) {\n            console.error('更新互相匹配状态失败:', error);\n            throw error;\n        }\n    }\n    /**\n   * 获取联系信息\n   */ static async getContactInfo(userId, candidateId) {\n        try {\n            // 获取双方的联系信息\n            const userList = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n                id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id,\n                name: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.name,\n                email: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.email\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, candidateId)));\n            return {\n                users: userList,\n                message: '恭喜！你们互相喜欢了！现在可以开始联系了。'\n            };\n        } catch (error) {\n            console.error('获取联系信息失败:', error);\n            return null;\n        }\n    }\n    /**\n   * 获取用户的匹配历史\n   */ static async getUserMatchHistory(userId, limit = 10) {\n        try {\n            const history = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n                request: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests,\n                candidates: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, userId)).orderBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.createdAt).limit(limit);\n            return history;\n        } catch (error) {\n            console.error('获取匹配历史失败:', error);\n            throw error;\n        }\n    }\n    /**\n   * 获取匹配统计信息\n   */ static async getMatchingStats(userId) {\n        try {\n            // 总请求数\n            const totalRequests = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, userId));\n            // 成功的匹配数（至少喜欢一个候选人）\n            const successfulMatches = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.userDecision, 'liked')));\n            // 互相匹配数\n            // 这里需要更复杂的查询，暂时简化\n            const mutualMatches = 0; // TODO: 实现互相匹配统计\n            return {\n                totalRequests: totalRequests.length,\n                successfulMatches: successfulMatches.length,\n                mutualMatches,\n                successRate: totalRequests.length > 0 ? (successfulMatches.length / totalRequests.length * 100).toFixed(1) : '0'\n            };\n        } catch (error) {\n            console.error('获取匹配统计失败:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/matching-v2.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   createRouteClient: () => (/* binding */ createRouteClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nconst createClient = ()=>{\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createServerComponentClient)({\n        cookies: ()=>cookieStore\n    });\n};\nconst createRouteClient = ()=>{\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createRouteHandlerClient)({\n        cookies: ()=>cookieStore\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/env.ts":
/*!******************************!*\
  !*** ./src/lib/utils/env.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureEnvLoaded: () => (/* binding */ ensureEnvLoaded),\n/* harmony export */   getOptionalEnv: () => (/* binding */ getOptionalEnv),\n/* harmony export */   getRequiredEnv: () => (/* binding */ getRequiredEnv),\n/* harmony export */   validateRequiredEnvVars: () => (/* binding */ validateRequiredEnvVars)\n/* harmony export */ });\n// 环境变量加载工具\nlet envLoaded = false;\nfunction ensureEnvLoaded() {\n    if (envLoaded || \"undefined\" !== 'undefined') {\n        return;\n    }\n    try {\n        // 尝试加载 .env 文件\n        (__webpack_require__(/*! dotenv */ \"(rsc)/./node_modules/dotenv/lib/main.js\").config)({\n            path: '.env'\n        });\n        envLoaded = true;\n        console.log('✅ 环境变量已加载');\n    } catch (error) {\n        // dotenv 可能不存在，尝试其他路径\n        try {\n            (__webpack_require__(/*! dotenv */ \"(rsc)/./node_modules/dotenv/lib/main.js\").config)({\n                path: '.env.local'\n            });\n            envLoaded = true;\n            console.log('✅ 环境变量已从 .env.local 加载');\n        } catch (error2) {\n            console.warn('⚠️ 无法加载 dotenv，使用系统环境变量');\n        }\n    }\n}\nfunction getRequiredEnv(key) {\n    ensureEnvLoaded();\n    const value = process.env[key];\n    if (!value) {\n        throw new Error(`环境变量 ${key} 未设置`);\n    }\n    return value;\n}\nfunction getOptionalEnv(key, defaultValue) {\n    ensureEnvLoaded();\n    return process.env[key] || defaultValue;\n}\n// 验证所有必需的环境变量\nfunction validateRequiredEnvVars() {\n    ensureEnvLoaded();\n    const required = [\n        'DATABASE_URL',\n        'OPENROUTER_API_KEY',\n        'NEXT_PUBLIC_SUPABASE_URL',\n        'NEXT_PUBLIC_SUPABASE_ANON_KEY'\n    ];\n    const missing = required.filter((key)=>!process.env[key]);\n    if (missing.length > 0) {\n        throw new Error(`缺少必需的环境变量: ${missing.join(', ')}`);\n    }\n    console.log('✅ 所有必需的环境变量都已设置');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/env.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/drizzle-orm","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/postgres","vendor-chunks/dotenv","vendor-chunks/@trigger.dev","vendor-chunks/@langchain","vendor-chunks/openai","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/zod-to-json-schema","vendor-chunks/langsmith","vendor-chunks/@cfworker","vendor-chunks/@jsonhero","vendor-chunks/uuid","vendor-chunks/zod-validation-error","vendor-chunks/retry","vendor-chunks/p-queue","vendor-chunks/js-tiktoken","vendor-chunks/eventsource-parser","vendor-chunks/uncrypto","vendor-chunks/mustache","vendor-chunks/eventsource","vendor-chunks/dequal","vendor-chunks/@electric-sql","vendor-chunks/p-timeout","vendor-chunks/p-retry","vendor-chunks/p-finally","vendor-chunks/humanize-duration","vendor-chunks/eventemitter3","vendor-chunks/decamelize","vendor-chunks/camelcase","vendor-chunks/base64-js","vendor-chunks/@google-cloud"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();