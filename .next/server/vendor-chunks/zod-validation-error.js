"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zod-validation-error";
exports.ids = ["vendor-chunks/zod-validation-error"];
exports.modules = {

/***/ "(rsc)/./node_modules/zod-validation-error/dist/esm/ValidationError.js":
/*!***********************************************************************!*\
  !*** ./node_modules/zod-validation-error/dist/esm/ValidationError.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError),\n/* harmony export */   errorMap: () => (/* binding */ errorMap),\n/* harmony export */   fromZodError: () => (/* binding */ fromZodError),\n/* harmony export */   fromZodIssue: () => (/* binding */ fromZodIssue),\n/* harmony export */   isValidationError: () => (/* binding */ isValidationError),\n/* harmony export */   isValidationErrorLike: () => (/* binding */ isValidationErrorLike),\n/* harmony export */   toValidationError: () => (/* binding */ toValidationError)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _utils_joinPath__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/joinPath */ \"(rsc)/./node_modules/zod-validation-error/dist/esm/utils/joinPath.js\");\n/* harmony import */ var _utils_NonEmptyArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/NonEmptyArray */ \"(rsc)/./node_modules/zod-validation-error/dist/esm/utils/NonEmptyArray.js\");\n\n\n\nconst MAX_ISSUES_IN_MESSAGE = 99;\nconst ISSUE_SEPARATOR = '; ';\nconst UNION_SEPARATOR = ', or ';\nconst PREFIX = 'Validation error';\nconst PREFIX_SEPARATOR = ': ';\nclass ValidationError extends Error {\n    details;\n    name;\n    constructor(message, details = []) {\n        super(message);\n        this.details = details;\n        this.name = 'ZodValidationError';\n    }\n    toString() {\n        return this.message;\n    }\n}\nfunction getMessageFromZodIssue(issue, issueSeparator, unionSeparator) {\n    if (issue.code === 'invalid_union') {\n        return issue.unionErrors\n            .reduce((acc, zodError) => {\n            const newIssues = zodError.issues\n                .map((issue) => getMessageFromZodIssue(issue, issueSeparator, unionSeparator))\n                .join(issueSeparator);\n            if (!acc.includes(newIssues)) {\n                acc.push(newIssues);\n            }\n            return acc;\n        }, [])\n            .join(unionSeparator);\n    }\n    if ((0,_utils_NonEmptyArray__WEBPACK_IMPORTED_MODULE_1__.isNonEmptyArray)(issue.path)) {\n        if (issue.path.length === 1) {\n            const identifier = issue.path[0];\n            if (typeof identifier === 'number') {\n                return `${issue.message} at index ${identifier}`;\n            }\n        }\n        return `${issue.message} at \"${(0,_utils_joinPath__WEBPACK_IMPORTED_MODULE_2__.joinPath)(issue.path)}\"`;\n    }\n    return issue.message;\n}\nfunction conditionallyPrefixMessage(reason, prefix, prefixSeparator) {\n    if (prefix !== null) {\n        if (reason.length > 0) {\n            return [prefix, reason].join(prefixSeparator);\n        }\n        return prefix;\n    }\n    if (reason.length > 0) {\n        return reason;\n    }\n    return PREFIX;\n}\nfunction fromZodIssue(issue, options = {}) {\n    const { issueSeparator = ISSUE_SEPARATOR, unionSeparator = UNION_SEPARATOR, prefixSeparator = PREFIX_SEPARATOR, prefix = PREFIX, } = options;\n    const reason = getMessageFromZodIssue(issue, issueSeparator, unionSeparator);\n    const message = conditionallyPrefixMessage(reason, prefix, prefixSeparator);\n    return new ValidationError(message, [issue]);\n}\nfunction fromZodError(zodError, options = {}) {\n    const { maxIssuesInMessage = MAX_ISSUES_IN_MESSAGE, issueSeparator = ISSUE_SEPARATOR, unionSeparator = UNION_SEPARATOR, prefixSeparator = PREFIX_SEPARATOR, prefix = PREFIX, } = options;\n    const reason = zodError.errors\n        .slice(0, maxIssuesInMessage)\n        .map((issue) => getMessageFromZodIssue(issue, issueSeparator, unionSeparator))\n        .join(issueSeparator);\n    const message = conditionallyPrefixMessage(reason, prefix, prefixSeparator);\n    return new ValidationError(message, zodError.errors);\n}\nconst toValidationError = (options = {}) => (err) => {\n    if (err instanceof zod__WEBPACK_IMPORTED_MODULE_0__.ZodError) {\n        return fromZodError(err, options);\n    }\n    if (err instanceof Error) {\n        return err;\n    }\n    return new Error('Unknown error');\n};\nfunction isValidationError(err) {\n    return err instanceof ValidationError;\n}\nfunction isValidationErrorLike(err) {\n    return err instanceof Error && err.name === 'ZodValidationError';\n}\nconst errorMap = (issue, ctx) => {\n    const error = fromZodIssue({\n        ...issue,\n        message: issue.message ?? ctx.defaultError,\n    });\n    return {\n        message: error.message,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/zod-validation-error/dist/esm/ValidationError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/zod-validation-error/dist/esm/utils/NonEmptyArray.js":
/*!***************************************************************************!*\
  !*** ./node_modules/zod-validation-error/dist/esm/utils/NonEmptyArray.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isNonEmptyArray: () => (/* binding */ isNonEmptyArray)\n/* harmony export */ });\nfunction isNonEmptyArray(value) {\n    return value.length !== 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvem9kLXZhbGlkYXRpb24tZXJyb3IvZGlzdC9lc20vdXRpbHMvTm9uRW1wdHlBcnJheS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyIvaG9tZS91YnQyMi93b3Jrc3BhY2UvaW5kaWUvbGluZ3hpYWktZ2VtaW5pL25vZGVfbW9kdWxlcy96b2QtdmFsaWRhdGlvbi1lcnJvci9kaXN0L2VzbS91dGlscy9Ob25FbXB0eUFycmF5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBpc05vbkVtcHR5QXJyYXkodmFsdWUpIHtcbiAgICByZXR1cm4gdmFsdWUubGVuZ3RoICE9PSAwO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/zod-validation-error/dist/esm/utils/NonEmptyArray.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/zod-validation-error/dist/esm/utils/joinPath.js":
/*!**********************************************************************!*\
  !*** ./node_modules/zod-validation-error/dist/esm/utils/joinPath.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   joinPath: () => (/* binding */ joinPath)\n/* harmony export */ });\nconst identifierRegex = /[$_\\p{ID_Start}][$\\u200c\\u200d\\p{ID_Continue}]*/u;\nfunction joinPath(path) {\n    if (path.length === 1) {\n        return path[0].toString();\n    }\n    return path.reduce((acc, item) => {\n        if (typeof item === 'number') {\n            return acc + '[' + item.toString() + ']';\n        }\n        if (item.includes('\"')) {\n            return acc + '[\"' + escapeQuotes(item) + '\"]';\n        }\n        if (!identifierRegex.test(item)) {\n            return acc + '[\"' + item + '\"]';\n        }\n        const separator = acc.length === 0 ? '' : '.';\n        return acc + separator + item;\n    }, '');\n}\nfunction escapeQuotes(str) {\n    return str.replace(/\"/g, '\\\\\"');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvem9kLXZhbGlkYXRpb24tZXJyb3IvZGlzdC9lc20vdXRpbHMvam9pblBhdGguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLCtCQUErQixTQUFTLGtCQUFrQixZQUFZO0FBQy9EO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS91YnQyMi93b3Jrc3BhY2UvaW5kaWUvbGluZ3hpYWktZ2VtaW5pL25vZGVfbW9kdWxlcy96b2QtdmFsaWRhdGlvbi1lcnJvci9kaXN0L2VzbS91dGlscy9qb2luUGF0aC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpZGVudGlmaWVyUmVnZXggPSAvWyRfXFxwe0lEX1N0YXJ0fV1bJFxcdTIwMGNcXHUyMDBkXFxwe0lEX0NvbnRpbnVlfV0qL3U7XG5leHBvcnQgZnVuY3Rpb24gam9pblBhdGgocGF0aCkge1xuICAgIGlmIChwYXRoLmxlbmd0aCA9PT0gMSkge1xuICAgICAgICByZXR1cm4gcGF0aFswXS50b1N0cmluZygpO1xuICAgIH1cbiAgICByZXR1cm4gcGF0aC5yZWR1Y2UoKGFjYywgaXRlbSkgPT4ge1xuICAgICAgICBpZiAodHlwZW9mIGl0ZW0gPT09ICdudW1iZXInKSB7XG4gICAgICAgICAgICByZXR1cm4gYWNjICsgJ1snICsgaXRlbS50b1N0cmluZygpICsgJ10nO1xuICAgICAgICB9XG4gICAgICAgIGlmIChpdGVtLmluY2x1ZGVzKCdcIicpKSB7XG4gICAgICAgICAgICByZXR1cm4gYWNjICsgJ1tcIicgKyBlc2NhcGVRdW90ZXMoaXRlbSkgKyAnXCJdJztcbiAgICAgICAgfVxuICAgICAgICBpZiAoIWlkZW50aWZpZXJSZWdleC50ZXN0KGl0ZW0pKSB7XG4gICAgICAgICAgICByZXR1cm4gYWNjICsgJ1tcIicgKyBpdGVtICsgJ1wiXSc7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3Qgc2VwYXJhdG9yID0gYWNjLmxlbmd0aCA9PT0gMCA/ICcnIDogJy4nO1xuICAgICAgICByZXR1cm4gYWNjICsgc2VwYXJhdG9yICsgaXRlbTtcbiAgICB9LCAnJyk7XG59XG5mdW5jdGlvbiBlc2NhcGVRdW90ZXMoc3RyKSB7XG4gICAgcmV0dXJuIHN0ci5yZXBsYWNlKC9cIi9nLCAnXFxcXFwiJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/zod-validation-error/dist/esm/utils/joinPath.js\n");

/***/ })

};
;