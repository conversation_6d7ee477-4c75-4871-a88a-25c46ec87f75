"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eventsource-parser";
exports.ids = ["vendor-chunks/eventsource-parser"];
exports.modules = {

/***/ "(rsc)/./node_modules/eventsource-parser/dist/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/eventsource-parser/dist/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParseError: () => (/* binding */ ParseError),\n/* harmony export */   createParser: () => (/* binding */ createParser)\n/* harmony export */ });\nclass ParseError extends Error {\n  constructor(message, options) {\n    super(message), this.name = \"ParseError\", this.type = options.type, this.field = options.field, this.value = options.value, this.line = options.line;\n  }\n}\nfunction noop(_arg) {\n}\nfunction createParser(callbacks) {\n  if (typeof callbacks == \"function\")\n    throw new TypeError(\n      \"`callbacks` must be an object, got a function instead. Did you mean `{onEvent: fn}`?\"\n    );\n  const { onEvent = noop, onError = noop, onRetry = noop, onComment } = callbacks;\n  let incompleteLine = \"\", isFirstChunk = !0, id, data = \"\", eventType = \"\";\n  function feed(newChunk) {\n    const chunk = isFirstChunk ? newChunk.replace(/^\\xEF\\xBB\\xBF/, \"\") : newChunk, [complete, incomplete] = splitLines(`${incompleteLine}${chunk}`);\n    for (const line of complete)\n      parseLine(line);\n    incompleteLine = incomplete, isFirstChunk = !1;\n  }\n  function parseLine(line) {\n    if (line === \"\") {\n      dispatchEvent();\n      return;\n    }\n    if (line.startsWith(\":\")) {\n      onComment && onComment(line.slice(line.startsWith(\": \") ? 2 : 1));\n      return;\n    }\n    const fieldSeparatorIndex = line.indexOf(\":\");\n    if (fieldSeparatorIndex !== -1) {\n      const field = line.slice(0, fieldSeparatorIndex), offset = line[fieldSeparatorIndex + 1] === \" \" ? 2 : 1, value = line.slice(fieldSeparatorIndex + offset);\n      processField(field, value, line);\n      return;\n    }\n    processField(line, \"\", line);\n  }\n  function processField(field, value, line) {\n    switch (field) {\n      case \"event\":\n        eventType = value;\n        break;\n      case \"data\":\n        data = `${data}${value}\n`;\n        break;\n      case \"id\":\n        id = value.includes(\"\\0\") ? void 0 : value;\n        break;\n      case \"retry\":\n        /^\\d+$/.test(value) ? onRetry(parseInt(value, 10)) : onError(\n          new ParseError(`Invalid \\`retry\\` value: \"${value}\"`, {\n            type: \"invalid-retry\",\n            value,\n            line\n          })\n        );\n        break;\n      default:\n        onError(\n          new ParseError(\n            `Unknown field \"${field.length > 20 ? `${field.slice(0, 20)}\\u2026` : field}\"`,\n            { type: \"unknown-field\", field, value, line }\n          )\n        );\n        break;\n    }\n  }\n  function dispatchEvent() {\n    data.length > 0 && onEvent({\n      id,\n      event: eventType || void 0,\n      // If the data buffer's last character is a U+000A LINE FEED (LF) character,\n      // then remove the last character from the data buffer.\n      data: data.endsWith(`\n`) ? data.slice(0, -1) : data\n    }), id = void 0, data = \"\", eventType = \"\";\n  }\n  function reset(options = {}) {\n    incompleteLine && options.consume && parseLine(incompleteLine), isFirstChunk = !0, id = void 0, data = \"\", eventType = \"\", incompleteLine = \"\";\n  }\n  return { feed, reset };\n}\nfunction splitLines(chunk) {\n  const lines = [];\n  let incompleteLine = \"\", searchIndex = 0;\n  for (; searchIndex < chunk.length; ) {\n    const crIndex = chunk.indexOf(\"\\r\", searchIndex), lfIndex = chunk.indexOf(`\n`, searchIndex);\n    let lineEnd = -1;\n    if (crIndex !== -1 && lfIndex !== -1 ? lineEnd = Math.min(crIndex, lfIndex) : crIndex !== -1 ? lineEnd = crIndex : lfIndex !== -1 && (lineEnd = lfIndex), lineEnd === -1) {\n      incompleteLine = chunk.slice(searchIndex);\n      break;\n    } else {\n      const line = chunk.slice(searchIndex, lineEnd);\n      lines.push(line), searchIndex = lineEnd + 1, chunk[searchIndex - 1] === \"\\r\" && chunk[searchIndex] === `\n` && searchIndex++;\n    }\n  }\n  return [lines, incompleteLine];\n}\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/eventsource-parser/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/eventsource-parser/dist/stream.js":
/*!********************************************************!*\
  !*** ./node_modules/eventsource-parser/dist/stream.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventSourceParserStream: () => (/* binding */ EventSourceParserStream),\n/* harmony export */   ParseError: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_0__.ParseError)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(rsc)/./node_modules/eventsource-parser/dist/index.js\");\n\n\nclass EventSourceParserStream extends TransformStream {\n  constructor({ onError, onRetry, onComment } = {}) {\n    let parser;\n    super({\n      start(controller) {\n        parser = (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.createParser)({\n          onEvent: (event) => {\n            controller.enqueue(event);\n          },\n          onError(error) {\n            onError === \"terminate\" ? controller.error(error) : typeof onError == \"function\" && onError(error);\n          },\n          onRetry,\n          onComment\n        });\n      },\n      transform(chunk) {\n        parser.feed(chunk);\n      }\n    });\n  }\n}\n\n//# sourceMappingURL=stream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZXZlbnRzb3VyY2UtcGFyc2VyL2Rpc3Qvc3RyZWFtLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEwQztBQUNGO0FBQ3hDO0FBQ0EsZ0JBQWdCLDhCQUE4QixJQUFJO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQix1REFBWTtBQUM3QjtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFJRTtBQUNGIiwic291cmNlcyI6WyIvaG9tZS91YnQyMi93b3Jrc3BhY2UvaW5kaWUvbGluZ3hpYWktZ2VtaW5pL25vZGVfbW9kdWxlcy9ldmVudHNvdXJjZS1wYXJzZXIvZGlzdC9zdHJlYW0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlUGFyc2VyIH0gZnJvbSBcIi4vaW5kZXguanNcIjtcbmltcG9ydCB7IFBhcnNlRXJyb3IgfSBmcm9tIFwiLi9pbmRleC5qc1wiO1xuY2xhc3MgRXZlbnRTb3VyY2VQYXJzZXJTdHJlYW0gZXh0ZW5kcyBUcmFuc2Zvcm1TdHJlYW0ge1xuICBjb25zdHJ1Y3Rvcih7IG9uRXJyb3IsIG9uUmV0cnksIG9uQ29tbWVudCB9ID0ge30pIHtcbiAgICBsZXQgcGFyc2VyO1xuICAgIHN1cGVyKHtcbiAgICAgIHN0YXJ0KGNvbnRyb2xsZXIpIHtcbiAgICAgICAgcGFyc2VyID0gY3JlYXRlUGFyc2VyKHtcbiAgICAgICAgICBvbkV2ZW50OiAoZXZlbnQpID0+IHtcbiAgICAgICAgICAgIGNvbnRyb2xsZXIuZW5xdWV1ZShldmVudCk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBvbkVycm9yKGVycm9yKSB7XG4gICAgICAgICAgICBvbkVycm9yID09PSBcInRlcm1pbmF0ZVwiID8gY29udHJvbGxlci5lcnJvcihlcnJvcikgOiB0eXBlb2Ygb25FcnJvciA9PSBcImZ1bmN0aW9uXCIgJiYgb25FcnJvcihlcnJvcik7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBvblJldHJ5LFxuICAgICAgICAgIG9uQ29tbWVudFxuICAgICAgICB9KTtcbiAgICAgIH0sXG4gICAgICB0cmFuc2Zvcm0oY2h1bmspIHtcbiAgICAgICAgcGFyc2VyLmZlZWQoY2h1bmspO1xuICAgICAgfVxuICAgIH0pO1xuICB9XG59XG5leHBvcnQge1xuICBFdmVudFNvdXJjZVBhcnNlclN0cmVhbSxcbiAgUGFyc2VFcnJvclxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN0cmVhbS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/eventsource-parser/dist/stream.js\n");

/***/ })

};
;