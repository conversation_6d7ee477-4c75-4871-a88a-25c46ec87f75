/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/humanize-duration";
exports.ids = ["vendor-chunks/humanize-duration"];
exports.modules = {

/***/ "(rsc)/./node_modules/humanize-duration/humanize-duration.js":
/*!*************************************************************!*\
  !*** ./node_modules/humanize-duration/humanize-duration.js ***!
  \*************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("var __WEBPACK_AMD_DEFINE_RESULT__;// HumanizeDuration.js - https://git.io/j0HgmQ\n\n// @ts-check\n\n/**\n * @typedef {string | ((unitCount: number) => string)} Unit\n */\n\n/**\n * @typedef {\"y\" | \"mo\" | \"w\" | \"d\" | \"h\" | \"m\" | \"s\" | \"ms\"} UnitName\n */\n\n/**\n * @typedef {Object} UnitMeasures\n * @prop {number} y\n * @prop {number} mo\n * @prop {number} w\n * @prop {number} d\n * @prop {number} h\n * @prop {number} m\n * @prop {number} s\n * @prop {number} ms\n */\n\n/**\n * @internal\n * @typedef {[string, string, string, string, string, string, string, string, string, string]} DigitReplacements\n */\n\n/**\n * @typedef {Object} Language\n * @prop {Unit} y\n * @prop {Unit} mo\n * @prop {Unit} w\n * @prop {Unit} d\n * @prop {Unit} h\n * @prop {Unit} m\n * @prop {Unit} s\n * @prop {Unit} ms\n * @prop {string} [decimal]\n * @prop {string} [delimiter]\n * @prop {DigitReplacements} [_digitReplacements]\n * @prop {boolean} [_numberFirst]\n * @prop {boolean} [_hideCountIf2]\n */\n\n/**\n * @typedef {Object} Options\n * @prop {string} [language]\n * @prop {Record<string, Language>} [languages]\n * @prop {string[]} [fallbacks]\n * @prop {string} [delimiter]\n * @prop {string} [spacer]\n * @prop {boolean} [round]\n * @prop {number} [largest]\n * @prop {UnitName[]} [units]\n * @prop {string} [decimal]\n * @prop {string} [conjunction]\n * @prop {number} [maxDecimalPoints]\n * @prop {UnitMeasures} [unitMeasures]\n * @prop {boolean} [serialComma]\n * @prop {DigitReplacements} [digitReplacements]\n */\n\n/**\n * @internal\n * @typedef {Required<Options>} NormalizedOptions\n */\n\n(function () {\n  // Fallback for `Object.assign` if relevant.\n  var assign =\n    Object.assign ||\n    /** @param {...any} destination */\n    function (destination) {\n      var source;\n      for (var i = 1; i < arguments.length; i++) {\n        source = arguments[i];\n        for (var prop in source) {\n          if (has(source, prop)) {\n            destination[prop] = source[prop];\n          }\n        }\n      }\n      return destination;\n    };\n\n  // Fallback for `Array.isArray` if relevant.\n  var isArray =\n    Array.isArray ||\n    function (arg) {\n      return Object.prototype.toString.call(arg) === \"[object Array]\";\n    };\n\n  // This has to be defined separately because of a bug: we want to alias\n  // `gr` and `el` for backwards-compatiblity. In a breaking change, we can\n  // remove `gr` entirely.\n  // See https://github.com/EvanHahn/HumanizeDuration.js/issues/143 for more.\n  var GREEK = language(\n    function (c) {\n      return c === 1 ? \"χρόνος\" : \"χρόνια\";\n    },\n    function (c) {\n      return c === 1 ? \"μήνας\" : \"μήνες\";\n    },\n    function (c) {\n      return c === 1 ? \"εβδομάδα\" : \"εβδομάδες\";\n    },\n    function (c) {\n      return c === 1 ? \"μέρα\" : \"μέρες\";\n    },\n    function (c) {\n      return c === 1 ? \"ώρα\" : \"ώρες\";\n    },\n    function (c) {\n      return c === 1 ? \"λεπτό\" : \"λεπτά\";\n    },\n    function (c) {\n      return c === 1 ? \"δευτερόλεπτο\" : \"δευτερόλεπτα\";\n    },\n    function (c) {\n      return (c === 1 ? \"χιλιοστό\" : \"χιλιοστά\") + \" του δευτερολέπτου\";\n    },\n    \",\"\n  );\n\n  /**\n   * @internal\n   * @type {Record<string, Language>}\n   */\n  var LANGUAGES = {\n    af: language(\n      \"jaar\",\n      function (c) {\n        return \"maand\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return c === 1 ? \"week\" : \"weke\";\n      },\n      function (c) {\n        return c === 1 ? \"dag\" : \"dae\";\n      },\n      function (c) {\n        return c === 1 ? \"uur\" : \"ure\";\n      },\n      function (c) {\n        return c === 1 ? \"minuut\" : \"minute\";\n      },\n      function (c) {\n        return \"sekonde\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"millisekonde\" + (c === 1 ? \"\" : \"s\");\n      },\n      \",\"\n    ),\n    am: language(\"ዓመት\", \"ወር\", \"ሳምንት\", \"ቀን\", \"ሰዓት\", \"ደቂቃ\", \"ሰከንድ\", \"ሚሊሰከንድ\"),\n    ar: assign(\n      language(\n        function (c) {\n          return [\"سنة\", \"سنتان\", \"سنوات\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"شهر\", \"شهران\", \"أشهر\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"أسبوع\", \"أسبوعين\", \"أسابيع\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"يوم\", \"يومين\", \"أيام\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"ساعة\", \"ساعتين\", \"ساعات\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"دقيقة\", \"دقيقتان\", \"دقائق\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"ثانية\", \"ثانيتان\", \"ثواني\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"جزء من الثانية\", \"جزآن من الثانية\", \"أجزاء من الثانية\"][\n            getArabicForm(c)\n          ];\n        },\n        \",\"\n      ),\n      {\n        delimiter: \" ﻭ \",\n        _hideCountIf2: true,\n        _digitReplacements: [\"۰\", \"١\", \"٢\", \"٣\", \"٤\", \"٥\", \"٦\", \"٧\", \"٨\", \"٩\"]\n      }\n    ),\n    bg: language(\n      function (c) {\n        return [\"години\", \"година\", \"години\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"месеца\", \"месец\", \"месеца\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"седмици\", \"седмица\", \"седмици\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"дни\", \"ден\", \"дни\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"часа\", \"час\", \"часа\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"минути\", \"минута\", \"минути\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"секунди\", \"секунда\", \"секунди\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"милисекунди\", \"милисекунда\", \"милисекунди\"][getSlavicForm(c)];\n      },\n      \",\"\n    ),\n    bn: language(\n      \"বছর\",\n      \"মাস\",\n      \"সপ্তাহ\",\n      \"দিন\",\n      \"ঘন্টা\",\n      \"মিনিট\",\n      \"সেকেন্ড\",\n      \"মিলিসেকেন্ড\"\n    ),\n    ca: language(\n      function (c) {\n        return \"any\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"mes\" + (c === 1 ? \"\" : \"os\");\n      },\n      function (c) {\n        return \"setman\" + (c === 1 ? \"a\" : \"es\");\n      },\n      function (c) {\n        return \"di\" + (c === 1 ? \"a\" : \"es\");\n      },\n      function (c) {\n        return \"hor\" + (c === 1 ? \"a\" : \"es\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"segon\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"milisegon\" + (c === 1 ? \"\" : \"s\");\n      },\n      \",\"\n    ),\n    ckb: language(\n      \"ساڵ\",\n      \"مانگ\",\n      \"هەفتە\",\n      \"ڕۆژ\",\n      \"کاژێر\",\n      \"خولەک\",\n      \"چرکە\",\n      \"میلی چرکە\",\n      \".\"\n    ),\n    cs: language(\n      function (c) {\n        return [\"rok\", \"roku\", \"roky\", \"let\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"měsíc\", \"měsíce\", \"měsíce\", \"měsíců\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"týden\", \"týdne\", \"týdny\", \"týdnů\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"den\", \"dne\", \"dny\", \"dní\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"hodina\", \"hodiny\", \"hodiny\", \"hodin\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"minuta\", \"minuty\", \"minuty\", \"minut\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"sekunda\", \"sekundy\", \"sekundy\", \"sekund\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      function (c) {\n        return [\"milisekunda\", \"milisekundy\", \"milisekundy\", \"milisekund\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      \",\"\n    ),\n    cy: language(\n      \"flwyddyn\",\n      \"mis\",\n      \"wythnos\",\n      \"diwrnod\",\n      \"awr\",\n      \"munud\",\n      \"eiliad\",\n      \"milieiliad\"\n    ),\n    da: language(\n      \"år\",\n      function (c) {\n        return \"måned\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"uge\" + (c === 1 ? \"\" : \"r\");\n      },\n      function (c) {\n        return \"dag\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return \"time\" + (c === 1 ? \"\" : \"r\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"\" : \"ter\");\n      },\n      function (c) {\n        return \"sekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"millisekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      \",\"\n    ),\n    de: language(\n      function (c) {\n        return \"Jahr\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return \"Monat\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return \"Woche\" + (c === 1 ? \"\" : \"n\");\n      },\n      function (c) {\n        return \"Tag\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return \"Stunde\" + (c === 1 ? \"\" : \"n\");\n      },\n      function (c) {\n        return \"Minute\" + (c === 1 ? \"\" : \"n\");\n      },\n      function (c) {\n        return \"Sekunde\" + (c === 1 ? \"\" : \"n\");\n      },\n      function (c) {\n        return \"Millisekunde\" + (c === 1 ? \"\" : \"n\");\n      },\n      \",\"\n    ),\n    el: GREEK,\n    en: language(\n      function (c) {\n        return \"year\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"month\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"week\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"day\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"hour\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"minute\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"second\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"millisecond\" + (c === 1 ? \"\" : \"s\");\n      }\n    ),\n    eo: language(\n      function (c) {\n        return \"jaro\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"monato\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"semajno\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"tago\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"horo\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"minuto\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"sekundo\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"milisekundo\" + (c === 1 ? \"\" : \"j\");\n      },\n      \",\"\n    ),\n    es: language(\n      function (c) {\n        return \"año\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"mes\" + (c === 1 ? \"\" : \"es\");\n      },\n      function (c) {\n        return \"semana\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"día\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"hora\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"minuto\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"segundo\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"milisegundo\" + (c === 1 ? \"\" : \"s\");\n      },\n      \",\"\n    ),\n    et: language(\n      function (c) {\n        return \"aasta\" + (c === 1 ? \"\" : \"t\");\n      },\n      function (c) {\n        return \"kuu\" + (c === 1 ? \"\" : \"d\");\n      },\n      function (c) {\n        return \"nädal\" + (c === 1 ? \"\" : \"at\");\n      },\n      function (c) {\n        return \"päev\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"tund\" + (c === 1 ? \"\" : \"i\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"\" : \"it\");\n      },\n      function (c) {\n        return \"sekund\" + (c === 1 ? \"\" : \"it\");\n      },\n      function (c) {\n        return \"millisekund\" + (c === 1 ? \"\" : \"it\");\n      },\n      \",\"\n    ),\n    eu: language(\n      \"urte\",\n      \"hilabete\",\n      \"aste\",\n      \"egun\",\n      \"ordu\",\n      \"minutu\",\n      \"segundo\",\n      \"milisegundo\",\n      \",\"\n    ),\n    fa: language(\n      \"سال\",\n      \"ماه\",\n      \"هفته\",\n      \"روز\",\n      \"ساعت\",\n      \"دقیقه\",\n      \"ثانیه\",\n      \"میلی ثانیه\"\n    ),\n    fi: language(\n      function (c) {\n        return c === 1 ? \"vuosi\" : \"vuotta\";\n      },\n      function (c) {\n        return c === 1 ? \"kuukausi\" : \"kuukautta\";\n      },\n      function (c) {\n        return \"viikko\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"päivä\" + (c === 1 ? \"\" : \"ä\");\n      },\n      function (c) {\n        return \"tunti\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"minuutti\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"sekunti\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"millisekunti\" + (c === 1 ? \"\" : \"a\");\n      },\n      \",\"\n    ),\n    fo: language(\n      \"ár\",\n      function (c) {\n        return c === 1 ? \"mánaður\" : \"mánaðir\";\n      },\n      function (c) {\n        return c === 1 ? \"vika\" : \"vikur\";\n      },\n      function (c) {\n        return c === 1 ? \"dagur\" : \"dagar\";\n      },\n      function (c) {\n        return c === 1 ? \"tími\" : \"tímar\";\n      },\n      function (c) {\n        return c === 1 ? \"minuttur\" : \"minuttir\";\n      },\n      \"sekund\",\n      \"millisekund\",\n      \",\"\n    ),\n    fr: language(\n      function (c) {\n        return \"an\" + (c >= 2 ? \"s\" : \"\");\n      },\n      \"mois\",\n      function (c) {\n        return \"semaine\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"jour\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"heure\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"minute\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"seconde\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"milliseconde\" + (c >= 2 ? \"s\" : \"\");\n      },\n      \",\"\n    ),\n    gr: GREEK,\n    he: language(\n      function (c) {\n        return c === 1 ? \"שנה\" : \"שנים\";\n      },\n      function (c) {\n        return c === 1 ? \"חודש\" : \"חודשים\";\n      },\n      function (c) {\n        return c === 1 ? \"שבוע\" : \"שבועות\";\n      },\n      function (c) {\n        return c === 1 ? \"יום\" : \"ימים\";\n      },\n      function (c) {\n        return c === 1 ? \"שעה\" : \"שעות\";\n      },\n      function (c) {\n        return c === 1 ? \"דקה\" : \"דקות\";\n      },\n      function (c) {\n        return c === 1 ? \"שניה\" : \"שניות\";\n      },\n      function (c) {\n        return c === 1 ? \"מילישנייה\" : \"מילישניות\";\n      }\n    ),\n    hr: language(\n      function (c) {\n        if (c % 10 === 2 || c % 10 === 3 || c % 10 === 4) {\n          return \"godine\";\n        }\n        return \"godina\";\n      },\n      function (c) {\n        if (c === 1) {\n          return \"mjesec\";\n        } else if (c === 2 || c === 3 || c === 4) {\n          return \"mjeseca\";\n        }\n        return \"mjeseci\";\n      },\n      function (c) {\n        if (c % 10 === 1 && c !== 11) {\n          return \"tjedan\";\n        }\n        return \"tjedna\";\n      },\n      function (c) {\n        return c === 1 ? \"dan\" : \"dana\";\n      },\n      function (c) {\n        if (c === 1) {\n          return \"sat\";\n        } else if (c === 2 || c === 3 || c === 4) {\n          return \"sata\";\n        }\n        return \"sati\";\n      },\n      function (c) {\n        var mod10 = c % 10;\n        if ((mod10 === 2 || mod10 === 3 || mod10 === 4) && (c < 10 || c > 14)) {\n          return \"minute\";\n        }\n        return \"minuta\";\n      },\n      function (c) {\n        var mod10 = c % 10;\n        if (mod10 === 5 || (Math.floor(c) === c && c >= 10 && c <= 19)) {\n          return \"sekundi\";\n        } else if (mod10 === 1) {\n          return \"sekunda\";\n        } else if (mod10 === 2 || mod10 === 3 || mod10 === 4) {\n          return \"sekunde\";\n        }\n        return \"sekundi\";\n      },\n      function (c) {\n        if (c === 1) {\n          return \"milisekunda\";\n        } else if (c % 10 === 2 || c % 10 === 3 || c % 10 === 4) {\n          return \"milisekunde\";\n        }\n        return \"milisekundi\";\n      },\n      \",\"\n    ),\n    hi: language(\n      \"साल\",\n      function (c) {\n        return c === 1 ? \"महीना\" : \"महीने\";\n      },\n      function (c) {\n        return c === 1 ? \"हफ़्ता\" : \"हफ्ते\";\n      },\n      \"दिन\",\n      function (c) {\n        return c === 1 ? \"घंटा\" : \"घंटे\";\n      },\n      \"मिनट\",\n      \"सेकंड\",\n      \"मिलीसेकंड\"\n    ),\n    hu: language(\n      \"év\",\n      \"hónap\",\n      \"hét\",\n      \"nap\",\n      \"óra\",\n      \"perc\",\n      \"másodperc\",\n      \"ezredmásodperc\",\n      \",\"\n    ),\n    id: language(\n      \"tahun\",\n      \"bulan\",\n      \"minggu\",\n      \"hari\",\n      \"jam\",\n      \"menit\",\n      \"detik\",\n      \"milidetik\"\n    ),\n    is: language(\n      \"ár\",\n      function (c) {\n        return \"mánuð\" + (c === 1 ? \"ur\" : \"ir\");\n      },\n      function (c) {\n        return \"vik\" + (c === 1 ? \"a\" : \"ur\");\n      },\n      function (c) {\n        return \"dag\" + (c === 1 ? \"ur\" : \"ar\");\n      },\n      function (c) {\n        return \"klukkutím\" + (c === 1 ? \"i\" : \"ar\");\n      },\n      function (c) {\n        return \"mínút\" + (c === 1 ? \"a\" : \"ur\");\n      },\n      function (c) {\n        return \"sekúnd\" + (c === 1 ? \"a\" : \"ur\");\n      },\n      function (c) {\n        return \"millisekúnd\" + (c === 1 ? \"a\" : \"ur\");\n      }\n    ),\n    it: language(\n      function (c) {\n        return \"ann\" + (c === 1 ? \"o\" : \"i\");\n      },\n      function (c) {\n        return \"mes\" + (c === 1 ? \"e\" : \"i\");\n      },\n      function (c) {\n        return \"settiman\" + (c === 1 ? \"a\" : \"e\");\n      },\n      function (c) {\n        return \"giorn\" + (c === 1 ? \"o\" : \"i\");\n      },\n      function (c) {\n        return \"or\" + (c === 1 ? \"a\" : \"e\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"o\" : \"i\");\n      },\n      function (c) {\n        return \"second\" + (c === 1 ? \"o\" : \"i\");\n      },\n      function (c) {\n        return \"millisecond\" + (c === 1 ? \"o\" : \"i\");\n      },\n      \",\"\n    ),\n    ja: language(\"年\", \"ヶ月\", \"週間\", \"日\", \"時間\", \"分\", \"秒\", \"ミリ秒\"),\n    km: language(\n      \"ឆ្នាំ\",\n      \"ខែ\",\n      \"សប្តាហ៍\",\n      \"ថ្ងៃ\",\n      \"ម៉ោង\",\n      \"នាទី\",\n      \"វិនាទី\",\n      \"មិល្លីវិនាទី\"\n    ),\n    kn: language(\n      function (c) {\n        return c === 1 ? \"ವರ್ಷ\" : \"ವರ್ಷಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ತಿಂಗಳು\" : \"ತಿಂಗಳುಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ವಾರ\" : \"ವಾರಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ದಿನ\" : \"ದಿನಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ಗಂಟೆ\" : \"ಗಂಟೆಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ನಿಮಿಷ\" : \"ನಿಮಿಷಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ಸೆಕೆಂಡ್\" : \"ಸೆಕೆಂಡುಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ಮಿಲಿಸೆಕೆಂಡ್\" : \"ಮಿಲಿಸೆಕೆಂಡುಗಳು\";\n      }\n    ),\n    ko: language(\"년\", \"개월\", \"주일\", \"일\", \"시간\", \"분\", \"초\", \"밀리 초\"),\n    ku: language(\n      \"sal\",\n      \"meh\",\n      \"hefte\",\n      \"roj\",\n      \"seet\",\n      \"deqe\",\n      \"saniye\",\n      \"mîlîçirk\",\n      \",\"\n    ),\n    lo: language(\n      \"ປີ\",\n      \"ເດືອນ\",\n      \"ອາທິດ\",\n      \"ມື້\",\n      \"ຊົ່ວໂມງ\",\n      \"ນາທີ\",\n      \"ວິນາທີ\",\n      \"ມິນລິວິນາທີ\",\n      \",\"\n    ),\n    lt: language(\n      function (c) {\n        return c % 10 === 0 || (c % 100 >= 10 && c % 100 <= 20)\n          ? \"metų\"\n          : \"metai\";\n      },\n      function (c) {\n        return [\"mėnuo\", \"mėnesiai\", \"mėnesių\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"savaitė\", \"savaitės\", \"savaičių\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"diena\", \"dienos\", \"dienų\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"valanda\", \"valandos\", \"valandų\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"minutė\", \"minutės\", \"minučių\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"sekundė\", \"sekundės\", \"sekundžių\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"milisekundė\", \"milisekundės\", \"milisekundžių\"][\n          getLithuanianForm(c)\n        ];\n      },\n      \",\"\n    ),\n    lv: language(\n      function (c) {\n        return getLatvianForm(c) ? \"gads\" : \"gadi\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"mēnesis\" : \"mēneši\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"nedēļa\" : \"nedēļas\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"diena\" : \"dienas\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"stunda\" : \"stundas\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"minūte\" : \"minūtes\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"sekunde\" : \"sekundes\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"milisekunde\" : \"milisekundes\";\n      },\n      \",\"\n    ),\n    mk: language(\n      function (c) {\n        return c === 1 ? \"година\" : \"години\";\n      },\n      function (c) {\n        return c === 1 ? \"месец\" : \"месеци\";\n      },\n      function (c) {\n        return c === 1 ? \"недела\" : \"недели\";\n      },\n      function (c) {\n        return c === 1 ? \"ден\" : \"дена\";\n      },\n      function (c) {\n        return c === 1 ? \"час\" : \"часа\";\n      },\n      function (c) {\n        return c === 1 ? \"минута\" : \"минути\";\n      },\n      function (c) {\n        return c === 1 ? \"секунда\" : \"секунди\";\n      },\n      function (c) {\n        return c === 1 ? \"милисекунда\" : \"милисекунди\";\n      },\n      \",\"\n    ),\n    mn: language(\n      \"жил\",\n      \"сар\",\n      \"долоо хоног\",\n      \"өдөр\",\n      \"цаг\",\n      \"минут\",\n      \"секунд\",\n      \"миллисекунд\"\n    ),\n    mr: language(\n      function (c) {\n        return c === 1 ? \"वर्ष\" : \"वर्षे\";\n      },\n      function (c) {\n        return c === 1 ? \"महिना\" : \"महिने\";\n      },\n      function (c) {\n        return c === 1 ? \"आठवडा\" : \"आठवडे\";\n      },\n      \"दिवस\",\n      \"तास\",\n      function (c) {\n        return c === 1 ? \"मिनिट\" : \"मिनिटे\";\n      },\n      \"सेकंद\",\n      \"मिलिसेकंद\"\n    ),\n    ms: language(\n      \"tahun\",\n      \"bulan\",\n      \"minggu\",\n      \"hari\",\n      \"jam\",\n      \"minit\",\n      \"saat\",\n      \"milisaat\"\n    ),\n    nl: language(\n      \"jaar\",\n      function (c) {\n        return c === 1 ? \"maand\" : \"maanden\";\n      },\n      function (c) {\n        return c === 1 ? \"week\" : \"weken\";\n      },\n      function (c) {\n        return c === 1 ? \"dag\" : \"dagen\";\n      },\n      \"uur\",\n      function (c) {\n        return c === 1 ? \"minuut\" : \"minuten\";\n      },\n      function (c) {\n        return c === 1 ? \"seconde\" : \"seconden\";\n      },\n      function (c) {\n        return c === 1 ? \"milliseconde\" : \"milliseconden\";\n      },\n      \",\"\n    ),\n    no: language(\n      \"år\",\n      function (c) {\n        return \"måned\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"uke\" + (c === 1 ? \"\" : \"r\");\n      },\n      function (c) {\n        return \"dag\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"time\" + (c === 1 ? \"\" : \"r\");\n      },\n      function (c) {\n        return \"minutt\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"sekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"millisekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      \",\"\n    ),\n    pl: language(\n      function (c) {\n        return [\"rok\", \"roku\", \"lata\", \"lat\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"miesiąc\", \"miesiąca\", \"miesiące\", \"miesięcy\"][\n          getPolishForm(c)\n        ];\n      },\n      function (c) {\n        return [\"tydzień\", \"tygodnia\", \"tygodnie\", \"tygodni\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"dzień\", \"dnia\", \"dni\", \"dni\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"godzina\", \"godziny\", \"godziny\", \"godzin\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"minuta\", \"minuty\", \"minuty\", \"minut\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"sekunda\", \"sekundy\", \"sekundy\", \"sekund\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"milisekunda\", \"milisekundy\", \"milisekundy\", \"milisekund\"][\n          getPolishForm(c)\n        ];\n      },\n      \",\"\n    ),\n    pt: language(\n      function (c) {\n        return \"ano\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return c === 1 ? \"mês\" : \"meses\";\n      },\n      function (c) {\n        return \"semana\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"dia\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"hora\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"minuto\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"segundo\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"milissegundo\" + (c === 1 ? \"\" : \"s\");\n      },\n      \",\"\n    ),\n    ro: language(\n      function (c) {\n        return c === 1 ? \"an\" : \"ani\";\n      },\n      function (c) {\n        return c === 1 ? \"lună\" : \"luni\";\n      },\n      function (c) {\n        return c === 1 ? \"săptămână\" : \"săptămâni\";\n      },\n      function (c) {\n        return c === 1 ? \"zi\" : \"zile\";\n      },\n      function (c) {\n        return c === 1 ? \"oră\" : \"ore\";\n      },\n      function (c) {\n        return c === 1 ? \"minut\" : \"minute\";\n      },\n      function (c) {\n        return c === 1 ? \"secundă\" : \"secunde\";\n      },\n      function (c) {\n        return c === 1 ? \"milisecundă\" : \"milisecunde\";\n      },\n      \",\"\n    ),\n    ru: language(\n      function (c) {\n        return [\"лет\", \"год\", \"года\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"месяцев\", \"месяц\", \"месяца\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"недель\", \"неделя\", \"недели\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"дней\", \"день\", \"дня\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"часов\", \"час\", \"часа\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"минут\", \"минута\", \"минуты\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"секунд\", \"секунда\", \"секунды\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"миллисекунд\", \"миллисекунда\", \"миллисекунды\"][\n          getSlavicForm(c)\n        ];\n      },\n      \",\"\n    ),\n    sq: language(\n      function (c) {\n        return c === 1 ? \"vit\" : \"vjet\";\n      },\n      \"muaj\",\n      \"javë\",\n      \"ditë\",\n      \"orë\",\n      function (c) {\n        return \"minut\" + (c === 1 ? \"ë\" : \"a\");\n      },\n      function (c) {\n        return \"sekond\" + (c === 1 ? \"ë\" : \"a\");\n      },\n      function (c) {\n        return \"milisekond\" + (c === 1 ? \"ë\" : \"a\");\n      },\n      \",\"\n    ),\n    sr: language(\n      function (c) {\n        return [\"години\", \"година\", \"године\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"месеци\", \"месец\", \"месеца\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"недељи\", \"недеља\", \"недеље\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"дани\", \"дан\", \"дана\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"сати\", \"сат\", \"сата\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"минута\", \"минут\", \"минута\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"секунди\", \"секунда\", \"секунде\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"милисекунди\", \"милисекунда\", \"милисекунде\"][getSlavicForm(c)];\n      },\n      \",\"\n    ),\n    sr_Latn: language(\n      function (c) {\n        return [\"godini\", \"godina\", \"godine\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"meseci\", \"mesec\", \"meseca\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"nedelji\", \"nedelja\", \"nedelje\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"dani\", \"dan\", \"dana\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"sati\", \"sat\", \"sata\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"minuta\", \"minut\", \"minuta\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"sekundi\", \"sekunda\", \"sekunde\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"milisekundi\", \"milisekunda\", \"milisekunde\"][getSlavicForm(c)];\n      },\n      \",\"\n    ),\n    ta: language(\n      function (c) {\n        return c === 1 ? \"வருடம்\" : \"ஆண்டுகள்\";\n      },\n      function (c) {\n        return c === 1 ? \"மாதம்\" : \"மாதங்கள்\";\n      },\n      function (c) {\n        return c === 1 ? \"வாரம்\" : \"வாரங்கள்\";\n      },\n      function (c) {\n        return c === 1 ? \"நாள்\" : \"நாட்கள்\";\n      },\n      function (c) {\n        return c === 1 ? \"மணி\" : \"மணிநேரம்\";\n      },\n      function (c) {\n        return \"நிமிட\" + (c === 1 ? \"ம்\" : \"ங்கள்\");\n      },\n      function (c) {\n        return \"வினாடி\" + (c === 1 ? \"\" : \"கள்\");\n      },\n      function (c) {\n        return \"மில்லி விநாடி\" + (c === 1 ? \"\" : \"கள்\");\n      }\n    ),\n    te: language(\n      function (c) {\n        return \"సంవత్స\" + (c === 1 ? \"రం\" : \"రాల\");\n      },\n      function (c) {\n        return \"నెల\" + (c === 1 ? \"\" : \"ల\");\n      },\n      function (c) {\n        return c === 1 ? \"వారం\" : \"వారాలు\";\n      },\n      function (c) {\n        return \"రోజు\" + (c === 1 ? \"\" : \"లు\");\n      },\n      function (c) {\n        return \"గంట\" + (c === 1 ? \"\" : \"లు\");\n      },\n      function (c) {\n        return c === 1 ? \"నిమిషం\" : \"నిమిషాలు\";\n      },\n      function (c) {\n        return c === 1 ? \"సెకను\" : \"సెకన్లు\";\n      },\n      function (c) {\n        return c === 1 ? \"మిల్లీసెకన్\" : \"మిల్లీసెకన్లు\";\n      }\n    ),\n    uk: language(\n      function (c) {\n        return [\"років\", \"рік\", \"роки\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"місяців\", \"місяць\", \"місяці\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"тижнів\", \"тиждень\", \"тижні\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"днів\", \"день\", \"дні\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"годин\", \"година\", \"години\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"хвилин\", \"хвилина\", \"хвилини\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"секунд\", \"секунда\", \"секунди\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"мілісекунд\", \"мілісекунда\", \"мілісекунди\"][getSlavicForm(c)];\n      },\n      \",\"\n    ),\n    ur: language(\n      \"سال\",\n      function (c) {\n        return c === 1 ? \"مہینہ\" : \"مہینے\";\n      },\n      function (c) {\n        return c === 1 ? \"ہفتہ\" : \"ہفتے\";\n      },\n      \"دن\",\n      function (c) {\n        return c === 1 ? \"گھنٹہ\" : \"گھنٹے\";\n      },\n      \"منٹ\",\n      \"سیکنڈ\",\n      \"ملی سیکنڈ\"\n    ),\n    sk: language(\n      function (c) {\n        return [\"rok\", \"roky\", \"roky\", \"rokov\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"mesiac\", \"mesiace\", \"mesiace\", \"mesiacov\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      function (c) {\n        return [\"týždeň\", \"týždne\", \"týždne\", \"týždňov\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      function (c) {\n        return [\"deň\", \"dni\", \"dni\", \"dní\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"hodina\", \"hodiny\", \"hodiny\", \"hodín\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"minúta\", \"minúty\", \"minúty\", \"minút\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"sekunda\", \"sekundy\", \"sekundy\", \"sekúnd\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      function (c) {\n        return [\"milisekunda\", \"milisekundy\", \"milisekundy\", \"milisekúnd\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      \",\"\n    ),\n    sl: language(\n      function (c) {\n        if (c % 10 === 1) {\n          return \"leto\";\n        } else if (c % 100 === 2) {\n          return \"leti\";\n        } else if (\n          c % 100 === 3 ||\n          c % 100 === 4 ||\n          (Math.floor(c) !== c && c % 100 <= 5)\n        ) {\n          return \"leta\";\n        } else {\n          return \"let\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"mesec\";\n        } else if (c % 100 === 2 || (Math.floor(c) !== c && c % 100 <= 5)) {\n          return \"meseca\";\n        } else if (c % 10 === 3 || c % 10 === 4) {\n          return \"mesece\";\n        } else {\n          return \"mesecev\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"teden\";\n        } else if (c % 10 === 2 || (Math.floor(c) !== c && c % 100 <= 4)) {\n          return \"tedna\";\n        } else if (c % 10 === 3 || c % 10 === 4) {\n          return \"tedne\";\n        } else {\n          return \"tednov\";\n        }\n      },\n      function (c) {\n        return c % 100 === 1 ? \"dan\" : \"dni\";\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"ura\";\n        } else if (c % 100 === 2) {\n          return \"uri\";\n        } else if (c % 10 === 3 || c % 10 === 4 || Math.floor(c) !== c) {\n          return \"ure\";\n        } else {\n          return \"ur\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"minuta\";\n        } else if (c % 10 === 2) {\n          return \"minuti\";\n        } else if (\n          c % 10 === 3 ||\n          c % 10 === 4 ||\n          (Math.floor(c) !== c && c % 100 <= 4)\n        ) {\n          return \"minute\";\n        } else {\n          return \"minut\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"sekunda\";\n        } else if (c % 100 === 2) {\n          return \"sekundi\";\n        } else if (c % 100 === 3 || c % 100 === 4 || Math.floor(c) !== c) {\n          return \"sekunde\";\n        } else {\n          return \"sekund\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"milisekunda\";\n        } else if (c % 100 === 2) {\n          return \"milisekundi\";\n        } else if (c % 100 === 3 || c % 100 === 4 || Math.floor(c) !== c) {\n          return \"milisekunde\";\n        } else {\n          return \"milisekund\";\n        }\n      },\n      \",\"\n    ),\n    sv: language(\n      \"år\",\n      function (c) {\n        return \"månad\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"veck\" + (c === 1 ? \"a\" : \"or\");\n      },\n      function (c) {\n        return \"dag\" + (c === 1 ? \"\" : \"ar\");\n      },\n      function (c) {\n        return \"timm\" + (c === 1 ? \"e\" : \"ar\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"sekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"millisekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      \",\"\n    ),\n    sw: assign(\n      language(\n        function (c) {\n          return c === 1 ? \"mwaka\" : \"miaka\";\n        },\n        function (c) {\n          return c === 1 ? \"mwezi\" : \"miezi\";\n        },\n        \"wiki\",\n        function (c) {\n          return c === 1 ? \"siku\" : \"masiku\";\n        },\n        function (c) {\n          return c === 1 ? \"saa\" : \"masaa\";\n        },\n        \"dakika\",\n        \"sekunde\",\n        \"milisekunde\"\n      ),\n      { _numberFirst: true }\n    ),\n    tr: language(\n      \"yıl\",\n      \"ay\",\n      \"hafta\",\n      \"gün\",\n      \"saat\",\n      \"dakika\",\n      \"saniye\",\n      \"milisaniye\",\n      \",\"\n    ),\n    th: language(\n      \"ปี\",\n      \"เดือน\",\n      \"สัปดาห์\",\n      \"วัน\",\n      \"ชั่วโมง\",\n      \"นาที\",\n      \"วินาที\",\n      \"มิลลิวินาที\"\n    ),\n    uz: language(\n      \"yil\",\n      \"oy\",\n      \"hafta\",\n      \"kun\",\n      \"soat\",\n      \"minut\",\n      \"sekund\",\n      \"millisekund\"\n    ),\n    uz_CYR: language(\n      \"йил\",\n      \"ой\",\n      \"ҳафта\",\n      \"кун\",\n      \"соат\",\n      \"минут\",\n      \"секунд\",\n      \"миллисекунд\"\n    ),\n    vi: language(\n      \"năm\",\n      \"tháng\",\n      \"tuần\",\n      \"ngày\",\n      \"giờ\",\n      \"phút\",\n      \"giây\",\n      \"mili giây\",\n      \",\"\n    ),\n    zh_CN: language(\"年\", \"个月\", \"周\", \"天\", \"小时\", \"分钟\", \"秒\", \"毫秒\"),\n    zh_TW: language(\"年\", \"個月\", \"周\", \"天\", \"小時\", \"分鐘\", \"秒\", \"毫秒\")\n  };\n\n  /**\n   * Helper function for creating language definitions.\n   *\n   * @internal\n   * @param {Unit} y\n   * @param {Unit} mo\n   * @param {Unit} w\n   * @param {Unit} d\n   * @param {Unit} h\n   * @param {Unit} m\n   * @param {Unit} s\n   * @param {Unit} ms\n   * @param {string} [decimal]\n   * @returns {Language}\n   */\n  function language(y, mo, w, d, h, m, s, ms, decimal) {\n    /** @type {Language} */\n    var result = { y: y, mo: mo, w: w, d: d, h: h, m: m, s: s, ms: ms };\n    if (typeof decimal !== \"undefined\") {\n      result.decimal = decimal;\n    }\n    return result;\n  }\n\n  /**\n   * Helper function for Arabic.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2}\n   */\n  function getArabicForm(c) {\n    if (c === 2) {\n      return 1;\n    }\n    if (c > 2 && c < 11) {\n      return 2;\n    }\n    return 0;\n  }\n\n  /**\n   * Helper function for Polish.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2 | 3}\n   */\n  function getPolishForm(c) {\n    if (c === 1) {\n      return 0;\n    }\n    if (Math.floor(c) !== c) {\n      return 1;\n    }\n    if (c % 10 >= 2 && c % 10 <= 4 && !(c % 100 > 10 && c % 100 < 20)) {\n      return 2;\n    }\n    return 3;\n  }\n\n  /**\n   * Helper function for Slavic languages.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2 | 3}\n   */\n  function getSlavicForm(c) {\n    if (Math.floor(c) !== c) {\n      return 2;\n    }\n    if (\n      (c % 100 >= 5 && c % 100 <= 20) ||\n      (c % 10 >= 5 && c % 10 <= 9) ||\n      c % 10 === 0\n    ) {\n      return 0;\n    }\n    if (c % 10 === 1) {\n      return 1;\n    }\n    if (c > 1) {\n      return 2;\n    }\n    return 0;\n  }\n\n  /**\n   * Helper function for Czech or Slovak.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2 | 3}\n   */\n  function getCzechOrSlovakForm(c) {\n    if (c === 1) {\n      return 0;\n    }\n    if (Math.floor(c) !== c) {\n      return 1;\n    }\n    if (c % 10 >= 2 && c % 10 <= 4 && c % 100 < 10) {\n      return 2;\n    }\n    return 3;\n  }\n\n  /**\n   * Helper function for Lithuanian.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2}\n   */\n  function getLithuanianForm(c) {\n    if (c === 1 || (c % 10 === 1 && c % 100 > 20)) {\n      return 0;\n    }\n    if (\n      Math.floor(c) !== c ||\n      (c % 10 >= 2 && c % 100 > 20) ||\n      (c % 10 >= 2 && c % 100 < 10)\n    ) {\n      return 1;\n    }\n    return 2;\n  }\n\n  /**\n   * Helper function for Latvian.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {boolean}\n   */\n  function getLatvianForm(c) {\n    return c % 10 === 1 && c % 100 !== 11;\n  }\n\n  /**\n   * @internal\n   * @template T\n   * @param {T} obj\n   * @param {keyof T} key\n   * @returns {boolean}\n   */\n  function has(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n  }\n\n  /**\n   * @internal\n   * @param {Pick<Required<Options>, \"language\" | \"fallbacks\" | \"languages\">} options\n   * @throws {Error} Throws an error if language is not found.\n   * @returns {Language}\n   */\n  function getLanguage(options) {\n    var possibleLanguages = [options.language];\n\n    if (has(options, \"fallbacks\")) {\n      if (isArray(options.fallbacks) && options.fallbacks.length) {\n        possibleLanguages = possibleLanguages.concat(options.fallbacks);\n      } else {\n        throw new Error(\"fallbacks must be an array with at least one element\");\n      }\n    }\n\n    for (var i = 0; i < possibleLanguages.length; i++) {\n      var languageToTry = possibleLanguages[i];\n      if (has(options.languages, languageToTry)) {\n        return options.languages[languageToTry];\n      }\n      if (has(LANGUAGES, languageToTry)) {\n        return LANGUAGES[languageToTry];\n      }\n    }\n\n    throw new Error(\"No language found.\");\n  }\n\n  /**\n   * @internal\n   * @param {Piece} piece\n   * @param {Language} language\n   * @param {Pick<Required<Options>, \"decimal\" | \"spacer\" | \"maxDecimalPoints\" | \"digitReplacements\">} options\n   */\n  function renderPiece(piece, language, options) {\n    var unitName = piece.unitName;\n    var unitCount = piece.unitCount;\n\n    var spacer = options.spacer;\n    var maxDecimalPoints = options.maxDecimalPoints;\n\n    /** @type {string} */\n    var decimal;\n    if (has(options, \"decimal\")) {\n      decimal = options.decimal;\n    } else if (has(language, \"decimal\")) {\n      decimal = language.decimal;\n    } else {\n      decimal = \".\";\n    }\n\n    /** @type {undefined | DigitReplacements} */\n    var digitReplacements;\n    if (\"digitReplacements\" in options) {\n      digitReplacements = options.digitReplacements;\n    } else if (\"_digitReplacements\" in language) {\n      digitReplacements = language._digitReplacements;\n    }\n\n    /** @type {string} */\n    var formattedCount;\n    var normalizedUnitCount =\n      maxDecimalPoints === void 0\n        ? unitCount\n        : Math.floor(unitCount * Math.pow(10, maxDecimalPoints)) /\n          Math.pow(10, maxDecimalPoints);\n    var countStr = normalizedUnitCount.toString();\n\n    if (language._hideCountIf2 && unitCount === 2) {\n      formattedCount = \"\";\n      spacer = \"\";\n    } else {\n      if (digitReplacements) {\n        formattedCount = \"\";\n        for (var i = 0; i < countStr.length; i++) {\n          var character = countStr[i];\n          if (character === \".\") {\n            formattedCount += decimal;\n          } else {\n            formattedCount +=\n              digitReplacements[\n                /** @type {\"0\" | \"1\" | \"2\" | \"3\" | \"4\" | \"5\" | \"6\" | \"7\" | \"8\" | \"9\"} */ (\n                  character\n                )\n              ];\n          }\n        }\n      } else {\n        formattedCount = countStr.replace(\".\", decimal);\n      }\n    }\n\n    var languageWord = language[unitName];\n    var word;\n    if (typeof languageWord === \"function\") {\n      word = languageWord(unitCount);\n    } else {\n      word = languageWord;\n    }\n\n    if (language._numberFirst) {\n      return word + spacer + formattedCount;\n    }\n    return formattedCount + spacer + word;\n  }\n\n  /**\n   * @internal\n   * @typedef {Object} Piece\n   * @prop {UnitName} unitName\n   * @prop {number} unitCount\n   */\n\n  /**\n   * @internal\n   * @param {number} ms\n   * @param {Pick<Required<Options>, \"units\" | \"unitMeasures\" | \"largest\" | \"round\">} options\n   * @returns {Piece[]}\n   */\n  function getPieces(ms, options) {\n    /** @type {UnitName} */\n    var unitName;\n\n    /** @type {number} */\n    var i;\n\n    /** @type {number} */\n    var unitCount;\n\n    /** @type {number} */\n    var msRemaining;\n\n    var units = options.units;\n    var unitMeasures = options.unitMeasures;\n    var largest = \"largest\" in options ? options.largest : Infinity;\n\n    if (!units.length) return [];\n\n    // Get the counts for each unit. Doesn't round or truncate anything.\n    // For example, might create an object like `{ y: 7, m: 6, w: 0, d: 5, h: 23.99 }`.\n    /** @type {Partial<Record<UnitName, number>>} */\n    var unitCounts = {};\n    msRemaining = ms;\n    for (i = 0; i < units.length; i++) {\n      unitName = units[i];\n      var unitMs = unitMeasures[unitName];\n\n      var isLast = i === units.length - 1;\n      unitCount = isLast\n        ? msRemaining / unitMs\n        : Math.floor(msRemaining / unitMs);\n      unitCounts[unitName] = unitCount;\n\n      msRemaining -= unitCount * unitMs;\n    }\n\n    if (options.round) {\n      // Update counts based on the `largest` option.\n      // For example, if `largest === 2` and `unitCount` is `{ y: 7, m: 6, w: 0, d: 5, h: 23.99 }`,\n      // updates to something like `{ y: 7, m: 6.2 }`.\n      var unitsRemainingBeforeRound = largest;\n      for (i = 0; i < units.length; i++) {\n        unitName = units[i];\n        unitCount = unitCounts[unitName];\n\n        if (unitCount === 0) continue;\n\n        unitsRemainingBeforeRound--;\n\n        // \"Take\" the rest of the units into this one.\n        if (unitsRemainingBeforeRound === 0) {\n          for (var j = i + 1; j < units.length; j++) {\n            var smallerUnitName = units[j];\n            var smallerUnitCount = unitCounts[smallerUnitName];\n            unitCounts[unitName] +=\n              (smallerUnitCount * unitMeasures[smallerUnitName]) /\n              unitMeasures[unitName];\n            unitCounts[smallerUnitName] = 0;\n          }\n          break;\n        }\n      }\n\n      // Round the last piece (which should be the only non-integer).\n      //\n      // This can be a little tricky if the last piece \"bubbles up\" to a larger\n      // unit. For example, \"3 days, 23.99 hours\" should be rounded to \"4 days\".\n      // It can also require multiple passes. For example, \"6 days, 23.99 hours\"\n      // should become \"1 week\".\n      for (i = units.length - 1; i >= 0; i--) {\n        unitName = units[i];\n        unitCount = unitCounts[unitName];\n\n        if (unitCount === 0) continue;\n\n        var rounded = Math.round(unitCount);\n        unitCounts[unitName] = rounded;\n\n        if (i === 0) break;\n\n        var previousUnitName = units[i - 1];\n        var previousUnitMs = unitMeasures[previousUnitName];\n        var amountOfPreviousUnit = Math.floor(\n          (rounded * unitMeasures[unitName]) / previousUnitMs\n        );\n        if (amountOfPreviousUnit) {\n          unitCounts[previousUnitName] += amountOfPreviousUnit;\n          unitCounts[unitName] = 0;\n        } else {\n          break;\n        }\n      }\n    }\n\n    /** @type {Piece[]} */\n    var result = [];\n    for (i = 0; i < units.length && result.length < largest; i++) {\n      unitName = units[i];\n      unitCount = unitCounts[unitName];\n      if (unitCount) {\n        result.push({ unitName: unitName, unitCount: unitCount });\n      }\n    }\n    return result;\n  }\n\n  /**\n   * @internal\n   * @param {Piece[]} pieces\n   * @param {Pick<Required<Options>, \"units\" | \"language\" | \"languages\" | \"fallbacks\" | \"delimiter\" | \"spacer\" | \"decimal\" | \"conjunction\" | \"maxDecimalPoints\" | \"serialComma\" | \"digitReplacements\">} options\n   * @returns {string}\n   */\n  function formatPieces(pieces, options) {\n    var language = getLanguage(options);\n\n    if (!pieces.length) {\n      var units = options.units;\n      var smallestUnitName = units[units.length - 1];\n      return renderPiece(\n        { unitName: smallestUnitName, unitCount: 0 },\n        language,\n        options\n      );\n    }\n\n    var conjunction = options.conjunction;\n    var serialComma = options.serialComma;\n\n    var delimiter;\n    if (has(options, \"delimiter\")) {\n      delimiter = options.delimiter;\n    } else if (has(language, \"delimiter\")) {\n      delimiter = language.delimiter;\n    } else {\n      delimiter = \", \";\n    }\n\n    /** @type {string[]} */\n    var renderedPieces = [];\n    for (var i = 0; i < pieces.length; i++) {\n      renderedPieces.push(renderPiece(pieces[i], language, options));\n    }\n\n    if (!conjunction || pieces.length === 1) {\n      return renderedPieces.join(delimiter);\n    }\n\n    if (pieces.length === 2) {\n      return renderedPieces.join(conjunction);\n    }\n\n    return (\n      renderedPieces.slice(0, -1).join(delimiter) +\n      (serialComma ? \",\" : \"\") +\n      conjunction +\n      renderedPieces.slice(-1)\n    );\n  }\n\n  /**\n   * Create a humanizer, which lets you change the default options.\n   *\n   * @param {Options} [passedOptions]\n   */\n  function humanizer(passedOptions) {\n    /**\n     * @param {number} ms\n     * @param {Options} [humanizerOptions]\n     * @returns {string}\n     */\n    var result = function humanizer(ms, humanizerOptions) {\n      // Make sure we have a positive number.\n      //\n      // Has the nice side-effect of converting things to numbers. For example,\n      // converts `\"123\"` and `Number(123)` to `123`.\n      ms = Math.abs(ms);\n\n      var options = assign({}, result, humanizerOptions || {});\n\n      var pieces = getPieces(ms, options);\n\n      return formatPieces(pieces, options);\n    };\n\n    return assign(\n      result,\n      {\n        language: \"en\",\n        spacer: \" \",\n        conjunction: \"\",\n        serialComma: true,\n        units: [\"y\", \"mo\", \"w\", \"d\", \"h\", \"m\", \"s\"],\n        languages: {},\n        round: false,\n        unitMeasures: {\n          y: 31557600000,\n          mo: 2629800000,\n          w: 604800000,\n          d: 86400000,\n          h: 3600000,\n          m: 60000,\n          s: 1000,\n          ms: 1\n        }\n      },\n      passedOptions\n    );\n  }\n\n  /**\n   * Humanize a duration.\n   *\n   * This is a wrapper around the default humanizer.\n   */\n  var humanizeDuration = assign(humanizer({}), {\n    getSupportedLanguages: function getSupportedLanguages() {\n      var result = [];\n      for (var language in LANGUAGES) {\n        if (has(LANGUAGES, language) && language !== \"gr\") {\n          result.push(language);\n        }\n      }\n      return result;\n    },\n    humanizer: humanizer\n  });\n\n  // @ts-ignore\n  if (true) {\n    // @ts-ignore\n    !(__WEBPACK_AMD_DEFINE_RESULT__ = (function () {\n      return humanizeDuration;\n    }).call(exports, __webpack_require__, exports, module),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n  } else {}\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/humanize-duration/humanize-duration.js\n");

/***/ })

};
;