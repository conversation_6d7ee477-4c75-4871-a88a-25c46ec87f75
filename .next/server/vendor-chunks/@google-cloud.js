"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@google-cloud";
exports.ids = ["vendor-chunks/@google-cloud"];
exports.modules = {

/***/ "(rsc)/./node_modules/@google-cloud/precise-date/build/src/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/@google-cloud/precise-date/build/src/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*!\n * Copyright 2019 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PreciseDate = void 0;\nconst FULL_ISO_REG = /\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d\\.\\d{4,9}Z/;\nconst NO_BIG_INT = 'BigInt only available in Node >= v10.7. Consider using getFullTimeString instead.';\nvar Sign;\n(function (Sign) {\n    Sign[Sign[\"NEGATIVE\"] = -1] = \"NEGATIVE\";\n    Sign[Sign[\"POSITIVE\"] = 1] = \"POSITIVE\";\n    Sign[Sign[\"ZERO\"] = 0] = \"ZERO\";\n})(Sign || (Sign = {}));\n/**\n * The native Date object.\n * @external Date\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date}\n */\n/**\n * @typedef {array} DateTuple\n * @property {number} 0 Represents seconds of UTC time since Unix epoch\n *     1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to\n *     9999-12-31T23:59:59Z inclusive.\n * @property {number} 1 Non-negative fractions of a second at nanosecond\n *     resolution. Negative second values with fractions must still have\n *     non-negative nanos values that count forward in time. Must be from 0 to\n *     999,999,999 inclusive.\n */\n/**\n * @typedef {object} DateStruct\n * @property {number} seconds Represents seconds of UTC time since Unix epoch\n *     1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to\n *     9999-12-31T23:59:59Z inclusive.\n * @property {number} nanos Non-negative fractions of a second at nanosecond\n *     resolution. Negative second values with fractions must still have\n *     non-negative nanos values that count forward in time. Must be from 0 to\n *     999,999,999 inclusive.\n */\n/**\n * Date object with nanosecond precision. Supports all standard Date arguments\n * in addition to several custom types as noted below.\n *\n * @class\n * @extends external:Date\n *\n * @param {number|string|bigint|Date|DateTuple|DateStruct} [time] The time\n *     value.\n * @param {...number} [dateFields] Additional date fields (month, date, hours,\n *     minutes, seconds, milliseconds, microseconds, nanoseconds).\n *\n * @example <caption>With a RFC 3339 formatted string.</caption>\n * const date = new PreciseDate('2019-02-08T10:34:29.481145231Z');\n *\n * @example <caption>With a nanosecond timestamp string.</caption>\n * const date = new PreciseDate('1549622069481320032');\n *\n * @example <caption>With a BigInt (requires Node >= v10.7)</caption>\n * const date = new PreciseDate(1549622069481320032n);\n *\n * @example <caption>With a tuple containing seconds and nanoseconds.</caption>\n * const date = new PreciseDate([1549622069, 481320032]);\n *\n * @example <caption>With an object containing `seconds` and `nanos`</caption>\n * const date = new PreciseDate({seconds: 1549622069, nanos: 481320032});\n *\n * @example <caption>Specifiying date fields</caption>\n * const date = new PreciseDate(2018, 5, 14, 41, 11, 34, 123, 874, 321);\n */\nclass PreciseDate extends Date {\n    constructor(time) {\n        super();\n        this._micros = 0;\n        this._nanos = 0;\n        if (time && typeof time !== 'number' && !(time instanceof Date)) {\n            this.setFullTime(PreciseDate.parseFull(time));\n            return;\n        }\n        // eslint-disable-next-line prefer-rest-params\n        const args = Array.from(arguments);\n        const dateFields = args.slice(0, 7);\n        const date = new Date(...dateFields);\n        const nanos = args.length === 9 ? args.pop() : 0;\n        const micros = args.length === 8 ? args.pop() : 0;\n        this.setTime(date.getTime());\n        this.setMicroseconds(micros);\n        this.setNanoseconds(nanos);\n    }\n    /**\n     * Returns the specified date represented in nanoseconds according to\n     * universal time.\n     *\n     * **NOTE:** Because this method returns a `BigInt` it requires Node >= v10.7.\n     * Use {@link PreciseDate#getFullTimeString} to get the time as a string.\n     *\n     * @see {@link https://github.com/tc39/proposal-bigint|BigInt}\n     *\n     * @throws {error} If `BigInt` is unavailable.\n     * @returns {bigint}\n     *\n     * @example\n     * const date = new PreciseDate('2019-02-08T10:34:29.481145231Z');\n     *\n     * console.log(date.getFullTime());\n     * // expected output: 1549622069481145231n\n     */\n    getFullTime() {\n        if (typeof BigInt !== 'function') {\n            throw new Error(NO_BIG_INT);\n        }\n        return BigInt(this.getFullTimeString());\n    }\n    /**\n     * Returns a string of the specified date represented in nanoseconds according\n     * to universal time.\n     *\n     * @returns {string}\n     *\n     * @example\n     * const date = new PreciseDate('2019-02-08T10:34:29.481145231Z');\n     *\n     * console.log(date.getFullTimeString());\n     * // expected output: \"1549622069481145231\"\n     */\n    getFullTimeString() {\n        const seconds = this._getSeconds();\n        let nanos = this._getNanos();\n        if (nanos && Math.sign(seconds) === Sign.NEGATIVE) {\n            nanos = 1e9 - nanos;\n        }\n        return `${seconds}${padLeft(nanos, 9)}`;\n    }\n    /**\n     * Returns the microseconds in the specified date according to universal time.\n     *\n     * @returns {number}\n     *\n     * @example\n     * const date = new PreciseDate('2019-02-08T10:34:29.481145Z');\n     *\n     * console.log(date.getMicroseconds());\n     * // expected output: 145\n     */\n    getMicroseconds() {\n        return this._micros;\n    }\n    /**\n     * Returns the nanoseconds in the specified date according to universal time.\n     *\n     * @returns {number}\n     *\n     * @example\n     * const date = new PreciseDate('2019-02-08T10:34:29.481145231Z');\n     *\n     * console.log(date.getNanoseconds());\n     * // expected output: 231\n     */\n    getNanoseconds() {\n        return this._nanos;\n    }\n    /**\n     * Sets the microseconds for a specified date according to universal time.\n     *\n     * @param {number} microseconds A number representing the microseconds.\n     * @returns {string} Returns a string representing the nanoseconds in the\n     *     specified date according to universal time.\n     *\n     * @example\n     * const date = new PreciseDate();\n     *\n     * date.setMicroseconds(149);\n     *\n     * console.log(date.getMicroseconds());\n     * // expected output: 149\n     */\n    setMicroseconds(micros) {\n        const abs = Math.abs(micros);\n        let millis = this.getUTCMilliseconds();\n        if (abs >= 1000) {\n            millis += Math.floor(abs / 1000) * Math.sign(micros);\n            micros %= 1000;\n        }\n        if (Math.sign(micros) === Sign.NEGATIVE) {\n            millis -= 1;\n            micros += 1000;\n        }\n        this._micros = micros;\n        this.setUTCMilliseconds(millis);\n        return this.getFullTimeString();\n    }\n    /**\n     * Sets the nanoseconds for a specified date according to universal time.\n     *\n     * @param {number} nanoseconds A number representing the nanoseconds.\n     * @returns {string} Returns a string representing the nanoseconds in the\n     *     specified date according to universal time.\n     *\n     * @example\n     * const date = new PreciseDate();\n     *\n     * date.setNanoseconds(231);\n     *\n     * console.log(date.getNanoseconds());\n     * // expected output: 231\n     */\n    setNanoseconds(nanos) {\n        const abs = Math.abs(nanos);\n        let micros = this._micros;\n        if (abs >= 1000) {\n            micros += Math.floor(abs / 1000) * Math.sign(nanos);\n            nanos %= 1000;\n        }\n        if (Math.sign(nanos) === Sign.NEGATIVE) {\n            micros -= 1;\n            nanos += 1000;\n        }\n        this._nanos = nanos;\n        return this.setMicroseconds(micros);\n    }\n    /**\n     * Sets the PreciseDate object to the time represented by a number of\n     * nanoseconds since January 1, 1970, 00:00:00 UTC.\n     *\n     * @param {bigint|number|string} time Value representing the number of\n     *     nanoseconds since January 1, 1970, 00:00:00 UTC.\n     * @returns {string} Returns a string representing the nanoseconds in the\n     *     specified date according to universal time (effectively, the value of\n     *     the argument).\n     *\n     * @see {@link https://github.com/tc39/proposal-bigint|BigInt}\n     *\n     * @example <caption>With a nanosecond string.</caption>\n     * const date = new PreciseDate();\n     * date.setFullTime('1549622069481145231');\n     *\n     * @example <caption>With a BigInt</caption>\n     * date.setFullTime(1549622069481145231n);\n     */\n    setFullTime(time) {\n        if (typeof time !== 'string') {\n            time = time.toString();\n        }\n        const sign = Math.sign(Number(time));\n        time = time.replace(/^-/, '');\n        const seconds = Number(time.substr(0, time.length - 9)) * sign;\n        const nanos = Number(time.substr(-9)) * sign;\n        this.setTime(seconds * 1000);\n        return this.setNanoseconds(nanos);\n    }\n    /**\n     * Sets the PreciseDate object to the time represented by a number of\n     * milliseconds since January 1, 1970, 00:00:00 UTC. Calling this method will\n     * reset both the microseconds and nanoseconds to 0.\n     *\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/setTime|Date#setTime}\n     *\n     * @param {number} time Value representing the number of milliseconds since\n     *     January 1, 1970, 00:00:00 UTC.\n     * @returns {string} The number of milliseconds between January 1, 1970,\n     *     00:00:00 UTC and the updated date (effectively, the value of the\n     *     argument).\n     */\n    setTime(time) {\n        this._micros = 0;\n        this._nanos = 0;\n        return super.setTime(time);\n    }\n    /**\n     * Returns a string in RFC 3339 format. Unlike the native `Date#toISOString`,\n     * this will return 9 digits to represent sub-second precision.\n     *\n     * @see {@link https://tools.ietf.org/html/rfc3339|RFC 3339}\n     *\n     * @returns {string}\n     *\n     * @example\n     * const date = new PreciseDate(1549622069481145231n);\n     *\n     * console.log(date.toISOString());\n     * // expected output: \"2019-02-08T10:34:29.481145231Z\"\n     */\n    toISOString() {\n        const micros = padLeft(this._micros, 3);\n        const nanos = padLeft(this._nanos, 3);\n        return super.toISOString().replace(/z$/i, `${micros}${nanos}Z`);\n    }\n    /**\n     * Returns an object representing the specified date according to universal\n     * time.\n     *\n     * @see {@link https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#timestamp|google.protobuf.Timestamp}\n     *\n     * @returns {DateStruct}\n     *\n     * @example\n     * const date = new PreciseDate('2019-02-08T10:34:29.481145231Z');\n     *\n     * console.log(date.toStruct());\n     * // expected output: {seconds: 1549622069, nanos: 481145231}\n     */\n    toStruct() {\n        let seconds = this._getSeconds();\n        const nanos = this._getNanos();\n        const sign = Math.sign(seconds);\n        // These objects are essentially a mirror of protobuf timestamps.\n        // `nanos` must always count forward in time, even if the date is <= Unix\n        // epoch. To do this we just need to count backwards 1 second and return the\n        // nanoseconds as is.\n        if (sign === Sign.NEGATIVE && nanos) {\n            seconds -= 1;\n        }\n        return { seconds, nanos };\n    }\n    /**\n     * Returns a tuple representing the specified date according to universal\n     * time.\n     *\n     * @returns {DateTuple}\n     *\n     * @example\n     * const date = new PreciseDate('2019-02-08T10:34:29.481145231Z');\n     *\n     * console.log(date.toTuple());\n     * // expected output: [1549622069, 481145231]\n     */\n    toTuple() {\n        const { seconds, nanos } = this.toStruct();\n        return [seconds, nanos];\n    }\n    /**\n     * Returns the total number of seconds in the specified date since Unix epoch.\n     * Numbers representing < epoch will be negative.\n     *\n     * @private\n     *\n     * @returns {number}\n     */\n    _getSeconds() {\n        const time = this.getTime();\n        const sign = Math.sign(time);\n        return Math.floor(Math.abs(time) / 1000) * sign;\n    }\n    /**\n     * Returns the sub-second precision of the specified date. This will always be\n     * a positive number.\n     *\n     * @private\n     *\n     * @returns {number}\n     */\n    _getNanos() {\n        const msInNanos = this.getUTCMilliseconds() * 1e6;\n        const microsInNanos = this._micros * 1000;\n        return this._nanos + msInNanos + microsInNanos;\n    }\n    /**\n     * Parses a precise time.\n     *\n     * @static\n     *\n     * @param {string|bigint|DateTuple|DateStruct} time The precise time value.\n     * @returns {string} Returns a string representing the nanoseconds in the\n     *     specified date according to universal time.\n     *\n     * @example <caption>From a RFC 3339 formatted string.</caption>\n     * const time = PreciseDate.parseFull('2019-02-08T10:34:29.481145231Z');\n     * console.log(time); // expected output: \"1549622069481145231\"\n     *\n     * @example <caption>From a nanosecond timestamp string.</caption>\n     * const time = PreciseDate.parseFull('1549622069481145231');\n     * console.log(time); // expected output: \"1549622069481145231\"\n     *\n     * @example <caption>From a BigInt (requires Node >= v10.7)</caption>\n     * const time = PreciseDate.parseFull(1549622069481145231n);\n     * console.log(time); // expected output: \"1549622069481145231\"\n     *\n     * @example <caption>From a tuple.</caption>\n     * const time = PreciseDate.parseFull([1549622069, 481145231]);\n     * console.log(time); // expected output: \"1549622069481145231\"\n     *\n     * @example <caption>From an object.</caption>\n     * const struct = {seconds: 1549622069, nanos: 481145231};\n     * const time = PreciseDate.parseFull(struct);\n     * console.log(time); // expected output: \"1549622069481145231\"\n     */\n    static parseFull(time) {\n        const date = new PreciseDate();\n        if (Array.isArray(time)) {\n            const [seconds, nanos] = time;\n            time = { seconds, nanos };\n        }\n        if (isFullTime(time)) {\n            date.setFullTime(time);\n        }\n        else if (isStruct(time)) {\n            const { seconds, nanos } = parseProto(time);\n            date.setTime(seconds * 1000);\n            date.setNanoseconds(nanos);\n        }\n        else if (isFullISOString(time)) {\n            date.setFullTime(parseFullISO(time));\n        }\n        else {\n            date.setTime(new Date(time).getTime());\n        }\n        return date.getFullTimeString();\n    }\n    /**\n     * Accepts the same number parameters as the PreciseDate constructor, but\n     * treats them as UTC. It returns a string that represents the number of\n     * nanoseconds since January 1, 1970, 00:00:00 UTC.\n     *\n     * **NOTE:** Because this method returns a `BigInt` it requires Node >= v10.7.\n     *\n     * @see {@link https://github.com/tc39/proposal-bigint|BigInt}\n     *\n     * @static\n     *\n     * @throws {error} If `BigInt` is unavailable.\n     *\n     * @param {...number} [dateFields] The date fields.\n     * @returns {bigint}\n     *\n     * @example\n     * const time = PreciseDate.fullUTC(2019, 1, 8, 10, 34, 29, 481, 145, 231);\n     * console.log(time); // expected output: 1549622069481145231n\n     */\n    static fullUTC(...args) {\n        if (typeof BigInt !== 'function') {\n            throw new Error(NO_BIG_INT);\n        }\n        return BigInt(PreciseDate.fullUTCString(...args));\n    }\n    /**\n     * Accepts the same number parameters as the PreciseDate constructor, but\n     * treats them as UTC. It returns a string that represents the number of\n     * nanoseconds since January 1, 1970, 00:00:00 UTC.\n     *\n     * @static\n     *\n     * @param {...number} [dateFields] The date fields.\n     * @returns {string}\n     *\n     * @example\n     * const time = PreciseDate.fullUTCString(2019, 1, 8, 10, 34, 29, 481, 145,\n     * 231); console.log(time); // expected output: '1549622069481145231'\n     */\n    static fullUTCString(...args) {\n        const milliseconds = Date.UTC(...args.slice(0, 7));\n        const date = new PreciseDate(milliseconds);\n        if (args.length === 9) {\n            date.setNanoseconds(args.pop());\n        }\n        if (args.length === 8) {\n            date.setMicroseconds(args.pop());\n        }\n        return date.getFullTimeString();\n    }\n}\nexports.PreciseDate = PreciseDate;\n/**\n * Parses a RFC 3339 formatted string representation of the date, and returns\n * a string representing the nanoseconds since January 1, 1970, 00:00:00.\n *\n * @private\n *\n * @param {string} time The RFC 3339 formatted string.\n * @returns {string}\n */\nfunction parseFullISO(time) {\n    let digits = '0';\n    time = time.replace(/\\.(\\d+)/, ($0, $1) => {\n        digits = $1;\n        return '.000';\n    });\n    const nanos = Number(padRight(digits, 9));\n    const date = new PreciseDate(time);\n    return date.setNanoseconds(nanos);\n}\n/**\n * Normalizes a {@link google.protobuf.Timestamp} object.\n *\n * @private\n *\n * @param {google.protobuf.Timestamp} timestamp The timestamp object.\n * @returns {DateStruct}\n */\nfunction parseProto({ seconds = 0, nanos = 0 }) {\n    if (typeof seconds.toNumber === 'function') {\n        seconds = seconds.toNumber();\n    }\n    seconds = Number(seconds);\n    nanos = Number(nanos);\n    return { seconds, nanos };\n}\n/**\n * Checks to see if time value is specified in nanoseconds. We assume that all\n * BigInt and string timestamps represent nanoseconds.\n *\n * @private\n *\n * @param {*} time The time to check.\n * @returns {boolean}\n */\nfunction isFullTime(time) {\n    return (typeof time === 'bigint' || (typeof time === 'string' && /^\\d+$/.test(time)));\n}\n/**\n * Checks to see if time value is a {@link DateStruct}.\n *\n * @private\n *\n * @param {*} time The time to check.\n * @returns {boolean}\n */\nfunction isStruct(time) {\n    return ((typeof time === 'object' &&\n        typeof time.seconds !== 'undefined') ||\n        typeof time.nanos === 'number');\n}\n/**\n * Checks to see if the time value is a RFC 3339 formatted string.\n *\n * @private\n *\n * @param {*} time The time to check.\n * @returns {boolean}\n */\nfunction isFullISOString(time) {\n    return typeof time === 'string' && FULL_ISO_REG.test(time);\n}\n/**\n * Pads a number/string with \"0\" to the left.\n *\n * @private\n *\n * @param {string|number} n The number/string to pad.\n * @param {number} min The min size of the padded string.\n * @returns {string}\n */\nfunction padLeft(n, min) {\n    const padding = getPadding(n, min);\n    return `${padding}${n}`;\n}\n/**\n * Pads a number/string with \"0\" to the right.\n *\n * @private\n *\n * @param {string|number} n The number/string to pad.\n * @param {number} min The min size of the padded string.\n * @returns {string}\n */\nfunction padRight(n, min) {\n    const padding = getPadding(n, min);\n    return `${n}${padding}`;\n}\n/**\n * Creates padding based on current size and min size needed.\n *\n * @private\n *\n * @param {string|number} n The number/string to pad.\n * @param {number} [min=3] The min size of the padded string.\n * @returns {string}\n */\nfunction getPadding(n, min) {\n    const size = Math.max(min - n.toString().length, 0);\n    return '0'.repeat(size);\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@google-cloud/precise-date/build/src/index.js\n");

/***/ })

};
;