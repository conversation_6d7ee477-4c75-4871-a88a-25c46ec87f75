"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/postgres";
exports.ids = ["vendor-chunks/postgres"];
exports.modules = {

/***/ "(rsc)/./node_modules/postgres/src/bytes.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/bytes.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst size = 256\nlet buffer = Buffer.allocUnsafe(size)\n\nconst messages = 'BCcDdEFfHPpQSX'.split('').reduce((acc, x) => {\n  const v = x.charCodeAt(0)\n  acc[x] = () => {\n    buffer[0] = v\n    b.i = 5\n    return b\n  }\n  return acc\n}, {})\n\nconst b = Object.assign(reset, messages, {\n  N: String.fromCharCode(0),\n  i: 0,\n  inc(x) {\n    b.i += x\n    return b\n  },\n  str(x) {\n    const length = Buffer.byteLength(x)\n    fit(length)\n    b.i += buffer.write(x, b.i, length, 'utf8')\n    return b\n  },\n  i16(x) {\n    fit(2)\n    buffer.writeUInt16BE(x, b.i)\n    b.i += 2\n    return b\n  },\n  i32(x, i) {\n    if (i || i === 0) {\n      buffer.writeUInt32BE(x, i)\n      return b\n    }\n    fit(4)\n    buffer.writeUInt32BE(x, b.i)\n    b.i += 4\n    return b\n  },\n  z(x) {\n    fit(x)\n    buffer.fill(0, b.i, b.i + x)\n    b.i += x\n    return b\n  },\n  raw(x) {\n    buffer = Buffer.concat([buffer.subarray(0, b.i), x])\n    b.i = buffer.length\n    return b\n  },\n  end(at = 1) {\n    buffer.writeUInt32BE(b.i - at, at)\n    const out = buffer.subarray(0, b.i)\n    b.i = 0\n    buffer = Buffer.allocUnsafe(size)\n    return out\n  }\n})\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (b);\n\nfunction fit(x) {\n  if (buffer.length - b.i < x) {\n    const prev = buffer\n        , length = prev.length\n\n    buffer = Buffer.allocUnsafe(length + (length >> 1) + x)\n    prev.copy(buffer)\n  }\n}\n\nfunction reset() {\n  b.i = 0\n  return b\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/bytes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/connection.js":
/*!*************************************************!*\
  !*** ./node_modules/postgres/src/connection.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var net__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! net */ \"net\");\n/* harmony import */ var tls__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tls */ \"tls\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var perf_hooks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! perf_hooks */ \"perf_hooks\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./types.js */ \"(rsc)/./node_modules/postgres/src/types.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors.js */ \"(rsc)/./node_modules/postgres/src/errors.js\");\n/* harmony import */ var _result_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./result.js */ \"(rsc)/./node_modules/postgres/src/result.js\");\n/* harmony import */ var _queue_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./queue.js */ \"(rsc)/./node_modules/postgres/src/queue.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./query.js */ \"(rsc)/./node_modules/postgres/src/query.js\");\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./bytes.js */ \"(rsc)/./node_modules/postgres/src/bytes.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Connection);\n\nlet uid = 1\n\nconst Sync = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().S().end()\n    , Flush = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().H().end()\n    , SSLRequest = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().i32(8).i32(80877103).end(8)\n    , ExecuteUnnamed = Buffer.concat([(0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().E().str(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).i32(0).end(), Sync])\n    , DescribeUnnamed = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().D().str('S').str(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end()\n    , noop = () => { /* noop */ }\n\nconst retryRoutines = new Set([\n  'FetchPreparedStatement',\n  'RevalidateCachedQuery',\n  'transformAssignedExpr'\n])\n\nconst errorFields = {\n  83  : 'severity_local',    // S\n  86  : 'severity',          // V\n  67  : 'code',              // C\n  77  : 'message',           // M\n  68  : 'detail',            // D\n  72  : 'hint',              // H\n  80  : 'position',          // P\n  112 : 'internal_position', // p\n  113 : 'internal_query',    // q\n  87  : 'where',             // W\n  115 : 'schema_name',       // s\n  116 : 'table_name',        // t\n  99  : 'column_name',       // c\n  100 : 'data type_name',    // d\n  110 : 'constraint_name',   // n\n  70  : 'file',              // F\n  76  : 'line',              // L\n  82  : 'routine'            // R\n}\n\nfunction Connection(options, queues = {}, { onopen = noop, onend = noop, onclose = noop } = {}) {\n  const {\n    ssl,\n    max,\n    user,\n    host,\n    port,\n    database,\n    parsers,\n    transform,\n    onnotice,\n    onnotify,\n    onparameter,\n    max_pipeline,\n    keep_alive,\n    backoff,\n    target_session_attrs\n  } = options\n\n  const sent = (0,_queue_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])()\n      , id = uid++\n      , backend = { pid: null, secret: null }\n      , idleTimer = timer(end, options.idle_timeout)\n      , lifeTimer = timer(end, options.max_lifetime)\n      , connectTimer = timer(connectTimedOut, options.connect_timeout)\n\n  let socket = null\n    , cancelMessage\n    , result = new _result_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]()\n    , incoming = Buffer.alloc(0)\n    , needsTypes = options.fetch_types\n    , backendParameters = {}\n    , statements = {}\n    , statementId = Math.random().toString(36).slice(2)\n    , statementCount = 1\n    , closedDate = 0\n    , remaining = 0\n    , hostIndex = 0\n    , retries = 0\n    , length = 0\n    , delay = 0\n    , rows = 0\n    , serverSignature = null\n    , nextWriteTimer = null\n    , terminated = false\n    , incomings = null\n    , results = null\n    , initial = null\n    , ending = null\n    , stream = null\n    , chunk = null\n    , ended = null\n    , nonce = null\n    , query = null\n    , final = null\n\n  const connection = {\n    queue: queues.closed,\n    idleTimer,\n    connect(query) {\n      initial = query\n      reconnect()\n    },\n    terminate,\n    execute,\n    cancel,\n    end,\n    count: 0,\n    id\n  }\n\n  queues.closed && queues.closed.push(connection)\n\n  return connection\n\n  async function createSocket() {\n    let x\n    try {\n      x = options.socket\n        ? (await Promise.resolve(options.socket(options)))\n        : new net__WEBPACK_IMPORTED_MODULE_0__.Socket()\n    } catch (e) {\n      error(e)\n      return\n    }\n    x.on('error', error)\n    x.on('close', closed)\n    x.on('drain', drain)\n    return x\n  }\n\n  async function cancel({ pid, secret }, resolve, reject) {\n    try {\n      cancelMessage = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().i32(16).i32(80877102).i32(pid).i32(secret).end(16)\n      await connect()\n      socket.once('error', reject)\n      socket.once('close', resolve)\n    } catch (error) {\n      reject(error)\n    }\n  }\n\n  function execute(q) {\n    if (terminated)\n      return queryError(q, _errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_DESTROYED', options))\n\n    if (q.cancelled)\n      return\n\n    try {\n      q.state = backend\n      query\n        ? sent.push(q)\n        : (query = q, query.active = true)\n\n      build(q)\n      return write(toBuffer(q))\n        && !q.describeFirst\n        && !q.cursorFn\n        && sent.length < max_pipeline\n        && (!q.options.onexecute || q.options.onexecute(connection))\n    } catch (error) {\n      sent.length === 0 && write(Sync)\n      errored(error)\n      return true\n    }\n  }\n\n  function toBuffer(q) {\n    if (q.parameters.length >= 65534)\n      throw _errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.generic('MAX_PARAMETERS_EXCEEDED', 'Max number of parameters (65534) exceeded')\n\n    return q.options.simple\n      ? (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().Q().str(q.statement.string + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end()\n      : q.describeFirst\n        ? Buffer.concat([describe(q), Flush])\n        : q.prepare\n          ? q.prepared\n            ? prepared(q)\n            : Buffer.concat([describe(q), prepared(q)])\n          : unnamed(q)\n  }\n\n  function describe(q) {\n    return Buffer.concat([\n      Parse(q.statement.string, q.parameters, q.statement.types, q.statement.name),\n      Describe('S', q.statement.name)\n    ])\n  }\n\n  function prepared(q) {\n    return Buffer.concat([\n      Bind(q.parameters, q.statement.types, q.statement.name, q.cursorName),\n      q.cursorFn\n        ? Execute('', q.cursorRows)\n        : ExecuteUnnamed\n    ])\n  }\n\n  function unnamed(q) {\n    return Buffer.concat([\n      Parse(q.statement.string, q.parameters, q.statement.types),\n      DescribeUnnamed,\n      prepared(q)\n    ])\n  }\n\n  function build(q) {\n    const parameters = []\n        , types = []\n\n    const string = (0,_types_js__WEBPACK_IMPORTED_MODULE_5__.stringify)(q, q.strings[0], q.args[0], parameters, types, options)\n\n    !q.tagged && q.args.forEach(x => (0,_types_js__WEBPACK_IMPORTED_MODULE_5__.handleValue)(x, parameters, types, options))\n\n    q.prepare = options.prepare && ('prepare' in q.options ? q.options.prepare : true)\n    q.string = string\n    q.signature = q.prepare && types + string\n    q.onlyDescribe && (delete statements[q.signature])\n    q.parameters = q.parameters || parameters\n    q.prepared = q.prepare && q.signature in statements\n    q.describeFirst = q.onlyDescribe || (parameters.length && !q.prepared)\n    q.statement = q.prepared\n      ? statements[q.signature]\n      : { string, types, name: q.prepare ? statementId + statementCount++ : '' }\n\n    typeof options.debug === 'function' && options.debug(id, string, parameters, types)\n  }\n\n  function write(x, fn) {\n    chunk = chunk ? Buffer.concat([chunk, x]) : Buffer.from(x)\n    if (fn || chunk.length >= 1024)\n      return nextWrite(fn)\n    nextWriteTimer === null && (nextWriteTimer = setImmediate(nextWrite))\n    return true\n  }\n\n  function nextWrite(fn) {\n    const x = socket.write(chunk, fn)\n    nextWriteTimer !== null && clearImmediate(nextWriteTimer)\n    chunk = nextWriteTimer = null\n    return x\n  }\n\n  function connectTimedOut() {\n    errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECT_TIMEOUT', options, socket))\n    socket.destroy()\n  }\n\n  async function secure() {\n    write(SSLRequest)\n    const canSSL = await new Promise(r => socket.once('data', x => r(x[0] === 83))) // S\n\n    if (!canSSL && ssl === 'prefer')\n      return connected()\n\n    socket.removeAllListeners()\n    socket = tls__WEBPACK_IMPORTED_MODULE_1__.connect({\n      socket,\n      servername: net__WEBPACK_IMPORTED_MODULE_0__.isIP(socket.host) ? undefined : socket.host,\n      ...(ssl === 'require' || ssl === 'allow' || ssl === 'prefer'\n        ? { rejectUnauthorized: false }\n        : ssl === 'verify-full'\n          ? {}\n          : typeof ssl === 'object'\n            ? ssl\n            : {}\n      )\n    })\n    socket.on('secureConnect', connected)\n    socket.on('error', error)\n    socket.on('close', closed)\n    socket.on('drain', drain)\n  }\n\n  /* c8 ignore next 3 */\n  function drain() {\n    !query && onopen(connection)\n  }\n\n  function data(x) {\n    if (incomings) {\n      incomings.push(x)\n      remaining -= x.length\n      if (remaining > 0)\n        return\n    }\n\n    incoming = incomings\n      ? Buffer.concat(incomings, length - remaining)\n      : incoming.length === 0\n        ? x\n        : Buffer.concat([incoming, x], incoming.length + x.length)\n\n    while (incoming.length > 4) {\n      length = incoming.readUInt32BE(1)\n      if (length >= incoming.length) {\n        remaining = length - incoming.length\n        incomings = [incoming]\n        break\n      }\n\n      try {\n        handle(incoming.subarray(0, length + 1))\n      } catch (e) {\n        query && (query.cursorFn || query.describeFirst) && write(Sync)\n        errored(e)\n      }\n      incoming = incoming.subarray(length + 1)\n      remaining = 0\n      incomings = null\n    }\n  }\n\n  async function connect() {\n    terminated = false\n    backendParameters = {}\n    socket || (socket = await createSocket())\n\n    if (!socket)\n      return\n\n    connectTimer.start()\n\n    if (options.socket)\n      return ssl ? secure() : connected()\n\n    socket.on('connect', ssl ? secure : connected)\n\n    if (options.path)\n      return socket.connect(options.path)\n\n    socket.ssl = ssl\n    socket.connect(port[hostIndex], host[hostIndex])\n    socket.host = host[hostIndex]\n    socket.port = port[hostIndex]\n\n    hostIndex = (hostIndex + 1) % port.length\n  }\n\n  function reconnect() {\n    setTimeout(connect, closedDate ? closedDate + delay - perf_hooks__WEBPACK_IMPORTED_MODULE_4__.performance.now() : 0)\n  }\n\n  function connected() {\n    try {\n      statements = {}\n      needsTypes = options.fetch_types\n      statementId = Math.random().toString(36).slice(2)\n      statementCount = 1\n      lifeTimer.start()\n      socket.on('data', data)\n      keep_alive && socket.setKeepAlive && socket.setKeepAlive(true, 1000 * keep_alive)\n      const s = StartupMessage()\n      write(s)\n    } catch (err) {\n      error(err)\n    }\n  }\n\n  function error(err) {\n    if (connection.queue === queues.connecting && options.host[retries + 1])\n      return\n\n    errored(err)\n    while (sent.length)\n      queryError(sent.shift(), err)\n  }\n\n  function errored(err) {\n    stream && (stream.destroy(err), stream = null)\n    query && queryError(query, err)\n    initial && (queryError(initial, err), initial = null)\n  }\n\n  function queryError(query, err) {\n    if (query.reserve)\n      return query.reject(err)\n\n    if (!err || typeof err !== 'object')\n      err = new Error(err)\n\n    'query' in err || 'parameters' in err || Object.defineProperties(err, {\n      stack: { value: err.stack + query.origin.replace(/.*\\n/, '\\n'), enumerable: options.debug },\n      query: { value: query.string, enumerable: options.debug },\n      parameters: { value: query.parameters, enumerable: options.debug },\n      args: { value: query.args, enumerable: options.debug },\n      types: { value: query.statement && query.statement.types, enumerable: options.debug }\n    })\n    query.reject(err)\n  }\n\n  function end() {\n    return ending || (\n      !connection.reserved && onend(connection),\n      !connection.reserved && !initial && !query && sent.length === 0\n        ? (terminate(), new Promise(r => socket && socket.readyState !== 'closed' ? socket.once('close', r) : r()))\n        : ending = new Promise(r => ended = r)\n    )\n  }\n\n  function terminate() {\n    terminated = true\n    if (stream || query || initial || sent.length)\n      error(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_DESTROYED', options))\n\n    clearImmediate(nextWriteTimer)\n    if (socket) {\n      socket.removeListener('data', data)\n      socket.removeListener('connect', connected)\n      socket.readyState === 'open' && socket.end((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().X().end())\n    }\n    ended && (ended(), ending = ended = null)\n  }\n\n  async function closed(hadError) {\n    incoming = Buffer.alloc(0)\n    remaining = 0\n    incomings = null\n    clearImmediate(nextWriteTimer)\n    socket.removeListener('data', data)\n    socket.removeListener('connect', connected)\n    idleTimer.cancel()\n    lifeTimer.cancel()\n    connectTimer.cancel()\n\n    socket.removeAllListeners()\n    socket = null\n\n    if (initial)\n      return reconnect()\n\n    !hadError && (query || sent.length) && error(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_CLOSED', options, socket))\n    closedDate = perf_hooks__WEBPACK_IMPORTED_MODULE_4__.performance.now()\n    hadError && options.shared.retries++\n    delay = (typeof backoff === 'function' ? backoff(options.shared.retries) : backoff) * 1000\n    onclose(connection, _errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_CLOSED', options, socket))\n  }\n\n  /* Handlers */\n  function handle(xs, x = xs[0]) {\n    (\n      x === 68 ? DataRow :                   // D\n      x === 100 ? CopyData :                 // d\n      x === 65 ? NotificationResponse :      // A\n      x === 83 ? ParameterStatus :           // S\n      x === 90 ? ReadyForQuery :             // Z\n      x === 67 ? CommandComplete :           // C\n      x === 50 ? BindComplete :              // 2\n      x === 49 ? ParseComplete :             // 1\n      x === 116 ? ParameterDescription :     // t\n      x === 84 ? RowDescription :            // T\n      x === 82 ? Authentication :            // R\n      x === 110 ? NoData :                   // n\n      x === 75 ? BackendKeyData :            // K\n      x === 69 ? ErrorResponse :             // E\n      x === 115 ? PortalSuspended :          // s\n      x === 51 ? CloseComplete :             // 3\n      x === 71 ? CopyInResponse :            // G\n      x === 78 ? NoticeResponse :            // N\n      x === 72 ? CopyOutResponse :           // H\n      x === 99 ? CopyDone :                  // c\n      x === 73 ? EmptyQueryResponse :        // I\n      x === 86 ? FunctionCallResponse :      // V\n      x === 118 ? NegotiateProtocolVersion : // v\n      x === 87 ? CopyBothResponse :          // W\n      /* c8 ignore next */\n      UnknownMessage\n    )(xs)\n  }\n\n  function DataRow(x) {\n    let index = 7\n    let length\n    let column\n    let value\n\n    const row = query.isRaw ? new Array(query.statement.columns.length) : {}\n    for (let i = 0; i < query.statement.columns.length; i++) {\n      column = query.statement.columns[i]\n      length = x.readInt32BE(index)\n      index += 4\n\n      value = length === -1\n        ? null\n        : query.isRaw === true\n          ? x.subarray(index, index += length)\n          : column.parser === undefined\n            ? x.toString('utf8', index, index += length)\n            : column.parser.array === true\n              ? column.parser(x.toString('utf8', index + 1, index += length))\n              : column.parser(x.toString('utf8', index, index += length))\n\n      query.isRaw\n        ? (row[i] = query.isRaw === true\n          ? value\n          : transform.value.from ? transform.value.from(value, column) : value)\n        : (row[column.name] = transform.value.from ? transform.value.from(value, column) : value)\n    }\n\n    query.forEachFn\n      ? query.forEachFn(transform.row.from ? transform.row.from(row) : row, result)\n      : (result[rows++] = transform.row.from ? transform.row.from(row) : row)\n  }\n\n  function ParameterStatus(x) {\n    const [k, v] = x.toString('utf8', 5, x.length - 1).split(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N)\n    backendParameters[k] = v\n    if (options.parameters[k] !== v) {\n      options.parameters[k] = v\n      onparameter && onparameter(k, v)\n    }\n  }\n\n  function ReadyForQuery(x) {\n    query && query.options.simple && query.resolve(results || result)\n    query = results = null\n    result = new _result_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]()\n    connectTimer.cancel()\n\n    if (initial) {\n      if (target_session_attrs) {\n        if (!backendParameters.in_hot_standby || !backendParameters.default_transaction_read_only)\n          return fetchState()\n        else if (tryNext(target_session_attrs, backendParameters))\n          return terminate()\n      }\n\n      if (needsTypes) {\n        initial.reserve && (initial = null)\n        return fetchArrayTypes()\n      }\n\n      initial && !initial.reserve && execute(initial)\n      options.shared.retries = retries = 0\n      initial = null\n      return\n    }\n\n    while (sent.length && (query = sent.shift()) && (query.active = true, query.cancelled))\n      Connection(options).cancel(query.state, query.cancelled.resolve, query.cancelled.reject)\n\n    if (query)\n      return // Consider opening if able and sent.length < 50\n\n    connection.reserved\n      ? !connection.reserved.release && x[5] === 73 // I\n        ? ending\n          ? terminate()\n          : (connection.reserved = null, onopen(connection))\n        : connection.reserved()\n      : ending\n        ? terminate()\n        : onopen(connection)\n  }\n\n  function CommandComplete(x) {\n    rows = 0\n\n    for (let i = x.length - 1; i > 0; i--) {\n      if (x[i] === 32 && x[i + 1] < 58 && result.count === null)\n        result.count = +x.toString('utf8', i + 1, x.length - 1)\n      if (x[i - 1] >= 65) {\n        result.command = x.toString('utf8', 5, i)\n        result.state = backend\n        break\n      }\n    }\n\n    final && (final(), final = null)\n\n    if (result.command === 'BEGIN' && max !== 1 && !connection.reserved)\n      return errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.generic('UNSAFE_TRANSACTION', 'Only use sql.begin, sql.reserved or max: 1'))\n\n    if (query.options.simple)\n      return BindComplete()\n\n    if (query.cursorFn) {\n      result.count && query.cursorFn(result)\n      write(Sync)\n    }\n\n    query.resolve(result)\n  }\n\n  function ParseComplete() {\n    query.parsing = false\n  }\n\n  function BindComplete() {\n    !result.statement && (result.statement = query.statement)\n    result.columns = query.statement.columns\n  }\n\n  function ParameterDescription(x) {\n    const length = x.readUInt16BE(5)\n\n    for (let i = 0; i < length; ++i)\n      !query.statement.types[i] && (query.statement.types[i] = x.readUInt32BE(7 + i * 4))\n\n    query.prepare && (statements[query.signature] = query.statement)\n    query.describeFirst && !query.onlyDescribe && (write(prepared(query)), query.describeFirst = false)\n  }\n\n  function RowDescription(x) {\n    if (result.command) {\n      results = results || [result]\n      results.push(result = new _result_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]())\n      result.count = null\n      query.statement.columns = null\n    }\n\n    const length = x.readUInt16BE(5)\n    let index = 7\n    let start\n\n    query.statement.columns = Array(length)\n\n    for (let i = 0; i < length; ++i) {\n      start = index\n      while (x[index++] !== 0);\n      const table = x.readUInt32BE(index)\n      const number = x.readUInt16BE(index + 4)\n      const type = x.readUInt32BE(index + 6)\n      query.statement.columns[i] = {\n        name: transform.column.from\n          ? transform.column.from(x.toString('utf8', start, index - 1))\n          : x.toString('utf8', start, index - 1),\n        parser: parsers[type],\n        table,\n        number,\n        type\n      }\n      index += 18\n    }\n\n    result.statement = query.statement\n    if (query.onlyDescribe)\n      return (query.resolve(query.statement), write(Sync))\n  }\n\n  async function Authentication(x, type = x.readUInt32BE(5)) {\n    (\n      type === 3 ? AuthenticationCleartextPassword :\n      type === 5 ? AuthenticationMD5Password :\n      type === 10 ? SASL :\n      type === 11 ? SASLContinue :\n      type === 12 ? SASLFinal :\n      type !== 0 ? UnknownAuth :\n      noop\n    )(x, type)\n  }\n\n  /* c8 ignore next 5 */\n  async function AuthenticationCleartextPassword() {\n    const payload = await Pass()\n    write(\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().p().str(payload).z(1).end()\n    )\n  }\n\n  async function AuthenticationMD5Password(x) {\n    const payload = 'md5' + (\n      await md5(\n        Buffer.concat([\n          Buffer.from(await md5((await Pass()) + user)),\n          x.subarray(9)\n        ])\n      )\n    )\n    write(\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().p().str(payload).z(1).end()\n    )\n  }\n\n  async function SASL() {\n    nonce = (await crypto__WEBPACK_IMPORTED_MODULE_2__.randomBytes(18)).toString('base64')\n    ;(0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().p().str('SCRAM-SHA-256' + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N)\n    const i = _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i\n    write(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].inc(4).str('n,,n=*,r=' + nonce).i32(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i - i - 4, i).end())\n  }\n\n  async function SASLContinue(x) {\n    const res = x.toString('utf8', 9).split(',').reduce((acc, x) => (acc[x[0]] = x.slice(2), acc), {})\n\n    const saltedPassword = await crypto__WEBPACK_IMPORTED_MODULE_2__.pbkdf2Sync(\n      await Pass(),\n      Buffer.from(res.s, 'base64'),\n      parseInt(res.i), 32,\n      'sha256'\n    )\n\n    const clientKey = await hmac(saltedPassword, 'Client Key')\n\n    const auth = 'n=*,r=' + nonce + ','\n               + 'r=' + res.r + ',s=' + res.s + ',i=' + res.i\n               + ',c=biws,r=' + res.r\n\n    serverSignature = (await hmac(await hmac(saltedPassword, 'Server Key'), auth)).toString('base64')\n\n    const payload = 'c=biws,r=' + res.r + ',p=' + xor(\n      clientKey, Buffer.from(await hmac(await sha256(clientKey), auth))\n    ).toString('base64')\n\n    write(\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().p().str(payload).end()\n    )\n  }\n\n  function SASLFinal(x) {\n    if (x.toString('utf8', 9).split(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N, 1)[0].slice(2) === serverSignature)\n      return\n    /* c8 ignore next 5 */\n    errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.generic('SASL_SIGNATURE_MISMATCH', 'The server did not return the correct signature'))\n    socket.destroy()\n  }\n\n  function Pass() {\n    return Promise.resolve(typeof options.pass === 'function'\n      ? options.pass()\n      : options.pass\n    )\n  }\n\n  function NoData() {\n    result.statement = query.statement\n    result.statement.columns = []\n    if (query.onlyDescribe)\n      return (query.resolve(query.statement), write(Sync))\n  }\n\n  function BackendKeyData(x) {\n    backend.pid = x.readUInt32BE(5)\n    backend.secret = x.readUInt32BE(9)\n  }\n\n  async function fetchArrayTypes() {\n    needsTypes = false\n    const types = await new _query_js__WEBPACK_IMPORTED_MODULE_9__.Query([`\n      select b.oid, b.typarray\n      from pg_catalog.pg_type a\n      left join pg_catalog.pg_type b on b.oid = a.typelem\n      where a.typcategory = 'A'\n      group by b.oid, b.typarray\n      order by b.oid\n    `], [], execute)\n    types.forEach(({ oid, typarray }) => addArrayType(oid, typarray))\n  }\n\n  function addArrayType(oid, typarray) {\n    if (!!options.parsers[typarray] && !!options.serializers[typarray]) return\n    const parser = options.parsers[oid]\n    options.shared.typeArrayMap[oid] = typarray\n    options.parsers[typarray] = (xs) => (0,_types_js__WEBPACK_IMPORTED_MODULE_5__.arrayParser)(xs, parser, typarray)\n    options.parsers[typarray].array = true\n    options.serializers[typarray] = (xs) => (0,_types_js__WEBPACK_IMPORTED_MODULE_5__.arraySerializer)(xs, options.serializers[oid], options, typarray)\n  }\n\n  function tryNext(x, xs) {\n    return (\n      (x === 'read-write' && xs.default_transaction_read_only === 'on') ||\n      (x === 'read-only' && xs.default_transaction_read_only === 'off') ||\n      (x === 'primary' && xs.in_hot_standby === 'on') ||\n      (x === 'standby' && xs.in_hot_standby === 'off') ||\n      (x === 'prefer-standby' && xs.in_hot_standby === 'off' && options.host[retries])\n    )\n  }\n\n  function fetchState() {\n    const query = new _query_js__WEBPACK_IMPORTED_MODULE_9__.Query([`\n      show transaction_read_only;\n      select pg_catalog.pg_is_in_recovery()\n    `], [], execute, null, { simple: true })\n    query.resolve = ([[a], [b]]) => {\n      backendParameters.default_transaction_read_only = a.transaction_read_only\n      backendParameters.in_hot_standby = b.pg_is_in_recovery ? 'on' : 'off'\n    }\n    query.execute()\n  }\n\n  function ErrorResponse(x) {\n    query && (query.cursorFn || query.describeFirst) && write(Sync)\n    const error = _errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.postgres(parseError(x))\n    query && query.retried\n      ? errored(query.retried)\n      : query && query.prepared && retryRoutines.has(error.routine)\n        ? retry(query, error)\n        : errored(error)\n  }\n\n  function retry(q, error) {\n    delete statements[q.signature]\n    q.retried = error\n    execute(q)\n  }\n\n  function NotificationResponse(x) {\n    if (!onnotify)\n      return\n\n    let index = 9\n    while (x[index++] !== 0);\n    onnotify(\n      x.toString('utf8', 9, index - 1),\n      x.toString('utf8', index, x.length - 1)\n    )\n  }\n\n  async function PortalSuspended() {\n    try {\n      const x = await Promise.resolve(query.cursorFn(result))\n      rows = 0\n      x === _query_js__WEBPACK_IMPORTED_MODULE_9__.CLOSE\n        ? write(Close(query.portal))\n        : (result = new _result_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"](), write(Execute('', query.cursorRows)))\n    } catch (err) {\n      write(Sync)\n      query.reject(err)\n    }\n  }\n\n  function CloseComplete() {\n    result.count && query.cursorFn(result)\n    query.resolve(result)\n  }\n\n  function CopyInResponse() {\n    stream = new stream__WEBPACK_IMPORTED_MODULE_3__.Writable({\n      autoDestroy: true,\n      write(chunk, encoding, callback) {\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().d().raw(chunk).end(), callback)\n      },\n      destroy(error, callback) {\n        callback(error)\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().f().str(error + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end())\n        stream = null\n      },\n      final(callback) {\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().c().end())\n        final = callback\n      }\n    })\n    query.resolve(stream)\n  }\n\n  function CopyOutResponse() {\n    stream = new stream__WEBPACK_IMPORTED_MODULE_3__.Readable({\n      read() { socket.resume() }\n    })\n    query.resolve(stream)\n  }\n\n  /* c8 ignore next 3 */\n  function CopyBothResponse() {\n    stream = new stream__WEBPACK_IMPORTED_MODULE_3__.Duplex({\n      autoDestroy: true,\n      read() { socket.resume() },\n      /* c8 ignore next 11 */\n      write(chunk, encoding, callback) {\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().d().raw(chunk).end(), callback)\n      },\n      destroy(error, callback) {\n        callback(error)\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().f().str(error + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end())\n        stream = null\n      },\n      final(callback) {\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().c().end())\n        final = callback\n      }\n    })\n    query.resolve(stream)\n  }\n\n  function CopyData(x) {\n    stream && (stream.push(x.subarray(5)) || socket.pause())\n  }\n\n  function CopyDone() {\n    stream && stream.push(null)\n    stream = null\n  }\n\n  function NoticeResponse(x) {\n    onnotice\n      ? onnotice(parseError(x))\n      : console.log(parseError(x)) // eslint-disable-line\n\n  }\n\n  /* c8 ignore next 3 */\n  function EmptyQueryResponse() {\n    /* noop */\n  }\n\n  /* c8 ignore next 3 */\n  function FunctionCallResponse() {\n    errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.notSupported('FunctionCallResponse'))\n  }\n\n  /* c8 ignore next 3 */\n  function NegotiateProtocolVersion() {\n    errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.notSupported('NegotiateProtocolVersion'))\n  }\n\n  /* c8 ignore next 3 */\n  function UnknownMessage(x) {\n    console.error('Postgres.js : Unknown Message:', x[0]) // eslint-disable-line\n  }\n\n  /* c8 ignore next 3 */\n  function UnknownAuth(x, type) {\n    console.error('Postgres.js : Unknown Auth:', type) // eslint-disable-line\n  }\n\n  /* Messages */\n  function Bind(parameters, types, statement = '', portal = '') {\n    let prev\n      , type\n\n    ;(0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().B().str(portal + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).str(statement + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).i16(0).i16(parameters.length)\n\n    parameters.forEach((x, i) => {\n      if (x === null)\n        return _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i32(0xFFFFFFFF)\n\n      type = types[i]\n      parameters[i] = x = type in options.serializers\n        ? options.serializers[type](x)\n        : '' + x\n\n      prev = _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i\n      _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].inc(4).str(x).i32(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i - prev - 4, prev)\n    })\n\n    _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i16(0)\n\n    return _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].end()\n  }\n\n  function Parse(str, parameters, types, name = '') {\n    (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().P().str(name + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).str(str + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).i16(parameters.length)\n    parameters.forEach((x, i) => _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i32(types[i] || 0))\n    return _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].end()\n  }\n\n  function Describe(x, name = '') {\n    return (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().D().str(x).str(name + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end()\n  }\n\n  function Execute(portal = '', rows = 0) {\n    return Buffer.concat([\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().E().str(portal + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).i32(rows).end(),\n      Flush\n    ])\n  }\n\n  function Close(portal = '') {\n    return Buffer.concat([\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().C().str('P').str(portal + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end(),\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().S().end()\n    ])\n  }\n\n  function StartupMessage() {\n    return cancelMessage || (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().inc(4).i16(3).z(2).str(\n      Object.entries(Object.assign({\n        user,\n        database,\n        client_encoding: 'UTF8'\n      },\n        options.connection\n      )).filter(([, v]) => v).map(([k, v]) => k + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N + v).join(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N)\n    ).z(2).end(0)\n  }\n\n}\n\nfunction parseError(x) {\n  const error = {}\n  let start = 5\n  for (let i = 5; i < x.length - 1; i++) {\n    if (x[i] === 0) {\n      error[errorFields[x[start]]] = x.toString('utf8', start + 1, i)\n      start = i + 1\n    }\n  }\n  return error\n}\n\nfunction md5(x) {\n  return crypto__WEBPACK_IMPORTED_MODULE_2__.createHash('md5').update(x).digest('hex')\n}\n\nfunction hmac(key, x) {\n  return crypto__WEBPACK_IMPORTED_MODULE_2__.createHmac('sha256', key).update(x).digest()\n}\n\nfunction sha256(x) {\n  return crypto__WEBPACK_IMPORTED_MODULE_2__.createHash('sha256').update(x).digest()\n}\n\nfunction xor(a, b) {\n  const length = Math.max(a.length, b.length)\n  const buffer = Buffer.allocUnsafe(length)\n  for (let i = 0; i < length; i++)\n    buffer[i] = a[i] ^ b[i]\n  return buffer\n}\n\nfunction timer(fn, seconds) {\n  seconds = typeof seconds === 'function' ? seconds() : seconds\n  if (!seconds)\n    return { cancel: noop, start: noop }\n\n  let timer\n  return {\n    cancel() {\n      timer && (clearTimeout(timer), timer = null)\n    },\n    start() {\n      timer && clearTimeout(timer)\n      timer = setTimeout(done, seconds * 1000, arguments)\n    }\n  }\n\n  function done(args) {\n    fn.apply(null, args)\n    timer = null\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/connection.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/errors.js":
/*!*********************************************!*\
  !*** ./node_modules/postgres/src/errors.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Errors: () => (/* binding */ Errors),\n/* harmony export */   PostgresError: () => (/* binding */ PostgresError)\n/* harmony export */ });\nclass PostgresError extends Error {\n  constructor(x) {\n    super(x.message)\n    this.name = this.constructor.name\n    Object.assign(this, x)\n  }\n}\n\nconst Errors = {\n  connection,\n  postgres,\n  generic,\n  notSupported\n}\n\nfunction connection(x, options, socket) {\n  const { host, port } = socket || options\n  const error = Object.assign(\n    new Error(('write ' + x + ' ' + (options.path || (host + ':' + port)))),\n    {\n      code: x,\n      errno: x,\n      address: options.path || host\n    }, options.path ? {} : { port: port }\n  )\n  Error.captureStackTrace(error, connection)\n  return error\n}\n\nfunction postgres(x) {\n  const error = new PostgresError(x)\n  Error.captureStackTrace(error, postgres)\n  return error\n}\n\nfunction generic(code, message) {\n  const error = Object.assign(new Error(code + ': ' + message), { code })\n  Error.captureStackTrace(error, generic)\n  return error\n}\n\n/* c8 ignore next 10 */\nfunction notSupported(x) {\n  const error = Object.assign(\n    new Error(x + ' (B) is not supported'),\n    {\n      code: 'MESSAGE_NOT_SUPPORTED',\n      name: x\n    }\n  )\n  Error.captureStackTrace(error, notSupported)\n  return error\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/index.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! os */ \"os\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types.js */ \"(rsc)/./node_modules/postgres/src/types.js\");\n/* harmony import */ var _connection_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./connection.js */ \"(rsc)/./node_modules/postgres/src/connection.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./query.js */ \"(rsc)/./node_modules/postgres/src/query.js\");\n/* harmony import */ var _queue_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./queue.js */ \"(rsc)/./node_modules/postgres/src/queue.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors.js */ \"(rsc)/./node_modules/postgres/src/errors.js\");\n/* harmony import */ var _subscribe_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./subscribe.js */ \"(rsc)/./node_modules/postgres/src/subscribe.js\");\n/* harmony import */ var _large_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./large.js */ \"(rsc)/./node_modules/postgres/src/large.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nObject.assign(Postgres, {\n  PostgresError: _errors_js__WEBPACK_IMPORTED_MODULE_6__.PostgresError,\n  toPascal: _types_js__WEBPACK_IMPORTED_MODULE_2__.toPascal,\n  pascal: _types_js__WEBPACK_IMPORTED_MODULE_2__.pascal,\n  toCamel: _types_js__WEBPACK_IMPORTED_MODULE_2__.toCamel,\n  camel: _types_js__WEBPACK_IMPORTED_MODULE_2__.camel,\n  toKebab: _types_js__WEBPACK_IMPORTED_MODULE_2__.toKebab,\n  kebab: _types_js__WEBPACK_IMPORTED_MODULE_2__.kebab,\n  fromPascal: _types_js__WEBPACK_IMPORTED_MODULE_2__.fromPascal,\n  fromCamel: _types_js__WEBPACK_IMPORTED_MODULE_2__.fromCamel,\n  fromKebab: _types_js__WEBPACK_IMPORTED_MODULE_2__.fromKebab,\n  BigInt: {\n    to: 20,\n    from: [20],\n    parse: x => BigInt(x), // eslint-disable-line\n    serialize: x => x.toString()\n  }\n})\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Postgres);\n\nfunction Postgres(a, b) {\n  const options = parseOptions(a, b)\n      , subscribe = options.no_subscribe || (0,_subscribe_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(Postgres, { ...options })\n\n  let ending = false\n\n  const queries = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , connecting = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , reserved = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , closed = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , ended = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , open = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , busy = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , full = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , queues = { connecting, reserved, closed, ended, open, busy, full }\n\n  const connections = [...Array(options.max)].map(() => (0,_connection_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, queues, { onopen, onend, onclose }))\n\n  const sql = Sql(handler)\n\n  Object.assign(sql, {\n    get parameters() { return options.parameters },\n    largeObject: _large_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"].bind(null, sql),\n    subscribe,\n    CLOSE: _query_js__WEBPACK_IMPORTED_MODULE_4__.CLOSE,\n    END: _query_js__WEBPACK_IMPORTED_MODULE_4__.CLOSE,\n    PostgresError: _errors_js__WEBPACK_IMPORTED_MODULE_6__.PostgresError,\n    options,\n    reserve,\n    listen,\n    begin,\n    close,\n    end\n  })\n\n  return sql\n\n  function Sql(handler) {\n    handler.debug = options.debug\n\n    Object.entries(options.types).reduce((acc, [name, type]) => {\n      acc[name] = (x) => new _types_js__WEBPACK_IMPORTED_MODULE_2__.Parameter(x, type.to)\n      return acc\n    }, typed)\n\n    Object.assign(sql, {\n      types: typed,\n      typed,\n      unsafe,\n      notify,\n      array,\n      json,\n      file\n    })\n\n    return sql\n\n    function typed(value, type) {\n      return new _types_js__WEBPACK_IMPORTED_MODULE_2__.Parameter(value, type)\n    }\n\n    function sql(strings, ...args) {\n      const query = strings && Array.isArray(strings.raw)\n        ? new _query_js__WEBPACK_IMPORTED_MODULE_4__.Query(strings, args, handler, cancel)\n        : typeof strings === 'string' && !args.length\n          ? new _types_js__WEBPACK_IMPORTED_MODULE_2__.Identifier(options.transform.column.to ? options.transform.column.to(strings) : strings)\n          : new _types_js__WEBPACK_IMPORTED_MODULE_2__.Builder(strings, args)\n      return query\n    }\n\n    function unsafe(string, args = [], options = {}) {\n      arguments.length === 2 && !Array.isArray(args) && (options = args, args = [])\n      const query = new _query_js__WEBPACK_IMPORTED_MODULE_4__.Query([string], args, handler, cancel, {\n        prepare: false,\n        ...options,\n        simple: 'simple' in options ? options.simple : args.length === 0\n      })\n      return query\n    }\n\n    function file(path, args = [], options = {}) {\n      arguments.length === 2 && !Array.isArray(args) && (options = args, args = [])\n      const query = new _query_js__WEBPACK_IMPORTED_MODULE_4__.Query([], args, (query) => {\n        fs__WEBPACK_IMPORTED_MODULE_1__.readFile(path, 'utf8', (err, string) => {\n          if (err)\n            return query.reject(err)\n\n          query.strings = [string]\n          handler(query)\n        })\n      }, cancel, {\n        ...options,\n        simple: 'simple' in options ? options.simple : args.length === 0\n      })\n      return query\n    }\n  }\n\n  async function listen(name, fn, onlisten) {\n    const listener = { fn, onlisten }\n\n    const sql = listen.sql || (listen.sql = Postgres({\n      ...options,\n      max: 1,\n      idle_timeout: null,\n      max_lifetime: null,\n      fetch_types: false,\n      onclose() {\n        Object.entries(listen.channels).forEach(([name, { listeners }]) => {\n          delete listen.channels[name]\n          Promise.all(listeners.map(l => listen(name, l.fn, l.onlisten).catch(() => { /* noop */ })))\n        })\n      },\n      onnotify(c, x) {\n        c in listen.channels && listen.channels[c].listeners.forEach(l => l.fn(x))\n      }\n    }))\n\n    const channels = listen.channels || (listen.channels = {})\n        , exists = name in channels\n\n    if (exists) {\n      channels[name].listeners.push(listener)\n      const result = await channels[name].result\n      listener.onlisten && listener.onlisten()\n      return { state: result.state, unlisten }\n    }\n\n    channels[name] = { result: sql`listen ${\n      sql.unsafe('\"' + name.replace(/\"/g, '\"\"') + '\"')\n    }`, listeners: [listener] }\n    const result = await channels[name].result\n    listener.onlisten && listener.onlisten()\n    return { state: result.state, unlisten }\n\n    async function unlisten() {\n      if (name in channels === false)\n        return\n\n      channels[name].listeners = channels[name].listeners.filter(x => x !== listener)\n      if (channels[name].listeners.length)\n        return\n\n      delete channels[name]\n      return sql`unlisten ${\n        sql.unsafe('\"' + name.replace(/\"/g, '\"\"') + '\"')\n      }`\n    }\n  }\n\n  async function notify(channel, payload) {\n    return await sql`select pg_notify(${ channel }, ${ '' + payload })`\n  }\n\n  async function reserve() {\n    const queue = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n    const c = open.length\n      ? open.shift()\n      : await new Promise((resolve, reject) => {\n        const query = { reserve: resolve, reject }\n        queries.push(query)\n        closed.length && connect(closed.shift(), query)\n      })\n\n    move(c, reserved)\n    c.reserved = () => queue.length\n      ? c.execute(queue.shift())\n      : move(c, reserved)\n    c.reserved.release = true\n\n    const sql = Sql(handler)\n    sql.release = () => {\n      c.reserved = null\n      onopen(c)\n    }\n\n    return sql\n\n    function handler(q) {\n      c.queue === full\n        ? queue.push(q)\n        : c.execute(q) || move(c, full)\n    }\n  }\n\n  async function begin(options, fn) {\n    !fn && (fn = options, options = '')\n    const queries = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n    let savepoints = 0\n      , connection\n      , prepare = null\n\n    try {\n      await sql.unsafe('begin ' + options.replace(/[^a-z ]/ig, ''), [], { onexecute }).execute()\n      return await Promise.race([\n        scope(connection, fn),\n        new Promise((_, reject) => connection.onclose = reject)\n      ])\n    } catch (error) {\n      throw error\n    }\n\n    async function scope(c, fn, name) {\n      const sql = Sql(handler)\n      sql.savepoint = savepoint\n      sql.prepare = x => prepare = x.replace(/[^a-z0-9$-_. ]/gi)\n      let uncaughtError\n        , result\n\n      name && await sql`savepoint ${ sql(name) }`\n      try {\n        result = await new Promise((resolve, reject) => {\n          const x = fn(sql)\n          Promise.resolve(Array.isArray(x) ? Promise.all(x) : x).then(resolve, reject)\n        })\n\n        if (uncaughtError)\n          throw uncaughtError\n      } catch (e) {\n        await (name\n          ? sql`rollback to ${ sql(name) }`\n          : sql`rollback`\n        )\n        throw e instanceof _errors_js__WEBPACK_IMPORTED_MODULE_6__.PostgresError && e.code === '25P02' && uncaughtError || e\n      }\n\n      if (!name) {\n        prepare\n          ? await sql`prepare transaction '${ sql.unsafe(prepare) }'`\n          : await sql`commit`\n      }\n\n      return result\n\n      function savepoint(name, fn) {\n        if (name && Array.isArray(name.raw))\n          return savepoint(sql => sql.apply(sql, arguments))\n\n        arguments.length === 1 && (fn = name, name = null)\n        return scope(c, fn, 's' + savepoints++ + (name ? '_' + name : ''))\n      }\n\n      function handler(q) {\n        q.catch(e => uncaughtError || (uncaughtError = e))\n        c.queue === full\n          ? queries.push(q)\n          : c.execute(q) || move(c, full)\n      }\n    }\n\n    function onexecute(c) {\n      connection = c\n      move(c, reserved)\n      c.reserved = () => queries.length\n        ? c.execute(queries.shift())\n        : move(c, reserved)\n    }\n  }\n\n  function move(c, queue) {\n    c.queue.remove(c)\n    queue.push(c)\n    c.queue = queue\n    queue === open\n      ? c.idleTimer.start()\n      : c.idleTimer.cancel()\n    return c\n  }\n\n  function json(x) {\n    return new _types_js__WEBPACK_IMPORTED_MODULE_2__.Parameter(x, 3802)\n  }\n\n  function array(x, type) {\n    if (!Array.isArray(x))\n      return array(Array.from(arguments))\n\n    return new _types_js__WEBPACK_IMPORTED_MODULE_2__.Parameter(x, type || (x.length ? (0,_types_js__WEBPACK_IMPORTED_MODULE_2__.inferType)(x) || 25 : 0), options.shared.typeArrayMap)\n  }\n\n  function handler(query) {\n    if (ending)\n      return query.reject(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_ENDED', options, options))\n\n    if (open.length)\n      return go(open.shift(), query)\n\n    if (closed.length)\n      return connect(closed.shift(), query)\n\n    busy.length\n      ? go(busy.shift(), query)\n      : queries.push(query)\n  }\n\n  function go(c, query) {\n    return c.execute(query)\n      ? move(c, busy)\n      : move(c, full)\n  }\n\n  function cancel(query) {\n    return new Promise((resolve, reject) => {\n      query.state\n        ? query.active\n          ? (0,_connection_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options).cancel(query.state, resolve, reject)\n          : query.cancelled = { resolve, reject }\n        : (\n          queries.remove(query),\n          query.cancelled = true,\n          query.reject(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.generic('57014', 'canceling statement due to user request')),\n          resolve()\n        )\n    })\n  }\n\n  async function end({ timeout = null } = {}) {\n    if (ending)\n      return ending\n\n    await 1\n    let timer\n    return ending = Promise.race([\n      new Promise(r => timeout !== null && (timer = setTimeout(destroy, timeout * 1000, r))),\n      Promise.all(connections.map(c => c.end()).concat(\n        listen.sql ? listen.sql.end({ timeout: 0 }) : [],\n        subscribe.sql ? subscribe.sql.end({ timeout: 0 }) : []\n      ))\n    ]).then(() => clearTimeout(timer))\n  }\n\n  async function close() {\n    await Promise.all(connections.map(c => c.end()))\n  }\n\n  async function destroy(resolve) {\n    await Promise.all(connections.map(c => c.terminate()))\n    while (queries.length)\n      queries.shift().reject(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_DESTROYED', options))\n    resolve()\n  }\n\n  function connect(c, query) {\n    move(c, connecting)\n    c.connect(query)\n    return c\n  }\n\n  function onend(c) {\n    move(c, ended)\n  }\n\n  function onopen(c) {\n    if (queries.length === 0)\n      return move(c, open)\n\n    let max = Math.ceil(queries.length / (connecting.length + 1))\n      , ready = true\n\n    while (ready && queries.length && max-- > 0) {\n      const query = queries.shift()\n      if (query.reserve)\n        return query.reserve(c)\n\n      ready = c.execute(query)\n    }\n\n    ready\n      ? move(c, busy)\n      : move(c, full)\n  }\n\n  function onclose(c, e) {\n    move(c, closed)\n    c.reserved = null\n    c.onclose && (c.onclose(e), c.onclose = null)\n    options.onclose && options.onclose(c.id)\n    queries.length && connect(c, queries.shift())\n  }\n}\n\nfunction parseOptions(a, b) {\n  if (a && a.shared)\n    return a\n\n  const env = process.env // eslint-disable-line\n      , o = (!a || typeof a === 'string' ? b : a) || {}\n      , { url, multihost } = parseUrl(a)\n      , query = [...url.searchParams].reduce((a, [b, c]) => (a[b] = c, a), {})\n      , host = o.hostname || o.host || multihost || url.hostname || env.PGHOST || 'localhost'\n      , port = o.port || url.port || env.PGPORT || 5432\n      , user = o.user || o.username || url.username || env.PGUSERNAME || env.PGUSER || osUsername()\n\n  o.no_prepare && (o.prepare = false)\n  query.sslmode && (query.ssl = query.sslmode, delete query.sslmode)\n  'timeout' in o && (console.log('The timeout option is deprecated, use idle_timeout instead'), o.idle_timeout = o.timeout) // eslint-disable-line\n  query.sslrootcert === 'system' && (query.ssl = 'verify-full')\n\n  const ints = ['idle_timeout', 'connect_timeout', 'max_lifetime', 'max_pipeline', 'backoff', 'keep_alive']\n  const defaults = {\n    max             : 10,\n    ssl             : false,\n    idle_timeout    : null,\n    connect_timeout : 30,\n    max_lifetime    : max_lifetime,\n    max_pipeline    : 100,\n    backoff         : backoff,\n    keep_alive      : 60,\n    prepare         : true,\n    debug           : false,\n    fetch_types     : true,\n    publications    : 'alltables',\n    target_session_attrs: null\n  }\n\n  return {\n    host            : Array.isArray(host) ? host : host.split(',').map(x => x.split(':')[0]),\n    port            : Array.isArray(port) ? port : host.split(',').map(x => parseInt(x.split(':')[1] || port)),\n    path            : o.path || host.indexOf('/') > -1 && host + '/.s.PGSQL.' + port,\n    database        : o.database || o.db || (url.pathname || '').slice(1) || env.PGDATABASE || user,\n    user            : user,\n    pass            : o.pass || o.password || url.password || env.PGPASSWORD || '',\n    ...Object.entries(defaults).reduce(\n      (acc, [k, d]) => {\n        const value = k in o ? o[k] : k in query\n          ? (query[k] === 'disable' || query[k] === 'false' ? false : query[k])\n          : env['PG' + k.toUpperCase()] || d\n        acc[k] = typeof value === 'string' && ints.includes(k)\n          ? +value\n          : value\n        return acc\n      },\n      {}\n    ),\n    connection      : {\n      application_name: env.PGAPPNAME || 'postgres.js',\n      ...o.connection,\n      ...Object.entries(query).reduce((acc, [k, v]) => (k in defaults || (acc[k] = v), acc), {})\n    },\n    types           : o.types || {},\n    target_session_attrs: tsa(o, url, env),\n    onnotice        : o.onnotice,\n    onnotify        : o.onnotify,\n    onclose         : o.onclose,\n    onparameter     : o.onparameter,\n    socket          : o.socket,\n    transform       : parseTransform(o.transform || { undefined: undefined }),\n    parameters      : {},\n    shared          : { retries: 0, typeArrayMap: {} },\n    ...(0,_types_js__WEBPACK_IMPORTED_MODULE_2__.mergeUserTypes)(o.types)\n  }\n}\n\nfunction tsa(o, url, env) {\n  const x = o.target_session_attrs || url.searchParams.get('target_session_attrs') || env.PGTARGETSESSIONATTRS\n  if (!x || ['read-write', 'read-only', 'primary', 'standby', 'prefer-standby'].includes(x))\n    return x\n\n  throw new Error('target_session_attrs ' + x + ' is not supported')\n}\n\nfunction backoff(retries) {\n  return (0.5 + Math.random() / 2) * Math.min(3 ** retries / 100, 20)\n}\n\nfunction max_lifetime() {\n  return 60 * (30 + Math.random() * 30)\n}\n\nfunction parseTransform(x) {\n  return {\n    undefined: x.undefined,\n    column: {\n      from: typeof x.column === 'function' ? x.column : x.column && x.column.from,\n      to: x.column && x.column.to\n    },\n    value: {\n      from: typeof x.value === 'function' ? x.value : x.value && x.value.from,\n      to: x.value && x.value.to\n    },\n    row: {\n      from: typeof x.row === 'function' ? x.row : x.row && x.row.from,\n      to: x.row && x.row.to\n    }\n  }\n}\n\nfunction parseUrl(url) {\n  if (!url || typeof url !== 'string')\n    return { url: { searchParams: new Map() } }\n\n  let host = url\n  host = host.slice(host.indexOf('://') + 3).split(/[?/]/)[0]\n  host = decodeURIComponent(host.slice(host.indexOf('@') + 1))\n\n  const urlObj = new URL(url.replace(host, host.split(',')[0]))\n\n  return {\n    url: {\n      username: decodeURIComponent(urlObj.username),\n      password: decodeURIComponent(urlObj.password),\n      host: urlObj.host,\n      hostname: urlObj.hostname,\n      port: urlObj.port,\n      pathname: urlObj.pathname,\n      searchParams: urlObj.searchParams\n    },\n    multihost: host.indexOf(',') > -1 && host\n  }\n}\n\nfunction osUsername() {\n  try {\n    return os__WEBPACK_IMPORTED_MODULE_0__.userInfo().username // eslint-disable-line\n  } catch (_) {\n    return process.env.USERNAME || process.env.USER || process.env.LOGNAME  // eslint-disable-line\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/large.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/large.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ largeObject)\n/* harmony export */ });\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stream */ \"stream\");\n\n\nfunction largeObject(sql, oid, mode = 0x00020000 | 0x00040000) {\n  return new Promise(async(resolve, reject) => {\n    await sql.begin(async sql => {\n      let finish\n      !oid && ([{ oid }] = await sql`select lo_creat(-1) as oid`)\n      const [{ fd }] = await sql`select lo_open(${ oid }, ${ mode }) as fd`\n\n      const lo = {\n        writable,\n        readable,\n        close     : () => sql`select lo_close(${ fd })`.then(finish),\n        tell      : () => sql`select lo_tell64(${ fd })`,\n        read      : (x) => sql`select loread(${ fd }, ${ x }) as data`,\n        write     : (x) => sql`select lowrite(${ fd }, ${ x })`,\n        truncate  : (x) => sql`select lo_truncate64(${ fd }, ${ x })`,\n        seek      : (x, whence = 0) => sql`select lo_lseek64(${ fd }, ${ x }, ${ whence })`,\n        size      : () => sql`\n          select\n            lo_lseek64(${ fd }, location, 0) as position,\n            seek.size\n          from (\n            select\n              lo_lseek64($1, 0, 2) as size,\n              tell.location\n            from (select lo_tell64($1) as location) tell\n          ) seek\n        `\n      }\n\n      resolve(lo)\n\n      return new Promise(async r => finish = r)\n\n      async function readable({\n        highWaterMark = 2048 * 8,\n        start = 0,\n        end = Infinity\n      } = {}) {\n        let max = end - start\n        start && await lo.seek(start)\n        return new stream__WEBPACK_IMPORTED_MODULE_0__.Readable({\n          highWaterMark,\n          async read(size) {\n            const l = size > max ? size - max : size\n            max -= size\n            const [{ data }] = await lo.read(l)\n            this.push(data)\n            if (data.length < size)\n              this.push(null)\n          }\n        })\n      }\n\n      async function writable({\n        highWaterMark = 2048 * 8,\n        start = 0\n      } = {}) {\n        start && await lo.seek(start)\n        return new stream__WEBPACK_IMPORTED_MODULE_0__.Writable({\n          highWaterMark,\n          write(chunk, encoding, callback) {\n            lo.write(chunk).then(() => callback(), callback)\n          }\n        })\n      }\n    }).catch(reject)\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/large.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/query.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/query.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CLOSE: () => (/* binding */ CLOSE),\n/* harmony export */   Query: () => (/* binding */ Query)\n/* harmony export */ });\nconst originCache = new Map()\n    , originStackCache = new Map()\n    , originError = Symbol('OriginError')\n\nconst CLOSE = {}\nclass Query extends Promise {\n  constructor(strings, args, handler, canceller, options = {}) {\n    let resolve\n      , reject\n\n    super((a, b) => {\n      resolve = a\n      reject = b\n    })\n\n    this.tagged = Array.isArray(strings.raw)\n    this.strings = strings\n    this.args = args\n    this.handler = handler\n    this.canceller = canceller\n    this.options = options\n\n    this.state = null\n    this.statement = null\n\n    this.resolve = x => (this.active = false, resolve(x))\n    this.reject = x => (this.active = false, reject(x))\n\n    this.active = false\n    this.cancelled = null\n    this.executed = false\n    this.signature = ''\n\n    this[originError] = this.handler.debug\n      ? new Error()\n      : this.tagged && cachedError(this.strings)\n  }\n\n  get origin() {\n    return (this.handler.debug\n      ? this[originError].stack\n      : this.tagged && originStackCache.has(this.strings)\n        ? originStackCache.get(this.strings)\n        : originStackCache.set(this.strings, this[originError].stack).get(this.strings)\n    ) || ''\n  }\n\n  static get [Symbol.species]() {\n    return Promise\n  }\n\n  cancel() {\n    return this.canceller && (this.canceller(this), this.canceller = null)\n  }\n\n  simple() {\n    this.options.simple = true\n    this.options.prepare = false\n    return this\n  }\n\n  async readable() {\n    this.simple()\n    this.streaming = true\n    return this\n  }\n\n  async writable() {\n    this.simple()\n    this.streaming = true\n    return this\n  }\n\n  cursor(rows = 1, fn) {\n    this.options.simple = false\n    if (typeof rows === 'function') {\n      fn = rows\n      rows = 1\n    }\n\n    this.cursorRows = rows\n\n    if (typeof fn === 'function')\n      return (this.cursorFn = fn, this)\n\n    let prev\n    return {\n      [Symbol.asyncIterator]: () => ({\n        next: () => {\n          if (this.executed && !this.active)\n            return { done: true }\n\n          prev && prev()\n          const promise = new Promise((resolve, reject) => {\n            this.cursorFn = value => {\n              resolve({ value, done: false })\n              return new Promise(r => prev = r)\n            }\n            this.resolve = () => (this.active = false, resolve({ done: true }))\n            this.reject = x => (this.active = false, reject(x))\n          })\n          this.execute()\n          return promise\n        },\n        return() {\n          prev && prev(CLOSE)\n          return { done: true }\n        }\n      })\n    }\n  }\n\n  describe() {\n    this.options.simple = false\n    this.onlyDescribe = this.options.prepare = true\n    return this\n  }\n\n  stream() {\n    throw new Error('.stream has been renamed to .forEach')\n  }\n\n  forEach(fn) {\n    this.forEachFn = fn\n    this.handle()\n    return this\n  }\n\n  raw() {\n    this.isRaw = true\n    return this\n  }\n\n  values() {\n    this.isRaw = 'values'\n    return this\n  }\n\n  async handle() {\n    !this.executed && (this.executed = true) && await 1 && this.handler(this)\n  }\n\n  execute() {\n    this.handle()\n    return this\n  }\n\n  then() {\n    this.handle()\n    return super.then.apply(this, arguments)\n  }\n\n  catch() {\n    this.handle()\n    return super.catch.apply(this, arguments)\n  }\n\n  finally() {\n    this.handle()\n    return super.finally.apply(this, arguments)\n  }\n}\n\nfunction cachedError(xs) {\n  if (originCache.has(xs))\n    return originCache.get(xs)\n\n  const x = Error.stackTraceLimit\n  Error.stackTraceLimit = 4\n  originCache.set(xs, new Error())\n  Error.stackTraceLimit = x\n  return originCache.get(xs)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/query.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/queue.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/queue.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Queue);\n\nfunction Queue(initial = []) {\n  let xs = initial.slice()\n  let index = 0\n\n  return {\n    get length() {\n      return xs.length - index\n    },\n    remove: (x) => {\n      const index = xs.indexOf(x)\n      return index === -1\n        ? null\n        : (xs.splice(index, 1), x)\n    },\n    push: (x) => (xs.push(x), x),\n    shift: () => {\n      const out = xs[index++]\n\n      if (index === xs.length) {\n        index = 0\n        xs = []\n      } else {\n        xs[index - 1] = undefined\n      }\n\n      return out\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG9zdGdyZXMvc3JjL3F1ZXVlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxLQUFLOztBQUVwQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS91YnQyMi93b3Jrc3BhY2UvaW5kaWUvbGluZ3hpYWktZ2VtaW5pL25vZGVfbW9kdWxlcy9wb3N0Z3Jlcy9zcmMvcXVldWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgUXVldWVcblxuZnVuY3Rpb24gUXVldWUoaW5pdGlhbCA9IFtdKSB7XG4gIGxldCB4cyA9IGluaXRpYWwuc2xpY2UoKVxuICBsZXQgaW5kZXggPSAwXG5cbiAgcmV0dXJuIHtcbiAgICBnZXQgbGVuZ3RoKCkge1xuICAgICAgcmV0dXJuIHhzLmxlbmd0aCAtIGluZGV4XG4gICAgfSxcbiAgICByZW1vdmU6ICh4KSA9PiB7XG4gICAgICBjb25zdCBpbmRleCA9IHhzLmluZGV4T2YoeClcbiAgICAgIHJldHVybiBpbmRleCA9PT0gLTFcbiAgICAgICAgPyBudWxsXG4gICAgICAgIDogKHhzLnNwbGljZShpbmRleCwgMSksIHgpXG4gICAgfSxcbiAgICBwdXNoOiAoeCkgPT4gKHhzLnB1c2goeCksIHgpLFxuICAgIHNoaWZ0OiAoKSA9PiB7XG4gICAgICBjb25zdCBvdXQgPSB4c1tpbmRleCsrXVxuXG4gICAgICBpZiAoaW5kZXggPT09IHhzLmxlbmd0aCkge1xuICAgICAgICBpbmRleCA9IDBcbiAgICAgICAgeHMgPSBbXVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgeHNbaW5kZXggLSAxXSA9IHVuZGVmaW5lZFxuICAgICAgfVxuXG4gICAgICByZXR1cm4gb3V0XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/queue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/result.js":
/*!*********************************************!*\
  !*** ./node_modules/postgres/src/result.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Result)\n/* harmony export */ });\nclass Result extends Array {\n  constructor() {\n    super()\n    Object.defineProperties(this, {\n      count: { value: null, writable: true },\n      state: { value: null, writable: true },\n      command: { value: null, writable: true },\n      columns: { value: null, writable: true },\n      statement: { value: null, writable: true }\n    })\n  }\n\n  static get [Symbol.species]() {\n    return Array\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG9zdGdyZXMvc3JjL3Jlc3VsdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSxlQUFlLDZCQUE2QjtBQUM1QyxlQUFlLDZCQUE2QjtBQUM1QyxpQkFBaUIsNkJBQTZCO0FBQzlDLGlCQUFpQiw2QkFBNkI7QUFDOUMsbUJBQW1CO0FBQ25CLEtBQUs7QUFDTDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvdWJ0MjIvd29ya3NwYWNlL2luZGllL2xpbmd4aWFpLWdlbWluaS9ub2RlX21vZHVsZXMvcG9zdGdyZXMvc3JjL3Jlc3VsdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBjbGFzcyBSZXN1bHQgZXh0ZW5kcyBBcnJheSB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHN1cGVyKClcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydGllcyh0aGlzLCB7XG4gICAgICBjb3VudDogeyB2YWx1ZTogbnVsbCwgd3JpdGFibGU6IHRydWUgfSxcbiAgICAgIHN0YXRlOiB7IHZhbHVlOiBudWxsLCB3cml0YWJsZTogdHJ1ZSB9LFxuICAgICAgY29tbWFuZDogeyB2YWx1ZTogbnVsbCwgd3JpdGFibGU6IHRydWUgfSxcbiAgICAgIGNvbHVtbnM6IHsgdmFsdWU6IG51bGwsIHdyaXRhYmxlOiB0cnVlIH0sXG4gICAgICBzdGF0ZW1lbnQ6IHsgdmFsdWU6IG51bGwsIHdyaXRhYmxlOiB0cnVlIH1cbiAgICB9KVxuICB9XG5cbiAgc3RhdGljIGdldCBbU3ltYm9sLnNwZWNpZXNdKCkge1xuICAgIHJldHVybiBBcnJheVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/result.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/subscribe.js":
/*!************************************************!*\
  !*** ./node_modules/postgres/src/subscribe.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Subscribe)\n/* harmony export */ });\nconst noop = () => { /* noop */ }\n\nfunction Subscribe(postgres, options) {\n  const subscribers = new Map()\n      , slot = 'postgresjs_' + Math.random().toString(36).slice(2)\n      , state = {}\n\n  let connection\n    , stream\n    , ended = false\n\n  const sql = subscribe.sql = postgres({\n    ...options,\n    transform: { column: {}, value: {}, row: {} },\n    max: 1,\n    fetch_types: false,\n    idle_timeout: null,\n    max_lifetime: null,\n    connection: {\n      ...options.connection,\n      replication: 'database'\n    },\n    onclose: async function() {\n      if (ended)\n        return\n      stream = null\n      state.pid = state.secret = undefined\n      connected(await init(sql, slot, options.publications))\n      subscribers.forEach(event => event.forEach(({ onsubscribe }) => onsubscribe()))\n    },\n    no_subscribe: true\n  })\n\n  const end = sql.end\n      , close = sql.close\n\n  sql.end = async() => {\n    ended = true\n    stream && (await new Promise(r => (stream.once('close', r), stream.end())))\n    return end()\n  }\n\n  sql.close = async() => {\n    stream && (await new Promise(r => (stream.once('close', r), stream.end())))\n    return close()\n  }\n\n  return subscribe\n\n  async function subscribe(event, fn, onsubscribe = noop, onerror = noop) {\n    event = parseEvent(event)\n\n    if (!connection)\n      connection = init(sql, slot, options.publications)\n\n    const subscriber = { fn, onsubscribe }\n    const fns = subscribers.has(event)\n      ? subscribers.get(event).add(subscriber)\n      : subscribers.set(event, new Set([subscriber])).get(event)\n\n    const unsubscribe = () => {\n      fns.delete(subscriber)\n      fns.size === 0 && subscribers.delete(event)\n    }\n\n    return connection.then(x => {\n      connected(x)\n      onsubscribe()\n      stream && stream.on('error', onerror)\n      return { unsubscribe, state, sql }\n    })\n  }\n\n  function connected(x) {\n    stream = x.stream\n    state.pid = x.state.pid\n    state.secret = x.state.secret\n  }\n\n  async function init(sql, slot, publications) {\n    if (!publications)\n      throw new Error('Missing publication names')\n\n    const xs = await sql.unsafe(\n      `CREATE_REPLICATION_SLOT ${ slot } TEMPORARY LOGICAL pgoutput NOEXPORT_SNAPSHOT`\n    )\n\n    const [x] = xs\n\n    const stream = await sql.unsafe(\n      `START_REPLICATION SLOT ${ slot } LOGICAL ${\n        x.consistent_point\n      } (proto_version '1', publication_names '${ publications }')`\n    ).writable()\n\n    const state = {\n      lsn: Buffer.concat(x.consistent_point.split('/').map(x => Buffer.from(('00000000' + x).slice(-8), 'hex')))\n    }\n\n    stream.on('data', data)\n    stream.on('error', error)\n    stream.on('close', sql.close)\n\n    return { stream, state: xs.state }\n\n    function error(e) {\n      console.error('Unexpected error during logical streaming - reconnecting', e) // eslint-disable-line\n    }\n\n    function data(x) {\n      if (x[0] === 0x77) {\n        parse(x.subarray(25), state, sql.options.parsers, handle, options.transform)\n      } else if (x[0] === 0x6b && x[17]) {\n        state.lsn = x.subarray(1, 9)\n        pong()\n      }\n    }\n\n    function handle(a, b) {\n      const path = b.relation.schema + '.' + b.relation.table\n      call('*', a, b)\n      call('*:' + path, a, b)\n      b.relation.keys.length && call('*:' + path + '=' + b.relation.keys.map(x => a[x.name]), a, b)\n      call(b.command, a, b)\n      call(b.command + ':' + path, a, b)\n      b.relation.keys.length && call(b.command + ':' + path + '=' + b.relation.keys.map(x => a[x.name]), a, b)\n    }\n\n    function pong() {\n      const x = Buffer.alloc(34)\n      x[0] = 'r'.charCodeAt(0)\n      x.fill(state.lsn, 1)\n      x.writeBigInt64BE(BigInt(Date.now() - Date.UTC(2000, 0, 1)) * BigInt(1000), 25)\n      stream.write(x)\n    }\n  }\n\n  function call(x, a, b) {\n    subscribers.has(x) && subscribers.get(x).forEach(({ fn }) => fn(a, b, x))\n  }\n}\n\nfunction Time(x) {\n  return new Date(Date.UTC(2000, 0, 1) + Number(x / BigInt(1000)))\n}\n\nfunction parse(x, state, parsers, handle, transform) {\n  const char = (acc, [k, v]) => (acc[k.charCodeAt(0)] = v, acc)\n\n  Object.entries({\n    R: x => {  // Relation\n      let i = 1\n      const r = state[x.readUInt32BE(i)] = {\n        schema: x.toString('utf8', i += 4, i = x.indexOf(0, i)) || 'pg_catalog',\n        table: x.toString('utf8', i + 1, i = x.indexOf(0, i + 1)),\n        columns: Array(x.readUInt16BE(i += 2)),\n        keys: []\n      }\n      i += 2\n\n      let columnIndex = 0\n        , column\n\n      while (i < x.length) {\n        column = r.columns[columnIndex++] = {\n          key: x[i++],\n          name: transform.column.from\n            ? transform.column.from(x.toString('utf8', i, i = x.indexOf(0, i)))\n            : x.toString('utf8', i, i = x.indexOf(0, i)),\n          type: x.readUInt32BE(i += 1),\n          parser: parsers[x.readUInt32BE(i)],\n          atttypmod: x.readUInt32BE(i += 4)\n        }\n\n        column.key && r.keys.push(column)\n        i += 4\n      }\n    },\n    Y: () => { /* noop */ }, // Type\n    O: () => { /* noop */ }, // Origin\n    B: x => { // Begin\n      state.date = Time(x.readBigInt64BE(9))\n      state.lsn = x.subarray(1, 9)\n    },\n    I: x => { // Insert\n      let i = 1\n      const relation = state[x.readUInt32BE(i)]\n      const { row } = tuples(x, relation.columns, i += 7, transform)\n\n      handle(row, {\n        command: 'insert',\n        relation\n      })\n    },\n    D: x => { // Delete\n      let i = 1\n      const relation = state[x.readUInt32BE(i)]\n      i += 4\n      const key = x[i] === 75\n      handle(key || x[i] === 79\n        ? tuples(x, relation.columns, i += 3, transform).row\n        : null\n      , {\n        command: 'delete',\n        relation,\n        key\n      })\n    },\n    U: x => { // Update\n      let i = 1\n      const relation = state[x.readUInt32BE(i)]\n      i += 4\n      const key = x[i] === 75\n      const xs = key || x[i] === 79\n        ? tuples(x, relation.columns, i += 3, transform)\n        : null\n\n      xs && (i = xs.i)\n\n      const { row } = tuples(x, relation.columns, i + 3, transform)\n\n      handle(row, {\n        command: 'update',\n        relation,\n        key,\n        old: xs && xs.row\n      })\n    },\n    T: () => { /* noop */ }, // Truncate,\n    C: () => { /* noop */ }  // Commit\n  }).reduce(char, {})[x[0]](x)\n}\n\nfunction tuples(x, columns, xi, transform) {\n  let type\n    , column\n    , value\n\n  const row = transform.raw ? new Array(columns.length) : {}\n  for (let i = 0; i < columns.length; i++) {\n    type = x[xi++]\n    column = columns[i]\n    value = type === 110 // n\n      ? null\n      : type === 117 // u\n        ? undefined\n        : column.parser === undefined\n          ? x.toString('utf8', xi + 4, xi += 4 + x.readUInt32BE(xi))\n          : column.parser.array === true\n            ? column.parser(x.toString('utf8', xi + 5, xi += 4 + x.readUInt32BE(xi)))\n            : column.parser(x.toString('utf8', xi + 4, xi += 4 + x.readUInt32BE(xi)))\n\n    transform.raw\n      ? (row[i] = transform.raw === true\n        ? value\n        : transform.value.from ? transform.value.from(value, column) : value)\n      : (row[column.name] = transform.value.from\n        ? transform.value.from(value, column)\n        : value\n      )\n  }\n\n  return { i: xi, row: transform.row.from ? transform.row.from(row) : row }\n}\n\nfunction parseEvent(x) {\n  const xs = x.match(/^(\\*|insert|update|delete)?:?([^.]+?\\.?[^=]+)?=?(.+)?/i) || []\n\n  if (!xs)\n    throw new Error('Malformed subscribe pattern: ' + x)\n\n  const [, command, path, key] = xs\n\n  return (command || '*')\n       + (path ? ':' + (path.indexOf('.') === -1 ? 'public.' + path : path) : '')\n       + (key ? '=' + key : '')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/subscribe.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/types.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/types.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Builder: () => (/* binding */ Builder),\n/* harmony export */   END: () => (/* binding */ END),\n/* harmony export */   Identifier: () => (/* binding */ Identifier),\n/* harmony export */   Parameter: () => (/* binding */ Parameter),\n/* harmony export */   arrayParser: () => (/* binding */ arrayParser),\n/* harmony export */   arraySerializer: () => (/* binding */ arraySerializer),\n/* harmony export */   camel: () => (/* binding */ camel),\n/* harmony export */   escapeIdentifier: () => (/* binding */ escapeIdentifier),\n/* harmony export */   fromCamel: () => (/* binding */ fromCamel),\n/* harmony export */   fromKebab: () => (/* binding */ fromKebab),\n/* harmony export */   fromPascal: () => (/* binding */ fromPascal),\n/* harmony export */   handleValue: () => (/* binding */ handleValue),\n/* harmony export */   inferType: () => (/* binding */ inferType),\n/* harmony export */   kebab: () => (/* binding */ kebab),\n/* harmony export */   mergeUserTypes: () => (/* binding */ mergeUserTypes),\n/* harmony export */   parsers: () => (/* binding */ parsers),\n/* harmony export */   pascal: () => (/* binding */ pascal),\n/* harmony export */   serializers: () => (/* binding */ serializers),\n/* harmony export */   stringify: () => (/* binding */ stringify),\n/* harmony export */   toCamel: () => (/* binding */ toCamel),\n/* harmony export */   toKebab: () => (/* binding */ toKebab),\n/* harmony export */   toPascal: () => (/* binding */ toPascal),\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./query.js */ \"(rsc)/./node_modules/postgres/src/query.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./errors.js */ \"(rsc)/./node_modules/postgres/src/errors.js\");\n\n\n\nconst types = {\n  string: {\n    to: 25,\n    from: null,             // defaults to string\n    serialize: x => '' + x\n  },\n  number: {\n    to: 0,\n    from: [21, 23, 26, 700, 701],\n    serialize: x => '' + x,\n    parse: x => +x\n  },\n  json: {\n    to: 114,\n    from: [114, 3802],\n    serialize: x => JSON.stringify(x),\n    parse: x => JSON.parse(x)\n  },\n  boolean: {\n    to: 16,\n    from: 16,\n    serialize: x => x === true ? 't' : 'f',\n    parse: x => x === 't'\n  },\n  date: {\n    to: 1184,\n    from: [1082, 1114, 1184],\n    serialize: x => (x instanceof Date ? x : new Date(x)).toISOString(),\n    parse: x => new Date(x)\n  },\n  bytea: {\n    to: 17,\n    from: 17,\n    serialize: x => '\\\\x' + Buffer.from(x).toString('hex'),\n    parse: x => Buffer.from(x.slice(2), 'hex')\n  }\n}\n\nclass NotTagged { then() { notTagged() } catch() { notTagged() } finally() { notTagged() }}\n\nclass Identifier extends NotTagged {\n  constructor(value) {\n    super()\n    this.value = escapeIdentifier(value)\n  }\n}\n\nclass Parameter extends NotTagged {\n  constructor(value, type, array) {\n    super()\n    this.value = value\n    this.type = type\n    this.array = array\n  }\n}\n\nclass Builder extends NotTagged {\n  constructor(first, rest) {\n    super()\n    this.first = first\n    this.rest = rest\n  }\n\n  build(before, parameters, types, options) {\n    const keyword = builders.map(([x, fn]) => ({ fn, i: before.search(x) })).sort((a, b) => a.i - b.i).pop()\n    return keyword.i === -1\n      ? escapeIdentifiers(this.first, options)\n      : keyword.fn(this.first, this.rest, parameters, types, options)\n  }\n}\n\nfunction handleValue(x, parameters, types, options) {\n  let value = x instanceof Parameter ? x.value : x\n  if (value === undefined) {\n    x instanceof Parameter\n      ? x.value = options.transform.undefined\n      : value = x = options.transform.undefined\n\n    if (value === undefined)\n      throw _errors_js__WEBPACK_IMPORTED_MODULE_1__.Errors.generic('UNDEFINED_VALUE', 'Undefined values are not allowed')\n  }\n\n  return '$' + (types.push(\n    x instanceof Parameter\n      ? (parameters.push(x.value), x.array\n        ? x.array[x.type || inferType(x.value)] || x.type || firstIsString(x.value)\n        : x.type\n      )\n      : (parameters.push(x), inferType(x))\n  ))\n}\n\nconst defaultHandlers = typeHandlers(types)\n\nfunction stringify(q, string, value, parameters, types, options) { // eslint-disable-line\n  for (let i = 1; i < q.strings.length; i++) {\n    string += (stringifyValue(string, value, parameters, types, options)) + q.strings[i]\n    value = q.args[i]\n  }\n\n  return string\n}\n\nfunction stringifyValue(string, value, parameters, types, o) {\n  return (\n    value instanceof Builder ? value.build(string, parameters, types, o) :\n    value instanceof _query_js__WEBPACK_IMPORTED_MODULE_0__.Query ? fragment(value, parameters, types, o) :\n    value instanceof Identifier ? value.value :\n    value && value[0] instanceof _query_js__WEBPACK_IMPORTED_MODULE_0__.Query ? value.reduce((acc, x) => acc + ' ' + fragment(x, parameters, types, o), '') :\n    handleValue(value, parameters, types, o)\n  )\n}\n\nfunction fragment(q, parameters, types, options) {\n  q.fragment = true\n  return stringify(q, q.strings[0], q.args[0], parameters, types, options)\n}\n\nfunction valuesBuilder(first, parameters, types, columns, options) {\n  return first.map(row =>\n    '(' + columns.map(column =>\n      stringifyValue('values', row[column], parameters, types, options)\n    ).join(',') + ')'\n  ).join(',')\n}\n\nfunction values(first, rest, parameters, types, options) {\n  const multi = Array.isArray(first[0])\n  const columns = rest.length ? rest.flat() : Object.keys(multi ? first[0] : first)\n  return valuesBuilder(multi ? first : [first], parameters, types, columns, options)\n}\n\nfunction select(first, rest, parameters, types, options) {\n  typeof first === 'string' && (first = [first].concat(rest))\n  if (Array.isArray(first))\n    return escapeIdentifiers(first, options)\n\n  let value\n  const columns = rest.length ? rest.flat() : Object.keys(first)\n  return columns.map(x => {\n    value = first[x]\n    return (\n      value instanceof _query_js__WEBPACK_IMPORTED_MODULE_0__.Query ? fragment(value, parameters, types, options) :\n      value instanceof Identifier ? value.value :\n      handleValue(value, parameters, types, options)\n    ) + ' as ' + escapeIdentifier(options.transform.column.to ? options.transform.column.to(x) : x)\n  }).join(',')\n}\n\nconst builders = Object.entries({\n  values,\n  in: (...xs) => {\n    const x = values(...xs)\n    return x === '()' ? '(null)' : x\n  },\n  select,\n  as: select,\n  returning: select,\n  '\\\\(': select,\n\n  update(first, rest, parameters, types, options) {\n    return (rest.length ? rest.flat() : Object.keys(first)).map(x =>\n      escapeIdentifier(options.transform.column.to ? options.transform.column.to(x) : x) +\n      '=' + stringifyValue('values', first[x], parameters, types, options)\n    )\n  },\n\n  insert(first, rest, parameters, types, options) {\n    const columns = rest.length ? rest.flat() : Object.keys(Array.isArray(first) ? first[0] : first)\n    return '(' + escapeIdentifiers(columns, options) + ')values' +\n    valuesBuilder(Array.isArray(first) ? first : [first], parameters, types, columns, options)\n  }\n}).map(([x, fn]) => ([new RegExp('((?:^|[\\\\s(])' + x + '(?:$|[\\\\s(]))(?![\\\\s\\\\S]*\\\\1)', 'i'), fn]))\n\nfunction notTagged() {\n  throw _errors_js__WEBPACK_IMPORTED_MODULE_1__.Errors.generic('NOT_TAGGED_CALL', 'Query not called as a tagged template literal')\n}\n\nconst serializers = defaultHandlers.serializers\nconst parsers = defaultHandlers.parsers\n\nconst END = {}\n\nfunction firstIsString(x) {\n  if (Array.isArray(x))\n    return firstIsString(x[0])\n  return typeof x === 'string' ? 1009 : 0\n}\n\nconst mergeUserTypes = function(types) {\n  const user = typeHandlers(types || {})\n  return {\n    serializers: Object.assign({}, serializers, user.serializers),\n    parsers: Object.assign({}, parsers, user.parsers)\n  }\n}\n\nfunction typeHandlers(types) {\n  return Object.keys(types).reduce((acc, k) => {\n    types[k].from && [].concat(types[k].from).forEach(x => acc.parsers[x] = types[k].parse)\n    if (types[k].serialize) {\n      acc.serializers[types[k].to] = types[k].serialize\n      types[k].from && [].concat(types[k].from).forEach(x => acc.serializers[x] = types[k].serialize)\n    }\n    return acc\n  }, { parsers: {}, serializers: {} })\n}\n\nfunction escapeIdentifiers(xs, { transform: { column } }) {\n  return xs.map(x => escapeIdentifier(column.to ? column.to(x) : x)).join(',')\n}\n\nconst escapeIdentifier = function escape(str) {\n  return '\"' + str.replace(/\"/g, '\"\"').replace(/\\./g, '\".\"') + '\"'\n}\n\nconst inferType = function inferType(x) {\n  return (\n    x instanceof Parameter ? x.type :\n    x instanceof Date ? 1184 :\n    x instanceof Uint8Array ? 17 :\n    (x === true || x === false) ? 16 :\n    typeof x === 'bigint' ? 20 :\n    Array.isArray(x) ? inferType(x[0]) :\n    0\n  )\n}\n\nconst escapeBackslash = /\\\\/g\nconst escapeQuote = /\"/g\n\nfunction arrayEscape(x) {\n  return x\n    .replace(escapeBackslash, '\\\\\\\\')\n    .replace(escapeQuote, '\\\\\"')\n}\n\nconst arraySerializer = function arraySerializer(xs, serializer, options, typarray) {\n  if (Array.isArray(xs) === false)\n    return xs\n\n  if (!xs.length)\n    return '{}'\n\n  const first = xs[0]\n  // Only _box (1020) has the ';' delimiter for arrays, all other types use the ',' delimiter\n  const delimiter = typarray === 1020 ? ';' : ','\n\n  if (Array.isArray(first) && !first.type)\n    return '{' + xs.map(x => arraySerializer(x, serializer, options, typarray)).join(delimiter) + '}'\n\n  return '{' + xs.map(x => {\n    if (x === undefined) {\n      x = options.transform.undefined\n      if (x === undefined)\n        throw _errors_js__WEBPACK_IMPORTED_MODULE_1__.Errors.generic('UNDEFINED_VALUE', 'Undefined values are not allowed')\n    }\n\n    return x === null\n      ? 'null'\n      : '\"' + arrayEscape(serializer ? serializer(x.type ? x.value : x) : '' + x) + '\"'\n  }).join(delimiter) + '}'\n}\n\nconst arrayParserState = {\n  i: 0,\n  char: null,\n  str: '',\n  quoted: false,\n  last: 0\n}\n\nconst arrayParser = function arrayParser(x, parser, typarray) {\n  arrayParserState.i = arrayParserState.last = 0\n  return arrayParserLoop(arrayParserState, x, parser, typarray)\n}\n\nfunction arrayParserLoop(s, x, parser, typarray) {\n  const xs = []\n  // Only _box (1020) has the ';' delimiter for arrays, all other types use the ',' delimiter\n  const delimiter = typarray === 1020 ? ';' : ','\n  for (; s.i < x.length; s.i++) {\n    s.char = x[s.i]\n    if (s.quoted) {\n      if (s.char === '\\\\') {\n        s.str += x[++s.i]\n      } else if (s.char === '\"') {\n        xs.push(parser ? parser(s.str) : s.str)\n        s.str = ''\n        s.quoted = x[s.i + 1] === '\"'\n        s.last = s.i + 2\n      } else {\n        s.str += s.char\n      }\n    } else if (s.char === '\"') {\n      s.quoted = true\n    } else if (s.char === '{') {\n      s.last = ++s.i\n      xs.push(arrayParserLoop(s, x, parser, typarray))\n    } else if (s.char === '}') {\n      s.quoted = false\n      s.last < s.i && xs.push(parser ? parser(x.slice(s.last, s.i)) : x.slice(s.last, s.i))\n      s.last = s.i + 1\n      break\n    } else if (s.char === delimiter && s.p !== '}' && s.p !== '\"') {\n      xs.push(parser ? parser(x.slice(s.last, s.i)) : x.slice(s.last, s.i))\n      s.last = s.i + 1\n    }\n    s.p = s.char\n  }\n  s.last < s.i && xs.push(parser ? parser(x.slice(s.last, s.i + 1)) : x.slice(s.last, s.i + 1))\n  return xs\n}\n\nconst toCamel = x => {\n  let str = x[0]\n  for (let i = 1; i < x.length; i++)\n    str += x[i] === '_' ? x[++i].toUpperCase() : x[i]\n  return str\n}\n\nconst toPascal = x => {\n  let str = x[0].toUpperCase()\n  for (let i = 1; i < x.length; i++)\n    str += x[i] === '_' ? x[++i].toUpperCase() : x[i]\n  return str\n}\n\nconst toKebab = x => x.replace(/_/g, '-')\n\nconst fromCamel = x => x.replace(/([A-Z])/g, '_$1').toLowerCase()\nconst fromPascal = x => (x.slice(0, 1) + x.slice(1).replace(/([A-Z])/g, '_$1')).toLowerCase()\nconst fromKebab = x => x.replace(/-/g, '_')\n\nfunction createJsonTransform(fn) {\n  return function jsonTransform(x, column) {\n    return typeof x === 'object' && x !== null && (column.type === 114 || column.type === 3802)\n      ? Array.isArray(x)\n        ? x.map(x => jsonTransform(x, column))\n        : Object.entries(x).reduce((acc, [k, v]) => Object.assign(acc, { [fn(k)]: jsonTransform(v, column) }), {})\n      : x\n  }\n}\n\ntoCamel.column = { from: toCamel }\ntoCamel.value = { from: createJsonTransform(toCamel) }\nfromCamel.column = { to: fromCamel }\n\nconst camel = { ...toCamel }\ncamel.column.to = fromCamel\n\ntoPascal.column = { from: toPascal }\ntoPascal.value = { from: createJsonTransform(toPascal) }\nfromPascal.column = { to: fromPascal }\n\nconst pascal = { ...toPascal }\npascal.column.to = fromPascal\n\ntoKebab.column = { from: toKebab }\ntoKebab.value = { from: createJsonTransform(toKebab) }\nfromKebab.column = { to: fromKebab }\n\nconst kebab = { ...toKebab }\nkebab.column.to = fromKebab\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/types.js\n");

/***/ })

};
;