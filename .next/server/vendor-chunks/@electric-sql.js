"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@electric-sql";
exports.ids = ["vendor-chunks/@electric-sql"];
exports.modules = {

/***/ "(rsc)/./node_modules/@electric-sql/client/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@electric-sql/client/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackoffDefaults: () => (/* binding */ BackoffDefaults),\n/* harmony export */   FetchError: () => (/* binding */ FetchError),\n/* harmony export */   Shape: () => (/* binding */ Shape),\n/* harmony export */   ShapeStream: () => (/* binding */ ShapeStream),\n/* harmony export */   isChangeMessage: () => (/* binding */ isChangeMessage),\n/* harmony export */   isControlMessage: () => (/* binding */ isControlMessage)\n/* harmony export */ });\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __typeError = (msg) => {\n  throw TypeError(msg);\n};\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nvar __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg);\nvar __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), getter ? getter.call(obj) : member.get(obj));\nvar __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\nvar __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, \"write to private field\"), setter ? setter.call(obj, value) : member.set(obj, value), value);\nvar __privateMethod = (obj, member, method) => (__accessCheck(obj, member, \"access private method\"), method);\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// src/error.ts\nvar FetchError = class _FetchError extends Error {\n  constructor(status, text, json, headers, url, message) {\n    super(\n      message || `HTTP Error ${status} at ${url}: ${text != null ? text : JSON.stringify(json)}`\n    );\n    this.url = url;\n    this.name = `FetchError`;\n    this.status = status;\n    this.text = text;\n    this.json = json;\n    this.headers = headers;\n  }\n  static fromResponse(response, url) {\n    return __async(this, null, function* () {\n      const status = response.status;\n      const headers = Object.fromEntries([...response.headers.entries()]);\n      let text = void 0;\n      let json = void 0;\n      const contentType = response.headers.get(`content-type`);\n      if (contentType && contentType.includes(`application/json`)) {\n        json = yield response.json();\n      } else {\n        text = yield response.text();\n      }\n      return new _FetchError(status, text, json, headers, url);\n    });\n  }\n};\nvar FetchBackoffAbortError = class extends Error {\n  constructor() {\n    super(`Fetch with backoff aborted`);\n    this.name = `FetchBackoffAbortError`;\n  }\n};\nvar MissingShapeUrlError = class extends Error {\n  constructor() {\n    super(`Invalid shape options: missing required url parameter`);\n    this.name = `MissingShapeUrlError`;\n  }\n};\nvar InvalidSignalError = class extends Error {\n  constructor() {\n    super(`Invalid signal option. It must be an instance of AbortSignal.`);\n    this.name = `InvalidSignalError`;\n  }\n};\nvar MissingShapeHandleError = class extends Error {\n  constructor() {\n    super(\n      `shapeHandle is required if this isn't an initial fetch (i.e. offset > -1)`\n    );\n    this.name = `MissingShapeHandleError`;\n  }\n};\nvar ReservedParamError = class extends Error {\n  constructor(reservedParams) {\n    super(\n      `Cannot use reserved Electric parameter names in custom params: ${reservedParams.join(`, `)}`\n    );\n    this.name = `ReservedParamError`;\n  }\n};\nvar ParserNullValueError = class extends Error {\n  constructor(columnName) {\n    super(`Column \"${columnName != null ? columnName : `unknown`}\" does not allow NULL values`);\n    this.name = `ParserNullValueError`;\n  }\n};\nvar MissingHeadersError = class extends Error {\n  constructor(url, missingHeaders) {\n    let msg = `The response for the shape request to ${url} didn't include the following required headers:\n`;\n    missingHeaders.forEach((h) => {\n      msg += `- ${h}\n`;\n    });\n    msg += `\nThis is often due to a proxy not setting CORS correctly so that all Electric headers can be read by the client.`;\n    msg += `\nFor more information visit the troubleshooting guide: /docs/guides/troubleshooting/missing-headers`;\n    super(msg);\n  }\n};\n\n// src/parser.ts\nvar parseNumber = (value) => Number(value);\nvar parseBool = (value) => value === `true` || value === `t`;\nvar parseBigInt = (value) => BigInt(value);\nvar parseJson = (value) => JSON.parse(value);\nvar identityParser = (v) => v;\nvar defaultParser = {\n  int2: parseNumber,\n  int4: parseNumber,\n  int8: parseBigInt,\n  bool: parseBool,\n  float4: parseNumber,\n  float8: parseNumber,\n  json: parseJson,\n  jsonb: parseJson\n};\nfunction pgArrayParser(value, parser) {\n  let i = 0;\n  let char = null;\n  let str = ``;\n  let quoted = false;\n  let last = 0;\n  let p = void 0;\n  function loop(x) {\n    const xs = [];\n    for (; i < x.length; i++) {\n      char = x[i];\n      if (quoted) {\n        if (char === `\\\\`) {\n          str += x[++i];\n        } else if (char === `\"`) {\n          xs.push(parser ? parser(str) : str);\n          str = ``;\n          quoted = x[i + 1] === `\"`;\n          last = i + 2;\n        } else {\n          str += char;\n        }\n      } else if (char === `\"`) {\n        quoted = true;\n      } else if (char === `{`) {\n        last = ++i;\n        xs.push(loop(x));\n      } else if (char === `}`) {\n        quoted = false;\n        last < i && xs.push(parser ? parser(x.slice(last, i)) : x.slice(last, i));\n        last = i + 1;\n        break;\n      } else if (char === `,` && p !== `}` && p !== `\"`) {\n        xs.push(parser ? parser(x.slice(last, i)) : x.slice(last, i));\n        last = i + 1;\n      }\n      p = char;\n    }\n    last < i && xs.push(parser ? parser(x.slice(last, i + 1)) : x.slice(last, i + 1));\n    return xs;\n  }\n  return loop(value)[0];\n}\nvar MessageParser = class {\n  constructor(parser) {\n    this.parser = __spreadValues(__spreadValues({}, defaultParser), parser);\n  }\n  parse(messages, schema) {\n    return JSON.parse(messages, (key, value) => {\n      if (key === `value` && typeof value === `object` && value !== null) {\n        const row = value;\n        Object.keys(row).forEach((key2) => {\n          row[key2] = this.parseRow(key2, row[key2], schema);\n        });\n      }\n      return value;\n    });\n  }\n  // Parses the message values using the provided parser based on the schema information\n  parseRow(key, value, schema) {\n    var _b;\n    const columnInfo = schema[key];\n    if (!columnInfo) {\n      return value;\n    }\n    const _a = columnInfo, { type: typ, dims: dimensions } = _a, additionalInfo = __objRest(_a, [\"type\", \"dims\"]);\n    const typeParser = (_b = this.parser[typ]) != null ? _b : identityParser;\n    const parser = makeNullableParser(typeParser, columnInfo, key);\n    if (dimensions && dimensions > 0) {\n      const nullablePgArrayParser = makeNullableParser(\n        (value2, _) => pgArrayParser(value2, parser),\n        columnInfo,\n        key\n      );\n      return nullablePgArrayParser(value);\n    }\n    return parser(value, additionalInfo);\n  }\n};\nfunction makeNullableParser(parser, columnInfo, columnName) {\n  var _a;\n  const isNullable = !((_a = columnInfo.not_null) != null ? _a : false);\n  return (value) => {\n    if (isPgNull(value)) {\n      if (!isNullable) {\n        throw new ParserNullValueError(columnName != null ? columnName : `unknown`);\n      }\n      return null;\n    }\n    return parser(value, columnInfo);\n  };\n}\nfunction isPgNull(value) {\n  return value === null || value === `NULL`;\n}\n\n// src/helpers.ts\nfunction isChangeMessage(message) {\n  return `key` in message;\n}\nfunction isControlMessage(message) {\n  return !isChangeMessage(message);\n}\nfunction isUpToDateMessage(message) {\n  return isControlMessage(message) && message.headers.control === `up-to-date`;\n}\n\n// src/constants.ts\nvar LIVE_CACHE_BUSTER_HEADER = `electric-cursor`;\nvar SHAPE_HANDLE_HEADER = `electric-handle`;\nvar CHUNK_LAST_OFFSET_HEADER = `electric-offset`;\nvar SHAPE_SCHEMA_HEADER = `electric-schema`;\nvar CHUNK_UP_TO_DATE_HEADER = `electric-up-to-date`;\nvar COLUMNS_QUERY_PARAM = `columns`;\nvar LIVE_CACHE_BUSTER_QUERY_PARAM = `cursor`;\nvar SHAPE_HANDLE_QUERY_PARAM = `handle`;\nvar LIVE_QUERY_PARAM = `live`;\nvar OFFSET_QUERY_PARAM = `offset`;\nvar TABLE_QUERY_PARAM = `table`;\nvar WHERE_QUERY_PARAM = `where`;\nvar REPLICA_PARAM = `replica`;\n\n// src/fetch.ts\nvar HTTP_RETRY_STATUS_CODES = [429];\nvar BackoffDefaults = {\n  initialDelay: 100,\n  maxDelay: 1e4,\n  multiplier: 1.3\n};\nfunction createFetchWithBackoff(fetchClient, backoffOptions = BackoffDefaults) {\n  const {\n    initialDelay,\n    maxDelay,\n    multiplier,\n    debug = false,\n    onFailedAttempt\n  } = backoffOptions;\n  return (...args) => __async(this, null, function* () {\n    var _a;\n    const url = args[0];\n    const options = args[1];\n    let delay = initialDelay;\n    let attempt = 0;\n    while (true) {\n      try {\n        const result = yield fetchClient(...args);\n        if (result.ok) return result;\n        else throw yield FetchError.fromResponse(result, url.toString());\n      } catch (e) {\n        onFailedAttempt == null ? void 0 : onFailedAttempt();\n        if ((_a = options == null ? void 0 : options.signal) == null ? void 0 : _a.aborted) {\n          throw new FetchBackoffAbortError();\n        } else if (e instanceof FetchError && !HTTP_RETRY_STATUS_CODES.includes(e.status) && e.status >= 400 && e.status < 500) {\n          throw e;\n        } else {\n          yield new Promise((resolve) => setTimeout(resolve, delay));\n          delay = Math.min(delay * multiplier, maxDelay);\n          if (debug) {\n            attempt++;\n            console.log(`Retry attempt #${attempt} after ${delay}ms`);\n          }\n        }\n      }\n    }\n  });\n}\nvar ChunkPrefetchDefaults = {\n  maxChunksToPrefetch: 2\n};\nfunction createFetchWithChunkBuffer(fetchClient, prefetchOptions = ChunkPrefetchDefaults) {\n  const { maxChunksToPrefetch } = prefetchOptions;\n  let prefetchQueue;\n  const prefetchClient = (...args) => __async(this, null, function* () {\n    const url = args[0].toString();\n    const prefetchedRequest = prefetchQueue == null ? void 0 : prefetchQueue.consume(...args);\n    if (prefetchedRequest) {\n      return prefetchedRequest;\n    }\n    prefetchQueue == null ? void 0 : prefetchQueue.abort();\n    const response = yield fetchClient(...args);\n    const nextUrl = getNextChunkUrl(url, response);\n    if (nextUrl) {\n      prefetchQueue = new PrefetchQueue({\n        fetchClient,\n        maxPrefetchedRequests: maxChunksToPrefetch,\n        url: nextUrl,\n        requestInit: args[1]\n      });\n    }\n    return response;\n  });\n  return prefetchClient;\n}\nvar requiredElectricResponseHeaders = [\n  `electric-offset`,\n  `electric-handle`\n];\nvar requiredLiveResponseHeaders = [`electric-cursor`];\nvar requiredNonLiveResponseHeaders = [`electric-schema`];\nfunction createFetchWithResponseHeadersCheck(fetchClient) {\n  return (...args) => __async(this, null, function* () {\n    const response = yield fetchClient(...args);\n    if (response.ok) {\n      const headers = response.headers;\n      const missingHeaders = [];\n      const addMissingHeaders = (requiredHeaders) => missingHeaders.push(...requiredHeaders.filter((h) => !headers.has(h)));\n      addMissingHeaders(requiredElectricResponseHeaders);\n      const input = args[0];\n      const urlString = input.toString();\n      const url = new URL(urlString);\n      if (url.searchParams.get(LIVE_QUERY_PARAM) === `true`) {\n        addMissingHeaders(requiredLiveResponseHeaders);\n      }\n      if (!url.searchParams.has(LIVE_QUERY_PARAM) || url.searchParams.get(LIVE_QUERY_PARAM) === `false`) {\n        addMissingHeaders(requiredNonLiveResponseHeaders);\n      }\n      if (missingHeaders.length > 0) {\n        throw new MissingHeadersError(urlString, missingHeaders);\n      }\n    }\n    return response;\n  });\n}\nvar _fetchClient, _maxPrefetchedRequests, _prefetchQueue, _queueHeadUrl, _queueTailUrl, _PrefetchQueue_instances, prefetch_fn;\nvar PrefetchQueue = class {\n  constructor(options) {\n    __privateAdd(this, _PrefetchQueue_instances);\n    __privateAdd(this, _fetchClient);\n    __privateAdd(this, _maxPrefetchedRequests);\n    __privateAdd(this, _prefetchQueue, /* @__PURE__ */ new Map());\n    __privateAdd(this, _queueHeadUrl);\n    __privateAdd(this, _queueTailUrl);\n    var _a;\n    __privateSet(this, _fetchClient, (_a = options.fetchClient) != null ? _a : (...args) => fetch(...args));\n    __privateSet(this, _maxPrefetchedRequests, options.maxPrefetchedRequests);\n    __privateSet(this, _queueHeadUrl, options.url.toString());\n    __privateSet(this, _queueTailUrl, __privateGet(this, _queueHeadUrl));\n    __privateMethod(this, _PrefetchQueue_instances, prefetch_fn).call(this, options.url, options.requestInit);\n  }\n  abort() {\n    __privateGet(this, _prefetchQueue).forEach(([_, aborter]) => aborter.abort());\n  }\n  consume(...args) {\n    var _a;\n    const url = args[0].toString();\n    const request = (_a = __privateGet(this, _prefetchQueue).get(url)) == null ? void 0 : _a[0];\n    if (!request || url !== __privateGet(this, _queueHeadUrl)) return;\n    __privateGet(this, _prefetchQueue).delete(url);\n    request.then((response) => {\n      const nextUrl = getNextChunkUrl(url, response);\n      __privateSet(this, _queueHeadUrl, nextUrl);\n      if (__privateGet(this, _queueTailUrl) && !__privateGet(this, _prefetchQueue).has(__privateGet(this, _queueTailUrl))) {\n        __privateMethod(this, _PrefetchQueue_instances, prefetch_fn).call(this, __privateGet(this, _queueTailUrl), args[1]);\n      }\n    }).catch(() => {\n    });\n    return request;\n  }\n};\n_fetchClient = new WeakMap();\n_maxPrefetchedRequests = new WeakMap();\n_prefetchQueue = new WeakMap();\n_queueHeadUrl = new WeakMap();\n_queueTailUrl = new WeakMap();\n_PrefetchQueue_instances = new WeakSet();\nprefetch_fn = function(...args) {\n  var _a, _b;\n  const url = args[0].toString();\n  if (__privateGet(this, _prefetchQueue).size >= __privateGet(this, _maxPrefetchedRequests)) return;\n  const aborter = new AbortController();\n  try {\n    const request = __privateGet(this, _fetchClient).call(this, url, __spreadProps(__spreadValues({}, (_a = args[1]) != null ? _a : {}), {\n      signal: chainAborter(aborter, (_b = args[1]) == null ? void 0 : _b.signal)\n    }));\n    __privateGet(this, _prefetchQueue).set(url, [request, aborter]);\n    request.then((response) => {\n      if (!response.ok || aborter.signal.aborted) return;\n      const nextUrl = getNextChunkUrl(url, response);\n      if (!nextUrl || nextUrl === url) {\n        __privateSet(this, _queueTailUrl, void 0);\n        return;\n      }\n      __privateSet(this, _queueTailUrl, nextUrl);\n      return __privateMethod(this, _PrefetchQueue_instances, prefetch_fn).call(this, nextUrl, args[1]);\n    }).catch(() => {\n    });\n  } catch (_) {\n  }\n};\nfunction getNextChunkUrl(url, res) {\n  const shapeHandle = res.headers.get(SHAPE_HANDLE_HEADER);\n  const lastOffset = res.headers.get(CHUNK_LAST_OFFSET_HEADER);\n  const isUpToDate = res.headers.has(CHUNK_UP_TO_DATE_HEADER);\n  if (!shapeHandle || !lastOffset || isUpToDate) return;\n  const nextUrl = new URL(url);\n  if (nextUrl.searchParams.has(LIVE_QUERY_PARAM)) return;\n  nextUrl.searchParams.set(SHAPE_HANDLE_QUERY_PARAM, shapeHandle);\n  nextUrl.searchParams.set(OFFSET_QUERY_PARAM, lastOffset);\n  nextUrl.searchParams.sort();\n  return nextUrl.toString();\n}\nfunction chainAborter(aborter, sourceSignal) {\n  if (!sourceSignal) return aborter.signal;\n  if (sourceSignal.aborted) aborter.abort();\n  else\n    sourceSignal.addEventListener(`abort`, () => aborter.abort(), {\n      once: true\n    });\n  return aborter.signal;\n}\n\n// src/client.ts\nvar RESERVED_PARAMS = /* @__PURE__ */ new Set([\n  LIVE_CACHE_BUSTER_QUERY_PARAM,\n  SHAPE_HANDLE_QUERY_PARAM,\n  LIVE_QUERY_PARAM,\n  OFFSET_QUERY_PARAM\n]);\nfunction toInternalParams(params) {\n  const result = {};\n  for (const [key, value] of Object.entries(params)) {\n    result[key] = Array.isArray(value) ? value.join(`,`) : value;\n  }\n  return result;\n}\nvar _error, _fetchClient2, _messageParser, _subscribers, _lastOffset, _liveCacheBuster, _lastSyncedAt, _isUpToDate, _connected, _shapeHandle, _schema, _onError, _ShapeStream_instances, start_fn, publish_fn, sendErrorToSubscribers_fn, reset_fn;\nvar ShapeStream = class {\n  constructor(options) {\n    __privateAdd(this, _ShapeStream_instances);\n    __privateAdd(this, _error, null);\n    __privateAdd(this, _fetchClient2);\n    __privateAdd(this, _messageParser);\n    __privateAdd(this, _subscribers, /* @__PURE__ */ new Map());\n    __privateAdd(this, _lastOffset);\n    __privateAdd(this, _liveCacheBuster);\n    // Seconds since our Electric Epoch 😎\n    __privateAdd(this, _lastSyncedAt);\n    // unix time\n    __privateAdd(this, _isUpToDate, false);\n    __privateAdd(this, _connected, false);\n    __privateAdd(this, _shapeHandle);\n    __privateAdd(this, _schema);\n    __privateAdd(this, _onError);\n    var _a, _b, _c;\n    this.options = __spreadValues({ subscribe: true }, options);\n    validateOptions(this.options);\n    __privateSet(this, _lastOffset, (_a = this.options.offset) != null ? _a : `-1`);\n    __privateSet(this, _liveCacheBuster, ``);\n    __privateSet(this, _shapeHandle, this.options.handle);\n    __privateSet(this, _messageParser, new MessageParser(options.parser));\n    __privateSet(this, _onError, this.options.onError);\n    const baseFetchClient = (_b = options.fetchClient) != null ? _b : (...args) => fetch(...args);\n    const fetchWithBackoffClient = createFetchWithBackoff(baseFetchClient, __spreadProps(__spreadValues({}, (_c = options.backoffOptions) != null ? _c : BackoffDefaults), {\n      onFailedAttempt: () => {\n        var _a2, _b2;\n        __privateSet(this, _connected, false);\n        (_b2 = (_a2 = options.backoffOptions) == null ? void 0 : _a2.onFailedAttempt) == null ? void 0 : _b2.call(_a2);\n      }\n    }));\n    __privateSet(this, _fetchClient2, createFetchWithResponseHeadersCheck(\n      createFetchWithChunkBuffer(fetchWithBackoffClient)\n    ));\n    __privateMethod(this, _ShapeStream_instances, start_fn).call(this);\n  }\n  get shapeHandle() {\n    return __privateGet(this, _shapeHandle);\n  }\n  get error() {\n    return __privateGet(this, _error);\n  }\n  get isUpToDate() {\n    return __privateGet(this, _isUpToDate);\n  }\n  get lastOffset() {\n    return __privateGet(this, _lastOffset);\n  }\n  subscribe(callback, onError = () => {\n  }) {\n    const subscriptionId = Math.random();\n    __privateGet(this, _subscribers).set(subscriptionId, [callback, onError]);\n    return () => {\n      __privateGet(this, _subscribers).delete(subscriptionId);\n    };\n  }\n  unsubscribeAll() {\n    __privateGet(this, _subscribers).clear();\n  }\n  /** Unix time at which we last synced. Undefined when `isLoading` is true. */\n  lastSyncedAt() {\n    return __privateGet(this, _lastSyncedAt);\n  }\n  /** Time elapsed since last sync (in ms). Infinity if we did not yet sync. */\n  lastSynced() {\n    if (__privateGet(this, _lastSyncedAt) === void 0) return Infinity;\n    return Date.now() - __privateGet(this, _lastSyncedAt);\n  }\n  /** Indicates if we are connected to the Electric sync service. */\n  isConnected() {\n    return __privateGet(this, _connected);\n  }\n  /** True during initial fetch. False afterwise.  */\n  isLoading() {\n    return !__privateGet(this, _isUpToDate);\n  }\n};\n_error = new WeakMap();\n_fetchClient2 = new WeakMap();\n_messageParser = new WeakMap();\n_subscribers = new WeakMap();\n_lastOffset = new WeakMap();\n_liveCacheBuster = new WeakMap();\n_lastSyncedAt = new WeakMap();\n_isUpToDate = new WeakMap();\n_connected = new WeakMap();\n_shapeHandle = new WeakMap();\n_schema = new WeakMap();\n_onError = new WeakMap();\n_ShapeStream_instances = new WeakSet();\nstart_fn = function() {\n  return __async(this, null, function* () {\n    var _a, _b;\n    try {\n      while (!((_a = this.options.signal) == null ? void 0 : _a.aborted) && !__privateGet(this, _isUpToDate) || this.options.subscribe) {\n        const { url, signal } = this.options;\n        const fetchUrl = new URL(url);\n        if (this.options.params) {\n          const reservedParams = Object.keys(this.options.params).filter(\n            (key) => RESERVED_PARAMS.has(key)\n          );\n          if (reservedParams.length > 0) {\n            throw new Error(\n              `Cannot use reserved Electric parameter names in custom params: ${reservedParams.join(`, `)}`\n            );\n          }\n          const params = toInternalParams(this.options.params);\n          if (params.table)\n            fetchUrl.searchParams.set(TABLE_QUERY_PARAM, params.table);\n          if (params.where)\n            fetchUrl.searchParams.set(WHERE_QUERY_PARAM, params.where);\n          if (params.columns)\n            fetchUrl.searchParams.set(COLUMNS_QUERY_PARAM, params.columns);\n          if (params.replica)\n            fetchUrl.searchParams.set(REPLICA_PARAM, params.replica);\n          const customParams = __spreadValues({}, params);\n          delete customParams.table;\n          delete customParams.where;\n          delete customParams.columns;\n          delete customParams.replica;\n          for (const [key, value] of Object.entries(customParams)) {\n            fetchUrl.searchParams.set(key, value);\n          }\n        }\n        fetchUrl.searchParams.set(OFFSET_QUERY_PARAM, __privateGet(this, _lastOffset));\n        if (__privateGet(this, _isUpToDate)) {\n          fetchUrl.searchParams.set(LIVE_QUERY_PARAM, `true`);\n          fetchUrl.searchParams.set(\n            LIVE_CACHE_BUSTER_QUERY_PARAM,\n            __privateGet(this, _liveCacheBuster)\n          );\n        }\n        if (__privateGet(this, _shapeHandle)) {\n          fetchUrl.searchParams.set(\n            SHAPE_HANDLE_QUERY_PARAM,\n            __privateGet(this, _shapeHandle)\n          );\n        }\n        fetchUrl.searchParams.sort();\n        let response;\n        try {\n          response = yield __privateGet(this, _fetchClient2).call(this, fetchUrl.toString(), {\n            signal,\n            headers: this.options.headers\n          });\n          __privateSet(this, _connected, true);\n        } catch (e) {\n          if (e instanceof FetchBackoffAbortError) break;\n          if (!(e instanceof FetchError)) throw e;\n          if (e.status == 409) {\n            const newShapeHandle = e.headers[SHAPE_HANDLE_HEADER];\n            __privateMethod(this, _ShapeStream_instances, reset_fn).call(this, newShapeHandle);\n            yield __privateMethod(this, _ShapeStream_instances, publish_fn).call(this, e.json);\n            continue;\n          } else if (e.status >= 400 && e.status < 500) {\n            __privateMethod(this, _ShapeStream_instances, sendErrorToSubscribers_fn).call(this, e);\n            throw e;\n          }\n        }\n        const { headers, status } = response;\n        const shapeHandle = headers.get(SHAPE_HANDLE_HEADER);\n        if (shapeHandle) {\n          __privateSet(this, _shapeHandle, shapeHandle);\n        }\n        const lastOffset = headers.get(CHUNK_LAST_OFFSET_HEADER);\n        if (lastOffset) {\n          __privateSet(this, _lastOffset, lastOffset);\n        }\n        const liveCacheBuster = headers.get(LIVE_CACHE_BUSTER_HEADER);\n        if (liveCacheBuster) {\n          __privateSet(this, _liveCacheBuster, liveCacheBuster);\n        }\n        const getSchema = () => {\n          const schemaHeader = headers.get(SHAPE_SCHEMA_HEADER);\n          return schemaHeader ? JSON.parse(schemaHeader) : {};\n        };\n        __privateSet(this, _schema, (_b = __privateGet(this, _schema)) != null ? _b : getSchema());\n        const messages = status === 204 ? `[]` : yield response.text();\n        if (status === 204) {\n          __privateSet(this, _lastSyncedAt, Date.now());\n        }\n        const batch = __privateGet(this, _messageParser).parse(messages, __privateGet(this, _schema));\n        if (batch.length > 0) {\n          const lastMessage = batch[batch.length - 1];\n          if (isUpToDateMessage(lastMessage)) {\n            __privateSet(this, _lastSyncedAt, Date.now());\n            __privateSet(this, _isUpToDate, true);\n          }\n          yield __privateMethod(this, _ShapeStream_instances, publish_fn).call(this, batch);\n        }\n      }\n    } catch (err) {\n      __privateSet(this, _error, err);\n      if (__privateGet(this, _onError)) {\n        const retryOpts = yield __privateGet(this, _onError).call(this, err);\n        if (typeof retryOpts === `object`) {\n          __privateMethod(this, _ShapeStream_instances, reset_fn).call(this);\n          if (`params` in retryOpts) {\n            this.options.params = retryOpts.params;\n          }\n          if (`headers` in retryOpts) {\n            this.options.headers = retryOpts.headers;\n          }\n          __privateMethod(this, _ShapeStream_instances, start_fn).call(this);\n        }\n        return;\n      }\n      throw err;\n    } finally {\n      __privateSet(this, _connected, false);\n    }\n  });\n};\npublish_fn = function(messages) {\n  return __async(this, null, function* () {\n    yield Promise.all(\n      Array.from(__privateGet(this, _subscribers).values()).map((_0) => __async(this, [_0], function* ([callback, __]) {\n        try {\n          yield callback(messages);\n        } catch (err) {\n          queueMicrotask(() => {\n            throw err;\n          });\n        }\n      }))\n    );\n  });\n};\nsendErrorToSubscribers_fn = function(error) {\n  __privateGet(this, _subscribers).forEach(([_, errorFn]) => {\n    errorFn == null ? void 0 : errorFn(error);\n  });\n};\n/**\n * Resets the state of the stream, optionally with a provided\n * shape handle\n */\nreset_fn = function(handle) {\n  __privateSet(this, _lastOffset, `-1`);\n  __privateSet(this, _liveCacheBuster, ``);\n  __privateSet(this, _shapeHandle, handle);\n  __privateSet(this, _isUpToDate, false);\n  __privateSet(this, _connected, false);\n  __privateSet(this, _schema, void 0);\n};\nShapeStream.Replica = {\n  FULL: `full`,\n  DEFAULT: `default`\n};\nfunction validateOptions(options) {\n  if (!options.url) {\n    throw new MissingShapeUrlError();\n  }\n  if (options.signal && !(options.signal instanceof AbortSignal)) {\n    throw new InvalidSignalError();\n  }\n  if (options.offset !== void 0 && options.offset !== `-1` && !options.handle) {\n    throw new MissingShapeHandleError();\n  }\n  if (options.params) {\n    const reservedParams = Object.keys(options.params).filter(\n      (key) => RESERVED_PARAMS.has(key)\n    );\n    if (reservedParams.length > 0) {\n      throw new ReservedParamError(reservedParams);\n    }\n  }\n  return;\n}\n\n// src/shape.ts\nvar _data, _subscribers2, _hasNotifiedSubscribersUpToDate, _error2, _Shape_instances, process_fn, handleError_fn, notify_fn;\nvar Shape = class {\n  constructor(stream) {\n    __privateAdd(this, _Shape_instances);\n    __privateAdd(this, _data, /* @__PURE__ */ new Map());\n    __privateAdd(this, _subscribers2, /* @__PURE__ */ new Map());\n    __privateAdd(this, _hasNotifiedSubscribersUpToDate, false);\n    __privateAdd(this, _error2, false);\n    this.stream = stream;\n    this.stream.subscribe(\n      __privateMethod(this, _Shape_instances, process_fn).bind(this),\n      __privateMethod(this, _Shape_instances, handleError_fn).bind(this)\n    );\n  }\n  get isUpToDate() {\n    return this.stream.isUpToDate;\n  }\n  get lastOffset() {\n    return this.stream.lastOffset;\n  }\n  get handle() {\n    return this.stream.shapeHandle;\n  }\n  get rows() {\n    return this.value.then((v) => Array.from(v.values()));\n  }\n  get currentRows() {\n    return Array.from(this.currentValue.values());\n  }\n  get value() {\n    return new Promise((resolve, reject) => {\n      if (this.stream.isUpToDate) {\n        resolve(this.currentValue);\n      } else {\n        const unsubscribe = this.subscribe(({ value }) => {\n          unsubscribe();\n          if (__privateGet(this, _error2)) reject(__privateGet(this, _error2));\n          resolve(value);\n        });\n      }\n    });\n  }\n  get currentValue() {\n    return __privateGet(this, _data);\n  }\n  get error() {\n    return __privateGet(this, _error2);\n  }\n  /** Unix time at which we last synced. Undefined when `isLoading` is true. */\n  lastSyncedAt() {\n    return this.stream.lastSyncedAt();\n  }\n  /** Time elapsed since last sync (in ms). Infinity if we did not yet sync. */\n  lastSynced() {\n    return this.stream.lastSynced();\n  }\n  /** True during initial fetch. False afterwise.  */\n  isLoading() {\n    return this.stream.isLoading();\n  }\n  /** Indicates if we are connected to the Electric sync service. */\n  isConnected() {\n    return this.stream.isConnected();\n  }\n  subscribe(callback) {\n    const subscriptionId = Math.random();\n    __privateGet(this, _subscribers2).set(subscriptionId, callback);\n    return () => {\n      __privateGet(this, _subscribers2).delete(subscriptionId);\n    };\n  }\n  unsubscribeAll() {\n    __privateGet(this, _subscribers2).clear();\n  }\n  get numSubscribers() {\n    return __privateGet(this, _subscribers2).size;\n  }\n};\n_data = new WeakMap();\n_subscribers2 = new WeakMap();\n_hasNotifiedSubscribersUpToDate = new WeakMap();\n_error2 = new WeakMap();\n_Shape_instances = new WeakSet();\nprocess_fn = function(messages) {\n  let dataMayHaveChanged = false;\n  let isUpToDate = false;\n  let newlyUpToDate = false;\n  messages.forEach((message) => {\n    if (isChangeMessage(message)) {\n      dataMayHaveChanged = [`insert`, `update`, `delete`].includes(\n        message.headers.operation\n      );\n      switch (message.headers.operation) {\n        case `insert`:\n          __privateGet(this, _data).set(message.key, message.value);\n          break;\n        case `update`:\n          __privateGet(this, _data).set(message.key, __spreadValues(__spreadValues({}, __privateGet(this, _data).get(message.key)), message.value));\n          break;\n        case `delete`:\n          __privateGet(this, _data).delete(message.key);\n          break;\n      }\n    }\n    if (isControlMessage(message)) {\n      switch (message.headers.control) {\n        case `up-to-date`:\n          isUpToDate = true;\n          if (!__privateGet(this, _hasNotifiedSubscribersUpToDate)) {\n            newlyUpToDate = true;\n          }\n          break;\n        case `must-refetch`:\n          __privateGet(this, _data).clear();\n          __privateSet(this, _error2, false);\n          __privateSet(this, _hasNotifiedSubscribersUpToDate, false);\n          isUpToDate = false;\n          newlyUpToDate = false;\n          break;\n      }\n    }\n  });\n  if (newlyUpToDate || isUpToDate && dataMayHaveChanged) {\n    __privateSet(this, _hasNotifiedSubscribersUpToDate, true);\n    __privateMethod(this, _Shape_instances, notify_fn).call(this);\n  }\n};\nhandleError_fn = function(e) {\n  if (e instanceof FetchError) {\n    __privateSet(this, _error2, e);\n    __privateMethod(this, _Shape_instances, notify_fn).call(this);\n  }\n};\nnotify_fn = function() {\n  __privateGet(this, _subscribers2).forEach((callback) => {\n    callback({ value: this.currentValue, rows: this.currentRows });\n  });\n};\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@electric-sql/client/dist/index.mjs\n");

/***/ })

};
;