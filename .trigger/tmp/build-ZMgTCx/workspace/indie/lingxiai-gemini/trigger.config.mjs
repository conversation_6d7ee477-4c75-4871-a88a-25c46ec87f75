import {
  defineConfig
} from "../../../chunk-ZYTIYOJE.mjs";
import {
  require_main
} from "../../../chunk-H23MYWI4.mjs";
import {
  __toESM,
  init_esm
} from "../../../chunk-CYQ63LDX.mjs";

// trigger.config.ts
init_esm();
var import_dotenv = __toESM(require_main());
(0, import_dotenv.config)();
var trigger_config_default = defineConfig({
  // Your project ref (you can see it on the Project settings page in the dashboard)
  project: process.env.TRIGGER_PROJECT_REF,
  //The paths for your trigger folders
  dirs: ["./src/trigger"],
  retries: {
    //If you want to retry a task in dev mode (when using the CLI)
    enabledInDev: false,
    //the default retry settings. Used if you don't specify on a task.
    default: {
      maxAttempts: 3,
      minTimeoutInMs: 1e3,
      maxTimeoutInMs: 1e4,
      factor: 2,
      randomize: true
    }
  },
  maxDuration: 300,
  build: {}
});
var resolveEnvVars = void 0;
export {
  trigger_config_default as default,
  resolveEnvVars
};
//# sourceMappingURL=trigger.config.mjs.map
