{"version": 3, "sources": ["../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/index.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwe/compact/decrypt.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwe/flattened/decrypt.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/base64url.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/buffer_utils.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/digest.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/decrypt.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/check_iv_length.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/util/errors.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/iv.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/random.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/check_cek_length.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/is_key_object.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/timing_safe_equal.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/cbc_tag.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/webcrypto.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/crypto_key.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/invalid_key_input.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/ciphers.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/is_key_like.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/is_disjoint.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/is_object.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/decrypt_key_management.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/aeskw.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/ecdhes.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/get_named_curve.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/is_jwk.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/pbes2kw.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/check_p2s.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/rsaes.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/check_key_length.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/normalize_key.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/cek.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/key/import.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/asn1.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/jwk_to_key.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/check_key_type.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/aesgcmkw.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/encrypt.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/validate_crit.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/validate_algorithms.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwe/general/decrypt.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwe/general/encrypt.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwe/flattened/encrypt.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/private_symbols.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/encrypt_key_management.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/key/export.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/key_to_jwk.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jws/compact/verify.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jws/flattened/verify.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/verify.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/dsa_digest.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/node_key.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/sign.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/hmac_digest.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/get_sign_verify_key.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jws/general/verify.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwt/verify.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/jwt_claims_set.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/epoch.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/lib/secs.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwt/decrypt.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwe/compact/encrypt.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jws/compact/sign.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jws/flattened/sign.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jws/general/sign.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwt/sign.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwt/produce.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwt/encrypt.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwk/thumbprint.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwk/embedded.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwks/local.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwks/remote.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/fetch_jwks.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/jwt/unsecured.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/util/decode_protected_header.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/util/base64url.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/util/decode_jwt.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/key/generate_key_pair.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/generate.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/key/generate_secret.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/util/runtime.js", "../../../node_modules/@trigger.dev/core/node_modules/jose/dist/node/esm/runtime/runtime.js"], "sourcesContent": ["export { compactDecrypt } from './jwe/compact/decrypt.js';\nexport { flattenedDecrypt } from './jwe/flattened/decrypt.js';\nexport { generalDecrypt } from './jwe/general/decrypt.js';\nexport { GeneralEncrypt } from './jwe/general/encrypt.js';\nexport { compactVerify } from './jws/compact/verify.js';\nexport { flattenedVerify } from './jws/flattened/verify.js';\nexport { generalVerify } from './jws/general/verify.js';\nexport { jwtVerify } from './jwt/verify.js';\nexport { jwtDecrypt } from './jwt/decrypt.js';\nexport { CompactEncrypt } from './jwe/compact/encrypt.js';\nexport { FlattenedEncrypt } from './jwe/flattened/encrypt.js';\nexport { CompactSign } from './jws/compact/sign.js';\nexport { FlattenedSign } from './jws/flattened/sign.js';\nexport { GeneralSign } from './jws/general/sign.js';\nexport { SignJWT } from './jwt/sign.js';\nexport { EncryptJWT } from './jwt/encrypt.js';\nexport { calculateJwkThumbprint, calculateJwkThumbprintUri } from './jwk/thumbprint.js';\nexport { EmbeddedJWK } from './jwk/embedded.js';\nexport { createLocalJWKSet } from './jwks/local.js';\nexport { createRemoteJWKSet, jwksCache, experimental_jwksCache } from './jwks/remote.js';\nexport { UnsecuredJWT } from './jwt/unsecured.js';\nexport { exportPKCS8, exportSPKI, exportJWK } from './key/export.js';\nexport { importSPKI, importPKCS8, importX509, importJWK } from './key/import.js';\nexport { decodeProtectedHeader } from './util/decode_protected_header.js';\nexport { decodeJwt } from './util/decode_jwt.js';\nexport * as errors from './util/errors.js';\nexport { generateKeyPair } from './key/generate_key_pair.js';\nexport { generateSecret } from './key/generate_secret.js';\nexport * as base64url from './util/base64url.js';\nexport { default as cryptoRuntime } from './util/runtime.js';\n", "import { flattenedDecrypt } from '../flattened/decrypt.js';\nimport { JWEInvalid } from '../../util/errors.js';\nimport { decoder } from '../../lib/buffer_utils.js';\nexport async function compactDecrypt(jwe, key, options) {\n    if (jwe instanceof Uint8Array) {\n        jwe = decoder.decode(jwe);\n    }\n    if (typeof jwe !== 'string') {\n        throw new JWEInvalid('Compact JWE must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: encryptedKey, 2: iv, 3: ciphertext, 4: tag, length, } = jwe.split('.');\n    if (length !== 5) {\n        throw new JWEInvalid('Invalid Compact JWE');\n    }\n    const decrypted = await flattenedDecrypt({\n        ciphertext,\n        iv: iv || undefined,\n        protected: protectedHeader,\n        tag: tag || undefined,\n        encrypted_key: encryptedKey || undefined,\n    }, key, options);\n    const result = { plaintext: decrypted.plaintext, protectedHeader: decrypted.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n", "import { decode as base64url } from '../../runtime/base64url.js';\nimport decrypt from '../../runtime/decrypt.js';\nimport { JOSEAlgNotAllowed, JOSENotSupported, JWEInvalid } from '../../util/errors.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport isObject from '../../lib/is_object.js';\nimport decryptKeyManagement from '../../lib/decrypt_key_management.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport generateCek from '../../lib/cek.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport validateAlgorithms from '../../lib/validate_algorithms.js';\nexport async function flattenedDecrypt(jwe, key, options) {\n    if (!isObject(jwe)) {\n        throw new JWEInvalid('Flattened JWE must be an object');\n    }\n    if (jwe.protected === undefined && jwe.header === undefined && jwe.unprotected === undefined) {\n        throw new JWEInvalid('JOSE Header missing');\n    }\n    if (jwe.iv !== undefined && typeof jwe.iv !== 'string') {\n        throw new JWEInvalid('JWE Initialization Vector incorrect type');\n    }\n    if (typeof jwe.ciphertext !== 'string') {\n        throw new JWEInvalid('JWE Ciphertext missing or incorrect type');\n    }\n    if (jwe.tag !== undefined && typeof jwe.tag !== 'string') {\n        throw new JWEInvalid('JWE Authentication Tag incorrect type');\n    }\n    if (jwe.protected !== undefined && typeof jwe.protected !== 'string') {\n        throw new JWEInvalid('JWE Protected Header incorrect type');\n    }\n    if (jwe.encrypted_key !== undefined && typeof jwe.encrypted_key !== 'string') {\n        throw new JWEInvalid('JWE Encrypted Key incorrect type');\n    }\n    if (jwe.aad !== undefined && typeof jwe.aad !== 'string') {\n        throw new JWEInvalid('JWE AAD incorrect type');\n    }\n    if (jwe.header !== undefined && !isObject(jwe.header)) {\n        throw new JWEInvalid('JWE Shared Unprotected Header incorrect type');\n    }\n    if (jwe.unprotected !== undefined && !isObject(jwe.unprotected)) {\n        throw new JWEInvalid('JWE Per-Recipient Unprotected Header incorrect type');\n    }\n    let parsedProt;\n    if (jwe.protected) {\n        try {\n            const protectedHeader = base64url(jwe.protected);\n            parsedProt = JSON.parse(decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new JWEInvalid('JWE Protected Header is invalid');\n        }\n    }\n    if (!isDisjoint(parsedProt, jwe.header, jwe.unprotected)) {\n        throw new JWEInvalid('JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jwe.header,\n        ...jwe.unprotected,\n    };\n    validateCrit(JWEInvalid, new Map(), options?.crit, parsedProt, joseHeader);\n    if (joseHeader.zip !== undefined) {\n        throw new JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n    }\n    const { alg, enc } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new JWEInvalid('missing JWE Algorithm (alg) in JWE Header');\n    }\n    if (typeof enc !== 'string' || !enc) {\n        throw new JWEInvalid('missing JWE Encryption Algorithm (enc) in JWE Header');\n    }\n    const keyManagementAlgorithms = options && validateAlgorithms('keyManagementAlgorithms', options.keyManagementAlgorithms);\n    const contentEncryptionAlgorithms = options &&\n        validateAlgorithms('contentEncryptionAlgorithms', options.contentEncryptionAlgorithms);\n    if ((keyManagementAlgorithms && !keyManagementAlgorithms.has(alg)) ||\n        (!keyManagementAlgorithms && alg.startsWith('PBES2'))) {\n        throw new JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (contentEncryptionAlgorithms && !contentEncryptionAlgorithms.has(enc)) {\n        throw new JOSEAlgNotAllowed('\"enc\" (Encryption Algorithm) Header Parameter value not allowed');\n    }\n    let encryptedKey;\n    if (jwe.encrypted_key !== undefined) {\n        try {\n            encryptedKey = base64url(jwe.encrypted_key);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the encrypted_key');\n        }\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jwe);\n        resolvedKey = true;\n    }\n    let cek;\n    try {\n        cek = await decryptKeyManagement(alg, key, encryptedKey, joseHeader, options);\n    }\n    catch (err) {\n        if (err instanceof TypeError || err instanceof JWEInvalid || err instanceof JOSENotSupported) {\n            throw err;\n        }\n        cek = generateCek(enc);\n    }\n    let iv;\n    let tag;\n    if (jwe.iv !== undefined) {\n        try {\n            iv = base64url(jwe.iv);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the iv');\n        }\n    }\n    if (jwe.tag !== undefined) {\n        try {\n            tag = base64url(jwe.tag);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the tag');\n        }\n    }\n    const protectedHeader = encoder.encode(jwe.protected ?? '');\n    let additionalData;\n    if (jwe.aad !== undefined) {\n        additionalData = concat(protectedHeader, encoder.encode('.'), encoder.encode(jwe.aad));\n    }\n    else {\n        additionalData = protectedHeader;\n    }\n    let ciphertext;\n    try {\n        ciphertext = base64url(jwe.ciphertext);\n    }\n    catch {\n        throw new JWEInvalid('Failed to base64url decode the ciphertext');\n    }\n    const plaintext = await decrypt(enc, cek, ciphertext, iv, tag, additionalData);\n    const result = { plaintext };\n    if (jwe.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jwe.aad !== undefined) {\n        try {\n            result.additionalAuthenticatedData = base64url(jwe.aad);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the aad');\n        }\n    }\n    if (jwe.unprotected !== undefined) {\n        result.sharedUnprotectedHeader = jwe.unprotected;\n    }\n    if (jwe.header !== undefined) {\n        result.unprotectedHeader = jwe.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key };\n    }\n    return result;\n}\n", "import { <PERSON><PERSON><PERSON> } from 'node:buffer';\nimport { decoder } from '../lib/buffer_utils.js';\nfunction normalize(input) {\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = decoder.decode(encoded);\n    }\n    return encoded;\n}\nconst encode = (input) => Buffer.from(input).toString('base64url');\nexport const decodeBase64 = (input) => new Uint8Array(Buffer.from(input, 'base64'));\nexport const encodeBase64 = (input) => Buffer.from(input).toString('base64');\nexport { encode };\nexport const decode = (input) => new Uint8Array(Buffer.from(normalize(input), 'base64url'));\n", "import digest from '../runtime/digest.js';\nexport const encoder = new TextEncoder();\nexport const decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nexport function concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    for (const buffer of buffers) {\n        buf.set(buffer, i);\n        i += buffer.length;\n    }\n    return buf;\n}\nexport function p2s(alg, p2sInput) {\n    return concat(encoder.encode(alg), new Uint8Array([0]), p2sInput);\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nexport function uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nexport function uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\nexport function lengthAndInput(input) {\n    return concat(uint32be(input.length), input);\n}\nexport async function concatKdf(secret, bits, value) {\n    const iterations = Math.ceil((bits >> 3) / 32);\n    const res = new Uint8Array(iterations * 32);\n    for (let iter = 0; iter < iterations; iter++) {\n        const buf = new Uint8Array(4 + secret.length + value.length);\n        buf.set(uint32be(iter + 1));\n        buf.set(secret, 4);\n        buf.set(value, 4 + secret.length);\n        res.set(await digest('sha256', buf), iter * 32);\n    }\n    return res.slice(0, bits >> 3);\n}\n", "import { createHash } from 'node:crypto';\nconst digest = (algorithm, data) => createHash(algorithm).update(data).digest();\nexport default digest;\n", "import { createDecipheriv, KeyObject } from 'node:crypto';\nimport checkIvLength from '../lib/check_iv_length.js';\nimport checkCekLength from './check_cek_length.js';\nimport { concat } from '../lib/buffer_utils.js';\nimport { JOSENotSupported, JWEDecryptionFailed, JWEInvalid } from '../util/errors.js';\nimport timingSafeEqual from './timing_safe_equal.js';\nimport cbcTag from './cbc_tag.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport { checkEncCryptoKey } from '../lib/crypto_key.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport supported from './ciphers.js';\nimport { types } from './is_key_like.js';\nfunction cbcDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    if (isKeyObject(cek)) {\n        cek = cek.export();\n    }\n    const encKey = cek.subarray(keySize >> 3);\n    const macKey = cek.subarray(0, keySize >> 3);\n    const macSize = parseInt(enc.slice(-3), 10);\n    const algorithm = `aes-${keySize}-cbc`;\n    if (!supported(algorithm)) {\n        throw new JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const expectedTag = cbcTag(aad, iv, ciphertext, macSize, macKey, keySize);\n    let macCheckPassed;\n    try {\n        macCheckPassed = timingSafeEqual(tag, expectedTag);\n    }\n    catch {\n    }\n    if (!macCheckPassed) {\n        throw new JWEDecryptionFailed();\n    }\n    let plaintext;\n    try {\n        const decipher = createDecipheriv(algorithm, encKey, iv);\n        plaintext = concat(decipher.update(ciphertext), decipher.final());\n    }\n    catch {\n    }\n    if (!plaintext) {\n        throw new JWEDecryptionFailed();\n    }\n    return plaintext;\n}\nfunction gcmDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const algorithm = `aes-${keySize}-gcm`;\n    if (!supported(algorithm)) {\n        throw new JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    try {\n        const decipher = createDecipheriv(algorithm, cek, iv, { authTagLength: 16 });\n        decipher.setAuthTag(tag);\n        if (aad.byteLength) {\n            decipher.setAAD(aad, { plaintextLength: ciphertext.length });\n        }\n        const plaintext = decipher.update(ciphertext);\n        decipher.final();\n        return plaintext;\n    }\n    catch {\n        throw new JWEDecryptionFailed();\n    }\n}\nconst decrypt = (enc, cek, ciphertext, iv, tag, aad) => {\n    let key;\n    if (isCryptoKey(cek)) {\n        checkEncCryptoKey(cek, enc, 'decrypt');\n        key = KeyObject.from(cek);\n    }\n    else if (cek instanceof Uint8Array || isKeyObject(cek)) {\n        key = cek;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(cek, ...types, 'Uint8Array'));\n    }\n    if (!iv) {\n        throw new JWEInvalid('JWE Initialization Vector missing');\n    }\n    if (!tag) {\n        throw new JWEInvalid('JWE Authentication Tag missing');\n    }\n    checkCekLength(enc, key);\n    checkIvLength(enc, iv);\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return cbcDecrypt(enc, key, ciphertext, iv, tag, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            return gcmDecrypt(enc, key, ciphertext, iv, tag, aad);\n        default:\n            throw new JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\nexport default decrypt;\n", "import { JWEInvalid } from '../util/errors.js';\nimport { bitLength } from './iv.js';\nconst checkIvLength = (enc, iv) => {\n    if (iv.length << 3 !== bitLength(enc)) {\n        throw new JWEInvalid('Invalid Initialization Vector length');\n    }\n};\nexport default checkIvLength;\n", "export class JOSEError extends Error {\n    static code = 'ERR_JOSE_GENERIC';\n    code = 'ERR_JOSE_GENERIC';\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nexport class JWTClaimValidationFailed extends JOSEError {\n    static code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JWTExpired extends JOSEError {\n    static code = 'ERR_JWT_EXPIRED';\n    code = 'ERR_JWT_EXPIRED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JOSEAlgNotAllowed extends J<PERSON><PERSON><PERSON>r {\n    static code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n}\nexport class JOSENotSupported extends JOSEError {\n    static code = 'ERR_JOSE_NOT_SUPPORTED';\n    code = 'ERR_JOSE_NOT_SUPPORTED';\n}\nexport class JWEDecryptionFailed extends JOSEError {\n    static code = 'ERR_JWE_DECRYPTION_FAILED';\n    code = 'ERR_JWE_DECRYPTION_FAILED';\n    constructor(message = 'decryption operation failed', options) {\n        super(message, options);\n    }\n}\nexport class JWEInvalid extends JOSEError {\n    static code = 'ERR_JWE_INVALID';\n    code = 'ERR_JWE_INVALID';\n}\nexport class JWSInvalid extends JOSEError {\n    static code = 'ERR_JWS_INVALID';\n    code = 'ERR_JWS_INVALID';\n}\nexport class JWTInvalid extends JOSEError {\n    static code = 'ERR_JWT_INVALID';\n    code = 'ERR_JWT_INVALID';\n}\nexport class JWKInvalid extends JOSEError {\n    static code = 'ERR_JWK_INVALID';\n    code = 'ERR_JWK_INVALID';\n}\nexport class JWKSInvalid extends JOSEError {\n    static code = 'ERR_JWKS_INVALID';\n    code = 'ERR_JWKS_INVALID';\n}\nexport class JWKSNoMatchingKey extends JOSEError {\n    static code = 'ERR_JWKS_NO_MATCHING_KEY';\n    code = 'ERR_JWKS_NO_MATCHING_KEY';\n    constructor(message = 'no applicable key found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSMultipleMatchingKeys extends JOSEError {\n    [Symbol.asyncIterator];\n    static code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    constructor(message = 'multiple matching keys found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSTimeout extends JOSEError {\n    static code = 'ERR_JWKS_TIMEOUT';\n    code = 'ERR_JWKS_TIMEOUT';\n    constructor(message = 'request timed out', options) {\n        super(message, options);\n    }\n}\nexport class JWSSignatureVerificationFailed extends JOSEError {\n    static code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    constructor(message = 'signature verification failed', options) {\n        super(message, options);\n    }\n}\n", "import { JOSENotSupported } from '../util/errors.js';\nimport random from '../runtime/random.js';\nexport function bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A128GCMKW':\n        case 'A192GCM':\n        case 'A192GCMKW':\n        case 'A256GCM':\n        case 'A256GCMKW':\n            return 96;\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return 128;\n        default:\n            throw new JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\nexport default (alg) => random(new Uint8Array(bitLength(alg) >> 3));\n", "export { randomFillSync as default } from 'node:crypto';\n", "import { JWEInvalid, JOSENotSupported } from '../util/errors.js';\nimport isKeyObject from './is_key_object.js';\nconst checkCekLength = (enc, cek) => {\n    let expected;\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            expected = parseInt(enc.slice(-3), 10);\n            break;\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            expected = parseInt(enc.slice(1, 4), 10);\n            break;\n        default:\n            throw new JOSENotSupported(`Content Encryption Algorithm ${enc} is not supported either by JOSE or your javascript runtime`);\n    }\n    if (cek instanceof Uint8Array) {\n        const actual = cek.byteLength << 3;\n        if (actual !== expected) {\n            throw new JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n        }\n        return;\n    }\n    if (isKeyObject(cek) && cek.type === 'secret') {\n        const actual = cek.symmetricKeySize << 3;\n        if (actual !== expected) {\n            throw new JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n        }\n        return;\n    }\n    throw new TypeError('Invalid Content Encryption Key type');\n};\nexport default checkCekLength;\n", "import * as util from 'node:util';\nexport default (obj) => util.types.isKeyObject(obj);\n", "import { timingSafeEqual as impl } from 'node:crypto';\nconst timingSafeEqual = impl;\nexport default timingSafeEqual;\n", "import { createHmac } from 'node:crypto';\nimport { concat, uint64be } from '../lib/buffer_utils.js';\nexport default function cbcTag(aad, iv, ciphertext, macSize, macKey, keySize) {\n    const macData = concat(aad, iv, ciphertext, uint64be(aad.length << 3));\n    const hmac = createHmac(`sha${macSize}`, macKey);\n    hmac.update(macData);\n    return hmac.digest().slice(0, keySize >> 3);\n}\n", "import * as crypto from 'node:crypto';\nimport * as util from 'node:util';\nconst webcrypto = crypto.webcrypto;\nexport default webcrypto;\nexport const isCryptoKey = (key) => util.types.isCryptoKey(key);\n", "function unusable(name, prop = 'algorithm.name') {\n    return new TypeError(`CryptoKey does not support this operation, its ${prop} must be ${name}`);\n}\nfunction isAlgorithm(algorithm, name) {\n    return algorithm.name === name;\n}\nfunction getHashLength(hash) {\n    return parseInt(hash.name.slice(4), 10);\n}\nfunction getNamedCurve(alg) {\n    switch (alg) {\n        case 'ES256':\n            return 'P-256';\n        case 'ES384':\n            return 'P-384';\n        case 'ES512':\n            return 'P-521';\n        default:\n            throw new Error('unreachable');\n    }\n}\nfunction checkUsage(key, usages) {\n    if (usages.length && !usages.some((expected) => key.usages.includes(expected))) {\n        let msg = 'CryptoKey does not support this operation, its usages must include ';\n        if (usages.length > 2) {\n            const last = usages.pop();\n            msg += `one of ${usages.join(', ')}, or ${last}.`;\n        }\n        else if (usages.length === 2) {\n            msg += `one of ${usages[0]} or ${usages[1]}.`;\n        }\n        else {\n            msg += `${usages[0]}.`;\n        }\n        throw new TypeError(msg);\n    }\n}\nexport function checkSigCryptoKey(key, alg, ...usages) {\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512': {\n            if (!isAlgorithm(key.algorithm, 'HMAC'))\n                throw unusable('HMAC');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'RS256':\n        case 'RS384':\n        case 'RS512': {\n            if (!isAlgorithm(key.algorithm, 'RSASSA-PKCS1-v1_5'))\n                throw unusable('RSASSA-PKCS1-v1_5');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'PS256':\n        case 'PS384':\n        case 'PS512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-PSS'))\n                throw unusable('RSA-PSS');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'EdDSA': {\n            if (key.algorithm.name !== 'Ed25519' && key.algorithm.name !== 'Ed448') {\n                throw unusable('Ed25519 or Ed448');\n            }\n            break;\n        }\n        case 'Ed25519': {\n            if (!isAlgorithm(key.algorithm, 'Ed25519'))\n                throw unusable('Ed25519');\n            break;\n        }\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            if (!isAlgorithm(key.algorithm, 'ECDSA'))\n                throw unusable('ECDSA');\n            const expected = getNamedCurve(alg);\n            const actual = key.algorithm.namedCurve;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.namedCurve');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usages);\n}\nexport function checkEncCryptoKey(key, alg, ...usages) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM': {\n            if (!isAlgorithm(key.algorithm, 'AES-GCM'))\n                throw unusable('AES-GCM');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (!isAlgorithm(key.algorithm, 'AES-KW'))\n                throw unusable('AES-KW');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'ECDH': {\n            switch (key.algorithm.name) {\n                case 'ECDH':\n                case 'X25519':\n                case 'X448':\n                    break;\n                default:\n                    throw unusable('ECDH, X25519, or X448');\n            }\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW':\n            if (!isAlgorithm(key.algorithm, 'PBKDF2'))\n                throw unusable('PBKDF2');\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-OAEP'))\n                throw unusable('RSA-OAEP');\n            const expected = parseInt(alg.slice(9), 10) || 1;\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usages);\n}\n", "function message(msg, actual, ...types) {\n    types = types.filter(<PERSON><PERSON><PERSON>);\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor?.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\nexport default (actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n};\nexport function withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\n", "import { getCiphers } from 'node:crypto';\nlet ciphers;\nexport default (algorithm) => {\n    ciphers ||= new Set(getCiphers());\n    return ciphers.has(algorithm);\n};\n", "import webcrypto, { isCryptoKey } from './webcrypto.js';\nimport isKeyObject from './is_key_object.js';\nexport default (key) => isKeyObject(key) || isCryptoKey(key);\nconst types = ['KeyObject'];\nif (globalThis.CryptoKey || webcrypto?.CryptoKey) {\n    types.push('CryptoKey');\n}\nexport { types };\n", "const isDisjoint = (...headers) => {\n    const sources = headers.filter(Boolean);\n    if (sources.length === 0 || sources.length === 1) {\n        return true;\n    }\n    let acc;\n    for (const header of sources) {\n        const parameters = Object.keys(header);\n        if (!acc || acc.size === 0) {\n            acc = new Set(parameters);\n            continue;\n        }\n        for (const parameter of parameters) {\n            if (acc.has(parameter)) {\n                return false;\n            }\n            acc.add(parameter);\n        }\n    }\n    return true;\n};\nexport default isDisjoint;\n", "function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nexport default function isObject(input) {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n}\n", "import { unwrap as aesKw } from '../runtime/aeskw.js';\nimport * as ECDH from '../runtime/ecdhes.js';\nimport { decrypt as pbes2Kw } from '../runtime/pbes2kw.js';\nimport { decrypt as rsaEs } from '../runtime/rsaes.js';\nimport { decode as base64url } from '../runtime/base64url.js';\nimport normalize from '../runtime/normalize_key.js';\nimport { JOSENotSupported, JWEInvalid } from '../util/errors.js';\nimport { bitLength as cekLength } from '../lib/cek.js';\nimport { importJWK } from '../key/import.js';\nimport checkKeyType from './check_key_type.js';\nimport isObject from './is_object.js';\nimport { unwrap as aesGcmKw } from './aesgcmkw.js';\nasync function decryptKeyManagement(alg, key, encrypted<PERSON>ey, jose<PERSON><PERSON>er, options) {\n    checkKeyType(alg, key, 'decrypt');\n    key = (await normalize.normalizePrivateKey?.(key, alg)) || key;\n    switch (alg) {\n        case 'dir': {\n            if (encryptedKey !== undefined)\n                throw new JWEInvalid('Encountered unexpected JWE Encrypted Key');\n            return key;\n        }\n        case 'ECDH-ES':\n            if (encryptedKey !== undefined)\n                throw new JWEInvalid('Encountered unexpected JWE Encrypted Key');\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!isObject(joseHeader.epk))\n                throw new JWEInvalid(`JOSE Header \"epk\" (Ephemeral Public Key) missing or invalid`);\n            if (!ECDH.ecdhAllowed(key))\n                throw new JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            const epk = await importJWK(joseHeader.epk, alg);\n            let partyUInfo;\n            let partyVInfo;\n            if (joseHeader.apu !== undefined) {\n                if (typeof joseHeader.apu !== 'string')\n                    throw new JWEInvalid(`JOSE Header \"apu\" (Agreement PartyUInfo) invalid`);\n                try {\n                    partyUInfo = base64url(joseHeader.apu);\n                }\n                catch {\n                    throw new JWEInvalid('Failed to base64url decode the apu');\n                }\n            }\n            if (joseHeader.apv !== undefined) {\n                if (typeof joseHeader.apv !== 'string')\n                    throw new JWEInvalid(`JOSE Header \"apv\" (Agreement PartyVInfo) invalid`);\n                try {\n                    partyVInfo = base64url(joseHeader.apv);\n                }\n                catch {\n                    throw new JWEInvalid('Failed to base64url decode the apv');\n                }\n            }\n            const sharedSecret = await ECDH.deriveKey(epk, key, alg === 'ECDH-ES' ? joseHeader.enc : alg, alg === 'ECDH-ES' ? cekLength(joseHeader.enc) : parseInt(alg.slice(-5, -2), 10), partyUInfo, partyVInfo);\n            if (alg === 'ECDH-ES')\n                return sharedSecret;\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            return aesKw(alg.slice(-6), sharedSecret, encryptedKey);\n        }\n        case 'RSA1_5':\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            return rsaEs(alg, key, encryptedKey);\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.p2c !== 'number')\n                throw new JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) missing or invalid`);\n            const p2cLimit = options?.maxPBES2Count || 10_000;\n            if (joseHeader.p2c > p2cLimit)\n                throw new JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) out is of acceptable bounds`);\n            if (typeof joseHeader.p2s !== 'string')\n                throw new JWEInvalid(`JOSE Header \"p2s\" (PBES2 Salt) missing or invalid`);\n            let p2s;\n            try {\n                p2s = base64url(joseHeader.p2s);\n            }\n            catch {\n                throw new JWEInvalid('Failed to base64url decode the p2s');\n            }\n            return pbes2Kw(alg, key, encryptedKey, joseHeader.p2c, p2s);\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            return aesKw(alg, key, encryptedKey);\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.iv !== 'string')\n                throw new JWEInvalid(`JOSE Header \"iv\" (Initialization Vector) missing or invalid`);\n            if (typeof joseHeader.tag !== 'string')\n                throw new JWEInvalid(`JOSE Header \"tag\" (Authentication Tag) missing or invalid`);\n            let iv;\n            try {\n                iv = base64url(joseHeader.iv);\n            }\n            catch {\n                throw new JWEInvalid('Failed to base64url decode the iv');\n            }\n            let tag;\n            try {\n                tag = base64url(joseHeader.tag);\n            }\n            catch {\n                throw new JWEInvalid('Failed to base64url decode the tag');\n            }\n            return aesGcmKw(alg, key, encryptedKey, iv, tag);\n        }\n        default: {\n            throw new JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n}\nexport default decryptKeyManagement;\n", "import { <PERSON><PERSON><PERSON> } from 'node:buffer';\nimport { KeyObject, createDecipher<PERSON>, createCipher<PERSON>, createS<PERSON>ret<PERSON><PERSON> } from 'node:crypto';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { concat } from '../lib/buffer_utils.js';\nimport { isCrypto<PERSON>ey } from './webcrypto.js';\nimport { checkEncCryptoKey } from '../lib/crypto_key.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport supported from './ciphers.js';\nimport { types } from './is_key_like.js';\nfunction checkKeySize(key, alg) {\n    if (key.symmetricKeySize << 3 !== parseInt(alg.slice(1, 4), 10)) {\n        throw new TypeError(`Invalid key size for alg: ${alg}`);\n    }\n}\nfunction ensureKeyObject(key, alg, usage) {\n    if (isKeyObject(key)) {\n        return key;\n    }\n    if (key instanceof Uint8Array) {\n        return createS<PERSON>ret<PERSON><PERSON>(key);\n    }\n    if (isCryptoKey(key)) {\n        checkEncCryptoKey(key, alg, usage);\n        return KeyObject.from(key);\n    }\n    throw new TypeError(invalidKeyInput(key, ...types, 'Uint8Array'));\n}\nexport const wrap = (alg, key, cek) => {\n    const size = parseInt(alg.slice(1, 4), 10);\n    const algorithm = `aes${size}-wrap`;\n    if (!supported(algorithm)) {\n        throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    const keyObject = ensureKeyObject(key, alg, 'wrapKey');\n    checkKeySize(keyObject, alg);\n    const cipher = createCipheriv(algorithm, keyObject, Buffer.alloc(8, 0xa6));\n    return concat(cipher.update(cek), cipher.final());\n};\nexport const unwrap = (alg, key, encryptedKey) => {\n    const size = parseInt(alg.slice(1, 4), 10);\n    const algorithm = `aes${size}-wrap`;\n    if (!supported(algorithm)) {\n        throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    const keyObject = ensureKeyObject(key, alg, 'unwrapKey');\n    checkKeySize(keyObject, alg);\n    const cipher = createDecipheriv(algorithm, keyObject, Buffer.alloc(8, 0xa6));\n    return concat(cipher.update(encryptedKey), cipher.final());\n};\n", "import { di<PERSON><PERSON><PERSON><PERSON><PERSON>, generate<PERSON>ey<PERSON>air as generateKey<PERSON>airCb, KeyObject } from 'node:crypto';\nimport { promisify } from 'node:util';\nimport getNamedCurve from './get_named_curve.js';\nimport { encoder, concat, uint32be, lengthAndInput, concatKdf } from '../lib/buffer_utils.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport { checkEncCryptoKey } from '../lib/crypto_key.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nconst generateKeyPair = promisify(generateKeyPairCb);\nexport async function deriveKey(publicKee, privateKee, algorithm, keyLength, apu = new Uint8Array(0), apv = new Uint8Array(0)) {\n    let publicKey;\n    if (isCryptoKey(publicKee)) {\n        checkEncCryptoKey(publicKee, 'ECDH');\n        publicKey = KeyObject.from(publicKee);\n    }\n    else if (isKeyObject(publicKee)) {\n        publicKey = publicKee;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(publicKee, ...types));\n    }\n    let privateKey;\n    if (isCryptoKey(privateKee)) {\n        checkEncCryptoKey(privateKee, 'ECDH', 'deriveBits');\n        privateKey = KeyObject.from(privateKee);\n    }\n    else if (isKeyObject(privateKee)) {\n        privateKey = privateKee;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(privateKee, ...types));\n    }\n    const value = concat(lengthAndInput(encoder.encode(algorithm)), lengthAndInput(apu), lengthAndInput(apv), uint32be(keyLength));\n    const sharedSecret = diffieHellman({ privateKey, publicKey });\n    return concatKdf(sharedSecret, keyLength, value);\n}\nexport async function generateEpk(kee) {\n    let key;\n    if (isCryptoKey(kee)) {\n        key = KeyObject.from(kee);\n    }\n    else if (isKeyObject(kee)) {\n        key = kee;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(kee, ...types));\n    }\n    switch (key.asymmetricKeyType) {\n        case 'x25519':\n            return generateKeyPair('x25519');\n        case 'x448': {\n            return generateKeyPair('x448');\n        }\n        case 'ec': {\n            const namedCurve = getNamedCurve(key);\n            return generateKeyPair('ec', { namedCurve });\n        }\n        default:\n            throw new JOSENotSupported('Invalid or unsupported EPK');\n    }\n}\nexport const ecdhAllowed = (key) => ['P-256', 'P-384', 'P-521', 'X25519', 'X448'].includes(getNamedCurve(key));\n", "import { KeyObject } from 'node:crypto';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nimport { isJWK } from '../lib/is_jwk.js';\nexport const weakMap = new WeakMap();\nconst namedCurveToJOSE = (namedCurve) => {\n    switch (namedCurve) {\n        case 'prime256v1':\n            return 'P-256';\n        case 'secp384r1':\n            return 'P-384';\n        case 'secp521r1':\n            return 'P-521';\n        case 'secp256k1':\n            return 'secp256k1';\n        default:\n            throw new JOSENotSupported('Unsupported key curve for this operation');\n    }\n};\nconst getNamedCurve = (kee, raw) => {\n    let key;\n    if (isCryptoKey(kee)) {\n        key = KeyObject.from(kee);\n    }\n    else if (isKeyObject(kee)) {\n        key = kee;\n    }\n    else if (isJWK(kee)) {\n        return kee.crv;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(kee, ...types));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError('only \"private\" or \"public\" type keys can be used for this operation');\n    }\n    switch (key.asymmetricKeyType) {\n        case 'ed25519':\n        case 'ed448':\n            return `Ed${key.asymmetricKeyType.slice(2)}`;\n        case 'x25519':\n        case 'x448':\n            return `X${key.asymmetricKeyType.slice(1)}`;\n        case 'ec': {\n            const namedCurve = key.asymmetricKeyDetails.namedCurve;\n            if (raw) {\n                return namedCurve;\n            }\n            return namedCurveToJOSE(namedCurve);\n        }\n        default:\n            throw new TypeError('Invalid asymmetric key type for this operation');\n    }\n};\nexport default getNamedCurve;\n", "import isObject from './is_object.js';\nexport function isJWK(key) {\n    return isObject(key) && typeof key.kty === 'string';\n}\nexport function isPrivateJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'string';\n}\nexport function isPublicJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'undefined';\n}\nexport function isSecretJWK(key) {\n    return isJWK(key) && key.kty === 'oct' && typeof key.k === 'string';\n}\n", "import { promisify } from 'node:util';\nimport { KeyObject, pbkdf2 as pbkdf2cb } from 'node:crypto';\nimport random from './random.js';\nimport { p2s as concatSalt } from '../lib/buffer_utils.js';\nimport { encode as base64url } from './base64url.js';\nimport { wrap, unwrap } from './aeskw.js';\nimport checkP2s from '../lib/check_p2s.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport { checkEncCryptoKey } from '../lib/crypto_key.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nconst pbkdf2 = promisify(pbkdf2cb);\nfunction getPassword(key, alg) {\n    if (isKeyObject(key)) {\n        return key.export();\n    }\n    if (key instanceof Uint8Array) {\n        return key;\n    }\n    if (isCryptoKey(key)) {\n        checkEncCryptoKey(key, alg, 'deriveBits', 'deriveKey');\n        return KeyObject.from(key).export();\n    }\n    throw new TypeError(invalidKeyInput(key, ...types, 'Uint8Array'));\n}\nexport const encrypt = async (alg, key, cek, p2c = 2048, p2s = random(new Uint8Array(16))) => {\n    checkP2s(p2s);\n    const salt = concatSalt(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10) >> 3;\n    const password = getPassword(key, alg);\n    const derivedKey = await pbkdf2(password, salt, p2c, keylen, `sha${alg.slice(8, 11)}`);\n    const encryptedKey = await wrap(alg.slice(-6), derivedKey, cek);\n    return { encryptedKey, p2c, p2s: base64url(p2s) };\n};\nexport const decrypt = async (alg, key, encryptedKey, p2c, p2s) => {\n    checkP2s(p2s);\n    const salt = concatSalt(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10) >> 3;\n    const password = getPassword(key, alg);\n    const derivedKey = await pbkdf2(password, salt, p2c, keylen, `sha${alg.slice(8, 11)}`);\n    return unwrap(alg.slice(-6), derivedKey, encryptedKey);\n};\n", "import { JWEInvalid } from '../util/errors.js';\nexport default function checkP2s(p2s) {\n    if (!(p2s instanceof Uint8Array) || p2s.length < 8) {\n        throw new JWEInvalid('PBES2 Salt Input must be 8 or more octets');\n    }\n}\n", "import { KeyObject, publicEncrypt, constants, privateDecrypt } from 'node:crypto';\nimport { deprecate } from 'node:util';\nimport checkKeyLength from './check_key_length.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport { checkEncCryptoKey } from '../lib/crypto_key.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nconst checkKey = (key, alg) => {\n    if (key.asymmetricKeyType !== 'rsa') {\n        throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa');\n    }\n    checkKeyLength(key, alg);\n};\nconst RSA1_5 = deprecate(() => constants.RSA_PKCS1_PADDING, 'The RSA1_5 \"alg\" (JWE Algorithm) is deprecated and will be removed in the next major revision.');\nconst resolvePadding = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n            return constants.RSA_PKCS1_OAEP_PADDING;\n        case 'RSA1_5':\n            return RSA1_5();\n        default:\n            return undefined;\n    }\n};\nconst resolveOaepHash = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n            return 'sha1';\n        case 'RSA-OAEP-256':\n            return 'sha256';\n        case 'RSA-OAEP-384':\n            return 'sha384';\n        case 'RSA-OAEP-512':\n            return 'sha512';\n        default:\n            return undefined;\n    }\n};\nfunction ensureKeyObject(key, alg, ...usages) {\n    if (isKeyObject(key)) {\n        return key;\n    }\n    if (isCryptoKey(key)) {\n        checkEncCryptoKey(key, alg, ...usages);\n        return KeyObject.from(key);\n    }\n    throw new TypeError(invalidKeyInput(key, ...types));\n}\nexport const encrypt = (alg, key, cek) => {\n    const padding = resolvePadding(alg);\n    const oaepHash = resolveOaepHash(alg);\n    const keyObject = ensureKeyObject(key, alg, 'wrapKey', 'encrypt');\n    checkKey(keyObject, alg);\n    return publicEncrypt({ key: keyObject, oaepHash, padding }, cek);\n};\nexport const decrypt = (alg, key, encryptedKey) => {\n    const padding = resolvePadding(alg);\n    const oaepHash = resolveOaepHash(alg);\n    const keyObject = ensureKeyObject(key, alg, 'unwrapKey', 'decrypt');\n    checkKey(keyObject, alg);\n    return privateDecrypt({ key: keyObject, oaepHash, padding }, encryptedKey);\n};\n", "import { KeyObject } from 'node:crypto';\nexport default (key, alg) => {\n    let modulusLength;\n    try {\n        if (key instanceof KeyObject) {\n            modulusLength = key.asymmetricKeyDetails?.modulusLength;\n        }\n        else {\n            modulusLength = Buffer.from(key.n, 'base64url').byteLength << 3;\n        }\n    }\n    catch { }\n    if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n        throw new TypeError(`${alg} requires key modulusLength to be 2048 bits or larger`);\n    }\n};\n", "export default {};\n", "import { JOSENotSupported } from '../util/errors.js';\nimport random from '../runtime/random.js';\nexport function bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n            return 128;\n        case 'A192GCM':\n            return 192;\n        case 'A256GCM':\n        case 'A128CBC-HS256':\n            return 256;\n        case 'A192CBC-HS384':\n            return 384;\n        case 'A256CBC-HS512':\n            return 512;\n        default:\n            throw new JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\nexport default (alg) => random(new Uint8Array(bitLength(alg) >> 3));\n", "import { decode as decodeBase64URL } from '../runtime/base64url.js';\nimport { fromSPKI, fromPKCS8, fromX509 } from '../runtime/asn1.js';\nimport asKeyObject from '../runtime/jwk_to_key.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport isObject from '../lib/is_object.js';\nexport async function importSPKI(spki, alg, options) {\n    if (typeof spki !== 'string' || spki.indexOf('-----BEGIN PUBLIC KEY-----') !== 0) {\n        throw new TypeError('\"spki\" must be SPKI formatted string');\n    }\n    return fromSPKI(spki, alg, options);\n}\nexport async function importX509(x509, alg, options) {\n    if (typeof x509 !== 'string' || x509.indexOf('-----BEGIN CERTIFICATE-----') !== 0) {\n        throw new TypeError('\"x509\" must be X.509 formatted string');\n    }\n    return fromX509(x509, alg, options);\n}\nexport async function importPKCS8(pkcs8, alg, options) {\n    if (typeof pkcs8 !== 'string' || pkcs8.indexOf('-----BEGIN PRIVATE KEY-----') !== 0) {\n        throw new TypeError('\"pkcs8\" must be PKCS#8 formatted string');\n    }\n    return fromPKCS8(pkcs8, alg, options);\n}\nexport async function importJWK(jwk, alg) {\n    if (!isObject(jwk)) {\n        throw new TypeError('JWK must be an object');\n    }\n    alg ||= jwk.alg;\n    switch (jwk.kty) {\n        case 'oct':\n            if (typeof jwk.k !== 'string' || !jwk.k) {\n                throw new TypeError('missing \"k\" (Key Value) Parameter value');\n            }\n            return decodeBase64URL(jwk.k);\n        case 'RSA':\n            if ('oth' in jwk && jwk.oth !== undefined) {\n                throw new JOSENotSupported('RSA JWK \"oth\" (Other Primes Info) Parameter value is not supported');\n            }\n        case 'EC':\n        case 'OKP':\n            return asKeyObject({ ...jwk, alg });\n        default:\n            throw new JOSENotSupported('Unsupported \"kty\" (Key Type) Parameter value');\n    }\n}\n", "import { createPrivate<PERSON><PERSON>, create<PERSON><PERSON><PERSON><PERSON><PERSON>, KeyObject } from 'node:crypto';\nimport { <PERSON>uffer } from 'node:buffer';\nimport { isCryptoKey } from './webcrypto.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nconst genericExport = (keyType, keyFormat, key) => {\n    let keyObject;\n    if (isCryptoKey(key)) {\n        if (!key.extractable) {\n            throw new TypeError('CryptoKey is not extractable');\n        }\n        keyObject = KeyObject.from(key);\n    }\n    else if (isKeyObject(key)) {\n        keyObject = key;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(key, ...types));\n    }\n    if (keyObject.type !== keyType) {\n        throw new TypeError(`key is not a ${keyType} key`);\n    }\n    return keyObject.export({ format: 'pem', type: keyFormat });\n};\nexport const toSPKI = (key) => {\n    return genericExport('public', 'spki', key);\n};\nexport const toPKCS8 = (key) => {\n    return genericExport('private', 'pkcs8', key);\n};\nexport const fromPKCS8 = (pem) => createPrivateKey({\n    key: Buffer.from(pem.replace(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\\s)/g, ''), 'base64'),\n    type: 'pkcs8',\n    format: 'der',\n});\nexport const fromSPKI = (pem) => createPublicKey({\n    key: Buffer.from(pem.replace(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\\s)/g, ''), 'base64'),\n    type: 'spki',\n    format: 'der',\n});\nexport const fromX509 = (pem) => createPublicKey({\n    key: pem,\n    type: 'spki',\n    format: 'pem',\n});\n", "import { createPrivate<PERSON><PERSON>, createP<PERSON><PERSON><PERSON><PERSON> } from 'node:crypto';\nconst parse = (key) => {\n    if (key.d) {\n        return createPrivateKey({ format: 'jwk', key });\n    }\n    return createPublicKey({ format: 'jwk', key });\n};\nexport default parse;\n", "import { withAlg as invalidKeyInput } from './invalid_key_input.js';\nimport isKeyLike, { types } from '../runtime/is_key_like.js';\nimport * as jwk from './is_jwk.js';\nconst tag = (key) => key?.[Symbol.toStringTag];\nconst jwkMatchesOp = (alg, key, usage) => {\n    if (key.use !== undefined && key.use !== 'sig') {\n        throw new TypeError('Invalid key for this operation, when present its use must be sig');\n    }\n    if (key.key_ops !== undefined && key.key_ops.includes?.(usage) !== true) {\n        throw new TypeError(`Invalid key for this operation, when present its key_ops must include ${usage}`);\n    }\n    if (key.alg !== undefined && key.alg !== alg) {\n        throw new TypeError(`Invalid key for this operation, when present its alg must be ${alg}`);\n    }\n    return true;\n};\nconst symmetricTypeCheck = (alg, key, usage, allowJwk) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (allowJwk && jwk.isJWK(key)) {\n        if (jwk.isSecretJWK(key) && jwkMatchesOp(alg, key, usage))\n            return;\n        throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK \"kty\" (Key Type) equal to \"oct\" and the JWK \"k\" (Key Value) present`);\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, ...types, 'Uint8Array', allowJwk ? 'JSON Web Key' : null));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage, allowJwk) => {\n    if (allowJwk && jwk.isJWK(key)) {\n        switch (usage) {\n            case 'sign':\n                if (jwk.isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a private JWK`);\n            case 'verify':\n                if (jwk.isPublicJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a public JWK`);\n        }\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, ...types, allowJwk ? 'JSON Web Key' : null));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (usage === 'sign' && key.type === 'public') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type \"private\"`);\n    }\n    if (usage === 'decrypt' && key.type === 'public') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type \"private\"`);\n    }\n    if (key.algorithm && usage === 'verify' && key.type === 'private') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type \"public\"`);\n    }\n    if (key.algorithm && usage === 'encrypt' && key.type === 'private') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type \"public\"`);\n    }\n};\nfunction checkKeyType(allowJwk, alg, key, usage) {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A\\d{3}(?:GCM)?KW$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key, usage, allowJwk);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage, allowJwk);\n    }\n}\nexport default checkKeyType.bind(undefined, false);\nexport const checkKeyTypeWithJwk = checkKeyType.bind(undefined, true);\n", "import encrypt from '../runtime/encrypt.js';\nimport decrypt from '../runtime/decrypt.js';\nimport { encode as base64url } from '../runtime/base64url.js';\nexport async function wrap(alg, key, cek, iv) {\n    const jweAlgorithm = alg.slice(0, 7);\n    const wrapped = await encrypt(jweAlgorithm, cek, key, iv, new Uint8Array(0));\n    return {\n        encryptedKey: wrapped.ciphertext,\n        iv: base64url(wrapped.iv),\n        tag: base64url(wrapped.tag),\n    };\n}\nexport async function unwrap(alg, key, encryptedKey, iv, tag) {\n    const jweAlgorithm = alg.slice(0, 7);\n    return decrypt(jweAlgorithm, key, encryptedKey, iv, tag, new Uint8Array(0));\n}\n", "import { createCipheriv, KeyObject } from 'node:crypto';\nimport checkIvLength from '../lib/check_iv_length.js';\nimport checkCekLength from './check_cek_length.js';\nimport { concat } from '../lib/buffer_utils.js';\nimport cbcTag from './cbc_tag.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport { checkEncCryptoKey } from '../lib/crypto_key.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport generateIv from '../lib/iv.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport supported from './ciphers.js';\nimport { types } from './is_key_like.js';\nfunction cbcEncrypt(enc, plaintext, cek, iv, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    if (isKeyObject(cek)) {\n        cek = cek.export();\n    }\n    const encKey = cek.subarray(keySize >> 3);\n    const macKey = cek.subarray(0, keySize >> 3);\n    const algorithm = `aes-${keySize}-cbc`;\n    if (!supported(algorithm)) {\n        throw new JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const cipher = createCipheriv(algorithm, encKey, iv);\n    const ciphertext = concat(cipher.update(plaintext), cipher.final());\n    const macSize = parseInt(enc.slice(-3), 10);\n    const tag = cbcTag(aad, iv, ciphertext, macSize, macKey, keySize);\n    return { ciphertext, tag, iv };\n}\nfunction gcmEncrypt(enc, plaintext, cek, iv, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const algorithm = `aes-${keySize}-gcm`;\n    if (!supported(algorithm)) {\n        throw new JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const cipher = createCipheriv(algorithm, cek, iv, { authTagLength: 16 });\n    if (aad.byteLength) {\n        cipher.setAAD(aad, { plaintextLength: plaintext.length });\n    }\n    const ciphertext = cipher.update(plaintext);\n    cipher.final();\n    const tag = cipher.getAuthTag();\n    return { ciphertext, tag, iv };\n}\nconst encrypt = (enc, plaintext, cek, iv, aad) => {\n    let key;\n    if (isCryptoKey(cek)) {\n        checkEncCryptoKey(cek, enc, 'encrypt');\n        key = KeyObject.from(cek);\n    }\n    else if (cek instanceof Uint8Array || isKeyObject(cek)) {\n        key = cek;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(cek, ...types, 'Uint8Array'));\n    }\n    checkCekLength(enc, key);\n    if (iv) {\n        checkIvLength(enc, iv);\n    }\n    else {\n        iv = generateIv(enc);\n    }\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return cbcEncrypt(enc, plaintext, key, iv, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            return gcmEncrypt(enc, plaintext, key, iv, aad);\n        default:\n            throw new JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\nexport default encrypt;\n", "import { JOSENotSupported } from '../util/errors.js';\nfunction validateCrit(Err, recognizedDefault, recognizedOption, protectedHeader, joseHeader) {\n    if (joseHeader.crit !== undefined && protectedHeader?.crit === undefined) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be integrity protected');\n    }\n    if (!protectedHeader || protectedHeader.crit === undefined) {\n        return new Set();\n    }\n    if (!Array.isArray(protectedHeader.crit) ||\n        protectedHeader.crit.length === 0 ||\n        protectedHeader.crit.some((input) => typeof input !== 'string' || input.length === 0)) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be an array of non-empty strings when present');\n    }\n    let recognized;\n    if (recognizedOption !== undefined) {\n        recognized = new Map([...Object.entries(recognizedOption), ...recognizedDefault.entries()]);\n    }\n    else {\n        recognized = recognizedDefault;\n    }\n    for (const parameter of protectedHeader.crit) {\n        if (!recognized.has(parameter)) {\n            throw new JOSENotSupported(`Extension Header Parameter \"${parameter}\" is not recognized`);\n        }\n        if (joseHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" is missing`);\n        }\n        if (recognized.get(parameter) && protectedHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" MUST be integrity protected`);\n        }\n    }\n    return new Set(protectedHeader.crit);\n}\nexport default validateCrit;\n", "const validateAlgorithms = (option, algorithms) => {\n    if (algorithms !== undefined &&\n        (!Array.isArray(algorithms) || algorithms.some((s) => typeof s !== 'string'))) {\n        throw new TypeError(`\"${option}\" option must be an array of strings`);\n    }\n    if (!algorithms) {\n        return undefined;\n    }\n    return new Set(algorithms);\n};\nexport default validateAlgorithms;\n", "import { flattenedDecrypt } from '../flattened/decrypt.js';\nimport { JWEDecryptionFailed, JWEInvalid } from '../../util/errors.js';\nimport isObject from '../../lib/is_object.js';\nexport async function generalDecrypt(jwe, key, options) {\n    if (!isObject(jwe)) {\n        throw new JWEInvalid('General JWE must be an object');\n    }\n    if (!Array.isArray(jwe.recipients) || !jwe.recipients.every(isObject)) {\n        throw new JWEInvalid('JWE Recipients missing or incorrect type');\n    }\n    if (!jwe.recipients.length) {\n        throw new JWEInvalid('JWE Recipients has no members');\n    }\n    for (const recipient of jwe.recipients) {\n        try {\n            return await flattenedDecrypt({\n                aad: jwe.aad,\n                ciphertext: jwe.ciphertext,\n                encrypted_key: recipient.encrypted_key,\n                header: recipient.header,\n                iv: jwe.iv,\n                protected: jwe.protected,\n                tag: jwe.tag,\n                unprotected: jwe.unprotected,\n            }, key, options);\n        }\n        catch {\n        }\n    }\n    throw new JWEDecryptionFailed();\n}\n", "import { FlattenedEncrypt } from '../flattened/encrypt.js';\nimport { unprotected } from '../../lib/private_symbols.js';\nimport { JOSENotSupported, JWEInvalid } from '../../util/errors.js';\nimport generateCek from '../../lib/cek.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport encryptKeyManagement from '../../lib/encrypt_key_management.js';\nimport { encode as base64url } from '../../runtime/base64url.js';\nimport validateCrit from '../../lib/validate_crit.js';\nclass IndividualRecipient {\n    parent;\n    unprotectedHeader;\n    key;\n    options;\n    constructor(enc, key, options) {\n        this.parent = enc;\n        this.key = key;\n        this.options = options;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this.unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this.unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    addRecipient(...args) {\n        return this.parent.addRecipient(...args);\n    }\n    encrypt(...args) {\n        return this.parent.encrypt(...args);\n    }\n    done() {\n        return this.parent;\n    }\n}\nexport class GeneralEncrypt {\n    _plaintext;\n    _recipients = [];\n    _protectedHeader;\n    _unprotectedHeader;\n    _aad;\n    constructor(plaintext) {\n        this._plaintext = plaintext;\n    }\n    addRecipient(key, options) {\n        const recipient = new IndividualRecipient(this, key, { crit: options?.crit });\n        this._recipients.push(recipient);\n        return recipient;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setSharedUnprotectedHeader(sharedUnprotectedHeader) {\n        if (this._unprotectedHeader) {\n            throw new TypeError('setSharedUnprotectedHeader can only be called once');\n        }\n        this._unprotectedHeader = sharedUnprotectedHeader;\n        return this;\n    }\n    setAdditionalAuthenticatedData(aad) {\n        this._aad = aad;\n        return this;\n    }\n    async encrypt() {\n        if (!this._recipients.length) {\n            throw new JWEInvalid('at least one recipient must be added');\n        }\n        if (this._recipients.length === 1) {\n            const [recipient] = this._recipients;\n            const flattened = await new FlattenedEncrypt(this._plaintext)\n                .setAdditionalAuthenticatedData(this._aad)\n                .setProtectedHeader(this._protectedHeader)\n                .setSharedUnprotectedHeader(this._unprotectedHeader)\n                .setUnprotectedHeader(recipient.unprotectedHeader)\n                .encrypt(recipient.key, { ...recipient.options });\n            const jwe = {\n                ciphertext: flattened.ciphertext,\n                iv: flattened.iv,\n                recipients: [{}],\n                tag: flattened.tag,\n            };\n            if (flattened.aad)\n                jwe.aad = flattened.aad;\n            if (flattened.protected)\n                jwe.protected = flattened.protected;\n            if (flattened.unprotected)\n                jwe.unprotected = flattened.unprotected;\n            if (flattened.encrypted_key)\n                jwe.recipients[0].encrypted_key = flattened.encrypted_key;\n            if (flattened.header)\n                jwe.recipients[0].header = flattened.header;\n            return jwe;\n        }\n        let enc;\n        for (let i = 0; i < this._recipients.length; i++) {\n            const recipient = this._recipients[i];\n            if (!isDisjoint(this._protectedHeader, this._unprotectedHeader, recipient.unprotectedHeader)) {\n                throw new JWEInvalid('JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint');\n            }\n            const joseHeader = {\n                ...this._protectedHeader,\n                ...this._unprotectedHeader,\n                ...recipient.unprotectedHeader,\n            };\n            const { alg } = joseHeader;\n            if (typeof alg !== 'string' || !alg) {\n                throw new JWEInvalid('JWE \"alg\" (Algorithm) Header Parameter missing or invalid');\n            }\n            if (alg === 'dir' || alg === 'ECDH-ES') {\n                throw new JWEInvalid('\"dir\" and \"ECDH-ES\" alg may only be used with a single recipient');\n            }\n            if (typeof joseHeader.enc !== 'string' || !joseHeader.enc) {\n                throw new JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter missing or invalid');\n            }\n            if (!enc) {\n                enc = joseHeader.enc;\n            }\n            else if (enc !== joseHeader.enc) {\n                throw new JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter must be the same for all recipients');\n            }\n            validateCrit(JWEInvalid, new Map(), recipient.options.crit, this._protectedHeader, joseHeader);\n            if (joseHeader.zip !== undefined) {\n                throw new JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n            }\n        }\n        const cek = generateCek(enc);\n        const jwe = {\n            ciphertext: '',\n            iv: '',\n            recipients: [],\n            tag: '',\n        };\n        for (let i = 0; i < this._recipients.length; i++) {\n            const recipient = this._recipients[i];\n            const target = {};\n            jwe.recipients.push(target);\n            const joseHeader = {\n                ...this._protectedHeader,\n                ...this._unprotectedHeader,\n                ...recipient.unprotectedHeader,\n            };\n            const p2c = joseHeader.alg.startsWith('PBES2') ? 2048 + i : undefined;\n            if (i === 0) {\n                const flattened = await new FlattenedEncrypt(this._plaintext)\n                    .setAdditionalAuthenticatedData(this._aad)\n                    .setContentEncryptionKey(cek)\n                    .setProtectedHeader(this._protectedHeader)\n                    .setSharedUnprotectedHeader(this._unprotectedHeader)\n                    .setUnprotectedHeader(recipient.unprotectedHeader)\n                    .setKeyManagementParameters({ p2c })\n                    .encrypt(recipient.key, {\n                    ...recipient.options,\n                    [unprotected]: true,\n                });\n                jwe.ciphertext = flattened.ciphertext;\n                jwe.iv = flattened.iv;\n                jwe.tag = flattened.tag;\n                if (flattened.aad)\n                    jwe.aad = flattened.aad;\n                if (flattened.protected)\n                    jwe.protected = flattened.protected;\n                if (flattened.unprotected)\n                    jwe.unprotected = flattened.unprotected;\n                target.encrypted_key = flattened.encrypted_key;\n                if (flattened.header)\n                    target.header = flattened.header;\n                continue;\n            }\n            const { encryptedKey, parameters } = await encryptKeyManagement(recipient.unprotectedHeader?.alg ||\n                this._protectedHeader?.alg ||\n                this._unprotectedHeader?.alg, enc, recipient.key, cek, { p2c });\n            target.encrypted_key = base64url(encryptedKey);\n            if (recipient.unprotectedHeader || parameters)\n                target.header = { ...recipient.unprotectedHeader, ...parameters };\n        }\n        return jwe;\n    }\n}\n", "import { encode as base64url } from '../../runtime/base64url.js';\nimport { unprotected } from '../../lib/private_symbols.js';\nimport encrypt from '../../runtime/encrypt.js';\nimport encryptKeyManagement from '../../lib/encrypt_key_management.js';\nimport { JOSENotSupported, JWEInvalid } from '../../util/errors.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport validateCrit from '../../lib/validate_crit.js';\nexport class FlattenedEncrypt {\n    _plaintext;\n    _protectedHeader;\n    _sharedUnprotectedHeader;\n    _unprotectedHeader;\n    _aad;\n    _cek;\n    _iv;\n    _keyManagementParameters;\n    constructor(plaintext) {\n        if (!(plaintext instanceof Uint8Array)) {\n            throw new TypeError('plaintext must be an instance of Uint8Array');\n        }\n        this._plaintext = plaintext;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this._keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this._keyManagementParameters = parameters;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setSharedUnprotectedHeader(sharedUnprotectedHeader) {\n        if (this._sharedUnprotectedHeader) {\n            throw new TypeError('setSharedUnprotectedHeader can only be called once');\n        }\n        this._sharedUnprotectedHeader = sharedUnprotectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this._unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this._unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    setAdditionalAuthenticatedData(aad) {\n        this._aad = aad;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this._cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this._cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this._iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this._iv = iv;\n        return this;\n    }\n    async encrypt(key, options) {\n        if (!this._protectedHeader && !this._unprotectedHeader && !this._sharedUnprotectedHeader) {\n            throw new JWEInvalid('either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()');\n        }\n        if (!isDisjoint(this._protectedHeader, this._unprotectedHeader, this._sharedUnprotectedHeader)) {\n            throw new JWEInvalid('JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this._protectedHeader,\n            ...this._unprotectedHeader,\n            ...this._sharedUnprotectedHeader,\n        };\n        validateCrit(JWEInvalid, new Map(), options?.crit, this._protectedHeader, joseHeader);\n        if (joseHeader.zip !== undefined) {\n            throw new JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n        }\n        const { alg, enc } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new JWEInvalid('JWE \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        if (typeof enc !== 'string' || !enc) {\n            throw new JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter missing or invalid');\n        }\n        let encryptedKey;\n        if (this._cek && (alg === 'dir' || alg === 'ECDH-ES')) {\n            throw new TypeError(`setContentEncryptionKey cannot be called with JWE \"alg\" (Algorithm) Header ${alg}`);\n        }\n        let cek;\n        {\n            let parameters;\n            ({ cek, encryptedKey, parameters } = await encryptKeyManagement(alg, enc, key, this._cek, this._keyManagementParameters));\n            if (parameters) {\n                if (options && unprotected in options) {\n                    if (!this._unprotectedHeader) {\n                        this.setUnprotectedHeader(parameters);\n                    }\n                    else {\n                        this._unprotectedHeader = { ...this._unprotectedHeader, ...parameters };\n                    }\n                }\n                else if (!this._protectedHeader) {\n                    this.setProtectedHeader(parameters);\n                }\n                else {\n                    this._protectedHeader = { ...this._protectedHeader, ...parameters };\n                }\n            }\n        }\n        let additionalData;\n        let protectedHeader;\n        let aadMember;\n        if (this._protectedHeader) {\n            protectedHeader = encoder.encode(base64url(JSON.stringify(this._protectedHeader)));\n        }\n        else {\n            protectedHeader = encoder.encode('');\n        }\n        if (this._aad) {\n            aadMember = base64url(this._aad);\n            additionalData = concat(protectedHeader, encoder.encode('.'), encoder.encode(aadMember));\n        }\n        else {\n            additionalData = protectedHeader;\n        }\n        const { ciphertext, tag, iv } = await encrypt(enc, this._plaintext, cek, this._iv, additionalData);\n        const jwe = {\n            ciphertext: base64url(ciphertext),\n        };\n        if (iv) {\n            jwe.iv = base64url(iv);\n        }\n        if (tag) {\n            jwe.tag = base64url(tag);\n        }\n        if (encryptedKey) {\n            jwe.encrypted_key = base64url(encryptedKey);\n        }\n        if (aadMember) {\n            jwe.aad = aadMember;\n        }\n        if (this._protectedHeader) {\n            jwe.protected = decoder.decode(protectedHeader);\n        }\n        if (this._sharedUnprotectedHeader) {\n            jwe.unprotected = this._sharedUnprotectedHeader;\n        }\n        if (this._unprotectedHeader) {\n            jwe.header = this._unprotectedHeader;\n        }\n        return jwe;\n    }\n}\n", "export const unprotected = Symbol();\n", "import { wrap as aesKw } from '../runtime/aeskw.js';\nimport * as ECDH from '../runtime/ecdhes.js';\nimport { encrypt as pbes2Kw } from '../runtime/pbes2kw.js';\nimport { encrypt as rsaEs } from '../runtime/rsaes.js';\nimport { encode as base64url } from '../runtime/base64url.js';\nimport normalize from '../runtime/normalize_key.js';\nimport generateCek, { bitLength as cekLength } from '../lib/cek.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { exportJWK } from '../key/export.js';\nimport checkKeyType from './check_key_type.js';\nimport { wrap as aesGcmKw } from './aesgcmkw.js';\nasync function encryptKeyManagement(alg, enc, key, providedCek, providedParameters = {}) {\n    let encryptedKey;\n    let parameters;\n    let cek;\n    checkKeyType(alg, key, 'encrypt');\n    key = (await normalize.normalizePublicKey?.(key, alg)) || key;\n    switch (alg) {\n        case 'dir': {\n            cek = key;\n            break;\n        }\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!ECDH.ecdhAllowed(key)) {\n                throw new JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            }\n            const { apu, apv } = providedParameters;\n            let { epk: ephemeralKey } = providedParameters;\n            ephemeralKey ||= (await ECDH.generateEpk(key)).privateKey;\n            const { x, y, crv, kty } = await exportJWK(ephemeralKey);\n            const sharedSecret = await ECDH.deriveKey(key, ephemeralKey, alg === 'ECDH-ES' ? enc : alg, alg === 'ECDH-ES' ? cekLength(enc) : parseInt(alg.slice(-5, -2), 10), apu, apv);\n            parameters = { epk: { x, crv, kty } };\n            if (kty === 'EC')\n                parameters.epk.y = y;\n            if (apu)\n                parameters.apu = base64url(apu);\n            if (apv)\n                parameters.apv = base64url(apv);\n            if (alg === 'ECDH-ES') {\n                cek = sharedSecret;\n                break;\n            }\n            cek = providedCek || generateCek(enc);\n            const kwAlg = alg.slice(-6);\n            encryptedKey = await aesKw(kwAlg, sharedSecret, cek);\n            break;\n        }\n        case 'RSA1_5':\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            cek = providedCek || generateCek(enc);\n            encryptedKey = await rsaEs(alg, key, cek);\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            cek = providedCek || generateCek(enc);\n            const { p2c, p2s } = providedParameters;\n            ({ encryptedKey, ...parameters } = await pbes2Kw(alg, key, cek, p2c, p2s));\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            cek = providedCek || generateCek(enc);\n            encryptedKey = await aesKw(alg, key, cek);\n            break;\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            cek = providedCek || generateCek(enc);\n            const { iv } = providedParameters;\n            ({ encryptedKey, ...parameters } = await aesGcmKw(alg, key, cek, iv));\n            break;\n        }\n        default: {\n            throw new JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n    return { cek, encryptedKey, parameters };\n}\nexport default encryptKeyManagement;\n", "import { toSPKI as exportPublic } from '../runtime/asn1.js';\nimport { toPKCS8 as exportPrivate } from '../runtime/asn1.js';\nimport keyToJWK from '../runtime/key_to_jwk.js';\nexport async function exportSPKI(key) {\n    return exportPublic(key);\n}\nexport async function exportPKCS8(key) {\n    return exportPrivate(key);\n}\nexport async function exportJWK(key) {\n    return keyToJWK(key);\n}\n", "import { KeyObject } from 'node:crypto';\nimport { encode as base64url } from './base64url.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nconst keyToJWK = (key) => {\n    let keyObject;\n    if (isCryptoKey(key)) {\n        if (!key.extractable) {\n            throw new TypeError('CryptoKey is not extractable');\n        }\n        keyObject = KeyObject.from(key);\n    }\n    else if (isKeyObject(key)) {\n        keyObject = key;\n    }\n    else if (key instanceof Uint8Array) {\n        return {\n            kty: 'oct',\n            k: base64url(key),\n        };\n    }\n    else {\n        throw new TypeError(invalidKeyInput(key, ...types, 'Uint8Array'));\n    }\n    if (keyObject.type !== 'secret' &&\n        !['rsa', 'ec', 'ed25519', 'x25519', 'ed448', 'x448'].includes(keyObject.asymmetricKeyType)) {\n        throw new JOSENotSupported('Unsupported key asymmetricKeyType');\n    }\n    return keyObject.export({ format: 'jwk' });\n};\nexport default keyToJWK;\n", "import { flattenedVerify } from '../flattened/verify.js';\nimport { JWSInvalid } from '../../util/errors.js';\nimport { decoder } from '../../lib/buffer_utils.js';\nexport async function compactVerify(jws, key, options) {\n    if (jws instanceof Uint8Array) {\n        jws = decoder.decode(jws);\n    }\n    if (typeof jws !== 'string') {\n        throw new JWSInvalid('Compact JWS must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: payload, 2: signature, length } = jws.split('.');\n    if (length !== 3) {\n        throw new JWSInvalid('Invalid Compact JWS');\n    }\n    const verified = await flattenedVerify({ payload, protected: protectedHeader, signature }, key, options);\n    const result = { payload: verified.payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\n", "import { decode as base64url } from '../../runtime/base64url.js';\nimport verify from '../../runtime/verify.js';\nimport { JOSEAlgNotAllowed, JWSInvalid, JWSSignatureVerificationFailed } from '../../util/errors.js';\nimport { concat, encoder, decoder } from '../../lib/buffer_utils.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport isObject from '../../lib/is_object.js';\nimport { checkKeyTypeWithJwk } from '../../lib/check_key_type.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport validateAlgorithms from '../../lib/validate_algorithms.js';\nimport { isJWK } from '../../lib/is_jwk.js';\nimport { importJWK } from '../../key/import.js';\nexport async function flattenedVerify(jws, key, options) {\n    if (!isObject(jws)) {\n        throw new JWSInvalid('Flattened JWS must be an object');\n    }\n    if (jws.protected === undefined && jws.header === undefined) {\n        throw new JWSInvalid('Flattened JWS must have either of the \"protected\" or \"header\" members');\n    }\n    if (jws.protected !== undefined && typeof jws.protected !== 'string') {\n        throw new JWSInvalid('JWS Protected Header incorrect type');\n    }\n    if (jws.payload === undefined) {\n        throw new JWSInvalid('JWS Payload missing');\n    }\n    if (typeof jws.signature !== 'string') {\n        throw new JWSInvalid('JWS Signature missing or incorrect type');\n    }\n    if (jws.header !== undefined && !isObject(jws.header)) {\n        throw new JWSInvalid('JWS Unprotected Header incorrect type');\n    }\n    let parsedProt = {};\n    if (jws.protected) {\n        try {\n            const protectedHeader = base64url(jws.protected);\n            parsedProt = JSON.parse(decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new JWSInvalid('JWS Protected Header is invalid');\n        }\n    }\n    if (!isDisjoint(parsedProt, jws.header)) {\n        throw new JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jws.header,\n    };\n    const extensions = validateCrit(JWSInvalid, new Map([['b64', true]]), options?.crit, parsedProt, joseHeader);\n    let b64 = true;\n    if (extensions.has('b64')) {\n        b64 = parsedProt.b64;\n        if (typeof b64 !== 'boolean') {\n            throw new JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n        }\n    }\n    const { alg } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n    }\n    const algorithms = options && validateAlgorithms('algorithms', options.algorithms);\n    if (algorithms && !algorithms.has(alg)) {\n        throw new JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (b64) {\n        if (typeof jws.payload !== 'string') {\n            throw new JWSInvalid('JWS Payload must be a string');\n        }\n    }\n    else if (typeof jws.payload !== 'string' && !(jws.payload instanceof Uint8Array)) {\n        throw new JWSInvalid('JWS Payload must be a string or an Uint8Array instance');\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jws);\n        resolvedKey = true;\n        checkKeyTypeWithJwk(alg, key, 'verify');\n        if (isJWK(key)) {\n            key = await importJWK(key, alg);\n        }\n    }\n    else {\n        checkKeyTypeWithJwk(alg, key, 'verify');\n    }\n    const data = concat(encoder.encode(jws.protected ?? ''), encoder.encode('.'), typeof jws.payload === 'string' ? encoder.encode(jws.payload) : jws.payload);\n    let signature;\n    try {\n        signature = base64url(jws.signature);\n    }\n    catch {\n        throw new JWSInvalid('Failed to base64url decode the signature');\n    }\n    const verified = await verify(alg, key, signature, data);\n    if (!verified) {\n        throw new JWSSignatureVerificationFailed();\n    }\n    let payload;\n    if (b64) {\n        try {\n            payload = base64url(jws.payload);\n        }\n        catch {\n            throw new JWSInvalid('Failed to base64url decode the payload');\n        }\n    }\n    else if (typeof jws.payload === 'string') {\n        payload = encoder.encode(jws.payload);\n    }\n    else {\n        payload = jws.payload;\n    }\n    const result = { payload };\n    if (jws.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jws.header !== undefined) {\n        result.unprotectedHeader = jws.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key };\n    }\n    return result;\n}\n", "import * as crypto from 'node:crypto';\nimport { promisify } from 'node:util';\nimport nodeDigest from './dsa_digest.js';\nimport nodeKey from './node_key.js';\nimport sign from './sign.js';\nimport getVerifyKey from './get_sign_verify_key.js';\nconst oneShotVerify = promisify(crypto.verify);\nconst verify = async (alg, key, signature, data) => {\n    const k = getVerifyKey(alg, key, 'verify');\n    if (alg.startsWith('HS')) {\n        const expected = await sign(alg, k, data);\n        const actual = signature;\n        try {\n            return crypto.timingSafeEqual(actual, expected);\n        }\n        catch {\n            return false;\n        }\n    }\n    const algorithm = nodeDigest(alg);\n    const keyInput = nodeKey(alg, k);\n    try {\n        return await oneShotVerify(algorithm, data, keyInput, signature);\n    }\n    catch {\n        return false;\n    }\n};\nexport default verify;\n", "import { JOSENotSupported } from '../util/errors.js';\nexport default function dsaDigest(alg) {\n    switch (alg) {\n        case 'PS256':\n        case 'RS256':\n        case 'ES256':\n        case 'ES256K':\n            return 'sha256';\n        case 'PS384':\n        case 'RS384':\n        case 'ES384':\n            return 'sha384';\n        case 'PS512':\n        case 'RS512':\n        case 'ES512':\n            return 'sha512';\n        case 'Ed25519':\n        case 'EdDSA':\n            return undefined;\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n}\n", "import { constants, KeyObject } from 'node:crypto';\nimport getNamedCurve from './get_named_curve.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport checkKeyLength from './check_key_length.js';\nconst ecCurveAlgMap = new Map([\n    ['ES256', 'P-256'],\n    ['ES256K', 'secp256k1'],\n    ['ES384', 'P-384'],\n    ['ES512', 'P-521'],\n]);\nexport default function keyForCrypto(alg, key) {\n    let asymmetricKeyType;\n    let asymmetricKeyDetails;\n    let isJWK;\n    if (key instanceof KeyObject) {\n        asymmetricKeyType = key.asymmetricKeyType;\n        asymmetricKeyDetails = key.asymmetricKeyDetails;\n    }\n    else {\n        isJWK = true;\n        switch (key.kty) {\n            case 'RSA':\n                asymmetricKeyType = 'rsa';\n                break;\n            case 'EC':\n                asymmetricKeyType = 'ec';\n                break;\n            case 'OKP': {\n                if (key.crv === 'Ed25519') {\n                    asymmetricKeyType = 'ed25519';\n                    break;\n                }\n                if (key.crv === 'Ed448') {\n                    asymmetricKeyType = 'ed448';\n                    break;\n                }\n                throw new TypeError('Invalid key for this operation, its crv must be Ed25519 or Ed448');\n            }\n            default:\n                throw new TypeError('Invalid key for this operation, its kty must be RSA, OKP, or EC');\n        }\n    }\n    let options;\n    switch (alg) {\n        case 'Ed25519':\n            if (asymmetricKeyType !== 'ed25519') {\n                throw new TypeError(`Invalid key for this operation, its asymmetricKeyType must be ed25519`);\n            }\n            break;\n        case 'EdDSA':\n            if (!['ed25519', 'ed448'].includes(asymmetricKeyType)) {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be ed25519 or ed448');\n            }\n            break;\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            if (asymmetricKeyType !== 'rsa') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa');\n            }\n            checkKeyLength(key, alg);\n            break;\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            if (asymmetricKeyType === 'rsa-pss') {\n                const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = asymmetricKeyDetails;\n                const length = parseInt(alg.slice(-3), 10);\n                if (hashAlgorithm !== undefined &&\n                    (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm)) {\n                    throw new TypeError(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${alg}`);\n                }\n                if (saltLength !== undefined && saltLength > length >> 3) {\n                    throw new TypeError(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${alg}`);\n                }\n            }\n            else if (asymmetricKeyType !== 'rsa') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa or rsa-pss');\n            }\n            checkKeyLength(key, alg);\n            options = {\n                padding: constants.RSA_PKCS1_PSS_PADDING,\n                saltLength: constants.RSA_PSS_SALTLEN_DIGEST,\n            };\n            break;\n        case 'ES256':\n        case 'ES256K':\n        case 'ES384':\n        case 'ES512': {\n            if (asymmetricKeyType !== 'ec') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be ec');\n            }\n            const actual = getNamedCurve(key);\n            const expected = ecCurveAlgMap.get(alg);\n            if (actual !== expected) {\n                throw new TypeError(`Invalid key curve for the algorithm, its curve must be ${expected}, got ${actual}`);\n            }\n            options = { dsaEncoding: 'ieee-p1363' };\n            break;\n        }\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    if (isJWK) {\n        return { format: 'jwk', key, ...options };\n    }\n    return options ? { ...options, key } : key;\n}\n", "import * as crypto from 'node:crypto';\nimport { promisify } from 'node:util';\nimport nodeDigest from './dsa_digest.js';\nimport hmacDigest from './hmac_digest.js';\nimport nodeKey from './node_key.js';\nimport getSignKey from './get_sign_verify_key.js';\nconst oneShotSign = promisify(crypto.sign);\nconst sign = async (alg, key, data) => {\n    const k = getSignKey(alg, key, 'sign');\n    if (alg.startsWith('HS')) {\n        const hmac = crypto.createHmac(hmacDigest(alg), k);\n        hmac.update(data);\n        return hmac.digest();\n    }\n    return oneShotSign(nodeDigest(alg), data, nodeKey(alg, k));\n};\nexport default sign;\n", "import { JOSENotSupported } from '../util/errors.js';\nexport default function hmacDigest(alg) {\n    switch (alg) {\n        case 'HS256':\n            return 'sha256';\n        case 'HS384':\n            return 'sha384';\n        case 'HS512':\n            return 'sha512';\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n}\n", "import { KeyObject, createS<PERSON>ret<PERSON><PERSON> } from 'node:crypto';\nimport { isCrypto<PERSON>ey } from './webcrypto.js';\nimport { checkSigCrypto<PERSON>ey } from '../lib/crypto_key.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nimport * as jwk from '../lib/is_jwk.js';\nexport default function getSignVerifyKey(alg, key, usage) {\n    if (key instanceof Uint8Array) {\n        if (!alg.startsWith('HS')) {\n            throw new TypeError(invalidKeyInput(key, ...types));\n        }\n        return createSecretKey(key);\n    }\n    if (key instanceof KeyObject) {\n        return key;\n    }\n    if (isCryptoKey(key)) {\n        checkSigCryptoKey(key, alg, usage);\n        return KeyObject.from(key);\n    }\n    if (jwk.isJWK(key)) {\n        if (alg.startsWith('HS')) {\n            return createS<PERSON><PERSON><PERSON><PERSON>(Buffer.from(key.k, 'base64url'));\n        }\n        return key;\n    }\n    throw new TypeError(invalidKeyInput(key, ...types, 'Uint8Array', 'JSON Web Key'));\n}\n", "import { flattenedVerify } from '../flattened/verify.js';\nimport { JWSInvalid, JWSSignatureVerificationFailed } from '../../util/errors.js';\nimport isObject from '../../lib/is_object.js';\nexport async function generalVerify(jws, key, options) {\n    if (!isObject(jws)) {\n        throw new JWSInvalid('General JWS must be an object');\n    }\n    if (!Array.isArray(jws.signatures) || !jws.signatures.every(isObject)) {\n        throw new JWSInvalid('JWS Signatures missing or incorrect type');\n    }\n    for (const signature of jws.signatures) {\n        try {\n            return await flattenedVerify({\n                header: signature.header,\n                payload: jws.payload,\n                protected: signature.protected,\n                signature: signature.signature,\n            }, key, options);\n        }\n        catch {\n        }\n    }\n    throw new JWSSignatureVerificationFailed();\n}\n", "import { compactVerify } from '../jws/compact/verify.js';\nimport jwtPayload from '../lib/jwt_claims_set.js';\nimport { JWTInvalid } from '../util/errors.js';\nexport async function jwtVerify(jwt, key, options) {\n    const verified = await compactVerify(jwt, key, options);\n    if (verified.protectedHeader.crit?.includes('b64') && verified.protectedHeader.b64 === false) {\n        throw new JWTInvalid('JWTs MUST NOT use unencoded payload');\n    }\n    const payload = jwtPayload(verified.protectedHeader, verified.payload, options);\n    const result = { payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\n", "import { JWTClaimValidationFailed, JWTExpired, JWTInvalid } from '../util/errors.js';\nimport { decoder } from './buffer_utils.js';\nimport epoch from './epoch.js';\nimport secs from './secs.js';\nimport isObject from './is_object.js';\nconst normalizeTyp = (value) => value.toLowerCase().replace(/^application\\//, '');\nconst checkAudiencePresence = (audPayload, audOption) => {\n    if (typeof audPayload === 'string') {\n        return audOption.includes(audPayload);\n    }\n    if (Array.isArray(audPayload)) {\n        return audOption.some(Set.prototype.has.bind(new Set(audPayload)));\n    }\n    return false;\n};\nexport default (protectedHeader, encodedPayload, options = {}) => {\n    let payload;\n    try {\n        payload = JSON.parse(decoder.decode(encodedPayload));\n    }\n    catch {\n    }\n    if (!isObject(payload)) {\n        throw new JWTInvalid('JWT Claims Set must be a top-level JSON object');\n    }\n    const { typ } = options;\n    if (typ &&\n        (typeof protectedHeader.typ !== 'string' ||\n            normalizeTyp(protectedHeader.typ) !== normalizeTyp(typ))) {\n        throw new JWTClaimValidationFailed('unexpected \"typ\" JWT header value', payload, 'typ', 'check_failed');\n    }\n    const { requiredClaims = [], issuer, subject, audience, maxTokenAge } = options;\n    const presenceCheck = [...requiredClaims];\n    if (maxTokenAge !== undefined)\n        presenceCheck.push('iat');\n    if (audience !== undefined)\n        presenceCheck.push('aud');\n    if (subject !== undefined)\n        presenceCheck.push('sub');\n    if (issuer !== undefined)\n        presenceCheck.push('iss');\n    for (const claim of new Set(presenceCheck.reverse())) {\n        if (!(claim in payload)) {\n            throw new JWTClaimValidationFailed(`missing required \"${claim}\" claim`, payload, claim, 'missing');\n        }\n    }\n    if (issuer &&\n        !(Array.isArray(issuer) ? issuer : [issuer]).includes(payload.iss)) {\n        throw new JWTClaimValidationFailed('unexpected \"iss\" claim value', payload, 'iss', 'check_failed');\n    }\n    if (subject && payload.sub !== subject) {\n        throw new JWTClaimValidationFailed('unexpected \"sub\" claim value', payload, 'sub', 'check_failed');\n    }\n    if (audience &&\n        !checkAudiencePresence(payload.aud, typeof audience === 'string' ? [audience] : audience)) {\n        throw new JWTClaimValidationFailed('unexpected \"aud\" claim value', payload, 'aud', 'check_failed');\n    }\n    let tolerance;\n    switch (typeof options.clockTolerance) {\n        case 'string':\n            tolerance = secs(options.clockTolerance);\n            break;\n        case 'number':\n            tolerance = options.clockTolerance;\n            break;\n        case 'undefined':\n            tolerance = 0;\n            break;\n        default:\n            throw new TypeError('Invalid clockTolerance option type');\n    }\n    const { currentDate } = options;\n    const now = epoch(currentDate || new Date());\n    if ((payload.iat !== undefined || maxTokenAge) && typeof payload.iat !== 'number') {\n        throw new JWTClaimValidationFailed('\"iat\" claim must be a number', payload, 'iat', 'invalid');\n    }\n    if (payload.nbf !== undefined) {\n        if (typeof payload.nbf !== 'number') {\n            throw new JWTClaimValidationFailed('\"nbf\" claim must be a number', payload, 'nbf', 'invalid');\n        }\n        if (payload.nbf > now + tolerance) {\n            throw new JWTClaimValidationFailed('\"nbf\" claim timestamp check failed', payload, 'nbf', 'check_failed');\n        }\n    }\n    if (payload.exp !== undefined) {\n        if (typeof payload.exp !== 'number') {\n            throw new JWTClaimValidationFailed('\"exp\" claim must be a number', payload, 'exp', 'invalid');\n        }\n        if (payload.exp <= now - tolerance) {\n            throw new JWTExpired('\"exp\" claim timestamp check failed', payload, 'exp', 'check_failed');\n        }\n    }\n    if (maxTokenAge) {\n        const age = now - payload.iat;\n        const max = typeof maxTokenAge === 'number' ? maxTokenAge : secs(maxTokenAge);\n        if (age - tolerance > max) {\n            throw new JWTExpired('\"iat\" claim timestamp check failed (too far in the past)', payload, 'iat', 'check_failed');\n        }\n        if (age < 0 - tolerance) {\n            throw new JWTClaimValidationFailed('\"iat\" claim timestamp check failed (it should be in the past)', payload, 'iat', 'check_failed');\n        }\n    }\n    return payload;\n};\n", "export default (date) => Math.floor(date.getTime() / 1000);\n", "const minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\+|\\-)? ?(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;\nexport default (str) => {\n    const matched = REGEX.exec(str);\n    if (!matched || (matched[4] && matched[1])) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[2]);\n    const unit = matched[3].toLowerCase();\n    let numericDate;\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            numericDate = Math.round(value);\n            break;\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            numericDate = Math.round(value * minute);\n            break;\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            numericDate = Math.round(value * hour);\n            break;\n        case 'day':\n        case 'days':\n        case 'd':\n            numericDate = Math.round(value * day);\n            break;\n        case 'week':\n        case 'weeks':\n        case 'w':\n            numericDate = Math.round(value * week);\n            break;\n        default:\n            numericDate = Math.round(value * year);\n            break;\n    }\n    if (matched[1] === '-' || matched[4] === 'ago') {\n        return -numericDate;\n    }\n    return numericDate;\n};\n", "import { compactDecrypt } from '../jwe/compact/decrypt.js';\nimport jwtPayload from '../lib/jwt_claims_set.js';\nimport { JWTClaimValidationFailed } from '../util/errors.js';\nexport async function jwtDecrypt(jwt, key, options) {\n    const decrypted = await compactDecrypt(jwt, key, options);\n    const payload = jwtPayload(decrypted.protectedHeader, decrypted.plaintext, options);\n    const { protectedHeader } = decrypted;\n    if (protectedHeader.iss !== undefined && protectedHeader.iss !== payload.iss) {\n        throw new JWTClaimValidationFailed('replicated \"iss\" claim header parameter mismatch', payload, 'iss', 'mismatch');\n    }\n    if (protectedHeader.sub !== undefined && protectedHeader.sub !== payload.sub) {\n        throw new JWTClaimValidationFailed('replicated \"sub\" claim header parameter mismatch', payload, 'sub', 'mismatch');\n    }\n    if (protectedHeader.aud !== undefined &&\n        JSON.stringify(protectedHeader.aud) !== JSON.stringify(payload.aud)) {\n        throw new JWTClaimValidationFailed('replicated \"aud\" claim header parameter mismatch', payload, 'aud', 'mismatch');\n    }\n    const result = { payload, protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n", "import { FlattenedEncrypt } from '../flattened/encrypt.js';\nexport class CompactEncrypt {\n    _flattened;\n    constructor(plaintext) {\n        this._flattened = new FlattenedEncrypt(plaintext);\n    }\n    setContentEncryptionKey(cek) {\n        this._flattened.setContentEncryptionKey(cek);\n        return this;\n    }\n    setInitializationVector(iv) {\n        this._flattened.setInitializationVector(iv);\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        this._flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        this._flattened.setKeyManagementParameters(parameters);\n        return this;\n    }\n    async encrypt(key, options) {\n        const jwe = await this._flattened.encrypt(key, options);\n        return [jwe.protected, jwe.encrypted_key, jwe.iv, jwe.ciphertext, jwe.tag].join('.');\n    }\n}\n", "import { FlattenedSign } from '../flattened/sign.js';\nexport class CompactSign {\n    _flattened;\n    constructor(payload) {\n        this._flattened = new FlattenedSign(payload);\n    }\n    setProtectedHeader(protectedHeader) {\n        this._flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    async sign(key, options) {\n        const jws = await this._flattened.sign(key, options);\n        if (jws.payload === undefined) {\n            throw new TypeError('use the flattened module for creating JWS with b64: false');\n        }\n        return `${jws.protected}.${jws.payload}.${jws.signature}`;\n    }\n}\n", "import { encode as base64url } from '../../runtime/base64url.js';\nimport sign from '../../runtime/sign.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport { JWSInvalid } from '../../util/errors.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport { checkKeyTypeWithJwk } from '../../lib/check_key_type.js';\nimport validateCrit from '../../lib/validate_crit.js';\nexport class FlattenedSign {\n    _payload;\n    _protectedHeader;\n    _unprotectedHeader;\n    constructor(payload) {\n        if (!(payload instanceof Uint8Array)) {\n            throw new TypeError('payload must be an instance of Uint8Array');\n        }\n        this._payload = payload;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this._unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this._unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        if (!this._protectedHeader && !this._unprotectedHeader) {\n            throw new JWSInvalid('either setProtectedHeader or setUnprotectedHeader must be called before #sign()');\n        }\n        if (!isDisjoint(this._protectedHeader, this._unprotectedHeader)) {\n            throw new JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this._protectedHeader,\n            ...this._unprotectedHeader,\n        };\n        const extensions = validateCrit(JWSInvalid, new Map([['b64', true]]), options?.crit, this._protectedHeader, joseHeader);\n        let b64 = true;\n        if (extensions.has('b64')) {\n            b64 = this._protectedHeader.b64;\n            if (typeof b64 !== 'boolean') {\n                throw new JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n            }\n        }\n        const { alg } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        checkKeyTypeWithJwk(alg, key, 'sign');\n        let payload = this._payload;\n        if (b64) {\n            payload = encoder.encode(base64url(payload));\n        }\n        let protectedHeader;\n        if (this._protectedHeader) {\n            protectedHeader = encoder.encode(base64url(JSON.stringify(this._protectedHeader)));\n        }\n        else {\n            protectedHeader = encoder.encode('');\n        }\n        const data = concat(protectedHeader, encoder.encode('.'), payload);\n        const signature = await sign(alg, key, data);\n        const jws = {\n            signature: base64url(signature),\n            payload: '',\n        };\n        if (b64) {\n            jws.payload = decoder.decode(payload);\n        }\n        if (this._unprotectedHeader) {\n            jws.header = this._unprotectedHeader;\n        }\n        if (this._protectedHeader) {\n            jws.protected = decoder.decode(protectedHeader);\n        }\n        return jws;\n    }\n}\n", "import { FlattenedSign } from '../flattened/sign.js';\nimport { JWSInvalid } from '../../util/errors.js';\nclass IndividualSignature {\n    parent;\n    protectedHeader;\n    unprotectedHeader;\n    options;\n    key;\n    constructor(sig, key, options) {\n        this.parent = sig;\n        this.key = key;\n        this.options = options;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.protectedHeader = protectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this.unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this.unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    addSignature(...args) {\n        return this.parent.addSignature(...args);\n    }\n    sign(...args) {\n        return this.parent.sign(...args);\n    }\n    done() {\n        return this.parent;\n    }\n}\nexport class GeneralSign {\n    _payload;\n    _signatures = [];\n    constructor(payload) {\n        this._payload = payload;\n    }\n    addSignature(key, options) {\n        const signature = new IndividualSignature(this, key, options);\n        this._signatures.push(signature);\n        return signature;\n    }\n    async sign() {\n        if (!this._signatures.length) {\n            throw new JWSInvalid('at least one signature must be added');\n        }\n        const jws = {\n            signatures: [],\n            payload: '',\n        };\n        for (let i = 0; i < this._signatures.length; i++) {\n            const signature = this._signatures[i];\n            const flattened = new FlattenedSign(this._payload);\n            flattened.setProtectedHeader(signature.protectedHeader);\n            flattened.setUnprotectedHeader(signature.unprotectedHeader);\n            const { payload, ...rest } = await flattened.sign(signature.key, signature.options);\n            if (i === 0) {\n                jws.payload = payload;\n            }\n            else if (jws.payload !== payload) {\n                throw new JWSInvalid('inconsistent use of JWS Unencoded Payload (RFC7797)');\n            }\n            jws.signatures.push(rest);\n        }\n        return jws;\n    }\n}\n", "import { CompactSign } from '../jws/compact/sign.js';\nimport { JWTInvalid } from '../util/errors.js';\nimport { encoder } from '../lib/buffer_utils.js';\nimport { ProduceJWT } from './produce.js';\nexport class SignJWT extends ProduceJWT {\n    _protectedHeader;\n    setProtectedHeader(protectedHeader) {\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        const sig = new CompactSign(encoder.encode(JSON.stringify(this._payload)));\n        sig.setProtectedHeader(this._protectedHeader);\n        if (Array.isArray(this._protectedHeader?.crit) &&\n            this._protectedHeader.crit.includes('b64') &&\n            this._protectedHeader.b64 === false) {\n            throw new JWTInvalid('JWTs MUST NOT use unencoded payload');\n        }\n        return sig.sign(key, options);\n    }\n}\n", "import epoch from '../lib/epoch.js';\nimport isObject from '../lib/is_object.js';\nimport secs from '../lib/secs.js';\nfunction validateInput(label, input) {\n    if (!Number.isFinite(input)) {\n        throw new TypeError(`Invalid ${label} input`);\n    }\n    return input;\n}\nexport class ProduceJWT {\n    _payload;\n    constructor(payload = {}) {\n        if (!isObject(payload)) {\n            throw new TypeError('JWT Claims Set MUST be an object');\n        }\n        this._payload = payload;\n    }\n    setIssuer(issuer) {\n        this._payload = { ...this._payload, iss: issuer };\n        return this;\n    }\n    setSubject(subject) {\n        this._payload = { ...this._payload, sub: subject };\n        return this;\n    }\n    setAudience(audience) {\n        this._payload = { ...this._payload, aud: audience };\n        return this;\n    }\n    setJti(jwtId) {\n        this._payload = { ...this._payload, jti: jwtId };\n        return this;\n    }\n    setNotBefore(input) {\n        if (typeof input === 'number') {\n            this._payload = { ...this._payload, nbf: validateInput('setNotBefore', input) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, nbf: validateInput('setNotBefore', epoch(input)) };\n        }\n        else {\n            this._payload = { ...this._payload, nbf: epoch(new Date()) + secs(input) };\n        }\n        return this;\n    }\n    setExpirationTime(input) {\n        if (typeof input === 'number') {\n            this._payload = { ...this._payload, exp: validateInput('setExpirationTime', input) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, exp: validateInput('setExpirationTime', epoch(input)) };\n        }\n        else {\n            this._payload = { ...this._payload, exp: epoch(new Date()) + secs(input) };\n        }\n        return this;\n    }\n    setIssuedAt(input) {\n        if (typeof input === 'undefined') {\n            this._payload = { ...this._payload, iat: epoch(new Date()) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, iat: validateInput('setIssuedAt', epoch(input)) };\n        }\n        else if (typeof input === 'string') {\n            this._payload = {\n                ...this._payload,\n                iat: validateInput('setIssuedAt', epoch(new Date()) + secs(input)),\n            };\n        }\n        else {\n            this._payload = { ...this._payload, iat: validateInput('setIssuedAt', input) };\n        }\n        return this;\n    }\n}\n", "import { CompactEncrypt } from '../jwe/compact/encrypt.js';\nimport { encoder } from '../lib/buffer_utils.js';\nimport { ProduceJWT } from './produce.js';\nexport class EncryptJWT extends ProduceJWT {\n    _cek;\n    _iv;\n    _keyManagementParameters;\n    _protectedHeader;\n    _replicateIssuerAsHeader;\n    _replicateSubjectAsHeader;\n    _replicateAudienceAsHeader;\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this._keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this._keyManagementParameters = parameters;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this._cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this._cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this._iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this._iv = iv;\n        return this;\n    }\n    replicateIssuerAsHeader() {\n        this._replicateIssuerAsHeader = true;\n        return this;\n    }\n    replicateSubjectAsHeader() {\n        this._replicateSubjectAsHeader = true;\n        return this;\n    }\n    replicateAudienceAsHeader() {\n        this._replicateAudienceAsHeader = true;\n        return this;\n    }\n    async encrypt(key, options) {\n        const enc = new CompactEncrypt(encoder.encode(JSON.stringify(this._payload)));\n        if (this._replicateIssuerAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, iss: this._payload.iss };\n        }\n        if (this._replicateSubjectAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, sub: this._payload.sub };\n        }\n        if (this._replicateAudienceAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, aud: this._payload.aud };\n        }\n        enc.setProtectedHeader(this._protectedHeader);\n        if (this._iv) {\n            enc.setInitializationVector(this._iv);\n        }\n        if (this._cek) {\n            enc.setContentEncryptionKey(this._cek);\n        }\n        if (this._keyManagementParameters) {\n            enc.setKeyManagementParameters(this._keyManagementParameters);\n        }\n        return enc.encrypt(key, options);\n    }\n}\n", "import digest from '../runtime/digest.js';\nimport { encode as base64url } from '../runtime/base64url.js';\nimport { JOSENotSupported, JWKInvalid } from '../util/errors.js';\nimport { encoder } from '../lib/buffer_utils.js';\nimport isObject from '../lib/is_object.js';\nconst check = (value, description) => {\n    if (typeof value !== 'string' || !value) {\n        throw new JWKInvalid(`${description} missing or invalid`);\n    }\n};\nexport async function calculateJwkThumbprint(jwk, digestAlgorithm) {\n    if (!isObject(jwk)) {\n        throw new TypeError('JWK must be an object');\n    }\n    digestAlgorithm ??= 'sha256';\n    if (digestAlgorithm !== 'sha256' &&\n        digestAlgorithm !== 'sha384' &&\n        digestAlgorithm !== 'sha512') {\n        throw new TypeError('digestAlgorithm must one of \"sha256\", \"sha384\", or \"sha512\"');\n    }\n    let components;\n    switch (jwk.kty) {\n        case 'EC':\n            check(jwk.crv, '\"crv\" (Curve) Parameter');\n            check(jwk.x, '\"x\" (X Coordinate) Parameter');\n            check(jwk.y, '\"y\" (Y Coordinate) Parameter');\n            components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x, y: jwk.y };\n            break;\n        case 'OKP':\n            check(jwk.crv, '\"crv\" (Subtype of Key Pair) Parameter');\n            check(jwk.x, '\"x\" (Public Key) Parameter');\n            components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x };\n            break;\n        case 'RSA':\n            check(jwk.e, '\"e\" (Exponent) Parameter');\n            check(jwk.n, '\"n\" (Modulus) Parameter');\n            components = { e: jwk.e, kty: jwk.kty, n: jwk.n };\n            break;\n        case 'oct':\n            check(jwk.k, '\"k\" (Key Value) Parameter');\n            components = { k: jwk.k, kty: jwk.kty };\n            break;\n        default:\n            throw new JOSENotSupported('\"kty\" (Key Type) Parameter missing or unsupported');\n    }\n    const data = encoder.encode(JSON.stringify(components));\n    return base64url(await digest(digestAlgorithm, data));\n}\nexport async function calculateJwkThumbprintUri(jwk, digestAlgorithm) {\n    digestAlgorithm ??= 'sha256';\n    const thumbprint = await calculateJwkThumbprint(jwk, digestAlgorithm);\n    return `urn:ietf:params:oauth:jwk-thumbprint:sha-${digestAlgorithm.slice(-3)}:${thumbprint}`;\n}\n", "import { importJWK } from '../key/import.js';\nimport isObject from '../lib/is_object.js';\nimport { JWSInvalid } from '../util/errors.js';\nexport async function EmbeddedJWK(protectedHeader, token) {\n    const joseHeader = {\n        ...protectedHeader,\n        ...token?.header,\n    };\n    if (!isObject(joseHeader.jwk)) {\n        throw new JWSInvalid('\"jwk\" (JSON Web Key) Header Parameter must be a JSON object');\n    }\n    const key = await importJWK({ ...joseHeader.jwk, ext: true }, joseHeader.alg);\n    if (key instanceof Uint8Array || key.type !== 'public') {\n        throw new JWSInvalid('\"jwk\" (JSON Web Key) Header Parameter must be a public key');\n    }\n    return key;\n}\n", "import { importJW<PERSON> } from '../key/import.js';\nimport { JW<PERSON>Invalid, JOSENotSupported, JWKSNoMatchingKey, JWKSMultipleMatchingKeys, } from '../util/errors.js';\nimport isObject from '../lib/is_object.js';\nfunction getKtyFromAlg(alg) {\n    switch (typeof alg === 'string' && alg.slice(0, 2)) {\n        case 'RS':\n        case 'PS':\n            return 'RSA';\n        case 'ES':\n            return 'EC';\n        case 'Ed':\n            return 'OKP';\n        default:\n            throw new JOSENotSupported('Unsupported \"alg\" value for a JSON Web Key Set');\n    }\n}\nfunction isJWKSLike(jwks) {\n    return (jwks &&\n        typeof jwks === 'object' &&\n        Array.isArray(jwks.keys) &&\n        jwks.keys.every(isJWKLike));\n}\nfunction isJWKLike(key) {\n    return isObject(key);\n}\nfunction clone(obj) {\n    if (typeof structuredClone === 'function') {\n        return structuredClone(obj);\n    }\n    return JSON.parse(JSON.stringify(obj));\n}\nclass LocalJWKSet {\n    _jwks;\n    _cached = new WeakMap();\n    constructor(jwks) {\n        if (!isJWKSLike(jwks)) {\n            throw new JWKSInvalid('JSON Web Key Set malformed');\n        }\n        this._jwks = clone(jwks);\n    }\n    async getKey(protectedHeader, token) {\n        const { alg, kid } = { ...protectedHeader, ...token?.header };\n        const kty = getKtyFromAlg(alg);\n        const candidates = this._jwks.keys.filter((jwk) => {\n            let candidate = kty === jwk.kty;\n            if (candidate && typeof kid === 'string') {\n                candidate = kid === jwk.kid;\n            }\n            if (candidate && typeof jwk.alg === 'string') {\n                candidate = alg === jwk.alg;\n            }\n            if (candidate && typeof jwk.use === 'string') {\n                candidate = jwk.use === 'sig';\n            }\n            if (candidate && Array.isArray(jwk.key_ops)) {\n                candidate = jwk.key_ops.includes('verify');\n            }\n            if (candidate) {\n                switch (alg) {\n                    case 'ES256':\n                        candidate = jwk.crv === 'P-256';\n                        break;\n                    case 'ES256K':\n                        candidate = jwk.crv === 'secp256k1';\n                        break;\n                    case 'ES384':\n                        candidate = jwk.crv === 'P-384';\n                        break;\n                    case 'ES512':\n                        candidate = jwk.crv === 'P-521';\n                        break;\n                    case 'Ed25519':\n                        candidate = jwk.crv === 'Ed25519';\n                        break;\n                    case 'EdDSA':\n                        candidate = jwk.crv === 'Ed25519' || jwk.crv === 'Ed448';\n                        break;\n                }\n            }\n            return candidate;\n        });\n        const { 0: jwk, length } = candidates;\n        if (length === 0) {\n            throw new JWKSNoMatchingKey();\n        }\n        if (length !== 1) {\n            const error = new JWKSMultipleMatchingKeys();\n            const { _cached } = this;\n            error[Symbol.asyncIterator] = async function* () {\n                for (const jwk of candidates) {\n                    try {\n                        yield await importWithAlgCache(_cached, jwk, alg);\n                    }\n                    catch { }\n                }\n            };\n            throw error;\n        }\n        return importWithAlgCache(this._cached, jwk, alg);\n    }\n}\nasync function importWithAlgCache(cache, jwk, alg) {\n    const cached = cache.get(jwk) || cache.set(jwk, {}).get(jwk);\n    if (cached[alg] === undefined) {\n        const key = await importJWK({ ...jwk, ext: true }, alg);\n        if (key instanceof Uint8Array || key.type !== 'public') {\n            throw new JWKSInvalid('JSON Web Key Set members must be public keys');\n        }\n        cached[alg] = key;\n    }\n    return cached[alg];\n}\nexport function createLocalJWKSet(jwks) {\n    const set = new LocalJWKSet(jwks);\n    const localJWKSet = async (protectedHeader, token) => set.getKey(protectedHeader, token);\n    Object.defineProperties(localJWKSet, {\n        jwks: {\n            value: () => clone(set._jwks),\n            enumerable: true,\n            configurable: false,\n            writable: false,\n        },\n    });\n    return localJWKSet;\n}\n", "import fetchJwks from '../runtime/fetch_jwks.js';\nimport { JWKSNoMatchingKey } from '../util/errors.js';\nimport { createLocalJWKSet } from './local.js';\nimport isObject from '../lib/is_object.js';\nfunction isCloudflareWorkers() {\n    return (typeof WebSocketPair !== 'undefined' ||\n        (typeof navigator !== 'undefined' && navigator.userAgent === 'Cloudflare-Workers') ||\n        (typeof EdgeRuntime !== 'undefined' && EdgeRuntime === 'vercel'));\n}\nlet USER_AGENT;\nif (typeof navigator === 'undefined' || !navigator.userAgent?.startsWith?.('Mozilla/5.0 ')) {\n    const NAME = 'jose';\n    const VERSION = 'v5.10.0';\n    USER_AGENT = `${NAME}/${VERSION}`;\n}\nexport const jwksCache = Symbol();\nfunction isFreshJwksCache(input, cacheMaxAge) {\n    if (typeof input !== 'object' || input === null) {\n        return false;\n    }\n    if (!('uat' in input) || typeof input.uat !== 'number' || Date.now() - input.uat >= cacheMaxAge) {\n        return false;\n    }\n    if (!('jwks' in input) ||\n        !isObject(input.jwks) ||\n        !Array.isArray(input.jwks.keys) ||\n        !Array.prototype.every.call(input.jwks.keys, isObject)) {\n        return false;\n    }\n    return true;\n}\nclass RemoteJWKSet {\n    _url;\n    _timeoutDuration;\n    _cooldownDuration;\n    _cacheMaxAge;\n    _jwksTimestamp;\n    _pendingFetch;\n    _options;\n    _local;\n    _cache;\n    constructor(url, options) {\n        if (!(url instanceof URL)) {\n            throw new TypeError('url must be an instance of URL');\n        }\n        this._url = new URL(url.href);\n        this._options = { agent: options?.agent, headers: options?.headers };\n        this._timeoutDuration =\n            typeof options?.timeoutDuration === 'number' ? options?.timeoutDuration : 5000;\n        this._cooldownDuration =\n            typeof options?.cooldownDuration === 'number' ? options?.cooldownDuration : 30000;\n        this._cacheMaxAge = typeof options?.cacheMaxAge === 'number' ? options?.cacheMaxAge : 600000;\n        if (options?.[jwksCache] !== undefined) {\n            this._cache = options?.[jwksCache];\n            if (isFreshJwksCache(options?.[jwksCache], this._cacheMaxAge)) {\n                this._jwksTimestamp = this._cache.uat;\n                this._local = createLocalJWKSet(this._cache.jwks);\n            }\n        }\n    }\n    coolingDown() {\n        return typeof this._jwksTimestamp === 'number'\n            ? Date.now() < this._jwksTimestamp + this._cooldownDuration\n            : false;\n    }\n    fresh() {\n        return typeof this._jwksTimestamp === 'number'\n            ? Date.now() < this._jwksTimestamp + this._cacheMaxAge\n            : false;\n    }\n    async getKey(protectedHeader, token) {\n        if (!this._local || !this.fresh()) {\n            await this.reload();\n        }\n        try {\n            return await this._local(protectedHeader, token);\n        }\n        catch (err) {\n            if (err instanceof JWKSNoMatchingKey) {\n                if (this.coolingDown() === false) {\n                    await this.reload();\n                    return this._local(protectedHeader, token);\n                }\n            }\n            throw err;\n        }\n    }\n    async reload() {\n        if (this._pendingFetch && isCloudflareWorkers()) {\n            this._pendingFetch = undefined;\n        }\n        const headers = new Headers(this._options.headers);\n        if (USER_AGENT && !headers.has('User-Agent')) {\n            headers.set('User-Agent', USER_AGENT);\n            this._options.headers = Object.fromEntries(headers.entries());\n        }\n        this._pendingFetch ||= fetchJwks(this._url, this._timeoutDuration, this._options)\n            .then((json) => {\n            this._local = createLocalJWKSet(json);\n            if (this._cache) {\n                this._cache.uat = Date.now();\n                this._cache.jwks = json;\n            }\n            this._jwksTimestamp = Date.now();\n            this._pendingFetch = undefined;\n        })\n            .catch((err) => {\n            this._pendingFetch = undefined;\n            throw err;\n        });\n        await this._pendingFetch;\n    }\n}\nexport function createRemoteJWKSet(url, options) {\n    const set = new RemoteJWKSet(url, options);\n    const remoteJWKSet = async (protectedHeader, token) => set.getKey(protectedHeader, token);\n    Object.defineProperties(remoteJWKSet, {\n        coolingDown: {\n            get: () => set.coolingDown(),\n            enumerable: true,\n            configurable: false,\n        },\n        fresh: {\n            get: () => set.fresh(),\n            enumerable: true,\n            configurable: false,\n        },\n        reload: {\n            value: () => set.reload(),\n            enumerable: true,\n            configurable: false,\n            writable: false,\n        },\n        reloading: {\n            get: () => !!set._pendingFetch,\n            enumerable: true,\n            configurable: false,\n        },\n        jwks: {\n            value: () => set._local?.jwks(),\n            enumerable: true,\n            configurable: false,\n            writable: false,\n        },\n    });\n    return remoteJWKSet;\n}\nexport const experimental_jwksCache = jwksCache;\n", "import * as http from 'node:http';\nimport * as https from 'node:https';\nimport { once } from 'node:events';\nimport { JOSEError, JWKSTimeout } from '../util/errors.js';\nimport { concat, decoder } from '../lib/buffer_utils.js';\nconst fetchJwks = async (url, timeout, options) => {\n    let get;\n    switch (url.protocol) {\n        case 'https:':\n            get = https.get;\n            break;\n        case 'http:':\n            get = http.get;\n            break;\n        default:\n            throw new TypeError('Unsupported URL protocol.');\n    }\n    const { agent, headers } = options;\n    const req = get(url.href, {\n        agent,\n        timeout,\n        headers,\n    });\n    const [response] = (await Promise.race([once(req, 'response'), once(req, 'timeout')]));\n    if (!response) {\n        req.destroy();\n        throw new JWKSTimeout();\n    }\n    if (response.statusCode !== 200) {\n        throw new JOSEError('Expected 200 OK from the JSON Web Key Set HTTP response');\n    }\n    const parts = [];\n    for await (const part of response) {\n        parts.push(part);\n    }\n    try {\n        return JSON.parse(decoder.decode(concat(...parts)));\n    }\n    catch {\n        throw new JOSEError('Failed to parse the JSON Web Key Set HTTP response as JSON');\n    }\n};\nexport default fetchJwks;\n", "import * as base64url from '../runtime/base64url.js';\nimport { decoder } from '../lib/buffer_utils.js';\nimport { JWTInvalid } from '../util/errors.js';\nimport jwtPayload from '../lib/jwt_claims_set.js';\nimport { ProduceJWT } from './produce.js';\nexport class UnsecuredJWT extends ProduceJWT {\n    encode() {\n        const header = base64url.encode(JSON.stringify({ alg: 'none' }));\n        const payload = base64url.encode(JSON.stringify(this._payload));\n        return `${header}.${payload}.`;\n    }\n    static decode(jwt, options) {\n        if (typeof jwt !== 'string') {\n            throw new JWTInvalid('Unsecured JWT must be a string');\n        }\n        const { 0: encodedHeader, 1: encodedPayload, 2: signature, length } = jwt.split('.');\n        if (length !== 3 || signature !== '') {\n            throw new JWTInvalid('Invalid Unsecured JWT');\n        }\n        let header;\n        try {\n            header = JSON.parse(decoder.decode(base64url.decode(encodedHeader)));\n            if (header.alg !== 'none')\n                throw new Error();\n        }\n        catch {\n            throw new JWTInvalid('Invalid Unsecured JWT');\n        }\n        const payload = jwtPayload(header, base64url.decode(encodedPayload), options);\n        return { payload, header };\n    }\n}\n", "import { decode as base64url } from './base64url.js';\nimport { decoder } from '../lib/buffer_utils.js';\nimport isObject from '../lib/is_object.js';\nexport function decodeProtectedHeader(token) {\n    let protectedB64u;\n    if (typeof token === 'string') {\n        const parts = token.split('.');\n        if (parts.length === 3 || parts.length === 5) {\n            ;\n            [protectedB64u] = parts;\n        }\n    }\n    else if (typeof token === 'object' && token) {\n        if ('protected' in token) {\n            protectedB64u = token.protected;\n        }\n        else {\n            throw new TypeError('Token does not contain a Protected Header');\n        }\n    }\n    try {\n        if (typeof protectedB64u !== 'string' || !protectedB64u) {\n            throw new Error();\n        }\n        const result = JSON.parse(decoder.decode(base64url(protectedB64u)));\n        if (!isObject(result)) {\n            throw new Error();\n        }\n        return result;\n    }\n    catch {\n        throw new TypeError('Invalid Token or Protected Header formatting');\n    }\n}\n", "import * as base64url from '../runtime/base64url.js';\nexport const encode = base64url.encode;\nexport const decode = base64url.decode;\n", "import { decode as base64url } from './base64url.js';\nimport { decoder } from '../lib/buffer_utils.js';\nimport isObject from '../lib/is_object.js';\nimport { JWTInvalid } from './errors.js';\nexport function decodeJwt(jwt) {\n    if (typeof jwt !== 'string')\n        throw new JWTInvalid('JWTs must use Compact JWS serialization, JWT must be a string');\n    const { 1: payload, length } = jwt.split('.');\n    if (length === 5)\n        throw new JWTInvalid('Only JWTs using Compact JWS serialization can be decoded');\n    if (length !== 3)\n        throw new JWTInvalid('Invalid JWT');\n    if (!payload)\n        throw new JWTInvalid('JWTs must contain a payload');\n    let decoded;\n    try {\n        decoded = base64url(payload);\n    }\n    catch {\n        throw new JWTInvalid('Failed to base64url decode the payload');\n    }\n    let result;\n    try {\n        result = JSON.parse(decoder.decode(decoded));\n    }\n    catch {\n        throw new JWTInvalid('Failed to parse the decoded payload as JSON');\n    }\n    if (!isObject(result))\n        throw new JWTInvalid('Invalid JWT Claims Set');\n    return result;\n}\n", "import { generateKeyPair as generate } from '../runtime/generate.js';\nexport async function generateKeyPair(alg, options) {\n    return generate(alg, options);\n}\n", "import { createSecretKey, generateKeyPair as generateKeyPairCb } from 'node:crypto';\nimport { promisify } from 'node:util';\nimport random from './random.js';\nimport { JOSENotSupported } from '../util/errors.js';\nconst generate = promisify(generateKeyPairCb);\nexport async function generateSecret(alg, options) {\n    let length;\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512':\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            length = parseInt(alg.slice(-3), 10);\n            break;\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW':\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW':\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            length = parseInt(alg.slice(1, 4), 10);\n            break;\n        default:\n            throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n    }\n    return createSecretKey(random(new Uint8Array(length >> 3)));\n}\nexport async function generateKeyPair(alg, options) {\n    switch (alg) {\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n        case 'RSA1_5': {\n            const modulusLength = options?.modulusLength ?? 2048;\n            if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n                throw new JOSENotSupported('Invalid or unsupported modulusLength option provided, 2048 bits or larger keys must be used');\n            }\n            const keypair = await generate('rsa', {\n                modulusLength,\n                publicExponent: 0x10001,\n            });\n            return keypair;\n        }\n        case 'ES256':\n            return generate('ec', { namedCurve: 'P-256' });\n        case 'ES256K':\n            return generate('ec', { namedCurve: 'secp256k1' });\n        case 'ES384':\n            return generate('ec', { namedCurve: 'P-384' });\n        case 'ES512':\n            return generate('ec', { namedCurve: 'P-521' });\n        case 'Ed25519':\n            return generate('ed25519');\n        case 'EdDSA': {\n            switch (options?.crv) {\n                case undefined:\n                case 'Ed25519':\n                    return generate('ed25519');\n                case 'Ed448':\n                    return generate('ed448');\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported crv option provided, supported values are Ed25519 and Ed448');\n            }\n        }\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            const crv = options?.crv ?? 'P-256';\n            switch (crv) {\n                case undefined:\n                case 'P-256':\n                case 'P-384':\n                case 'P-521':\n                    return generate('ec', { namedCurve: crv });\n                case 'X25519':\n                    return generate('x25519');\n                case 'X448':\n                    return generate('x448');\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported crv option provided, supported values are P-256, P-384, P-521, X25519, and X448');\n            }\n        }\n        default:\n            throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n    }\n}\n", "import { generateSecret as generate } from '../runtime/generate.js';\nexport async function generateSecret(alg, options) {\n    return generate(alg, options);\n}\n", "import value from '../runtime/runtime.js';\nexport default value;\n", "export default 'node:crypto';\n"], "mappings": ";;;;;;AAAA;;;ACAA;;;ACAA;;;ACAA;AAAA,SAAS,UAAAA,eAAc;;;ACAvB;;;ACAA;AAAA,SAAS,kBAAkB;AAC3B,IAAM,SAAS,CAAC,WAAW,SAAS,WAAW,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO;AAC9E,IAAO,iBAAQ;;;ADDR,IAAM,UAAU,IAAI,YAAY;AAChC,IAAM,UAAU,IAAI,YAAY;AACvC,IAAM,YAAY,KAAK;AAChB,SAAS,UAAU,SAAS;AAC/B,QAAM,OAAO,QAAQ,OAAO,CAAC,KAAK,EAAE,OAAO,MAAM,MAAM,QAAQ,CAAC;AAChE,QAAM,MAAM,IAAI,WAAW,IAAI;AAC/B,MAAI,IAAI;AACR,aAAW,UAAU,SAAS;AAC1B,QAAI,IAAI,QAAQ,CAAC;AACjB,SAAK,OAAO;AAAA,EAChB;AACA,SAAO;AACX;AACO,SAAS,IAAI,KAAK,UAAU;AAC/B,SAAO,OAAO,QAAQ,OAAO,GAAG,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG,QAAQ;AACpE;AACA,SAAS,cAAc,KAAK,OAAO,QAAQ;AACvC,MAAI,QAAQ,KAAK,SAAS,WAAW;AACjC,UAAM,IAAI,WAAW,6BAA6B,YAAY,CAAC,cAAc,KAAK,EAAE;AAAA,EACxF;AACA,MAAI,IAAI,CAAC,UAAU,IAAI,UAAU,IAAI,UAAU,GAAG,QAAQ,GAAI,GAAG,MAAM;AAC3E;AACO,SAAS,SAAS,OAAO;AAC5B,QAAM,OAAO,KAAK,MAAM,QAAQ,SAAS;AACzC,QAAM,MAAM,QAAQ;AACpB,QAAM,MAAM,IAAI,WAAW,CAAC;AAC5B,gBAAc,KAAK,MAAM,CAAC;AAC1B,gBAAc,KAAK,KAAK,CAAC;AACzB,SAAO;AACX;AACO,SAAS,SAAS,OAAO;AAC5B,QAAM,MAAM,IAAI,WAAW,CAAC;AAC5B,gBAAc,KAAK,KAAK;AACxB,SAAO;AACX;AACO,SAAS,eAAe,OAAO;AAClC,SAAO,OAAO,SAAS,MAAM,MAAM,GAAG,KAAK;AAC/C;AACA,eAAsB,UAAU,QAAQ,MAAM,OAAO;AACjD,QAAM,aAAa,KAAK,MAAM,QAAQ,KAAK,EAAE;AAC7C,QAAM,MAAM,IAAI,WAAW,aAAa,EAAE;AAC1C,WAAS,OAAO,GAAG,OAAO,YAAY,QAAQ;AAC1C,UAAM,MAAM,IAAI,WAAW,IAAI,OAAO,SAAS,MAAM,MAAM;AAC3D,QAAI,IAAI,SAAS,OAAO,CAAC,CAAC;AAC1B,QAAI,IAAI,QAAQ,CAAC;AACjB,QAAI,IAAI,OAAO,IAAI,OAAO,MAAM;AAChC,QAAI,IAAI,MAAM,eAAO,UAAU,GAAG,GAAG,OAAO,EAAE;AAAA,EAClD;AACA,SAAO,IAAI,MAAM,GAAG,QAAQ,CAAC;AACjC;;;ADhDA,SAAS,UAAU,OAAO;AACtB,MAAI,UAAU;AACd,MAAI,mBAAmB,YAAY;AAC/B,cAAU,QAAQ,OAAO,OAAO;AAAA,EACpC;AACA,SAAO;AACX;AACA,IAAM,SAAS,CAAC,UAAUC,QAAO,KAAK,KAAK,EAAE,SAAS,WAAW;AAI1D,IAAM,SAAS,CAAC,UAAU,IAAI,WAAWC,QAAO,KAAK,UAAU,KAAK,GAAG,WAAW,CAAC;;;AGb1F;AAAA,SAAS,kBAAkB,iBAAiB;;;ACA5C;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,IAAM,YAAN,cAAwB,MAAM;AAAA,EACjC,OAAO,OAAO;AAAA,EACd,OAAO;AAAA,EACP,YAAYC,UAAS,SAAS;AAC1B,UAAMA,UAAS,OAAO;AACtB,SAAK,OAAO,KAAK,YAAY;AAC7B,UAAM,oBAAoB,MAAM,KAAK,WAAW;AAAA,EACpD;AACJ;AACO,IAAM,2BAAN,cAAuC,UAAU;AAAA,EACpD,OAAO,OAAO;AAAA,EACd,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAYA,UAAS,SAAS,QAAQ,eAAe,SAAS,eAAe;AACzE,UAAMA,UAAS,EAAE,OAAO,EAAE,OAAO,QAAQ,QAAQ,EAAE,CAAC;AACpD,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACnB;AACJ;AACO,IAAM,aAAN,cAAyB,UAAU;AAAA,EACtC,OAAO,OAAO;AAAA,EACd,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAYA,UAAS,SAAS,QAAQ,eAAe,SAAS,eAAe;AACzE,UAAMA,UAAS,EAAE,OAAO,EAAE,OAAO,QAAQ,QAAQ,EAAE,CAAC;AACpD,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACnB;AACJ;AACO,IAAM,oBAAN,cAAgC,UAAU;AAAA,EAC7C,OAAO,OAAO;AAAA,EACd,OAAO;AACX;AACO,IAAM,mBAAN,cAA+B,UAAU;AAAA,EAC5C,OAAO,OAAO;AAAA,EACd,OAAO;AACX;AACO,IAAM,sBAAN,cAAkC,UAAU;AAAA,EAC/C,OAAO,OAAO;AAAA,EACd,OAAO;AAAA,EACP,YAAYA,WAAU,+BAA+B,SAAS;AAC1D,UAAMA,UAAS,OAAO;AAAA,EAC1B;AACJ;AACO,IAAM,aAAN,cAAyB,UAAU;AAAA,EACtC,OAAO,OAAO;AAAA,EACd,OAAO;AACX;AACO,IAAM,aAAN,cAAyB,UAAU;AAAA,EACtC,OAAO,OAAO;AAAA,EACd,OAAO;AACX;AACO,IAAM,aAAN,cAAyB,UAAU;AAAA,EACtC,OAAO,OAAO;AAAA,EACd,OAAO;AACX;AACO,IAAM,aAAN,cAAyB,UAAU;AAAA,EACtC,OAAO,OAAO;AAAA,EACd,OAAO;AACX;AACO,IAAM,cAAN,cAA0B,UAAU;AAAA,EACvC,OAAO,OAAO;AAAA,EACd,OAAO;AACX;AACO,IAAM,oBAAN,cAAgC,UAAU;AAAA,EAC7C,OAAO,OAAO;AAAA,EACd,OAAO;AAAA,EACP,YAAYA,WAAU,mDAAmD,SAAS;AAC9E,UAAMA,UAAS,OAAO;AAAA,EAC1B;AACJ;AACO,IAAM,2BAAN,cAAuC,UAAU;AAAA,EACpD,CAAC,OAAO,aAAa;AAAA,EACrB,OAAO,OAAO;AAAA,EACd,OAAO;AAAA,EACP,YAAYA,WAAU,wDAAwD,SAAS;AACnF,UAAMA,UAAS,OAAO;AAAA,EAC1B;AACJ;AACO,IAAM,cAAN,cAA0B,UAAU;AAAA,EACvC,OAAO,OAAO;AAAA,EACd,OAAO;AAAA,EACP,YAAYA,WAAU,qBAAqB,SAAS;AAChD,UAAMA,UAAS,OAAO;AAAA,EAC1B;AACJ;AACO,IAAM,iCAAN,cAA6C,UAAU;AAAA,EAC1D,OAAO,OAAO;AAAA,EACd,OAAO;AAAA,EACP,YAAYA,WAAU,iCAAiC,SAAS;AAC5D,UAAMA,UAAS,OAAO;AAAA,EAC1B;AACJ;;;AClGA;;;ACAA;AAAA,SAA2B,sBAAe;;;ADEnC,SAAS,UAAU,KAAK;AAC3B,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,iBAAiB,8BAA8B,GAAG,EAAE;AAAA,EACtE;AACJ;AACA,IAAO,aAAQ,CAAC,QAAQ,eAAO,IAAI,WAAW,UAAU,GAAG,KAAK,CAAC,CAAC;;;AFjBlE,IAAM,gBAAgB,CAAC,KAAK,OAAO;AAC/B,MAAI,GAAG,UAAU,MAAM,UAAU,GAAG,GAAG;AACnC,UAAM,IAAI,WAAW,sCAAsC;AAAA,EAC/D;AACJ;AACA,IAAO,0BAAQ;;;AIPf;;;ACAA;AAAA,YAAY,UAAU;AACtB,IAAO,wBAAQ,CAAC,QAAa,WAAM,YAAY,GAAG;;;ADClD,IAAM,iBAAiB,CAAC,KAAK,QAAQ;AACjC,MAAI;AACJ,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,iBAAW,SAAS,IAAI,MAAM,EAAE,GAAG,EAAE;AACrC;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,iBAAW,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AACvC;AAAA,IACJ;AACI,YAAM,IAAI,iBAAiB,gCAAgC,GAAG,6DAA6D;AAAA,EACnI;AACA,MAAI,eAAe,YAAY;AAC3B,UAAM,SAAS,IAAI,cAAc;AACjC,QAAI,WAAW,UAAU;AACrB,YAAM,IAAI,WAAW,mDAAmD,QAAQ,cAAc,MAAM,OAAO;AAAA,IAC/G;AACA;AAAA,EACJ;AACA,MAAI,sBAAY,GAAG,KAAK,IAAI,SAAS,UAAU;AAC3C,UAAM,SAAS,IAAI,oBAAoB;AACvC,QAAI,WAAW,UAAU;AACrB,YAAM,IAAI,WAAW,mDAAmD,QAAQ,cAAc,MAAM,OAAO;AAAA,IAC/G;AACA;AAAA,EACJ;AACA,QAAM,IAAI,UAAU,qCAAqC;AAC7D;AACA,IAAO,2BAAQ;;;AElCf;AAAA,SAAS,mBAAmB,YAAY;AACxC,IAAM,kBAAkB;AACxB,IAAO,4BAAQ;;;ACFf;AAAA,SAAS,kBAAkB;AAEZ,SAAR,OAAwB,KAAK,IAAI,YAAY,SAAS,QAAQ,SAAS;AAC1E,QAAM,UAAU,OAAO,KAAK,IAAI,YAAY,SAAS,IAAI,UAAU,CAAC,CAAC;AACrE,QAAM,OAAO,WAAW,MAAM,OAAO,IAAI,MAAM;AAC/C,OAAK,OAAO,OAAO;AACnB,SAAO,KAAK,OAAO,EAAE,MAAM,GAAG,WAAW,CAAC;AAC9C;;;ACPA;AAAA,YAAY,YAAY;AACxB,YAAYC,WAAU;AACtB,IAAMC,aAAmB;AACzB,IAAO,oBAAQA;AACR,IAAM,cAAc,CAAC,QAAa,YAAM,YAAY,GAAG;;;ACJ9D;AAAA,SAAS,SAAS,MAAM,OAAO,kBAAkB;AAC7C,SAAO,IAAI,UAAU,kDAAkD,IAAI,YAAY,IAAI,EAAE;AACjG;AACA,SAAS,YAAY,WAAW,MAAM;AAClC,SAAO,UAAU,SAAS;AAC9B;AACA,SAAS,cAAc,MAAM;AACzB,SAAO,SAAS,KAAK,KAAK,MAAM,CAAC,GAAG,EAAE;AAC1C;AACA,SAAS,cAAc,KAAK;AACxB,UAAQ,KAAK;AAAA,IACT,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,MAAM,aAAa;AAAA,EACrC;AACJ;AACA,SAAS,WAAW,KAAK,QAAQ;AAC7B,MAAI,OAAO,UAAU,CAAC,OAAO,KAAK,CAAC,aAAa,IAAI,OAAO,SAAS,QAAQ,CAAC,GAAG;AAC5E,QAAI,MAAM;AACV,QAAI,OAAO,SAAS,GAAG;AACnB,YAAM,OAAO,OAAO,IAAI;AACxB,aAAO,UAAU,OAAO,KAAK,IAAI,CAAC,QAAQ,IAAI;AAAA,IAClD,WACS,OAAO,WAAW,GAAG;AAC1B,aAAO,UAAU,OAAO,CAAC,CAAC,OAAO,OAAO,CAAC,CAAC;AAAA,IAC9C,OACK;AACD,aAAO,GAAG,OAAO,CAAC,CAAC;AAAA,IACvB;AACA,UAAM,IAAI,UAAU,GAAG;AAAA,EAC3B;AACJ;AACO,SAAS,kBAAkB,KAAK,QAAQ,QAAQ;AACnD,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,SAAS;AACV,UAAI,CAAC,YAAY,IAAI,WAAW,MAAM;AAClC,cAAM,SAAS,MAAM;AACzB,YAAM,WAAW,SAAS,IAAI,MAAM,CAAC,GAAG,EAAE;AAC1C,YAAM,SAAS,cAAc,IAAI,UAAU,IAAI;AAC/C,UAAI,WAAW;AACX,cAAM,SAAS,OAAO,QAAQ,IAAI,gBAAgB;AACtD;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,SAAS;AACV,UAAI,CAAC,YAAY,IAAI,WAAW,mBAAmB;AAC/C,cAAM,SAAS,mBAAmB;AACtC,YAAM,WAAW,SAAS,IAAI,MAAM,CAAC,GAAG,EAAE;AAC1C,YAAM,SAAS,cAAc,IAAI,UAAU,IAAI;AAC/C,UAAI,WAAW;AACX,cAAM,SAAS,OAAO,QAAQ,IAAI,gBAAgB;AACtD;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,SAAS;AACV,UAAI,CAAC,YAAY,IAAI,WAAW,SAAS;AACrC,cAAM,SAAS,SAAS;AAC5B,YAAM,WAAW,SAAS,IAAI,MAAM,CAAC,GAAG,EAAE;AAC1C,YAAM,SAAS,cAAc,IAAI,UAAU,IAAI;AAC/C,UAAI,WAAW;AACX,cAAM,SAAS,OAAO,QAAQ,IAAI,gBAAgB;AACtD;AAAA,IACJ;AAAA,IACA,KAAK,SAAS;AACV,UAAI,IAAI,UAAU,SAAS,aAAa,IAAI,UAAU,SAAS,SAAS;AACpE,cAAM,SAAS,kBAAkB;AAAA,MACrC;AACA;AAAA,IACJ;AAAA,IACA,KAAK,WAAW;AACZ,UAAI,CAAC,YAAY,IAAI,WAAW,SAAS;AACrC,cAAM,SAAS,SAAS;AAC5B;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,SAAS;AACV,UAAI,CAAC,YAAY,IAAI,WAAW,OAAO;AACnC,cAAM,SAAS,OAAO;AAC1B,YAAM,WAAW,cAAc,GAAG;AAClC,YAAM,SAAS,IAAI,UAAU;AAC7B,UAAI,WAAW;AACX,cAAM,SAAS,UAAU,sBAAsB;AACnD;AAAA,IACJ;AAAA,IACA;AACI,YAAM,IAAI,UAAU,2CAA2C;AAAA,EACvE;AACA,aAAW,KAAK,MAAM;AAC1B;AACO,SAAS,kBAAkB,KAAK,QAAQ,QAAQ;AACnD,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,WAAW;AACZ,UAAI,CAAC,YAAY,IAAI,WAAW,SAAS;AACrC,cAAM,SAAS,SAAS;AAC5B,YAAM,WAAW,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AAC7C,YAAM,SAAS,IAAI,UAAU;AAC7B,UAAI,WAAW;AACX,cAAM,SAAS,UAAU,kBAAkB;AAC/C;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,UAAU;AACX,UAAI,CAAC,YAAY,IAAI,WAAW,QAAQ;AACpC,cAAM,SAAS,QAAQ;AAC3B,YAAM,WAAW,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AAC7C,YAAM,SAAS,IAAI,UAAU;AAC7B,UAAI,WAAW;AACX,cAAM,SAAS,UAAU,kBAAkB;AAC/C;AAAA,IACJ;AAAA,IACA,KAAK,QAAQ;AACT,cAAQ,IAAI,UAAU,MAAM;AAAA,QACxB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD;AAAA,QACJ;AACI,gBAAM,SAAS,uBAAuB;AAAA,MAC9C;AACA;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,UAAI,CAAC,YAAY,IAAI,WAAW,QAAQ;AACpC,cAAM,SAAS,QAAQ;AAC3B;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,gBAAgB;AACjB,UAAI,CAAC,YAAY,IAAI,WAAW,UAAU;AACtC,cAAM,SAAS,UAAU;AAC7B,YAAM,WAAW,SAAS,IAAI,MAAM,CAAC,GAAG,EAAE,KAAK;AAC/C,YAAM,SAAS,cAAc,IAAI,UAAU,IAAI;AAC/C,UAAI,WAAW;AACX,cAAM,SAAS,OAAO,QAAQ,IAAI,gBAAgB;AACtD;AAAA,IACJ;AAAA,IACA;AACI,YAAM,IAAI,UAAU,2CAA2C;AAAA,EACvE;AACA,aAAW,KAAK,MAAM;AAC1B;;;AC5JA;AAAA,SAAS,QAAQ,KAAK,WAAWC,QAAO;AACpC,EAAAA,SAAQA,OAAM,OAAO,OAAO;AAC5B,MAAIA,OAAM,SAAS,GAAG;AAClB,UAAM,OAAOA,OAAM,IAAI;AACvB,WAAO,eAAeA,OAAM,KAAK,IAAI,CAAC,QAAQ,IAAI;AAAA,EACtD,WACSA,OAAM,WAAW,GAAG;AACzB,WAAO,eAAeA,OAAM,CAAC,CAAC,OAAOA,OAAM,CAAC,CAAC;AAAA,EACjD,OACK;AACD,WAAO,WAAWA,OAAM,CAAC,CAAC;AAAA,EAC9B;AACA,MAAI,UAAU,MAAM;AAChB,WAAO,aAAa,MAAM;AAAA,EAC9B,WACS,OAAO,WAAW,cAAc,OAAO,MAAM;AAClD,WAAO,sBAAsB,OAAO,IAAI;AAAA,EAC5C,WACS,OAAO,WAAW,YAAY,UAAU,MAAM;AACnD,QAAI,OAAO,aAAa,MAAM;AAC1B,aAAO,4BAA4B,OAAO,YAAY,IAAI;AAAA,IAC9D;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAO,4BAAQ,CAAC,WAAWA,WAAU;AACjC,SAAO,QAAQ,gBAAgB,QAAQ,GAAGA,MAAK;AACnD;AACO,SAAS,QAAQ,KAAK,WAAWA,QAAO;AAC3C,SAAO,QAAQ,eAAe,GAAG,uBAAuB,QAAQ,GAAGA,MAAK;AAC5E;;;AC9BA;AAAA,SAAS,kBAAkB;AAC3B,IAAI;AACJ,IAAO,kBAAQ,CAAC,cAAc;AAC1B,cAAY,IAAI,IAAI,WAAW,CAAC;AAChC,SAAO,QAAQ,IAAI,SAAS;AAChC;;;ACLA;AAEA,IAAO,sBAAQ,CAAC,QAAQ,sBAAY,GAAG,KAAK,YAAY,GAAG;AAC3D,IAAMC,SAAQ,CAAC,WAAW;AAC1B,IAAI,WAAW,aAAa,mBAAW,WAAW;AAC9C,EAAAA,OAAM,KAAK,WAAW;AAC1B;;;AbOA,SAAS,WAAW,KAAK,KAAK,YAAY,IAAIC,MAAK,KAAK;AACpD,QAAM,UAAU,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AAC5C,MAAI,sBAAY,GAAG,GAAG;AAClB,UAAM,IAAI,OAAO;AAAA,EACrB;AACA,QAAM,SAAS,IAAI,SAAS,WAAW,CAAC;AACxC,QAAM,SAAS,IAAI,SAAS,GAAG,WAAW,CAAC;AAC3C,QAAM,UAAU,SAAS,IAAI,MAAM,EAAE,GAAG,EAAE;AAC1C,QAAM,YAAY,OAAO,OAAO;AAChC,MAAI,CAAC,gBAAU,SAAS,GAAG;AACvB,UAAM,IAAI,iBAAiB,OAAO,GAAG,8CAA8C;AAAA,EACvF;AACA,QAAM,cAAc,OAAO,KAAK,IAAI,YAAY,SAAS,QAAQ,OAAO;AACxE,MAAI;AACJ,MAAI;AACA,qBAAiB,0BAAgBA,MAAK,WAAW;AAAA,EACrD,QACM;AAAA,EACN;AACA,MAAI,CAAC,gBAAgB;AACjB,UAAM,IAAI,oBAAoB;AAAA,EAClC;AACA,MAAI;AACJ,MAAI;AACA,UAAM,WAAW,iBAAiB,WAAW,QAAQ,EAAE;AACvD,gBAAY,OAAO,SAAS,OAAO,UAAU,GAAG,SAAS,MAAM,CAAC;AAAA,EACpE,QACM;AAAA,EACN;AACA,MAAI,CAAC,WAAW;AACZ,UAAM,IAAI,oBAAoB;AAAA,EAClC;AACA,SAAO;AACX;AACA,SAAS,WAAW,KAAK,KAAK,YAAY,IAAIA,MAAK,KAAK;AACpD,QAAM,UAAU,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AAC5C,QAAM,YAAY,OAAO,OAAO;AAChC,MAAI,CAAC,gBAAU,SAAS,GAAG;AACvB,UAAM,IAAI,iBAAiB,OAAO,GAAG,8CAA8C;AAAA,EACvF;AACA,MAAI;AACA,UAAM,WAAW,iBAAiB,WAAW,KAAK,IAAI,EAAE,eAAe,GAAG,CAAC;AAC3E,aAAS,WAAWA,IAAG;AACvB,QAAI,IAAI,YAAY;AAChB,eAAS,OAAO,KAAK,EAAE,iBAAiB,WAAW,OAAO,CAAC;AAAA,IAC/D;AACA,UAAM,YAAY,SAAS,OAAO,UAAU;AAC5C,aAAS,MAAM;AACf,WAAO;AAAA,EACX,QACM;AACF,UAAM,IAAI,oBAAoB;AAAA,EAClC;AACJ;AACA,IAAM,UAAU,CAAC,KAAK,KAAK,YAAY,IAAIA,MAAK,QAAQ;AACpD,MAAI;AACJ,MAAI,YAAY,GAAG,GAAG;AAClB,sBAAkB,KAAK,KAAK,SAAS;AACrC,UAAM,UAAU,KAAK,GAAG;AAAA,EAC5B,WACS,eAAe,cAAc,sBAAY,GAAG,GAAG;AACpD,UAAM;AAAA,EACV,OACK;AACD,UAAM,IAAI,UAAU,0BAAgB,KAAK,GAAGC,QAAO,YAAY,CAAC;AAAA,EACpE;AACA,MAAI,CAAC,IAAI;AACL,UAAM,IAAI,WAAW,mCAAmC;AAAA,EAC5D;AACA,MAAI,CAACD,MAAK;AACN,UAAM,IAAI,WAAW,gCAAgC;AAAA,EACzD;AACA,2BAAe,KAAK,GAAG;AACvB,0BAAc,KAAK,EAAE;AACrB,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO,WAAW,KAAK,KAAK,YAAY,IAAIA,MAAK,GAAG;AAAA,IACxD,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO,WAAW,KAAK,KAAK,YAAY,IAAIA,MAAK,GAAG;AAAA,IACxD;AACI,YAAM,IAAI,iBAAiB,8CAA8C;AAAA,EACjF;AACJ;AACA,IAAO,kBAAQ;;;AcpGf;AAAA,IAAM,aAAa,IAAI,YAAY;AAC/B,QAAM,UAAU,QAAQ,OAAO,OAAO;AACtC,MAAI,QAAQ,WAAW,KAAK,QAAQ,WAAW,GAAG;AAC9C,WAAO;AAAA,EACX;AACA,MAAI;AACJ,aAAW,UAAU,SAAS;AAC1B,UAAM,aAAa,OAAO,KAAK,MAAM;AACrC,QAAI,CAAC,OAAO,IAAI,SAAS,GAAG;AACxB,YAAM,IAAI,IAAI,UAAU;AACxB;AAAA,IACJ;AACA,eAAW,aAAa,YAAY;AAChC,UAAI,IAAI,IAAI,SAAS,GAAG;AACpB,eAAO;AAAA,MACX;AACA,UAAI,IAAI,SAAS;AAAA,IACrB;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAO,sBAAQ;;;ACrBf;AAAA,SAAS,aAAa,OAAO;AACzB,SAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AACe,SAAR,SAA0B,OAAO;AACpC,MAAI,CAAC,aAAa,KAAK,KAAK,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM,mBAAmB;AACrF,WAAO;AAAA,EACX;AACA,MAAI,OAAO,eAAe,KAAK,MAAM,MAAM;AACvC,WAAO;AAAA,EACX;AACA,MAAI,QAAQ;AACZ,SAAO,OAAO,eAAe,KAAK,MAAM,MAAM;AAC1C,YAAQ,OAAO,eAAe,KAAK;AAAA,EACvC;AACA,SAAO,OAAO,eAAe,KAAK,MAAM;AAC5C;;;ACfA;;;ACAA;AAAA,SAAS,UAAAE,eAAc;AACvB,SAAS,aAAAC,YAAW,oBAAAC,mBAAkB,gBAAgB,uBAAuB;AAS7E,SAAS,aAAa,KAAK,KAAK;AAC5B,MAAI,IAAI,oBAAoB,MAAM,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG;AAC7D,UAAM,IAAI,UAAU,6BAA6B,GAAG,EAAE;AAAA,EAC1D;AACJ;AACA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACtC,MAAI,sBAAY,GAAG,GAAG;AAClB,WAAO;AAAA,EACX;AACA,MAAI,eAAe,YAAY;AAC3B,WAAO,gBAAgB,GAAG;AAAA,EAC9B;AACA,MAAI,YAAY,GAAG,GAAG;AAClB,sBAAkB,KAAK,KAAK,KAAK;AACjC,WAAOC,WAAU,KAAK,GAAG;AAAA,EAC7B;AACA,QAAM,IAAI,UAAU,0BAAgB,KAAK,GAAGC,QAAO,YAAY,CAAC;AACpE;AACO,IAAM,OAAO,CAAC,KAAK,KAAK,QAAQ;AACnC,QAAM,OAAO,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AACzC,QAAM,YAAY,MAAM,IAAI;AAC5B,MAAI,CAAC,gBAAU,SAAS,GAAG;AACvB,UAAM,IAAI,iBAAiB,OAAO,GAAG,6DAA6D;AAAA,EACtG;AACA,QAAM,YAAY,gBAAgB,KAAK,KAAK,SAAS;AACrD,eAAa,WAAW,GAAG;AAC3B,QAAM,SAAS,eAAe,WAAW,WAAWC,QAAO,MAAM,GAAG,GAAI,CAAC;AACzE,SAAO,OAAO,OAAO,OAAO,GAAG,GAAG,OAAO,MAAM,CAAC;AACpD;AACO,IAAM,SAAS,CAAC,KAAK,KAAK,iBAAiB;AAC9C,QAAM,OAAO,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AACzC,QAAM,YAAY,MAAM,IAAI;AAC5B,MAAI,CAAC,gBAAU,SAAS,GAAG;AACvB,UAAM,IAAI,iBAAiB,OAAO,GAAG,6DAA6D;AAAA,EACtG;AACA,QAAM,YAAY,gBAAgB,KAAK,KAAK,WAAW;AACvD,eAAa,WAAW,GAAG;AAC3B,QAAM,SAASC,kBAAiB,WAAW,WAAWD,QAAO,MAAM,GAAG,GAAI,CAAC;AAC3E,SAAO,OAAO,OAAO,OAAO,YAAY,GAAG,OAAO,MAAM,CAAC;AAC7D;;;ACjDA;AAAA,SAAS,eAAe,mBAAmB,mBAAmB,aAAAE,kBAAiB;AAC/E,SAAS,iBAAiB;;;ACD1B;AAAA,SAAS,aAAAC,kBAAiB;;;ACA1B;AACO,SAAS,MAAM,KAAK;AACvB,SAAO,SAAS,GAAG,KAAK,OAAO,IAAI,QAAQ;AAC/C;AACO,SAAS,aAAa,KAAK;AAC9B,SAAO,IAAI,QAAQ,SAAS,OAAO,IAAI,MAAM;AACjD;AACO,SAAS,YAAY,KAAK;AAC7B,SAAO,IAAI,QAAQ,SAAS,OAAO,IAAI,MAAM;AACjD;AACO,SAAS,YAAY,KAAK;AAC7B,SAAO,MAAM,GAAG,KAAK,IAAI,QAAQ,SAAS,OAAO,IAAI,MAAM;AAC/D;;;ADJA,IAAM,mBAAmB,CAAC,eAAe;AACrC,UAAQ,YAAY;AAAA,IAChB,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,iBAAiB,0CAA0C;AAAA,EAC7E;AACJ;AACA,IAAMC,iBAAgB,CAAC,KAAK,QAAQ;AAChC,MAAI;AACJ,MAAI,YAAY,GAAG,GAAG;AAClB,UAAMC,WAAU,KAAK,GAAG;AAAA,EAC5B,WACS,sBAAY,GAAG,GAAG;AACvB,UAAM;AAAA,EACV,WACS,MAAM,GAAG,GAAG;AACjB,WAAO,IAAI;AAAA,EACf,OACK;AACD,UAAM,IAAI,UAAU,0BAAgB,KAAK,GAAGC,MAAK,CAAC;AAAA,EACtD;AACA,MAAI,IAAI,SAAS,UAAU;AACvB,UAAM,IAAI,UAAU,qEAAqE;AAAA,EAC7F;AACA,UAAQ,IAAI,mBAAmB;AAAA,IAC3B,KAAK;AAAA,IACL,KAAK;AACD,aAAO,KAAK,IAAI,kBAAkB,MAAM,CAAC,CAAC;AAAA,IAC9C,KAAK;AAAA,IACL,KAAK;AACD,aAAO,IAAI,IAAI,kBAAkB,MAAM,CAAC,CAAC;AAAA,IAC7C,KAAK,MAAM;AACP,YAAM,aAAa,IAAI,qBAAqB;AAC5C,UAAI,KAAK;AACL,eAAO;AAAA,MACX;AACA,aAAO,iBAAiB,UAAU;AAAA,IACtC;AAAA,IACA;AACI,YAAM,IAAI,UAAU,gDAAgD;AAAA,EAC5E;AACJ;AACA,IAAO,0BAAQF;;;AD/Cf,IAAM,kBAAkB,UAAU,iBAAiB;AACnD,eAAsB,UAAU,WAAW,YAAY,WAAW,WAAW,MAAM,IAAI,WAAW,CAAC,GAAG,MAAM,IAAI,WAAW,CAAC,GAAG;AAC3H,MAAI;AACJ,MAAI,YAAY,SAAS,GAAG;AACxB,sBAAkB,WAAW,MAAM;AACnC,gBAAYG,WAAU,KAAK,SAAS;AAAA,EACxC,WACS,sBAAY,SAAS,GAAG;AAC7B,gBAAY;AAAA,EAChB,OACK;AACD,UAAM,IAAI,UAAU,0BAAgB,WAAW,GAAGC,MAAK,CAAC;AAAA,EAC5D;AACA,MAAI;AACJ,MAAI,YAAY,UAAU,GAAG;AACzB,sBAAkB,YAAY,QAAQ,YAAY;AAClD,iBAAaD,WAAU,KAAK,UAAU;AAAA,EAC1C,WACS,sBAAY,UAAU,GAAG;AAC9B,iBAAa;AAAA,EACjB,OACK;AACD,UAAM,IAAI,UAAU,0BAAgB,YAAY,GAAGC,MAAK,CAAC;AAAA,EAC7D;AACA,QAAM,QAAQ,OAAO,eAAe,QAAQ,OAAO,SAAS,CAAC,GAAG,eAAe,GAAG,GAAG,eAAe,GAAG,GAAG,SAAS,SAAS,CAAC;AAC7H,QAAM,eAAe,cAAc,EAAE,YAAY,UAAU,CAAC;AAC5D,SAAO,UAAU,cAAc,WAAW,KAAK;AACnD;AACA,eAAsB,YAAY,KAAK;AACnC,MAAI;AACJ,MAAI,YAAY,GAAG,GAAG;AAClB,UAAMD,WAAU,KAAK,GAAG;AAAA,EAC5B,WACS,sBAAY,GAAG,GAAG;AACvB,UAAM;AAAA,EACV,OACK;AACD,UAAM,IAAI,UAAU,0BAAgB,KAAK,GAAGC,MAAK,CAAC;AAAA,EACtD;AACA,UAAQ,IAAI,mBAAmB;AAAA,IAC3B,KAAK;AACD,aAAO,gBAAgB,QAAQ;AAAA,IACnC,KAAK,QAAQ;AACT,aAAO,gBAAgB,MAAM;AAAA,IACjC;AAAA,IACA,KAAK,MAAM;AACP,YAAM,aAAa,wBAAc,GAAG;AACpC,aAAO,gBAAgB,MAAM,EAAE,WAAW,CAAC;AAAA,IAC/C;AAAA,IACA;AACI,YAAM,IAAI,iBAAiB,4BAA4B;AAAA,EAC/D;AACJ;AACO,IAAM,cAAc,CAAC,QAAQ,CAAC,SAAS,SAAS,SAAS,UAAU,MAAM,EAAE,SAAS,wBAAc,GAAG,CAAC;;;AG/D7G;AAAA,SAAS,aAAAC,kBAAiB;AAC1B,SAAS,aAAAC,YAAW,UAAU,gBAAgB;;;ACD9C;AACe,SAAR,SAA0BC,MAAK;AAClC,MAAI,EAAEA,gBAAe,eAAeA,KAAI,SAAS,GAAG;AAChD,UAAM,IAAI,WAAW,2CAA2C;AAAA,EACpE;AACJ;;;ADOA,IAAM,SAASC,WAAU,QAAQ;AACjC,SAAS,YAAY,KAAK,KAAK;AAC3B,MAAI,sBAAY,GAAG,GAAG;AAClB,WAAO,IAAI,OAAO;AAAA,EACtB;AACA,MAAI,eAAe,YAAY;AAC3B,WAAO;AAAA,EACX;AACA,MAAI,YAAY,GAAG,GAAG;AAClB,sBAAkB,KAAK,KAAK,cAAc,WAAW;AACrD,WAAOC,WAAU,KAAK,GAAG,EAAE,OAAO;AAAA,EACtC;AACA,QAAM,IAAI,UAAU,0BAAgB,KAAK,GAAGC,QAAO,YAAY,CAAC;AACpE;AACO,IAAM,UAAU,OAAO,KAAK,KAAK,KAAK,MAAM,MAAMC,OAAM,eAAO,IAAI,WAAW,EAAE,CAAC,MAAM;AAC1F,WAASA,IAAG;AACZ,QAAM,OAAO,IAAW,KAAKA,IAAG;AAChC,QAAM,SAAS,SAAS,IAAI,MAAM,IAAI,EAAE,GAAG,EAAE,KAAK;AAClD,QAAM,WAAW,YAAY,KAAK,GAAG;AACrC,QAAM,aAAa,MAAM,OAAO,UAAU,MAAM,KAAK,QAAQ,MAAM,IAAI,MAAM,GAAG,EAAE,CAAC,EAAE;AACrF,QAAM,eAAe,MAAM,KAAK,IAAI,MAAM,EAAE,GAAG,YAAY,GAAG;AAC9D,SAAO,EAAE,cAAc,KAAK,KAAK,OAAUA,IAAG,EAAE;AACpD;AACO,IAAMC,WAAU,OAAO,KAAK,KAAK,cAAc,KAAKD,SAAQ;AAC/D,WAASA,IAAG;AACZ,QAAM,OAAO,IAAW,KAAKA,IAAG;AAChC,QAAM,SAAS,SAAS,IAAI,MAAM,IAAI,EAAE,GAAG,EAAE,KAAK;AAClD,QAAM,WAAW,YAAY,KAAK,GAAG;AACrC,QAAM,aAAa,MAAM,OAAO,UAAU,MAAM,KAAK,QAAQ,MAAM,IAAI,MAAM,GAAG,EAAE,CAAC,EAAE;AACrF,SAAO,OAAO,IAAI,MAAM,EAAE,GAAG,YAAY,YAAY;AACzD;;;AE1CA;AAAA,SAAS,aAAAE,YAAW,eAAe,WAAW,sBAAsB;AACpE,SAAS,iBAAiB;;;ACD1B;AAAA,SAAS,aAAAC,kBAAiB;AAC1B,IAAO,2BAAQ,CAAC,KAAK,QAAQ;AACzB,MAAI;AACJ,MAAI;AACA,QAAI,eAAeA,YAAW;AAC1B,sBAAgB,IAAI,sBAAsB;AAAA,IAC9C,OACK;AACD,sBAAgB,OAAO,KAAK,IAAI,GAAG,WAAW,EAAE,cAAc;AAAA,IAClE;AAAA,EACJ,QACM;AAAA,EAAE;AACR,MAAI,OAAO,kBAAkB,YAAY,gBAAgB,MAAM;AAC3D,UAAM,IAAI,UAAU,GAAG,GAAG,uDAAuD;AAAA,EACrF;AACJ;;;ADPA,IAAM,WAAW,CAAC,KAAK,QAAQ;AAC3B,MAAI,IAAI,sBAAsB,OAAO;AACjC,UAAM,IAAI,UAAU,mEAAmE;AAAA,EAC3F;AACA,2BAAe,KAAK,GAAG;AAC3B;AACA,IAAM,SAAS,UAAU,MAAM,UAAU,mBAAmB,gGAAgG;AAC5J,IAAM,iBAAiB,CAAC,QAAQ;AAC5B,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO,UAAU;AAAA,IACrB,KAAK;AACD,aAAO,OAAO;AAAA,IAClB;AACI,aAAO;AAAA,EACf;AACJ;AACA,IAAM,kBAAkB,CAAC,QAAQ;AAC7B,UAAQ,KAAK;AAAA,IACT,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;AACA,SAASC,iBAAgB,KAAK,QAAQ,QAAQ;AAC1C,MAAI,sBAAY,GAAG,GAAG;AAClB,WAAO;AAAA,EACX;AACA,MAAI,YAAY,GAAG,GAAG;AAClB,sBAAkB,KAAK,KAAK,GAAG,MAAM;AACrC,WAAOC,WAAU,KAAK,GAAG;AAAA,EAC7B;AACA,QAAM,IAAI,UAAU,0BAAgB,KAAK,GAAGC,MAAK,CAAC;AACtD;AACO,IAAMC,WAAU,CAAC,KAAK,KAAK,QAAQ;AACtC,QAAM,UAAU,eAAe,GAAG;AAClC,QAAM,WAAW,gBAAgB,GAAG;AACpC,QAAM,YAAYH,iBAAgB,KAAK,KAAK,WAAW,SAAS;AAChE,WAAS,WAAW,GAAG;AACvB,SAAO,cAAc,EAAE,KAAK,WAAW,UAAU,QAAQ,GAAG,GAAG;AACnE;AACO,IAAMI,WAAU,CAAC,KAAK,KAAK,iBAAiB;AAC/C,QAAM,UAAU,eAAe,GAAG;AAClC,QAAM,WAAW,gBAAgB,GAAG;AACpC,QAAM,YAAYJ,iBAAgB,KAAK,KAAK,aAAa,SAAS;AAClE,WAAS,WAAW,GAAG;AACvB,SAAO,eAAe,EAAE,KAAK,WAAW,UAAU,QAAQ,GAAG,YAAY;AAC7E;;;AEjEA;AAAA,IAAO,wBAAQ,CAAC;;;ACAhB;AAEO,SAASK,WAAU,KAAK;AAC3B,UAAQ,KAAK;AAAA,IACT,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,iBAAiB,8BAA8B,GAAG,EAAE;AAAA,EACtE;AACJ;AACA,IAAO,cAAQ,CAAC,QAAQ,eAAO,IAAI,WAAWA,WAAU,GAAG,KAAK,CAAC,CAAC;;;ACnBlE;;;ACAA;AAAA,SAAS,kBAAkB,iBAAiB,aAAAC,kBAAiB;AAC7D,SAAS,UAAAC,eAAc;AAKvB,IAAM,gBAAgB,CAAC,SAAS,WAAW,QAAQ;AAC/C,MAAI;AACJ,MAAI,YAAY,GAAG,GAAG;AAClB,QAAI,CAAC,IAAI,aAAa;AAClB,YAAM,IAAI,UAAU,8BAA8B;AAAA,IACtD;AACA,gBAAYC,WAAU,KAAK,GAAG;AAAA,EAClC,WACS,sBAAY,GAAG,GAAG;AACvB,gBAAY;AAAA,EAChB,OACK;AACD,UAAM,IAAI,UAAU,0BAAgB,KAAK,GAAGC,MAAK,CAAC;AAAA,EACtD;AACA,MAAI,UAAU,SAAS,SAAS;AAC5B,UAAM,IAAI,UAAU,gBAAgB,OAAO,MAAM;AAAA,EACrD;AACA,SAAO,UAAU,OAAO,EAAE,QAAQ,OAAO,MAAM,UAAU,CAAC;AAC9D;AACO,IAAM,SAAS,CAAC,QAAQ;AAC3B,SAAO,cAAc,UAAU,QAAQ,GAAG;AAC9C;AACO,IAAM,UAAU,CAAC,QAAQ;AAC5B,SAAO,cAAc,WAAW,SAAS,GAAG;AAChD;AACO,IAAM,YAAY,CAAC,QAAQ,iBAAiB;AAAA,EAC/C,KAAKC,QAAO,KAAK,IAAI,QAAQ,+CAA+C,EAAE,GAAG,QAAQ;AAAA,EACzF,MAAM;AAAA,EACN,QAAQ;AACZ,CAAC;AACM,IAAM,WAAW,CAAC,QAAQ,gBAAgB;AAAA,EAC7C,KAAKA,QAAO,KAAK,IAAI,QAAQ,8CAA8C,EAAE,GAAG,QAAQ;AAAA,EACxF,MAAM;AAAA,EACN,QAAQ;AACZ,CAAC;AACM,IAAM,WAAW,CAAC,QAAQ,gBAAgB;AAAA,EAC7C,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AACZ,CAAC;;;AC7CD;AAAA,SAAS,oBAAAC,mBAAkB,mBAAAC,wBAAuB;AAClD,IAAM,QAAQ,CAAC,QAAQ;AACnB,MAAI,IAAI,GAAG;AACP,WAAOD,kBAAiB,EAAE,QAAQ,OAAO,IAAI,CAAC;AAAA,EAClD;AACA,SAAOC,iBAAgB,EAAE,QAAQ,OAAO,IAAI,CAAC;AACjD;AACA,IAAO,qBAAQ;;;AFFf,eAAsB,WAAW,MAAM,KAAK,SAAS;AACjD,MAAI,OAAO,SAAS,YAAY,KAAK,QAAQ,4BAA4B,MAAM,GAAG;AAC9E,UAAM,IAAI,UAAU,sCAAsC;AAAA,EAC9D;AACA,SAAO,SAAS,MAAM,KAAK,OAAO;AACtC;AACA,eAAsB,WAAW,MAAM,KAAK,SAAS;AACjD,MAAI,OAAO,SAAS,YAAY,KAAK,QAAQ,6BAA6B,MAAM,GAAG;AAC/E,UAAM,IAAI,UAAU,uCAAuC;AAAA,EAC/D;AACA,SAAO,SAAS,MAAM,KAAK,OAAO;AACtC;AACA,eAAsB,YAAY,OAAO,KAAK,SAAS;AACnD,MAAI,OAAO,UAAU,YAAY,MAAM,QAAQ,6BAA6B,MAAM,GAAG;AACjF,UAAM,IAAI,UAAU,yCAAyC;AAAA,EACjE;AACA,SAAO,UAAU,OAAO,KAAK,OAAO;AACxC;AACA,eAAsB,UAAU,KAAK,KAAK;AACtC,MAAI,CAAC,SAAS,GAAG,GAAG;AAChB,UAAM,IAAI,UAAU,uBAAuB;AAAA,EAC/C;AACA,UAAQ,IAAI;AACZ,UAAQ,IAAI,KAAK;AAAA,IACb,KAAK;AACD,UAAI,OAAO,IAAI,MAAM,YAAY,CAAC,IAAI,GAAG;AACrC,cAAM,IAAI,UAAU,yCAAyC;AAAA,MACjE;AACA,aAAO,OAAgB,IAAI,CAAC;AAAA,IAChC,KAAK;AACD,UAAI,SAAS,OAAO,IAAI,QAAQ,QAAW;AACvC,cAAM,IAAI,iBAAiB,oEAAoE;AAAA,MACnG;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO,mBAAY,EAAE,GAAG,KAAK,IAAI,CAAC;AAAA,IACtC;AACI,YAAM,IAAI,iBAAiB,8CAA8C;AAAA,EACjF;AACJ;;;AG5CA;AAGA,IAAM,MAAM,CAAC,QAAQ,MAAM,OAAO,WAAW;AAC7C,IAAM,eAAe,CAAC,KAAK,KAAK,UAAU;AACtC,MAAI,IAAI,QAAQ,UAAa,IAAI,QAAQ,OAAO;AAC5C,UAAM,IAAI,UAAU,kEAAkE;AAAA,EAC1F;AACA,MAAI,IAAI,YAAY,UAAa,IAAI,QAAQ,WAAW,KAAK,MAAM,MAAM;AACrE,UAAM,IAAI,UAAU,yEAAyE,KAAK,EAAE;AAAA,EACxG;AACA,MAAI,IAAI,QAAQ,UAAa,IAAI,QAAQ,KAAK;AAC1C,UAAM,IAAI,UAAU,gEAAgE,GAAG,EAAE;AAAA,EAC7F;AACA,SAAO;AACX;AACA,IAAM,qBAAqB,CAAC,KAAK,KAAK,OAAO,aAAa;AACtD,MAAI,eAAe;AACf;AACJ,MAAI,YAAgB,MAAM,GAAG,GAAG;AAC5B,QAAQ,YAAY,GAAG,KAAK,aAAa,KAAK,KAAK,KAAK;AACpD;AACJ,UAAM,IAAI,UAAU,yHAAyH;AAAA,EACjJ;AACA,MAAI,CAAC,oBAAU,GAAG,GAAG;AACjB,UAAM,IAAI,UAAU,QAAgB,KAAK,KAAK,GAAGC,QAAO,cAAc,WAAW,iBAAiB,IAAI,CAAC;AAAA,EAC3G;AACA,MAAI,IAAI,SAAS,UAAU;AACvB,UAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,8DAA8D;AAAA,EACjG;AACJ;AACA,IAAM,sBAAsB,CAAC,KAAK,KAAK,OAAO,aAAa;AACvD,MAAI,YAAgB,MAAM,GAAG,GAAG;AAC5B,YAAQ,OAAO;AAAA,MACX,KAAK;AACD,YAAQ,aAAa,GAAG,KAAK,aAAa,KAAK,KAAK,KAAK;AACrD;AACJ,cAAM,IAAI,UAAU,kDAAkD;AAAA,MAC1E,KAAK;AACD,YAAQ,YAAY,GAAG,KAAK,aAAa,KAAK,KAAK,KAAK;AACpD;AACJ,cAAM,IAAI,UAAU,iDAAiD;AAAA,IAC7E;AAAA,EACJ;AACA,MAAI,CAAC,oBAAU,GAAG,GAAG;AACjB,UAAM,IAAI,UAAU,QAAgB,KAAK,KAAK,GAAGA,QAAO,WAAW,iBAAiB,IAAI,CAAC;AAAA,EAC7F;AACA,MAAI,IAAI,SAAS,UAAU;AACvB,UAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,mEAAmE;AAAA,EACtG;AACA,MAAI,UAAU,UAAU,IAAI,SAAS,UAAU;AAC3C,UAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,uEAAuE;AAAA,EAC1G;AACA,MAAI,UAAU,aAAa,IAAI,SAAS,UAAU;AAC9C,UAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,0EAA0E;AAAA,EAC7G;AACA,MAAI,IAAI,aAAa,UAAU,YAAY,IAAI,SAAS,WAAW;AAC/D,UAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,wEAAwE;AAAA,EAC3G;AACA,MAAI,IAAI,aAAa,UAAU,aAAa,IAAI,SAAS,WAAW;AAChE,UAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,yEAAyE;AAAA,EAC5G;AACJ;AACA,SAAS,aAAa,UAAU,KAAK,KAAK,OAAO;AAC7C,QAAM,YAAY,IAAI,WAAW,IAAI,KACjC,QAAQ,SACR,IAAI,WAAW,OAAO,KACtB,qBAAqB,KAAK,GAAG;AACjC,MAAI,WAAW;AACX,uBAAmB,KAAK,KAAK,OAAO,QAAQ;AAAA,EAChD,OACK;AACD,wBAAoB,KAAK,KAAK,OAAO,QAAQ;AAAA,EACjD;AACJ;AACA,IAAO,yBAAQ,aAAa,KAAK,QAAW,KAAK;AAC1C,IAAM,sBAAsB,aAAa,KAAK,QAAW,IAAI;;;AC5EpE;;;ACAA;AAAA,SAAS,kBAAAC,iBAAgB,aAAAC,kBAAiB;AAa1C,SAAS,WAAW,KAAK,WAAW,KAAK,IAAI,KAAK;AAC9C,QAAM,UAAU,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AAC5C,MAAI,sBAAY,GAAG,GAAG;AAClB,UAAM,IAAI,OAAO;AAAA,EACrB;AACA,QAAM,SAAS,IAAI,SAAS,WAAW,CAAC;AACxC,QAAM,SAAS,IAAI,SAAS,GAAG,WAAW,CAAC;AAC3C,QAAM,YAAY,OAAO,OAAO;AAChC,MAAI,CAAC,gBAAU,SAAS,GAAG;AACvB,UAAM,IAAI,iBAAiB,OAAO,GAAG,8CAA8C;AAAA,EACvF;AACA,QAAM,SAASC,gBAAe,WAAW,QAAQ,EAAE;AACnD,QAAM,aAAa,OAAO,OAAO,OAAO,SAAS,GAAG,OAAO,MAAM,CAAC;AAClE,QAAM,UAAU,SAAS,IAAI,MAAM,EAAE,GAAG,EAAE;AAC1C,QAAMC,OAAM,OAAO,KAAK,IAAI,YAAY,SAAS,QAAQ,OAAO;AAChE,SAAO,EAAE,YAAY,KAAAA,MAAK,GAAG;AACjC;AACA,SAAS,WAAW,KAAK,WAAW,KAAK,IAAI,KAAK;AAC9C,QAAM,UAAU,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AAC5C,QAAM,YAAY,OAAO,OAAO;AAChC,MAAI,CAAC,gBAAU,SAAS,GAAG;AACvB,UAAM,IAAI,iBAAiB,OAAO,GAAG,8CAA8C;AAAA,EACvF;AACA,QAAM,SAASD,gBAAe,WAAW,KAAK,IAAI,EAAE,eAAe,GAAG,CAAC;AACvE,MAAI,IAAI,YAAY;AAChB,WAAO,OAAO,KAAK,EAAE,iBAAiB,UAAU,OAAO,CAAC;AAAA,EAC5D;AACA,QAAM,aAAa,OAAO,OAAO,SAAS;AAC1C,SAAO,MAAM;AACb,QAAMC,OAAM,OAAO,WAAW;AAC9B,SAAO,EAAE,YAAY,KAAAA,MAAK,GAAG;AACjC;AACA,IAAMC,WAAU,CAAC,KAAK,WAAW,KAAK,IAAI,QAAQ;AAC9C,MAAI;AACJ,MAAI,YAAY,GAAG,GAAG;AAClB,sBAAkB,KAAK,KAAK,SAAS;AACrC,UAAMC,WAAU,KAAK,GAAG;AAAA,EAC5B,WACS,eAAe,cAAc,sBAAY,GAAG,GAAG;AACpD,UAAM;AAAA,EACV,OACK;AACD,UAAM,IAAI,UAAU,0BAAgB,KAAK,GAAGC,QAAO,YAAY,CAAC;AAAA,EACpE;AACA,2BAAe,KAAK,GAAG;AACvB,MAAI,IAAI;AACJ,4BAAc,KAAK,EAAE;AAAA,EACzB,OACK;AACD,SAAK,WAAW,GAAG;AAAA,EACvB;AACA,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO,WAAW,KAAK,WAAW,KAAK,IAAI,GAAG;AAAA,IAClD,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO,WAAW,KAAK,WAAW,KAAK,IAAI,GAAG;AAAA,IAClD;AACI,YAAM,IAAI,iBAAiB,8CAA8C;AAAA,EACjF;AACJ;AACA,IAAO,kBAAQF;;;AD1Ef,eAAsBG,MAAK,KAAK,KAAK,KAAK,IAAI;AAC1C,QAAM,eAAe,IAAI,MAAM,GAAG,CAAC;AACnC,QAAM,UAAU,MAAM,gBAAQ,cAAc,KAAK,KAAK,IAAI,IAAI,WAAW,CAAC,CAAC;AAC3E,SAAO;AAAA,IACH,cAAc,QAAQ;AAAA,IACtB,IAAI,OAAU,QAAQ,EAAE;AAAA,IACxB,KAAK,OAAU,QAAQ,GAAG;AAAA,EAC9B;AACJ;AACA,eAAsBC,QAAO,KAAK,KAAK,cAAc,IAAIC,MAAK;AAC1D,QAAM,eAAe,IAAI,MAAM,GAAG,CAAC;AACnC,SAAO,gBAAQ,cAAc,KAAK,cAAc,IAAIA,MAAK,IAAI,WAAW,CAAC,CAAC;AAC9E;;;AfHA,eAAe,qBAAqB,KAAK,KAAK,cAAc,YAAY,SAAS;AAC7E,yBAAa,KAAK,KAAK,SAAS;AAChC,QAAO,MAAM,sBAAU,sBAAsB,KAAK,GAAG,KAAM;AAC3D,UAAQ,KAAK;AAAA,IACT,KAAK,OAAO;AACR,UAAI,iBAAiB;AACjB,cAAM,IAAI,WAAW,0CAA0C;AACnE,aAAO;AAAA,IACX;AAAA,IACA,KAAK;AACD,UAAI,iBAAiB;AACjB,cAAM,IAAI,WAAW,0CAA0C;AAAA,IACvE,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,kBAAkB;AACnB,UAAI,CAAC,SAAS,WAAW,GAAG;AACxB,cAAM,IAAI,WAAW,6DAA6D;AACtF,UAAI,CAAM,YAAY,GAAG;AACrB,cAAM,IAAI,iBAAiB,uFAAuF;AACtH,YAAM,MAAM,MAAM,UAAU,WAAW,KAAK,GAAG;AAC/C,UAAI;AACJ,UAAI;AACJ,UAAI,WAAW,QAAQ,QAAW;AAC9B,YAAI,OAAO,WAAW,QAAQ;AAC1B,gBAAM,IAAI,WAAW,kDAAkD;AAC3E,YAAI;AACA,uBAAa,OAAU,WAAW,GAAG;AAAA,QACzC,QACM;AACF,gBAAM,IAAI,WAAW,oCAAoC;AAAA,QAC7D;AAAA,MACJ;AACA,UAAI,WAAW,QAAQ,QAAW;AAC9B,YAAI,OAAO,WAAW,QAAQ;AAC1B,gBAAM,IAAI,WAAW,kDAAkD;AAC3E,YAAI;AACA,uBAAa,OAAU,WAAW,GAAG;AAAA,QACzC,QACM;AACF,gBAAM,IAAI,WAAW,oCAAoC;AAAA,QAC7D;AAAA,MACJ;AACA,YAAM,eAAe,MAAW,UAAU,KAAK,KAAK,QAAQ,YAAY,WAAW,MAAM,KAAK,QAAQ,YAAYC,WAAU,WAAW,GAAG,IAAI,SAAS,IAAI,MAAM,IAAI,EAAE,GAAG,EAAE,GAAG,YAAY,UAAU;AACrM,UAAI,QAAQ;AACR,eAAO;AACX,UAAI,iBAAiB;AACjB,cAAM,IAAI,WAAW,2BAA2B;AACpD,aAAO,OAAM,IAAI,MAAM,EAAE,GAAG,cAAc,YAAY;AAAA,IAC1D;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,gBAAgB;AACjB,UAAI,iBAAiB;AACjB,cAAM,IAAI,WAAW,2BAA2B;AACpD,aAAOC,SAAM,KAAK,KAAK,YAAY;AAAA,IACvC;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,sBAAsB;AACvB,UAAI,iBAAiB;AACjB,cAAM,IAAI,WAAW,2BAA2B;AACpD,UAAI,OAAO,WAAW,QAAQ;AAC1B,cAAM,IAAI,WAAW,oDAAoD;AAC7E,YAAM,WAAW,SAAS,iBAAiB;AAC3C,UAAI,WAAW,MAAM;AACjB,cAAM,IAAI,WAAW,6DAA6D;AACtF,UAAI,OAAO,WAAW,QAAQ;AAC1B,cAAM,IAAI,WAAW,mDAAmD;AAC5E,UAAIC;AACJ,UAAI;AACA,QAAAA,OAAM,OAAU,WAAW,GAAG;AAAA,MAClC,QACM;AACF,cAAM,IAAI,WAAW,oCAAoC;AAAA,MAC7D;AACA,aAAOD,SAAQ,KAAK,KAAK,cAAc,WAAW,KAAKC,IAAG;AAAA,IAC9D;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,UAAU;AACX,UAAI,iBAAiB;AACjB,cAAM,IAAI,WAAW,2BAA2B;AACpD,aAAO,OAAM,KAAK,KAAK,YAAY;AAAA,IACvC;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,aAAa;AACd,UAAI,iBAAiB;AACjB,cAAM,IAAI,WAAW,2BAA2B;AACpD,UAAI,OAAO,WAAW,OAAO;AACzB,cAAM,IAAI,WAAW,6DAA6D;AACtF,UAAI,OAAO,WAAW,QAAQ;AAC1B,cAAM,IAAI,WAAW,2DAA2D;AACpF,UAAI;AACJ,UAAI;AACA,aAAK,OAAU,WAAW,EAAE;AAAA,MAChC,QACM;AACF,cAAM,IAAI,WAAW,mCAAmC;AAAA,MAC5D;AACA,UAAIC;AACJ,UAAI;AACA,QAAAA,OAAM,OAAU,WAAW,GAAG;AAAA,MAClC,QACM;AACF,cAAM,IAAI,WAAW,oCAAoC;AAAA,MAC7D;AACA,aAAOC,QAAS,KAAK,KAAK,cAAc,IAAID,IAAG;AAAA,IACnD;AAAA,IACA,SAAS;AACL,YAAM,IAAI,iBAAiB,2DAA2D;AAAA,IAC1F;AAAA,EACJ;AACJ;AACA,IAAO,iCAAQ;;;AiBhIf;AACA,SAAS,aAAa,KAAK,mBAAmB,kBAAkB,iBAAiB,YAAY;AACzF,MAAI,WAAW,SAAS,UAAa,iBAAiB,SAAS,QAAW;AACtE,UAAM,IAAI,IAAI,gEAAgE;AAAA,EAClF;AACA,MAAI,CAAC,mBAAmB,gBAAgB,SAAS,QAAW;AACxD,WAAO,oBAAI,IAAI;AAAA,EACnB;AACA,MAAI,CAAC,MAAM,QAAQ,gBAAgB,IAAI,KACnC,gBAAgB,KAAK,WAAW,KAChC,gBAAgB,KAAK,KAAK,CAAC,UAAU,OAAO,UAAU,YAAY,MAAM,WAAW,CAAC,GAAG;AACvF,UAAM,IAAI,IAAI,uFAAuF;AAAA,EACzG;AACA,MAAI;AACJ,MAAI,qBAAqB,QAAW;AAChC,iBAAa,IAAI,IAAI,CAAC,GAAG,OAAO,QAAQ,gBAAgB,GAAG,GAAG,kBAAkB,QAAQ,CAAC,CAAC;AAAA,EAC9F,OACK;AACD,iBAAa;AAAA,EACjB;AACA,aAAW,aAAa,gBAAgB,MAAM;AAC1C,QAAI,CAAC,WAAW,IAAI,SAAS,GAAG;AAC5B,YAAM,IAAI,iBAAiB,+BAA+B,SAAS,qBAAqB;AAAA,IAC5F;AACA,QAAI,WAAW,SAAS,MAAM,QAAW;AACrC,YAAM,IAAI,IAAI,+BAA+B,SAAS,cAAc;AAAA,IACxE;AACA,QAAI,WAAW,IAAI,SAAS,KAAK,gBAAgB,SAAS,MAAM,QAAW;AACvE,YAAM,IAAI,IAAI,+BAA+B,SAAS,+BAA+B;AAAA,IACzF;AAAA,EACJ;AACA,SAAO,IAAI,IAAI,gBAAgB,IAAI;AACvC;AACA,IAAO,wBAAQ;;;ACjCf;AAAA,IAAM,qBAAqB,CAAC,QAAQ,eAAe;AAC/C,MAAI,eAAe,WACd,CAAC,MAAM,QAAQ,UAAU,KAAK,WAAW,KAAK,CAAC,MAAM,OAAO,MAAM,QAAQ,IAAI;AAC/E,UAAM,IAAI,UAAU,IAAI,MAAM,sCAAsC;AAAA,EACxE;AACA,MAAI,CAAC,YAAY;AACb,WAAO;AAAA,EACX;AACA,SAAO,IAAI,IAAI,UAAU;AAC7B;AACA,IAAO,8BAAQ;;;AtCAf,eAAsB,iBAAiB,KAAK,KAAK,SAAS;AACtD,MAAI,CAAC,SAAS,GAAG,GAAG;AAChB,UAAM,IAAI,WAAW,iCAAiC;AAAA,EAC1D;AACA,MAAI,IAAI,cAAc,UAAa,IAAI,WAAW,UAAa,IAAI,gBAAgB,QAAW;AAC1F,UAAM,IAAI,WAAW,qBAAqB;AAAA,EAC9C;AACA,MAAI,IAAI,OAAO,UAAa,OAAO,IAAI,OAAO,UAAU;AACpD,UAAM,IAAI,WAAW,0CAA0C;AAAA,EACnE;AACA,MAAI,OAAO,IAAI,eAAe,UAAU;AACpC,UAAM,IAAI,WAAW,0CAA0C;AAAA,EACnE;AACA,MAAI,IAAI,QAAQ,UAAa,OAAO,IAAI,QAAQ,UAAU;AACtD,UAAM,IAAI,WAAW,uCAAuC;AAAA,EAChE;AACA,MAAI,IAAI,cAAc,UAAa,OAAO,IAAI,cAAc,UAAU;AAClE,UAAM,IAAI,WAAW,qCAAqC;AAAA,EAC9D;AACA,MAAI,IAAI,kBAAkB,UAAa,OAAO,IAAI,kBAAkB,UAAU;AAC1E,UAAM,IAAI,WAAW,kCAAkC;AAAA,EAC3D;AACA,MAAI,IAAI,QAAQ,UAAa,OAAO,IAAI,QAAQ,UAAU;AACtD,UAAM,IAAI,WAAW,wBAAwB;AAAA,EACjD;AACA,MAAI,IAAI,WAAW,UAAa,CAAC,SAAS,IAAI,MAAM,GAAG;AACnD,UAAM,IAAI,WAAW,8CAA8C;AAAA,EACvE;AACA,MAAI,IAAI,gBAAgB,UAAa,CAAC,SAAS,IAAI,WAAW,GAAG;AAC7D,UAAM,IAAI,WAAW,qDAAqD;AAAA,EAC9E;AACA,MAAI;AACJ,MAAI,IAAI,WAAW;AACf,QAAI;AACA,YAAME,mBAAkB,OAAU,IAAI,SAAS;AAC/C,mBAAa,KAAK,MAAM,QAAQ,OAAOA,gBAAe,CAAC;AAAA,IAC3D,QACM;AACF,YAAM,IAAI,WAAW,iCAAiC;AAAA,IAC1D;AAAA,EACJ;AACA,MAAI,CAAC,oBAAW,YAAY,IAAI,QAAQ,IAAI,WAAW,GAAG;AACtD,UAAM,IAAI,WAAW,kHAAkH;AAAA,EAC3I;AACA,QAAM,aAAa;AAAA,IACf,GAAG;AAAA,IACH,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACX;AACA,wBAAa,YAAY,oBAAI,IAAI,GAAG,SAAS,MAAM,YAAY,UAAU;AACzE,MAAI,WAAW,QAAQ,QAAW;AAC9B,UAAM,IAAI,iBAAiB,sEAAsE;AAAA,EACrG;AACA,QAAM,EAAE,KAAK,IAAI,IAAI;AACrB,MAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;AACjC,UAAM,IAAI,WAAW,2CAA2C;AAAA,EACpE;AACA,MAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;AACjC,UAAM,IAAI,WAAW,sDAAsD;AAAA,EAC/E;AACA,QAAM,0BAA0B,WAAW,4BAAmB,2BAA2B,QAAQ,uBAAuB;AACxH,QAAM,8BAA8B,WAChC,4BAAmB,+BAA+B,QAAQ,2BAA2B;AACzF,MAAK,2BAA2B,CAAC,wBAAwB,IAAI,GAAG,KAC3D,CAAC,2BAA2B,IAAI,WAAW,OAAO,GAAI;AACvD,UAAM,IAAI,kBAAkB,sDAAsD;AAAA,EACtF;AACA,MAAI,+BAA+B,CAAC,4BAA4B,IAAI,GAAG,GAAG;AACtE,UAAM,IAAI,kBAAkB,iEAAiE;AAAA,EACjG;AACA,MAAI;AACJ,MAAI,IAAI,kBAAkB,QAAW;AACjC,QAAI;AACA,qBAAe,OAAU,IAAI,aAAa;AAAA,IAC9C,QACM;AACF,YAAM,IAAI,WAAW,8CAA8C;AAAA,IACvE;AAAA,EACJ;AACA,MAAI,cAAc;AAClB,MAAI,OAAO,QAAQ,YAAY;AAC3B,UAAM,MAAM,IAAI,YAAY,GAAG;AAC/B,kBAAc;AAAA,EAClB;AACA,MAAI;AACJ,MAAI;AACA,UAAM,MAAM,+BAAqB,KAAK,KAAK,cAAc,YAAY,OAAO;AAAA,EAChF,SACO,KAAK;AACR,QAAI,eAAe,aAAa,eAAe,cAAc,eAAe,kBAAkB;AAC1F,YAAM;AAAA,IACV;AACA,UAAM,YAAY,GAAG;AAAA,EACzB;AACA,MAAI;AACJ,MAAIC;AACJ,MAAI,IAAI,OAAO,QAAW;AACtB,QAAI;AACA,WAAK,OAAU,IAAI,EAAE;AAAA,IACzB,QACM;AACF,YAAM,IAAI,WAAW,mCAAmC;AAAA,IAC5D;AAAA,EACJ;AACA,MAAI,IAAI,QAAQ,QAAW;AACvB,QAAI;AACA,MAAAA,OAAM,OAAU,IAAI,GAAG;AAAA,IAC3B,QACM;AACF,YAAM,IAAI,WAAW,oCAAoC;AAAA,IAC7D;AAAA,EACJ;AACA,QAAM,kBAAkB,QAAQ,OAAO,IAAI,aAAa,EAAE;AAC1D,MAAI;AACJ,MAAI,IAAI,QAAQ,QAAW;AACvB,qBAAiB,OAAO,iBAAiB,QAAQ,OAAO,GAAG,GAAG,QAAQ,OAAO,IAAI,GAAG,CAAC;AAAA,EACzF,OACK;AACD,qBAAiB;AAAA,EACrB;AACA,MAAI;AACJ,MAAI;AACA,iBAAa,OAAU,IAAI,UAAU;AAAA,EACzC,QACM;AACF,UAAM,IAAI,WAAW,2CAA2C;AAAA,EACpE;AACA,QAAM,YAAY,MAAM,gBAAQ,KAAK,KAAK,YAAY,IAAIA,MAAK,cAAc;AAC7E,QAAM,SAAS,EAAE,UAAU;AAC3B,MAAI,IAAI,cAAc,QAAW;AAC7B,WAAO,kBAAkB;AAAA,EAC7B;AACA,MAAI,IAAI,QAAQ,QAAW;AACvB,QAAI;AACA,aAAO,8BAA8B,OAAU,IAAI,GAAG;AAAA,IAC1D,QACM;AACF,YAAM,IAAI,WAAW,oCAAoC;AAAA,IAC7D;AAAA,EACJ;AACA,MAAI,IAAI,gBAAgB,QAAW;AAC/B,WAAO,0BAA0B,IAAI;AAAA,EACzC;AACA,MAAI,IAAI,WAAW,QAAW;AAC1B,WAAO,oBAAoB,IAAI;AAAA,EACnC;AACA,MAAI,aAAa;AACb,WAAO,EAAE,GAAG,QAAQ,IAAI;AAAA,EAC5B;AACA,SAAO;AACX;;;AD7JA,eAAsB,eAAe,KAAK,KAAK,SAAS;AACpD,MAAI,eAAe,YAAY;AAC3B,UAAM,QAAQ,OAAO,GAAG;AAAA,EAC5B;AACA,MAAI,OAAO,QAAQ,UAAU;AACzB,UAAM,IAAI,WAAW,4CAA4C;AAAA,EACrE;AACA,QAAM,EAAE,GAAG,iBAAiB,GAAG,cAAc,GAAG,IAAI,GAAG,YAAY,GAAGC,MAAK,OAAQ,IAAI,IAAI,MAAM,GAAG;AACpG,MAAI,WAAW,GAAG;AACd,UAAM,IAAI,WAAW,qBAAqB;AAAA,EAC9C;AACA,QAAM,YAAY,MAAM,iBAAiB;AAAA,IACrC;AAAA,IACA,IAAI,MAAM;AAAA,IACV,WAAW;AAAA,IACX,KAAKA,QAAO;AAAA,IACZ,eAAe,gBAAgB;AAAA,EACnC,GAAG,KAAK,OAAO;AACf,QAAM,SAAS,EAAE,WAAW,UAAU,WAAW,iBAAiB,UAAU,gBAAgB;AAC5F,MAAI,OAAO,QAAQ,YAAY;AAC3B,WAAO,EAAE,GAAG,QAAQ,KAAK,UAAU,IAAI;AAAA,EAC3C;AACA,SAAO;AACX;;;AwC1BA;AAGA,eAAsB,eAAe,KAAK,KAAK,SAAS;AACpD,MAAI,CAAC,SAAS,GAAG,GAAG;AAChB,UAAM,IAAI,WAAW,+BAA+B;AAAA,EACxD;AACA,MAAI,CAAC,MAAM,QAAQ,IAAI,UAAU,KAAK,CAAC,IAAI,WAAW,MAAM,QAAQ,GAAG;AACnE,UAAM,IAAI,WAAW,0CAA0C;AAAA,EACnE;AACA,MAAI,CAAC,IAAI,WAAW,QAAQ;AACxB,UAAM,IAAI,WAAW,+BAA+B;AAAA,EACxD;AACA,aAAW,aAAa,IAAI,YAAY;AACpC,QAAI;AACA,aAAO,MAAM,iBAAiB;AAAA,QAC1B,KAAK,IAAI;AAAA,QACT,YAAY,IAAI;AAAA,QAChB,eAAe,UAAU;AAAA,QACzB,QAAQ,UAAU;AAAA,QAClB,IAAI,IAAI;AAAA,QACR,WAAW,IAAI;AAAA,QACf,KAAK,IAAI;AAAA,QACT,aAAa,IAAI;AAAA,MACrB,GAAG,KAAK,OAAO;AAAA,IACnB,QACM;AAAA,IACN;AAAA,EACJ;AACA,QAAM,IAAI,oBAAoB;AAClC;;;AC9BA;;;ACAA;;;ACAA;AAAO,IAAM,cAAc,OAAO;;;ACAlC;;;ACAA;;;ACAA;AAAA,SAAS,aAAAC,mBAAiB;AAO1B,IAAM,WAAW,CAAC,QAAQ;AACtB,MAAI;AACJ,MAAI,YAAY,GAAG,GAAG;AAClB,QAAI,CAAC,IAAI,aAAa;AAClB,YAAM,IAAI,UAAU,8BAA8B;AAAA,IACtD;AACA,gBAAYC,YAAU,KAAK,GAAG;AAAA,EAClC,WACS,sBAAY,GAAG,GAAG;AACvB,gBAAY;AAAA,EAChB,WACS,eAAe,YAAY;AAChC,WAAO;AAAA,MACH,KAAK;AAAA,MACL,GAAG,OAAU,GAAG;AAAA,IACpB;AAAA,EACJ,OACK;AACD,UAAM,IAAI,UAAU,0BAAgB,KAAK,GAAGC,QAAO,YAAY,CAAC;AAAA,EACpE;AACA,MAAI,UAAU,SAAS,YACnB,CAAC,CAAC,OAAO,MAAM,WAAW,UAAU,SAAS,MAAM,EAAE,SAAS,UAAU,iBAAiB,GAAG;AAC5F,UAAM,IAAI,iBAAiB,mCAAmC;AAAA,EAClE;AACA,SAAO,UAAU,OAAO,EAAE,QAAQ,MAAM,CAAC;AAC7C;AACA,IAAO,qBAAQ;;;AD9Bf,eAAsB,WAAW,KAAK;AAClC,SAAO,OAAa,GAAG;AAC3B;AACA,eAAsB,YAAY,KAAK;AACnC,SAAO,QAAc,GAAG;AAC5B;AACA,eAAsB,UAAU,KAAK;AACjC,SAAO,mBAAS,GAAG;AACvB;;;ADAA,eAAe,qBAAqB,KAAK,KAAK,KAAK,aAAa,qBAAqB,CAAC,GAAG;AACrF,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,yBAAa,KAAK,KAAK,SAAS;AAChC,QAAO,MAAM,sBAAU,qBAAqB,KAAK,GAAG,KAAM;AAC1D,UAAQ,KAAK;AAAA,IACT,KAAK,OAAO;AACR,YAAM;AACN;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,kBAAkB;AACnB,UAAI,CAAM,YAAY,GAAG,GAAG;AACxB,cAAM,IAAI,iBAAiB,uFAAuF;AAAA,MACtH;AACA,YAAM,EAAE,KAAK,IAAI,IAAI;AACrB,UAAI,EAAE,KAAK,aAAa,IAAI;AAC5B,wBAAkB,MAAW,YAAY,GAAG,GAAG;AAC/C,YAAM,EAAE,GAAG,GAAG,KAAK,IAAI,IAAI,MAAM,UAAU,YAAY;AACvD,YAAM,eAAe,MAAW,UAAU,KAAK,cAAc,QAAQ,YAAY,MAAM,KAAK,QAAQ,YAAYC,WAAU,GAAG,IAAI,SAAS,IAAI,MAAM,IAAI,EAAE,GAAG,EAAE,GAAG,KAAK,GAAG;AAC1K,mBAAa,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI,EAAE;AACpC,UAAI,QAAQ;AACR,mBAAW,IAAI,IAAI;AACvB,UAAI;AACA,mBAAW,MAAM,OAAU,GAAG;AAClC,UAAI;AACA,mBAAW,MAAM,OAAU,GAAG;AAClC,UAAI,QAAQ,WAAW;AACnB,cAAM;AACN;AAAA,MACJ;AACA,YAAM,eAAe,YAAY,GAAG;AACpC,YAAM,QAAQ,IAAI,MAAM,EAAE;AAC1B,qBAAe,MAAM,KAAM,OAAO,cAAc,GAAG;AACnD;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,gBAAgB;AACjB,YAAM,eAAe,YAAY,GAAG;AACpC,qBAAe,MAAMC,SAAM,KAAK,KAAK,GAAG;AACxC;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,sBAAsB;AACvB,YAAM,eAAe,YAAY,GAAG;AACpC,YAAM,EAAE,KAAK,KAAAC,KAAI,IAAI;AACrB,OAAC,EAAE,cAAc,GAAG,WAAW,IAAI,MAAM,QAAQ,KAAK,KAAK,KAAK,KAAKA,IAAG;AACxE;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,UAAU;AACX,YAAM,eAAe,YAAY,GAAG;AACpC,qBAAe,MAAM,KAAM,KAAK,KAAK,GAAG;AACxC;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,aAAa;AACd,YAAM,eAAe,YAAY,GAAG;AACpC,YAAM,EAAE,GAAG,IAAI;AACf,OAAC,EAAE,cAAc,GAAG,WAAW,IAAI,MAAMC,MAAS,KAAK,KAAK,KAAK,EAAE;AACnE;AAAA,IACJ;AAAA,IACA,SAAS;AACL,YAAM,IAAI,iBAAiB,2DAA2D;AAAA,IAC1F;AAAA,EACJ;AACA,SAAO,EAAE,KAAK,cAAc,WAAW;AAC3C;AACA,IAAO,iCAAQ;;;AFhFR,IAAM,mBAAN,MAAuB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,WAAW;AACnB,QAAI,EAAE,qBAAqB,aAAa;AACpC,YAAM,IAAI,UAAU,6CAA6C;AAAA,IACrE;AACA,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,2BAA2B,YAAY;AACnC,QAAI,KAAK,0BAA0B;AAC/B,YAAM,IAAI,UAAU,oDAAoD;AAAA,IAC5E;AACA,SAAK,2BAA2B;AAChC,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB,iBAAiB;AAChC,QAAI,KAAK,kBAAkB;AACvB,YAAM,IAAI,UAAU,4CAA4C;AAAA,IACpE;AACA,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACX;AAAA,EACA,2BAA2B,yBAAyB;AAChD,QAAI,KAAK,0BAA0B;AAC/B,YAAM,IAAI,UAAU,oDAAoD;AAAA,IAC5E;AACA,SAAK,2BAA2B;AAChC,WAAO;AAAA,EACX;AAAA,EACA,qBAAqB,mBAAmB;AACpC,QAAI,KAAK,oBAAoB;AACzB,YAAM,IAAI,UAAU,8CAA8C;AAAA,IACtE;AACA,SAAK,qBAAqB;AAC1B,WAAO;AAAA,EACX;AAAA,EACA,+BAA+B,KAAK;AAChC,SAAK,OAAO;AACZ,WAAO;AAAA,EACX;AAAA,EACA,wBAAwB,KAAK;AACzB,QAAI,KAAK,MAAM;AACX,YAAM,IAAI,UAAU,iDAAiD;AAAA,IACzE;AACA,SAAK,OAAO;AACZ,WAAO;AAAA,EACX;AAAA,EACA,wBAAwB,IAAI;AACxB,QAAI,KAAK,KAAK;AACV,YAAM,IAAI,UAAU,iDAAiD;AAAA,IACzE;AACA,SAAK,MAAM;AACX,WAAO;AAAA,EACX;AAAA,EACA,MAAM,QAAQ,KAAK,SAAS;AACxB,QAAI,CAAC,KAAK,oBAAoB,CAAC,KAAK,sBAAsB,CAAC,KAAK,0BAA0B;AACtF,YAAM,IAAI,WAAW,8GAA8G;AAAA,IACvI;AACA,QAAI,CAAC,oBAAW,KAAK,kBAAkB,KAAK,oBAAoB,KAAK,wBAAwB,GAAG;AAC5F,YAAM,IAAI,WAAW,qGAAqG;AAAA,IAC9H;AACA,UAAM,aAAa;AAAA,MACf,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,IACZ;AACA,0BAAa,YAAY,oBAAI,IAAI,GAAG,SAAS,MAAM,KAAK,kBAAkB,UAAU;AACpF,QAAI,WAAW,QAAQ,QAAW;AAC9B,YAAM,IAAI,iBAAiB,sEAAsE;AAAA,IACrG;AACA,UAAM,EAAE,KAAK,IAAI,IAAI;AACrB,QAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;AACjC,YAAM,IAAI,WAAW,2DAA2D;AAAA,IACpF;AACA,QAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;AACjC,YAAM,IAAI,WAAW,sEAAsE;AAAA,IAC/F;AACA,QAAI;AACJ,QAAI,KAAK,SAAS,QAAQ,SAAS,QAAQ,YAAY;AACnD,YAAM,IAAI,UAAU,8EAA8E,GAAG,EAAE;AAAA,IAC3G;AACA,QAAI;AACJ;AACI,UAAI;AACJ,OAAC,EAAE,KAAK,cAAc,WAAW,IAAI,MAAM,+BAAqB,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,wBAAwB;AACvH,UAAI,YAAY;AACZ,YAAI,WAAW,eAAe,SAAS;AACnC,cAAI,CAAC,KAAK,oBAAoB;AAC1B,iBAAK,qBAAqB,UAAU;AAAA,UACxC,OACK;AACD,iBAAK,qBAAqB,EAAE,GAAG,KAAK,oBAAoB,GAAG,WAAW;AAAA,UAC1E;AAAA,QACJ,WACS,CAAC,KAAK,kBAAkB;AAC7B,eAAK,mBAAmB,UAAU;AAAA,QACtC,OACK;AACD,eAAK,mBAAmB,EAAE,GAAG,KAAK,kBAAkB,GAAG,WAAW;AAAA,QACtE;AAAA,MACJ;AAAA,IACJ;AACA,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,kBAAkB;AACvB,wBAAkB,QAAQ,OAAO,OAAU,KAAK,UAAU,KAAK,gBAAgB,CAAC,CAAC;AAAA,IACrF,OACK;AACD,wBAAkB,QAAQ,OAAO,EAAE;AAAA,IACvC;AACA,QAAI,KAAK,MAAM;AACX,kBAAY,OAAU,KAAK,IAAI;AAC/B,uBAAiB,OAAO,iBAAiB,QAAQ,OAAO,GAAG,GAAG,QAAQ,OAAO,SAAS,CAAC;AAAA,IAC3F,OACK;AACD,uBAAiB;AAAA,IACrB;AACA,UAAM,EAAE,YAAY,KAAAC,MAAK,GAAG,IAAI,MAAM,gBAAQ,KAAK,KAAK,YAAY,KAAK,KAAK,KAAK,cAAc;AACjG,UAAM,MAAM;AAAA,MACR,YAAY,OAAU,UAAU;AAAA,IACpC;AACA,QAAI,IAAI;AACJ,UAAI,KAAK,OAAU,EAAE;AAAA,IACzB;AACA,QAAIA,MAAK;AACL,UAAI,MAAM,OAAUA,IAAG;AAAA,IAC3B;AACA,QAAI,cAAc;AACd,UAAI,gBAAgB,OAAU,YAAY;AAAA,IAC9C;AACA,QAAI,WAAW;AACX,UAAI,MAAM;AAAA,IACd;AACA,QAAI,KAAK,kBAAkB;AACvB,UAAI,YAAY,QAAQ,OAAO,eAAe;AAAA,IAClD;AACA,QAAI,KAAK,0BAA0B;AAC/B,UAAI,cAAc,KAAK;AAAA,IAC3B;AACA,QAAI,KAAK,oBAAoB;AACzB,UAAI,SAAS,KAAK;AAAA,IACtB;AACA,WAAO;AAAA,EACX;AACJ;;;ADxJA,IAAM,sBAAN,MAA0B;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,KAAK,KAAK,SAAS;AAC3B,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,qBAAqB,mBAAmB;AACpC,QAAI,KAAK,mBAAmB;AACxB,YAAM,IAAI,UAAU,8CAA8C;AAAA,IACtE;AACA,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,MAAM;AAClB,WAAO,KAAK,OAAO,aAAa,GAAG,IAAI;AAAA,EAC3C;AAAA,EACA,WAAW,MAAM;AACb,WAAO,KAAK,OAAO,QAAQ,GAAG,IAAI;AAAA,EACtC;AAAA,EACA,OAAO;AACH,WAAO,KAAK;AAAA,EAChB;AACJ;AACO,IAAM,iBAAN,MAAqB;AAAA,EACxB;AAAA,EACA,cAAc,CAAC;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,WAAW;AACnB,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,aAAa,KAAK,SAAS;AACvB,UAAM,YAAY,IAAI,oBAAoB,MAAM,KAAK,EAAE,MAAM,SAAS,KAAK,CAAC;AAC5E,SAAK,YAAY,KAAK,SAAS;AAC/B,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB,iBAAiB;AAChC,QAAI,KAAK,kBAAkB;AACvB,YAAM,IAAI,UAAU,4CAA4C;AAAA,IACpE;AACA,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACX;AAAA,EACA,2BAA2B,yBAAyB;AAChD,QAAI,KAAK,oBAAoB;AACzB,YAAM,IAAI,UAAU,oDAAoD;AAAA,IAC5E;AACA,SAAK,qBAAqB;AAC1B,WAAO;AAAA,EACX;AAAA,EACA,+BAA+B,KAAK;AAChC,SAAK,OAAO;AACZ,WAAO;AAAA,EACX;AAAA,EACA,MAAM,UAAU;AACZ,QAAI,CAAC,KAAK,YAAY,QAAQ;AAC1B,YAAM,IAAI,WAAW,sCAAsC;AAAA,IAC/D;AACA,QAAI,KAAK,YAAY,WAAW,GAAG;AAC/B,YAAM,CAAC,SAAS,IAAI,KAAK;AACzB,YAAM,YAAY,MAAM,IAAI,iBAAiB,KAAK,UAAU,EACvD,+BAA+B,KAAK,IAAI,EACxC,mBAAmB,KAAK,gBAAgB,EACxC,2BAA2B,KAAK,kBAAkB,EAClD,qBAAqB,UAAU,iBAAiB,EAChD,QAAQ,UAAU,KAAK,EAAE,GAAG,UAAU,QAAQ,CAAC;AACpD,YAAMC,OAAM;AAAA,QACR,YAAY,UAAU;AAAA,QACtB,IAAI,UAAU;AAAA,QACd,YAAY,CAAC,CAAC,CAAC;AAAA,QACf,KAAK,UAAU;AAAA,MACnB;AACA,UAAI,UAAU;AACV,QAAAA,KAAI,MAAM,UAAU;AACxB,UAAI,UAAU;AACV,QAAAA,KAAI,YAAY,UAAU;AAC9B,UAAI,UAAU;AACV,QAAAA,KAAI,cAAc,UAAU;AAChC,UAAI,UAAU;AACV,QAAAA,KAAI,WAAW,CAAC,EAAE,gBAAgB,UAAU;AAChD,UAAI,UAAU;AACV,QAAAA,KAAI,WAAW,CAAC,EAAE,SAAS,UAAU;AACzC,aAAOA;AAAA,IACX;AACA,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAC9C,YAAM,YAAY,KAAK,YAAY,CAAC;AACpC,UAAI,CAAC,oBAAW,KAAK,kBAAkB,KAAK,oBAAoB,UAAU,iBAAiB,GAAG;AAC1F,cAAM,IAAI,WAAW,qGAAqG;AAAA,MAC9H;AACA,YAAM,aAAa;AAAA,QACf,GAAG,KAAK;AAAA,QACR,GAAG,KAAK;AAAA,QACR,GAAG,UAAU;AAAA,MACjB;AACA,YAAM,EAAE,IAAI,IAAI;AAChB,UAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;AACjC,cAAM,IAAI,WAAW,2DAA2D;AAAA,MACpF;AACA,UAAI,QAAQ,SAAS,QAAQ,WAAW;AACpC,cAAM,IAAI,WAAW,kEAAkE;AAAA,MAC3F;AACA,UAAI,OAAO,WAAW,QAAQ,YAAY,CAAC,WAAW,KAAK;AACvD,cAAM,IAAI,WAAW,sEAAsE;AAAA,MAC/F;AACA,UAAI,CAAC,KAAK;AACN,cAAM,WAAW;AAAA,MACrB,WACS,QAAQ,WAAW,KAAK;AAC7B,cAAM,IAAI,WAAW,uFAAuF;AAAA,MAChH;AACA,4BAAa,YAAY,oBAAI,IAAI,GAAG,UAAU,QAAQ,MAAM,KAAK,kBAAkB,UAAU;AAC7F,UAAI,WAAW,QAAQ,QAAW;AAC9B,cAAM,IAAI,iBAAiB,sEAAsE;AAAA,MACrG;AAAA,IACJ;AACA,UAAM,MAAM,YAAY,GAAG;AAC3B,UAAM,MAAM;AAAA,MACR,YAAY;AAAA,MACZ,IAAI;AAAA,MACJ,YAAY,CAAC;AAAA,MACb,KAAK;AAAA,IACT;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAC9C,YAAM,YAAY,KAAK,YAAY,CAAC;AACpC,YAAM,SAAS,CAAC;AAChB,UAAI,WAAW,KAAK,MAAM;AAC1B,YAAM,aAAa;AAAA,QACf,GAAG,KAAK;AAAA,QACR,GAAG,KAAK;AAAA,QACR,GAAG,UAAU;AAAA,MACjB;AACA,YAAM,MAAM,WAAW,IAAI,WAAW,OAAO,IAAI,OAAO,IAAI;AAC5D,UAAI,MAAM,GAAG;AACT,cAAM,YAAY,MAAM,IAAI,iBAAiB,KAAK,UAAU,EACvD,+BAA+B,KAAK,IAAI,EACxC,wBAAwB,GAAG,EAC3B,mBAAmB,KAAK,gBAAgB,EACxC,2BAA2B,KAAK,kBAAkB,EAClD,qBAAqB,UAAU,iBAAiB,EAChD,2BAA2B,EAAE,IAAI,CAAC,EAClC,QAAQ,UAAU,KAAK;AAAA,UACxB,GAAG,UAAU;AAAA,UACb,CAAC,WAAW,GAAG;AAAA,QACnB,CAAC;AACD,YAAI,aAAa,UAAU;AAC3B,YAAI,KAAK,UAAU;AACnB,YAAI,MAAM,UAAU;AACpB,YAAI,UAAU;AACV,cAAI,MAAM,UAAU;AACxB,YAAI,UAAU;AACV,cAAI,YAAY,UAAU;AAC9B,YAAI,UAAU;AACV,cAAI,cAAc,UAAU;AAChC,eAAO,gBAAgB,UAAU;AACjC,YAAI,UAAU;AACV,iBAAO,SAAS,UAAU;AAC9B;AAAA,MACJ;AACA,YAAM,EAAE,cAAc,WAAW,IAAI,MAAM,+BAAqB,UAAU,mBAAmB,OACzF,KAAK,kBAAkB,OACvB,KAAK,oBAAoB,KAAK,KAAK,UAAU,KAAK,KAAK,EAAE,IAAI,CAAC;AAClE,aAAO,gBAAgB,OAAU,YAAY;AAC7C,UAAI,UAAU,qBAAqB;AAC/B,eAAO,SAAS,EAAE,GAAG,UAAU,mBAAmB,GAAG,WAAW;AAAA,IACxE;AACA,WAAO;AAAA,EACX;AACJ;;;AMrLA;;;ACAA;;;ACAA;AAAA,YAAYC,aAAY;AACxB,SAAS,aAAAC,kBAAiB;;;ACD1B;AACe,SAAR,UAA2B,KAAK;AACnC,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,iBAAiB,OAAO,GAAG,6DAA6D;AAAA,EAC1G;AACJ;;;ACtBA;AAAA,SAAS,aAAAC,YAAW,aAAAC,mBAAiB;AAIrC,IAAM,gBAAgB,oBAAI,IAAI;AAAA,EAC1B,CAAC,SAAS,OAAO;AAAA,EACjB,CAAC,UAAU,WAAW;AAAA,EACtB,CAAC,SAAS,OAAO;AAAA,EACjB,CAAC,SAAS,OAAO;AACrB,CAAC;AACc,SAAR,aAA8B,KAAK,KAAK;AAC3C,MAAI;AACJ,MAAI;AACJ,MAAIC;AACJ,MAAI,eAAeC,aAAW;AAC1B,wBAAoB,IAAI;AACxB,2BAAuB,IAAI;AAAA,EAC/B,OACK;AACD,IAAAD,SAAQ;AACR,YAAQ,IAAI,KAAK;AAAA,MACb,KAAK;AACD,4BAAoB;AACpB;AAAA,MACJ,KAAK;AACD,4BAAoB;AACpB;AAAA,MACJ,KAAK,OAAO;AACR,YAAI,IAAI,QAAQ,WAAW;AACvB,8BAAoB;AACpB;AAAA,QACJ;AACA,YAAI,IAAI,QAAQ,SAAS;AACrB,8BAAoB;AACpB;AAAA,QACJ;AACA,cAAM,IAAI,UAAU,kEAAkE;AAAA,MAC1F;AAAA,MACA;AACI,cAAM,IAAI,UAAU,iEAAiE;AAAA,IAC7F;AAAA,EACJ;AACA,MAAI;AACJ,UAAQ,KAAK;AAAA,IACT,KAAK;AACD,UAAI,sBAAsB,WAAW;AACjC,cAAM,IAAI,UAAU,uEAAuE;AAAA,MAC/F;AACA;AAAA,IACJ,KAAK;AACD,UAAI,CAAC,CAAC,WAAW,OAAO,EAAE,SAAS,iBAAiB,GAAG;AACnD,cAAM,IAAI,UAAU,gFAAgF;AAAA,MACxG;AACA;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,UAAI,sBAAsB,OAAO;AAC7B,cAAM,IAAI,UAAU,mEAAmE;AAAA,MAC3F;AACA,+BAAe,KAAK,GAAG;AACvB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,UAAI,sBAAsB,WAAW;AACjC,cAAM,EAAE,eAAe,mBAAmB,WAAW,IAAI;AACzD,cAAM,SAAS,SAAS,IAAI,MAAM,EAAE,GAAG,EAAE;AACzC,YAAI,kBAAkB,WACjB,kBAAkB,MAAM,MAAM,MAAM,sBAAsB,gBAAgB;AAC3E,gBAAM,IAAI,UAAU,gGAAgG,GAAG,EAAE;AAAA,QAC7H;AACA,YAAI,eAAe,UAAa,aAAa,UAAU,GAAG;AACtD,gBAAM,IAAI,UAAU,4GAA4G,GAAG,EAAE;AAAA,QACzI;AAAA,MACJ,WACS,sBAAsB,OAAO;AAClC,cAAM,IAAI,UAAU,8EAA8E;AAAA,MACtG;AACA,+BAAe,KAAK,GAAG;AACvB,gBAAU;AAAA,QACN,SAASE,WAAU;AAAA,QACnB,YAAYA,WAAU;AAAA,MAC1B;AACA;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,SAAS;AACV,UAAI,sBAAsB,MAAM;AAC5B,cAAM,IAAI,UAAU,kEAAkE;AAAA,MAC1F;AACA,YAAM,SAAS,wBAAc,GAAG;AAChC,YAAM,WAAW,cAAc,IAAI,GAAG;AACtC,UAAI,WAAW,UAAU;AACrB,cAAM,IAAI,UAAU,0DAA0D,QAAQ,SAAS,MAAM,EAAE;AAAA,MAC3G;AACA,gBAAU,EAAE,aAAa,aAAa;AACtC;AAAA,IACJ;AAAA,IACA;AACI,YAAM,IAAI,iBAAiB,OAAO,GAAG,6DAA6D;AAAA,EAC1G;AACA,MAAIF,QAAO;AACP,WAAO,EAAE,QAAQ,OAAO,KAAK,GAAG,QAAQ;AAAA,EAC5C;AACA,SAAO,UAAU,EAAE,GAAG,SAAS,IAAI,IAAI;AAC3C;;;AC3GA;AAAA,YAAYG,aAAY;AACxB,SAAS,aAAAC,kBAAiB;;;ACD1B;AACe,SAAR,WAA4B,KAAK;AACpC,UAAQ,KAAK;AAAA,IACT,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,iBAAiB,OAAO,GAAG,6DAA6D;AAAA,EAC1G;AACJ;;;ACZA;AAAA,SAAS,aAAAC,aAAW,mBAAAC,wBAAuB;AAM5B,SAAR,iBAAkC,KAAK,KAAK,OAAO;AACtD,MAAI,eAAe,YAAY;AAC3B,QAAI,CAAC,IAAI,WAAW,IAAI,GAAG;AACvB,YAAM,IAAI,UAAU,0BAAgB,KAAK,GAAGC,MAAK,CAAC;AAAA,IACtD;AACA,WAAOC,iBAAgB,GAAG;AAAA,EAC9B;AACA,MAAI,eAAeC,aAAW;AAC1B,WAAO;AAAA,EACX;AACA,MAAI,YAAY,GAAG,GAAG;AAClB,sBAAkB,KAAK,KAAK,KAAK;AACjC,WAAOA,YAAU,KAAK,GAAG;AAAA,EAC7B;AACA,MAAQ,MAAM,GAAG,GAAG;AAChB,QAAI,IAAI,WAAW,IAAI,GAAG;AACtB,aAAOD,iBAAgB,OAAO,KAAK,IAAI,GAAG,WAAW,CAAC;AAAA,IAC1D;AACA,WAAO;AAAA,EACX;AACA,QAAM,IAAI,UAAU,0BAAgB,KAAK,GAAGD,QAAO,cAAc,cAAc,CAAC;AACpF;;;AFrBA,IAAM,cAAcG,WAAiB,YAAI;AACzC,IAAMC,QAAO,OAAO,KAAK,KAAK,SAAS;AACnC,QAAM,IAAI,iBAAW,KAAK,KAAK,MAAM;AACrC,MAAI,IAAI,WAAW,IAAI,GAAG;AACtB,UAAM,OAAc,mBAAW,WAAW,GAAG,GAAG,CAAC;AACjD,SAAK,OAAO,IAAI;AAChB,WAAO,KAAK,OAAO;AAAA,EACvB;AACA,SAAO,YAAY,UAAW,GAAG,GAAG,MAAM,aAAQ,KAAK,CAAC,CAAC;AAC7D;AACA,IAAO,eAAQA;;;AHVf,IAAM,gBAAgBC,WAAiB,cAAM;AAC7C,IAAMC,UAAS,OAAO,KAAK,KAAK,WAAW,SAAS;AAChD,QAAM,IAAI,iBAAa,KAAK,KAAK,QAAQ;AACzC,MAAI,IAAI,WAAW,IAAI,GAAG;AACtB,UAAM,WAAW,MAAM,aAAK,KAAK,GAAG,IAAI;AACxC,UAAM,SAAS;AACf,QAAI;AACA,aAAc,wBAAgB,QAAQ,QAAQ;AAAA,IAClD,QACM;AACF,aAAO;AAAA,IACX;AAAA,EACJ;AACA,QAAM,YAAY,UAAW,GAAG;AAChC,QAAM,WAAW,aAAQ,KAAK,CAAC;AAC/B,MAAI;AACA,WAAO,MAAM,cAAc,WAAW,MAAM,UAAU,SAAS;AAAA,EACnE,QACM;AACF,WAAO;AAAA,EACX;AACJ;AACA,IAAO,iBAAQA;;;ADjBf,eAAsB,gBAAgB,KAAK,KAAK,SAAS;AACrD,MAAI,CAAC,SAAS,GAAG,GAAG;AAChB,UAAM,IAAI,WAAW,iCAAiC;AAAA,EAC1D;AACA,MAAI,IAAI,cAAc,UAAa,IAAI,WAAW,QAAW;AACzD,UAAM,IAAI,WAAW,uEAAuE;AAAA,EAChG;AACA,MAAI,IAAI,cAAc,UAAa,OAAO,IAAI,cAAc,UAAU;AAClE,UAAM,IAAI,WAAW,qCAAqC;AAAA,EAC9D;AACA,MAAI,IAAI,YAAY,QAAW;AAC3B,UAAM,IAAI,WAAW,qBAAqB;AAAA,EAC9C;AACA,MAAI,OAAO,IAAI,cAAc,UAAU;AACnC,UAAM,IAAI,WAAW,yCAAyC;AAAA,EAClE;AACA,MAAI,IAAI,WAAW,UAAa,CAAC,SAAS,IAAI,MAAM,GAAG;AACnD,UAAM,IAAI,WAAW,uCAAuC;AAAA,EAChE;AACA,MAAI,aAAa,CAAC;AAClB,MAAI,IAAI,WAAW;AACf,QAAI;AACA,YAAM,kBAAkB,OAAU,IAAI,SAAS;AAC/C,mBAAa,KAAK,MAAM,QAAQ,OAAO,eAAe,CAAC;AAAA,IAC3D,QACM;AACF,YAAM,IAAI,WAAW,iCAAiC;AAAA,IAC1D;AAAA,EACJ;AACA,MAAI,CAAC,oBAAW,YAAY,IAAI,MAAM,GAAG;AACrC,UAAM,IAAI,WAAW,2EAA2E;AAAA,EACpG;AACA,QAAM,aAAa;AAAA,IACf,GAAG;AAAA,IACH,GAAG,IAAI;AAAA,EACX;AACA,QAAM,aAAa,sBAAa,YAAY,oBAAI,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,SAAS,MAAM,YAAY,UAAU;AAC3G,MAAI,MAAM;AACV,MAAI,WAAW,IAAI,KAAK,GAAG;AACvB,UAAM,WAAW;AACjB,QAAI,OAAO,QAAQ,WAAW;AAC1B,YAAM,IAAI,WAAW,yEAAyE;AAAA,IAClG;AAAA,EACJ;AACA,QAAM,EAAE,IAAI,IAAI;AAChB,MAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;AACjC,UAAM,IAAI,WAAW,2DAA2D;AAAA,EACpF;AACA,QAAM,aAAa,WAAW,4BAAmB,cAAc,QAAQ,UAAU;AACjF,MAAI,cAAc,CAAC,WAAW,IAAI,GAAG,GAAG;AACpC,UAAM,IAAI,kBAAkB,sDAAsD;AAAA,EACtF;AACA,MAAI,KAAK;AACL,QAAI,OAAO,IAAI,YAAY,UAAU;AACjC,YAAM,IAAI,WAAW,8BAA8B;AAAA,IACvD;AAAA,EACJ,WACS,OAAO,IAAI,YAAY,YAAY,EAAE,IAAI,mBAAmB,aAAa;AAC9E,UAAM,IAAI,WAAW,wDAAwD;AAAA,EACjF;AACA,MAAI,cAAc;AAClB,MAAI,OAAO,QAAQ,YAAY;AAC3B,UAAM,MAAM,IAAI,YAAY,GAAG;AAC/B,kBAAc;AACd,wBAAoB,KAAK,KAAK,QAAQ;AACtC,QAAI,MAAM,GAAG,GAAG;AACZ,YAAM,MAAM,UAAU,KAAK,GAAG;AAAA,IAClC;AAAA,EACJ,OACK;AACD,wBAAoB,KAAK,KAAK,QAAQ;AAAA,EAC1C;AACA,QAAM,OAAO,OAAO,QAAQ,OAAO,IAAI,aAAa,EAAE,GAAG,QAAQ,OAAO,GAAG,GAAG,OAAO,IAAI,YAAY,WAAW,QAAQ,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO;AACzJ,MAAI;AACJ,MAAI;AACA,gBAAY,OAAU,IAAI,SAAS;AAAA,EACvC,QACM;AACF,UAAM,IAAI,WAAW,0CAA0C;AAAA,EACnE;AACA,QAAM,WAAW,MAAM,eAAO,KAAK,KAAK,WAAW,IAAI;AACvD,MAAI,CAAC,UAAU;AACX,UAAM,IAAI,+BAA+B;AAAA,EAC7C;AACA,MAAI;AACJ,MAAI,KAAK;AACL,QAAI;AACA,gBAAU,OAAU,IAAI,OAAO;AAAA,IACnC,QACM;AACF,YAAM,IAAI,WAAW,wCAAwC;AAAA,IACjE;AAAA,EACJ,WACS,OAAO,IAAI,YAAY,UAAU;AACtC,cAAU,QAAQ,OAAO,IAAI,OAAO;AAAA,EACxC,OACK;AACD,cAAU,IAAI;AAAA,EAClB;AACA,QAAM,SAAS,EAAE,QAAQ;AACzB,MAAI,IAAI,cAAc,QAAW;AAC7B,WAAO,kBAAkB;AAAA,EAC7B;AACA,MAAI,IAAI,WAAW,QAAW;AAC1B,WAAO,oBAAoB,IAAI;AAAA,EACnC;AACA,MAAI,aAAa;AACb,WAAO,EAAE,GAAG,QAAQ,IAAI;AAAA,EAC5B;AACA,SAAO;AACX;;;ADtHA,eAAsB,cAAc,KAAK,KAAK,SAAS;AACnD,MAAI,eAAe,YAAY;AAC3B,UAAM,QAAQ,OAAO,GAAG;AAAA,EAC5B;AACA,MAAI,OAAO,QAAQ,UAAU;AACzB,UAAM,IAAI,WAAW,4CAA4C;AAAA,EACrE;AACA,QAAM,EAAE,GAAG,iBAAiB,GAAG,SAAS,GAAG,WAAW,OAAO,IAAI,IAAI,MAAM,GAAG;AAC9E,MAAI,WAAW,GAAG;AACd,UAAM,IAAI,WAAW,qBAAqB;AAAA,EAC9C;AACA,QAAM,WAAW,MAAM,gBAAgB,EAAE,SAAS,WAAW,iBAAiB,UAAU,GAAG,KAAK,OAAO;AACvG,QAAM,SAAS,EAAE,SAAS,SAAS,SAAS,iBAAiB,SAAS,gBAAgB;AACtF,MAAI,OAAO,QAAQ,YAAY;AAC3B,WAAO,EAAE,GAAG,QAAQ,KAAK,SAAS,IAAI;AAAA,EAC1C;AACA,SAAO;AACX;;;AQpBA;AAGA,eAAsB,cAAc,KAAK,KAAK,SAAS;AACnD,MAAI,CAAC,SAAS,GAAG,GAAG;AAChB,UAAM,IAAI,WAAW,+BAA+B;AAAA,EACxD;AACA,MAAI,CAAC,MAAM,QAAQ,IAAI,UAAU,KAAK,CAAC,IAAI,WAAW,MAAM,QAAQ,GAAG;AACnE,UAAM,IAAI,WAAW,0CAA0C;AAAA,EACnE;AACA,aAAW,aAAa,IAAI,YAAY;AACpC,QAAI;AACA,aAAO,MAAM,gBAAgB;AAAA,QACzB,QAAQ,UAAU;AAAA,QAClB,SAAS,IAAI;AAAA,QACb,WAAW,UAAU;AAAA,QACrB,WAAW,UAAU;AAAA,MACzB,GAAG,KAAK,OAAO;AAAA,IACnB,QACM;AAAA,IACN;AAAA,EACJ;AACA,QAAM,IAAI,+BAA+B;AAC7C;;;ACvBA;;;ACAA;;;ACAA;AAAA,IAAO,gBAAQ,CAAC,SAAS,KAAK,MAAM,KAAK,QAAQ,IAAI,GAAI;;;ACAzD;AAAA,IAAM,SAAS;AACf,IAAM,OAAO,SAAS;AACtB,IAAM,MAAM,OAAO;AACnB,IAAM,OAAO,MAAM;AACnB,IAAM,OAAO,MAAM;AACnB,IAAM,QAAQ;AACd,IAAO,eAAQ,CAAC,QAAQ;AACpB,QAAM,UAAU,MAAM,KAAK,GAAG;AAC9B,MAAI,CAAC,WAAY,QAAQ,CAAC,KAAK,QAAQ,CAAC,GAAI;AACxC,UAAM,IAAI,UAAU,4BAA4B;AAAA,EACpD;AACA,QAAM,QAAQ,WAAW,QAAQ,CAAC,CAAC;AACnC,QAAM,OAAO,QAAQ,CAAC,EAAE,YAAY;AACpC,MAAI;AACJ,UAAQ,MAAM;AAAA,IACV,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,oBAAc,KAAK,MAAM,KAAK;AAC9B;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,oBAAc,KAAK,MAAM,QAAQ,MAAM;AACvC;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,oBAAc,KAAK,MAAM,QAAQ,IAAI;AACrC;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,oBAAc,KAAK,MAAM,QAAQ,GAAG;AACpC;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,oBAAc,KAAK,MAAM,QAAQ,IAAI;AACrC;AAAA,IACJ;AACI,oBAAc,KAAK,MAAM,QAAQ,IAAI;AACrC;AAAA,EACR;AACA,MAAI,QAAQ,CAAC,MAAM,OAAO,QAAQ,CAAC,MAAM,OAAO;AAC5C,WAAO,CAAC;AAAA,EACZ;AACA,SAAO;AACX;;;AFjDA,IAAM,eAAe,CAAC,UAAU,MAAM,YAAY,EAAE,QAAQ,kBAAkB,EAAE;AAChF,IAAM,wBAAwB,CAAC,YAAY,cAAc;AACrD,MAAI,OAAO,eAAe,UAAU;AAChC,WAAO,UAAU,SAAS,UAAU;AAAA,EACxC;AACA,MAAI,MAAM,QAAQ,UAAU,GAAG;AAC3B,WAAO,UAAU,KAAK,IAAI,UAAU,IAAI,KAAK,IAAI,IAAI,UAAU,CAAC,CAAC;AAAA,EACrE;AACA,SAAO;AACX;AACA,IAAO,yBAAQ,CAAC,iBAAiB,gBAAgB,UAAU,CAAC,MAAM;AAC9D,MAAI;AACJ,MAAI;AACA,cAAU,KAAK,MAAM,QAAQ,OAAO,cAAc,CAAC;AAAA,EACvD,QACM;AAAA,EACN;AACA,MAAI,CAAC,SAAS,OAAO,GAAG;AACpB,UAAM,IAAI,WAAW,gDAAgD;AAAA,EACzE;AACA,QAAM,EAAE,IAAI,IAAI;AAChB,MAAI,QACC,OAAO,gBAAgB,QAAQ,YAC5B,aAAa,gBAAgB,GAAG,MAAM,aAAa,GAAG,IAAI;AAC9D,UAAM,IAAI,yBAAyB,qCAAqC,SAAS,OAAO,cAAc;AAAA,EAC1G;AACA,QAAM,EAAE,iBAAiB,CAAC,GAAG,QAAQ,SAAS,UAAU,YAAY,IAAI;AACxE,QAAM,gBAAgB,CAAC,GAAG,cAAc;AACxC,MAAI,gBAAgB;AAChB,kBAAc,KAAK,KAAK;AAC5B,MAAI,aAAa;AACb,kBAAc,KAAK,KAAK;AAC5B,MAAI,YAAY;AACZ,kBAAc,KAAK,KAAK;AAC5B,MAAI,WAAW;AACX,kBAAc,KAAK,KAAK;AAC5B,aAAW,SAAS,IAAI,IAAI,cAAc,QAAQ,CAAC,GAAG;AAClD,QAAI,EAAE,SAAS,UAAU;AACrB,YAAM,IAAI,yBAAyB,qBAAqB,KAAK,WAAW,SAAS,OAAO,SAAS;AAAA,IACrG;AAAA,EACJ;AACA,MAAI,UACA,EAAE,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM,GAAG,SAAS,QAAQ,GAAG,GAAG;AACpE,UAAM,IAAI,yBAAyB,gCAAgC,SAAS,OAAO,cAAc;AAAA,EACrG;AACA,MAAI,WAAW,QAAQ,QAAQ,SAAS;AACpC,UAAM,IAAI,yBAAyB,gCAAgC,SAAS,OAAO,cAAc;AAAA,EACrG;AACA,MAAI,YACA,CAAC,sBAAsB,QAAQ,KAAK,OAAO,aAAa,WAAW,CAAC,QAAQ,IAAI,QAAQ,GAAG;AAC3F,UAAM,IAAI,yBAAyB,gCAAgC,SAAS,OAAO,cAAc;AAAA,EACrG;AACA,MAAI;AACJ,UAAQ,OAAO,QAAQ,gBAAgB;AAAA,IACnC,KAAK;AACD,kBAAY,aAAK,QAAQ,cAAc;AACvC;AAAA,IACJ,KAAK;AACD,kBAAY,QAAQ;AACpB;AAAA,IACJ,KAAK;AACD,kBAAY;AACZ;AAAA,IACJ;AACI,YAAM,IAAI,UAAU,oCAAoC;AAAA,EAChE;AACA,QAAM,EAAE,YAAY,IAAI;AACxB,QAAM,MAAM,cAAM,eAAe,oBAAI,KAAK,CAAC;AAC3C,OAAK,QAAQ,QAAQ,UAAa,gBAAgB,OAAO,QAAQ,QAAQ,UAAU;AAC/E,UAAM,IAAI,yBAAyB,gCAAgC,SAAS,OAAO,SAAS;AAAA,EAChG;AACA,MAAI,QAAQ,QAAQ,QAAW;AAC3B,QAAI,OAAO,QAAQ,QAAQ,UAAU;AACjC,YAAM,IAAI,yBAAyB,gCAAgC,SAAS,OAAO,SAAS;AAAA,IAChG;AACA,QAAI,QAAQ,MAAM,MAAM,WAAW;AAC/B,YAAM,IAAI,yBAAyB,sCAAsC,SAAS,OAAO,cAAc;AAAA,IAC3G;AAAA,EACJ;AACA,MAAI,QAAQ,QAAQ,QAAW;AAC3B,QAAI,OAAO,QAAQ,QAAQ,UAAU;AACjC,YAAM,IAAI,yBAAyB,gCAAgC,SAAS,OAAO,SAAS;AAAA,IAChG;AACA,QAAI,QAAQ,OAAO,MAAM,WAAW;AAChC,YAAM,IAAI,WAAW,sCAAsC,SAAS,OAAO,cAAc;AAAA,IAC7F;AAAA,EACJ;AACA,MAAI,aAAa;AACb,UAAM,MAAM,MAAM,QAAQ;AAC1B,UAAM,MAAM,OAAO,gBAAgB,WAAW,cAAc,aAAK,WAAW;AAC5E,QAAI,MAAM,YAAY,KAAK;AACvB,YAAM,IAAI,WAAW,4DAA4D,SAAS,OAAO,cAAc;AAAA,IACnH;AACA,QAAI,MAAM,IAAI,WAAW;AACrB,YAAM,IAAI,yBAAyB,iEAAiE,SAAS,OAAO,cAAc;AAAA,IACtI;AAAA,EACJ;AACA,SAAO;AACX;;;ADpGA,eAAsB,UAAU,KAAK,KAAK,SAAS;AAC/C,QAAM,WAAW,MAAM,cAAc,KAAK,KAAK,OAAO;AACtD,MAAI,SAAS,gBAAgB,MAAM,SAAS,KAAK,KAAK,SAAS,gBAAgB,QAAQ,OAAO;AAC1F,UAAM,IAAI,WAAW,qCAAqC;AAAA,EAC9D;AACA,QAAM,UAAU,uBAAW,SAAS,iBAAiB,SAAS,SAAS,OAAO;AAC9E,QAAM,SAAS,EAAE,SAAS,iBAAiB,SAAS,gBAAgB;AACpE,MAAI,OAAO,QAAQ,YAAY;AAC3B,WAAO,EAAE,GAAG,QAAQ,KAAK,SAAS,IAAI;AAAA,EAC1C;AACA,SAAO;AACX;;;AIdA;AAGA,eAAsB,WAAW,KAAK,KAAK,SAAS;AAChD,QAAM,YAAY,MAAM,eAAe,KAAK,KAAK,OAAO;AACxD,QAAM,UAAU,uBAAW,UAAU,iBAAiB,UAAU,WAAW,OAAO;AAClF,QAAM,EAAE,gBAAgB,IAAI;AAC5B,MAAI,gBAAgB,QAAQ,UAAa,gBAAgB,QAAQ,QAAQ,KAAK;AAC1E,UAAM,IAAI,yBAAyB,oDAAoD,SAAS,OAAO,UAAU;AAAA,EACrH;AACA,MAAI,gBAAgB,QAAQ,UAAa,gBAAgB,QAAQ,QAAQ,KAAK;AAC1E,UAAM,IAAI,yBAAyB,oDAAoD,SAAS,OAAO,UAAU;AAAA,EACrH;AACA,MAAI,gBAAgB,QAAQ,UACxB,KAAK,UAAU,gBAAgB,GAAG,MAAM,KAAK,UAAU,QAAQ,GAAG,GAAG;AACrE,UAAM,IAAI,yBAAyB,oDAAoD,SAAS,OAAO,UAAU;AAAA,EACrH;AACA,QAAM,SAAS,EAAE,SAAS,gBAAgB;AAC1C,MAAI,OAAO,QAAQ,YAAY;AAC3B,WAAO,EAAE,GAAG,QAAQ,KAAK,UAAU,IAAI;AAAA,EAC3C;AACA,SAAO;AACX;;;ACtBA;AACO,IAAM,iBAAN,MAAqB;AAAA,EACxB;AAAA,EACA,YAAY,WAAW;AACnB,SAAK,aAAa,IAAI,iBAAiB,SAAS;AAAA,EACpD;AAAA,EACA,wBAAwB,KAAK;AACzB,SAAK,WAAW,wBAAwB,GAAG;AAC3C,WAAO;AAAA,EACX;AAAA,EACA,wBAAwB,IAAI;AACxB,SAAK,WAAW,wBAAwB,EAAE;AAC1C,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB,iBAAiB;AAChC,SAAK,WAAW,mBAAmB,eAAe;AAClD,WAAO;AAAA,EACX;AAAA,EACA,2BAA2B,YAAY;AACnC,SAAK,WAAW,2BAA2B,UAAU;AACrD,WAAO;AAAA,EACX;AAAA,EACA,MAAM,QAAQ,KAAK,SAAS;AACxB,UAAM,MAAM,MAAM,KAAK,WAAW,QAAQ,KAAK,OAAO;AACtD,WAAO,CAAC,IAAI,WAAW,IAAI,eAAe,IAAI,IAAI,IAAI,YAAY,IAAI,GAAG,EAAE,KAAK,GAAG;AAAA,EACvF;AACJ;;;AC1BA;;;ACAA;AAOO,IAAM,gBAAN,MAAoB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,SAAS;AACjB,QAAI,EAAE,mBAAmB,aAAa;AAClC,YAAM,IAAI,UAAU,2CAA2C;AAAA,IACnE;AACA,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,mBAAmB,iBAAiB;AAChC,QAAI,KAAK,kBAAkB;AACvB,YAAM,IAAI,UAAU,4CAA4C;AAAA,IACpE;AACA,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACX;AAAA,EACA,qBAAqB,mBAAmB;AACpC,QAAI,KAAK,oBAAoB;AACzB,YAAM,IAAI,UAAU,8CAA8C;AAAA,IACtE;AACA,SAAK,qBAAqB;AAC1B,WAAO;AAAA,EACX;AAAA,EACA,MAAM,KAAK,KAAK,SAAS;AACrB,QAAI,CAAC,KAAK,oBAAoB,CAAC,KAAK,oBAAoB;AACpD,YAAM,IAAI,WAAW,iFAAiF;AAAA,IAC1G;AACA,QAAI,CAAC,oBAAW,KAAK,kBAAkB,KAAK,kBAAkB,GAAG;AAC7D,YAAM,IAAI,WAAW,2EAA2E;AAAA,IACpG;AACA,UAAM,aAAa;AAAA,MACf,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,IACZ;AACA,UAAM,aAAa,sBAAa,YAAY,oBAAI,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,SAAS,MAAM,KAAK,kBAAkB,UAAU;AACtH,QAAI,MAAM;AACV,QAAI,WAAW,IAAI,KAAK,GAAG;AACvB,YAAM,KAAK,iBAAiB;AAC5B,UAAI,OAAO,QAAQ,WAAW;AAC1B,cAAM,IAAI,WAAW,yEAAyE;AAAA,MAClG;AAAA,IACJ;AACA,UAAM,EAAE,IAAI,IAAI;AAChB,QAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;AACjC,YAAM,IAAI,WAAW,2DAA2D;AAAA,IACpF;AACA,wBAAoB,KAAK,KAAK,MAAM;AACpC,QAAI,UAAU,KAAK;AACnB,QAAI,KAAK;AACL,gBAAU,QAAQ,OAAO,OAAU,OAAO,CAAC;AAAA,IAC/C;AACA,QAAI;AACJ,QAAI,KAAK,kBAAkB;AACvB,wBAAkB,QAAQ,OAAO,OAAU,KAAK,UAAU,KAAK,gBAAgB,CAAC,CAAC;AAAA,IACrF,OACK;AACD,wBAAkB,QAAQ,OAAO,EAAE;AAAA,IACvC;AACA,UAAM,OAAO,OAAO,iBAAiB,QAAQ,OAAO,GAAG,GAAG,OAAO;AACjE,UAAM,YAAY,MAAM,aAAK,KAAK,KAAK,IAAI;AAC3C,UAAM,MAAM;AAAA,MACR,WAAW,OAAU,SAAS;AAAA,MAC9B,SAAS;AAAA,IACb;AACA,QAAI,KAAK;AACL,UAAI,UAAU,QAAQ,OAAO,OAAO;AAAA,IACxC;AACA,QAAI,KAAK,oBAAoB;AACzB,UAAI,SAAS,KAAK;AAAA,IACtB;AACA,QAAI,KAAK,kBAAkB;AACvB,UAAI,YAAY,QAAQ,OAAO,eAAe;AAAA,IAClD;AACA,WAAO;AAAA,EACX;AACJ;;;ADlFO,IAAM,cAAN,MAAkB;AAAA,EACrB;AAAA,EACA,YAAY,SAAS;AACjB,SAAK,aAAa,IAAI,cAAc,OAAO;AAAA,EAC/C;AAAA,EACA,mBAAmB,iBAAiB;AAChC,SAAK,WAAW,mBAAmB,eAAe;AAClD,WAAO;AAAA,EACX;AAAA,EACA,MAAM,KAAK,KAAK,SAAS;AACrB,UAAM,MAAM,MAAM,KAAK,WAAW,KAAK,KAAK,OAAO;AACnD,QAAI,IAAI,YAAY,QAAW;AAC3B,YAAM,IAAI,UAAU,2DAA2D;AAAA,IACnF;AACA,WAAO,GAAG,IAAI,SAAS,IAAI,IAAI,OAAO,IAAI,IAAI,SAAS;AAAA,EAC3D;AACJ;;;AEjBA;AAEA,IAAM,sBAAN,MAA0B;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,KAAK,KAAK,SAAS;AAC3B,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,mBAAmB,iBAAiB;AAChC,QAAI,KAAK,iBAAiB;AACtB,YAAM,IAAI,UAAU,4CAA4C;AAAA,IACpE;AACA,SAAK,kBAAkB;AACvB,WAAO;AAAA,EACX;AAAA,EACA,qBAAqB,mBAAmB;AACpC,QAAI,KAAK,mBAAmB;AACxB,YAAM,IAAI,UAAU,8CAA8C;AAAA,IACtE;AACA,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,MAAM;AAClB,WAAO,KAAK,OAAO,aAAa,GAAG,IAAI;AAAA,EAC3C;AAAA,EACA,QAAQ,MAAM;AACV,WAAO,KAAK,OAAO,KAAK,GAAG,IAAI;AAAA,EACnC;AAAA,EACA,OAAO;AACH,WAAO,KAAK;AAAA,EAChB;AACJ;AACO,IAAM,cAAN,MAAkB;AAAA,EACrB;AAAA,EACA,cAAc,CAAC;AAAA,EACf,YAAY,SAAS;AACjB,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,aAAa,KAAK,SAAS;AACvB,UAAM,YAAY,IAAI,oBAAoB,MAAM,KAAK,OAAO;AAC5D,SAAK,YAAY,KAAK,SAAS;AAC/B,WAAO;AAAA,EACX;AAAA,EACA,MAAM,OAAO;AACT,QAAI,CAAC,KAAK,YAAY,QAAQ;AAC1B,YAAM,IAAI,WAAW,sCAAsC;AAAA,IAC/D;AACA,UAAM,MAAM;AAAA,MACR,YAAY,CAAC;AAAA,MACb,SAAS;AAAA,IACb;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAC9C,YAAM,YAAY,KAAK,YAAY,CAAC;AACpC,YAAM,YAAY,IAAI,cAAc,KAAK,QAAQ;AACjD,gBAAU,mBAAmB,UAAU,eAAe;AACtD,gBAAU,qBAAqB,UAAU,iBAAiB;AAC1D,YAAM,EAAE,SAAS,GAAG,KAAK,IAAI,MAAM,UAAU,KAAK,UAAU,KAAK,UAAU,OAAO;AAClF,UAAI,MAAM,GAAG;AACT,YAAI,UAAU;AAAA,MAClB,WACS,IAAI,YAAY,SAAS;AAC9B,cAAM,IAAI,WAAW,qDAAqD;AAAA,MAC9E;AACA,UAAI,WAAW,KAAK,IAAI;AAAA,IAC5B;AACA,WAAO;AAAA,EACX;AACJ;;;ACxEA;;;ACAA;AAGA,SAAS,cAAc,OAAO,OAAO;AACjC,MAAI,CAAC,OAAO,SAAS,KAAK,GAAG;AACzB,UAAM,IAAI,UAAU,WAAW,KAAK,QAAQ;AAAA,EAChD;AACA,SAAO;AACX;AACO,IAAM,aAAN,MAAiB;AAAA,EACpB;AAAA,EACA,YAAY,UAAU,CAAC,GAAG;AACtB,QAAI,CAAC,SAAS,OAAO,GAAG;AACpB,YAAM,IAAI,UAAU,kCAAkC;AAAA,IAC1D;AACA,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,UAAU,QAAQ;AACd,SAAK,WAAW,EAAE,GAAG,KAAK,UAAU,KAAK,OAAO;AAChD,WAAO;AAAA,EACX;AAAA,EACA,WAAW,SAAS;AAChB,SAAK,WAAW,EAAE,GAAG,KAAK,UAAU,KAAK,QAAQ;AACjD,WAAO;AAAA,EACX;AAAA,EACA,YAAY,UAAU;AAClB,SAAK,WAAW,EAAE,GAAG,KAAK,UAAU,KAAK,SAAS;AAClD,WAAO;AAAA,EACX;AAAA,EACA,OAAO,OAAO;AACV,SAAK,WAAW,EAAE,GAAG,KAAK,UAAU,KAAK,MAAM;AAC/C,WAAO;AAAA,EACX;AAAA,EACA,aAAa,OAAO;AAChB,QAAI,OAAO,UAAU,UAAU;AAC3B,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,KAAK,cAAc,gBAAgB,KAAK,EAAE;AAAA,IAClF,WACS,iBAAiB,MAAM;AAC5B,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,KAAK,cAAc,gBAAgB,cAAM,KAAK,CAAC,EAAE;AAAA,IACzF,OACK;AACD,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,KAAK,cAAM,oBAAI,KAAK,CAAC,IAAI,aAAK,KAAK,EAAE;AAAA,IAC7E;AACA,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB,OAAO;AACrB,QAAI,OAAO,UAAU,UAAU;AAC3B,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,KAAK,cAAc,qBAAqB,KAAK,EAAE;AAAA,IACvF,WACS,iBAAiB,MAAM;AAC5B,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,KAAK,cAAc,qBAAqB,cAAM,KAAK,CAAC,EAAE;AAAA,IAC9F,OACK;AACD,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,KAAK,cAAM,oBAAI,KAAK,CAAC,IAAI,aAAK,KAAK,EAAE;AAAA,IAC7E;AACA,WAAO;AAAA,EACX;AAAA,EACA,YAAY,OAAO;AACf,QAAI,OAAO,UAAU,aAAa;AAC9B,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,KAAK,cAAM,oBAAI,KAAK,CAAC,EAAE;AAAA,IAC/D,WACS,iBAAiB,MAAM;AAC5B,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,KAAK,cAAc,eAAe,cAAM,KAAK,CAAC,EAAE;AAAA,IACxF,WACS,OAAO,UAAU,UAAU;AAChC,WAAK,WAAW;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,KAAK,cAAc,eAAe,cAAM,oBAAI,KAAK,CAAC,IAAI,aAAK,KAAK,CAAC;AAAA,MACrE;AAAA,IACJ,OACK;AACD,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,KAAK,cAAc,eAAe,KAAK,EAAE;AAAA,IACjF;AACA,WAAO;AAAA,EACX;AACJ;;;ADvEO,IAAM,UAAN,cAAsB,WAAW;AAAA,EACpC;AAAA,EACA,mBAAmB,iBAAiB;AAChC,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACX;AAAA,EACA,MAAM,KAAK,KAAK,SAAS;AACrB,UAAM,MAAM,IAAI,YAAY,QAAQ,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,CAAC;AACzE,QAAI,mBAAmB,KAAK,gBAAgB;AAC5C,QAAI,MAAM,QAAQ,KAAK,kBAAkB,IAAI,KACzC,KAAK,iBAAiB,KAAK,SAAS,KAAK,KACzC,KAAK,iBAAiB,QAAQ,OAAO;AACrC,YAAM,IAAI,WAAW,qCAAqC;AAAA,IAC9D;AACA,WAAO,IAAI,KAAK,KAAK,OAAO;AAAA,EAChC;AACJ;;;AEpBA;AAGO,IAAM,aAAN,cAAyB,WAAW;AAAA,EACvC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,mBAAmB,iBAAiB;AAChC,QAAI,KAAK,kBAAkB;AACvB,YAAM,IAAI,UAAU,4CAA4C;AAAA,IACpE;AACA,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACX;AAAA,EACA,2BAA2B,YAAY;AACnC,QAAI,KAAK,0BAA0B;AAC/B,YAAM,IAAI,UAAU,oDAAoD;AAAA,IAC5E;AACA,SAAK,2BAA2B;AAChC,WAAO;AAAA,EACX;AAAA,EACA,wBAAwB,KAAK;AACzB,QAAI,KAAK,MAAM;AACX,YAAM,IAAI,UAAU,iDAAiD;AAAA,IACzE;AACA,SAAK,OAAO;AACZ,WAAO;AAAA,EACX;AAAA,EACA,wBAAwB,IAAI;AACxB,QAAI,KAAK,KAAK;AACV,YAAM,IAAI,UAAU,iDAAiD;AAAA,IACzE;AACA,SAAK,MAAM;AACX,WAAO;AAAA,EACX;AAAA,EACA,0BAA0B;AACtB,SAAK,2BAA2B;AAChC,WAAO;AAAA,EACX;AAAA,EACA,2BAA2B;AACvB,SAAK,4BAA4B;AACjC,WAAO;AAAA,EACX;AAAA,EACA,4BAA4B;AACxB,SAAK,6BAA6B;AAClC,WAAO;AAAA,EACX;AAAA,EACA,MAAM,QAAQ,KAAK,SAAS;AACxB,UAAM,MAAM,IAAI,eAAe,QAAQ,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,CAAC;AAC5E,QAAI,KAAK,0BAA0B;AAC/B,WAAK,mBAAmB,EAAE,GAAG,KAAK,kBAAkB,KAAK,KAAK,SAAS,IAAI;AAAA,IAC/E;AACA,QAAI,KAAK,2BAA2B;AAChC,WAAK,mBAAmB,EAAE,GAAG,KAAK,kBAAkB,KAAK,KAAK,SAAS,IAAI;AAAA,IAC/E;AACA,QAAI,KAAK,4BAA4B;AACjC,WAAK,mBAAmB,EAAE,GAAG,KAAK,kBAAkB,KAAK,KAAK,SAAS,IAAI;AAAA,IAC/E;AACA,QAAI,mBAAmB,KAAK,gBAAgB;AAC5C,QAAI,KAAK,KAAK;AACV,UAAI,wBAAwB,KAAK,GAAG;AAAA,IACxC;AACA,QAAI,KAAK,MAAM;AACX,UAAI,wBAAwB,KAAK,IAAI;AAAA,IACzC;AACA,QAAI,KAAK,0BAA0B;AAC/B,UAAI,2BAA2B,KAAK,wBAAwB;AAAA,IAChE;AACA,WAAO,IAAI,QAAQ,KAAK,OAAO;AAAA,EACnC;AACJ;;;AC1EA;AAKA,IAAM,QAAQ,CAAC,OAAO,gBAAgB;AAClC,MAAI,OAAO,UAAU,YAAY,CAAC,OAAO;AACrC,UAAM,IAAI,WAAW,GAAG,WAAW,qBAAqB;AAAA,EAC5D;AACJ;AACA,eAAsB,uBAAuB,KAAK,iBAAiB;AAC/D,MAAI,CAAC,SAAS,GAAG,GAAG;AAChB,UAAM,IAAI,UAAU,uBAAuB;AAAA,EAC/C;AACA,sBAAoB;AACpB,MAAI,oBAAoB,YACpB,oBAAoB,YACpB,oBAAoB,UAAU;AAC9B,UAAM,IAAI,UAAU,6DAA6D;AAAA,EACrF;AACA,MAAI;AACJ,UAAQ,IAAI,KAAK;AAAA,IACb,KAAK;AACD,YAAM,IAAI,KAAK,yBAAyB;AACxC,YAAM,IAAI,GAAG,8BAA8B;AAC3C,YAAM,IAAI,GAAG,8BAA8B;AAC3C,mBAAa,EAAE,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE;AAC9D;AAAA,IACJ,KAAK;AACD,YAAM,IAAI,KAAK,uCAAuC;AACtD,YAAM,IAAI,GAAG,4BAA4B;AACzC,mBAAa,EAAE,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,EAAE;AACpD;AAAA,IACJ,KAAK;AACD,YAAM,IAAI,GAAG,0BAA0B;AACvC,YAAM,IAAI,GAAG,yBAAyB;AACtC,mBAAa,EAAE,GAAG,IAAI,GAAG,KAAK,IAAI,KAAK,GAAG,IAAI,EAAE;AAChD;AAAA,IACJ,KAAK;AACD,YAAM,IAAI,GAAG,2BAA2B;AACxC,mBAAa,EAAE,GAAG,IAAI,GAAG,KAAK,IAAI,IAAI;AACtC;AAAA,IACJ;AACI,YAAM,IAAI,iBAAiB,mDAAmD;AAAA,EACtF;AACA,QAAM,OAAO,QAAQ,OAAO,KAAK,UAAU,UAAU,CAAC;AACtD,SAAO,OAAU,MAAM,eAAO,iBAAiB,IAAI,CAAC;AACxD;AACA,eAAsB,0BAA0B,KAAK,iBAAiB;AAClE,sBAAoB;AACpB,QAAM,aAAa,MAAM,uBAAuB,KAAK,eAAe;AACpE,SAAO,4CAA4C,gBAAgB,MAAM,EAAE,CAAC,IAAI,UAAU;AAC9F;;;ACpDA;AAGA,eAAsB,YAAY,iBAAiB,OAAO;AACtD,QAAM,aAAa;AAAA,IACf,GAAG;AAAA,IACH,GAAG,OAAO;AAAA,EACd;AACA,MAAI,CAAC,SAAS,WAAW,GAAG,GAAG;AAC3B,UAAM,IAAI,WAAW,6DAA6D;AAAA,EACtF;AACA,QAAM,MAAM,MAAM,UAAU,EAAE,GAAG,WAAW,KAAK,KAAK,KAAK,GAAG,WAAW,GAAG;AAC5E,MAAI,eAAe,cAAc,IAAI,SAAS,UAAU;AACpD,UAAM,IAAI,WAAW,4DAA4D;AAAA,EACrF;AACA,SAAO;AACX;;;AChBA;AAGA,SAAS,cAAc,KAAK;AACxB,UAAQ,OAAO,QAAQ,YAAY,IAAI,MAAM,GAAG,CAAC,GAAG;AAAA,IAChD,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,iBAAiB,gDAAgD;AAAA,EACnF;AACJ;AACA,SAAS,WAAW,MAAM;AACtB,SAAQ,QACJ,OAAO,SAAS,YAChB,MAAM,QAAQ,KAAK,IAAI,KACvB,KAAK,KAAK,MAAM,SAAS;AACjC;AACA,SAAS,UAAU,KAAK;AACpB,SAAO,SAAS,GAAG;AACvB;AACA,SAAS,MAAM,KAAK;AAChB,MAAI,OAAO,oBAAoB,YAAY;AACvC,WAAO,gBAAgB,GAAG;AAAA,EAC9B;AACA,SAAO,KAAK,MAAM,KAAK,UAAU,GAAG,CAAC;AACzC;AACA,IAAM,cAAN,MAAkB;AAAA,EACd;AAAA,EACA,UAAU,oBAAI,QAAQ;AAAA,EACtB,YAAY,MAAM;AACd,QAAI,CAAC,WAAW,IAAI,GAAG;AACnB,YAAM,IAAI,YAAY,4BAA4B;AAAA,IACtD;AACA,SAAK,QAAQ,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,MAAM,OAAO,iBAAiB,OAAO;AACjC,UAAM,EAAE,KAAK,IAAI,IAAI,EAAE,GAAG,iBAAiB,GAAG,OAAO,OAAO;AAC5D,UAAM,MAAM,cAAc,GAAG;AAC7B,UAAM,aAAa,KAAK,MAAM,KAAK,OAAO,CAACC,SAAQ;AAC/C,UAAI,YAAY,QAAQA,KAAI;AAC5B,UAAI,aAAa,OAAO,QAAQ,UAAU;AACtC,oBAAY,QAAQA,KAAI;AAAA,MAC5B;AACA,UAAI,aAAa,OAAOA,KAAI,QAAQ,UAAU;AAC1C,oBAAY,QAAQA,KAAI;AAAA,MAC5B;AACA,UAAI,aAAa,OAAOA,KAAI,QAAQ,UAAU;AAC1C,oBAAYA,KAAI,QAAQ;AAAA,MAC5B;AACA,UAAI,aAAa,MAAM,QAAQA,KAAI,OAAO,GAAG;AACzC,oBAAYA,KAAI,QAAQ,SAAS,QAAQ;AAAA,MAC7C;AACA,UAAI,WAAW;AACX,gBAAQ,KAAK;AAAA,UACT,KAAK;AACD,wBAAYA,KAAI,QAAQ;AACxB;AAAA,UACJ,KAAK;AACD,wBAAYA,KAAI,QAAQ;AACxB;AAAA,UACJ,KAAK;AACD,wBAAYA,KAAI,QAAQ;AACxB;AAAA,UACJ,KAAK;AACD,wBAAYA,KAAI,QAAQ;AACxB;AAAA,UACJ,KAAK;AACD,wBAAYA,KAAI,QAAQ;AACxB;AAAA,UACJ,KAAK;AACD,wBAAYA,KAAI,QAAQ,aAAaA,KAAI,QAAQ;AACjD;AAAA,QACR;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,EAAE,GAAG,KAAK,OAAO,IAAI;AAC3B,QAAI,WAAW,GAAG;AACd,YAAM,IAAI,kBAAkB;AAAA,IAChC;AACA,QAAI,WAAW,GAAG;AACd,YAAM,QAAQ,IAAI,yBAAyB;AAC3C,YAAM,EAAE,QAAQ,IAAI;AACpB,YAAM,OAAO,aAAa,IAAI,mBAAmB;AAC7C,mBAAWA,QAAO,YAAY;AAC1B,cAAI;AACA,kBAAM,MAAM,mBAAmB,SAASA,MAAK,GAAG;AAAA,UACpD,QACM;AAAA,UAAE;AAAA,QACZ;AAAA,MACJ;AACA,YAAM;AAAA,IACV;AACA,WAAO,mBAAmB,KAAK,SAAS,KAAK,GAAG;AAAA,EACpD;AACJ;AACA,eAAe,mBAAmB,OAAO,KAAK,KAAK;AAC/C,QAAM,SAAS,MAAM,IAAI,GAAG,KAAK,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,GAAG;AAC3D,MAAI,OAAO,GAAG,MAAM,QAAW;AAC3B,UAAM,MAAM,MAAM,UAAU,EAAE,GAAG,KAAK,KAAK,KAAK,GAAG,GAAG;AACtD,QAAI,eAAe,cAAc,IAAI,SAAS,UAAU;AACpD,YAAM,IAAI,YAAY,8CAA8C;AAAA,IACxE;AACA,WAAO,GAAG,IAAI;AAAA,EAClB;AACA,SAAO,OAAO,GAAG;AACrB;AACO,SAAS,kBAAkB,MAAM;AACpC,QAAM,MAAM,IAAI,YAAY,IAAI;AAChC,QAAM,cAAc,OAAO,iBAAiB,UAAU,IAAI,OAAO,iBAAiB,KAAK;AACvF,SAAO,iBAAiB,aAAa;AAAA,IACjC,MAAM;AAAA,MACF,OAAO,MAAM,MAAM,IAAI,KAAK;AAAA,MAC5B,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd;AAAA,EACJ,CAAC;AACD,SAAO;AACX;;;AC5HA;;;ACAA;AAAA,YAAY,UAAU;AACtB,YAAY,WAAW;AACvB,SAAS,YAAY;AAGrB,IAAM,YAAY,OAAO,KAAK,SAAS,YAAY;AAC/C,MAAIC;AACJ,UAAQ,IAAI,UAAU;AAAA,IAClB,KAAK;AACD,MAAAA,OAAY;AACZ;AAAA,IACJ,KAAK;AACD,MAAAA,OAAW;AACX;AAAA,IACJ;AACI,YAAM,IAAI,UAAU,2BAA2B;AAAA,EACvD;AACA,QAAM,EAAE,OAAO,QAAQ,IAAI;AAC3B,QAAM,MAAMA,KAAI,IAAI,MAAM;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,QAAM,CAAC,QAAQ,IAAK,MAAM,QAAQ,KAAK,CAAC,KAAK,KAAK,UAAU,GAAG,KAAK,KAAK,SAAS,CAAC,CAAC;AACpF,MAAI,CAAC,UAAU;AACX,QAAI,QAAQ;AACZ,UAAM,IAAI,YAAY;AAAA,EAC1B;AACA,MAAI,SAAS,eAAe,KAAK;AAC7B,UAAM,IAAI,UAAU,yDAAyD;AAAA,EACjF;AACA,QAAM,QAAQ,CAAC;AACf,mBAAiB,QAAQ,UAAU;AAC/B,UAAM,KAAK,IAAI;AAAA,EACnB;AACA,MAAI;AACA,WAAO,KAAK,MAAM,QAAQ,OAAO,OAAO,GAAG,KAAK,CAAC,CAAC;AAAA,EACtD,QACM;AACF,UAAM,IAAI,UAAU,4DAA4D;AAAA,EACpF;AACJ;AACA,IAAO,qBAAQ;;;ADtCf,SAAS,sBAAsB;AAC3B,SAAQ,OAAO,kBAAkB,eAC5B,OAAO,cAAc,eAAe,UAAU,cAAc,wBAC5D,OAAO,gBAAgB,eAAe,gBAAgB;AAC/D;AACA,IAAI;AACJ,IAAI,OAAO,cAAc,eAAe,CAAC,UAAU,WAAW,aAAa,cAAc,GAAG;AACxF,QAAM,OAAO;AACb,QAAM,UAAU;AAChB,eAAa,GAAG,IAAI,IAAI,OAAO;AACnC;AACO,IAAM,YAAY,OAAO;AAChC,SAAS,iBAAiB,OAAO,aAAa;AAC1C,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC7C,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,UAAU,OAAO,MAAM,QAAQ,YAAY,KAAK,IAAI,IAAI,MAAM,OAAO,aAAa;AAC7F,WAAO;AAAA,EACX;AACA,MAAI,EAAE,UAAU,UACZ,CAAC,SAAS,MAAM,IAAI,KACpB,CAAC,MAAM,QAAQ,MAAM,KAAK,IAAI,KAC9B,CAAC,MAAM,UAAU,MAAM,KAAK,MAAM,KAAK,MAAM,QAAQ,GAAG;AACxD,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,IAAM,eAAN,MAAmB;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,KAAK,SAAS;AACtB,QAAI,EAAE,eAAe,MAAM;AACvB,YAAM,IAAI,UAAU,gCAAgC;AAAA,IACxD;AACA,SAAK,OAAO,IAAI,IAAI,IAAI,IAAI;AAC5B,SAAK,WAAW,EAAE,OAAO,SAAS,OAAO,SAAS,SAAS,QAAQ;AACnE,SAAK,mBACD,OAAO,SAAS,oBAAoB,WAAW,SAAS,kBAAkB;AAC9E,SAAK,oBACD,OAAO,SAAS,qBAAqB,WAAW,SAAS,mBAAmB;AAChF,SAAK,eAAe,OAAO,SAAS,gBAAgB,WAAW,SAAS,cAAc;AACtF,QAAI,UAAU,SAAS,MAAM,QAAW;AACpC,WAAK,SAAS,UAAU,SAAS;AACjC,UAAI,iBAAiB,UAAU,SAAS,GAAG,KAAK,YAAY,GAAG;AAC3D,aAAK,iBAAiB,KAAK,OAAO;AAClC,aAAK,SAAS,kBAAkB,KAAK,OAAO,IAAI;AAAA,MACpD;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,cAAc;AACV,WAAO,OAAO,KAAK,mBAAmB,WAChC,KAAK,IAAI,IAAI,KAAK,iBAAiB,KAAK,oBACxC;AAAA,EACV;AAAA,EACA,QAAQ;AACJ,WAAO,OAAO,KAAK,mBAAmB,WAChC,KAAK,IAAI,IAAI,KAAK,iBAAiB,KAAK,eACxC;AAAA,EACV;AAAA,EACA,MAAM,OAAO,iBAAiB,OAAO;AACjC,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,MAAM,GAAG;AAC/B,YAAM,KAAK,OAAO;AAAA,IACtB;AACA,QAAI;AACA,aAAO,MAAM,KAAK,OAAO,iBAAiB,KAAK;AAAA,IACnD,SACO,KAAK;AACR,UAAI,eAAe,mBAAmB;AAClC,YAAI,KAAK,YAAY,MAAM,OAAO;AAC9B,gBAAM,KAAK,OAAO;AAClB,iBAAO,KAAK,OAAO,iBAAiB,KAAK;AAAA,QAC7C;AAAA,MACJ;AACA,YAAM;AAAA,IACV;AAAA,EACJ;AAAA,EACA,MAAM,SAAS;AACX,QAAI,KAAK,iBAAiB,oBAAoB,GAAG;AAC7C,WAAK,gBAAgB;AAAA,IACzB;AACA,UAAM,UAAU,IAAI,QAAQ,KAAK,SAAS,OAAO;AACjD,QAAI,cAAc,CAAC,QAAQ,IAAI,YAAY,GAAG;AAC1C,cAAQ,IAAI,cAAc,UAAU;AACpC,WAAK,SAAS,UAAU,OAAO,YAAY,QAAQ,QAAQ,CAAC;AAAA,IAChE;AACA,SAAK,kBAAkB,mBAAU,KAAK,MAAM,KAAK,kBAAkB,KAAK,QAAQ,EAC3E,KAAK,CAAC,SAAS;AAChB,WAAK,SAAS,kBAAkB,IAAI;AACpC,UAAI,KAAK,QAAQ;AACb,aAAK,OAAO,MAAM,KAAK,IAAI;AAC3B,aAAK,OAAO,OAAO;AAAA,MACvB;AACA,WAAK,iBAAiB,KAAK,IAAI;AAC/B,WAAK,gBAAgB;AAAA,IACzB,CAAC,EACI,MAAM,CAAC,QAAQ;AAChB,WAAK,gBAAgB;AACrB,YAAM;AAAA,IACV,CAAC;AACD,UAAM,KAAK;AAAA,EACf;AACJ;AACO,SAAS,mBAAmB,KAAK,SAAS;AAC7C,QAAM,MAAM,IAAI,aAAa,KAAK,OAAO;AACzC,QAAM,eAAe,OAAO,iBAAiB,UAAU,IAAI,OAAO,iBAAiB,KAAK;AACxF,SAAO,iBAAiB,cAAc;AAAA,IAClC,aAAa;AAAA,MACT,KAAK,MAAM,IAAI,YAAY;AAAA,MAC3B,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB;AAAA,IACA,OAAO;AAAA,MACH,KAAK,MAAM,IAAI,MAAM;AAAA,MACrB,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACJ,OAAO,MAAM,IAAI,OAAO;AAAA,MACxB,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd;AAAA,IACA,WAAW;AAAA,MACP,KAAK,MAAM,CAAC,CAAC,IAAI;AAAA,MACjB,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB;AAAA,IACA,MAAM;AAAA,MACF,OAAO,MAAM,IAAI,QAAQ,KAAK;AAAA,MAC9B,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACO,IAAM,yBAAyB;;;AEnJtC;AAKO,IAAM,eAAN,cAA2B,WAAW;AAAA,EACzC,SAAS;AACL,UAAM,SAAmB,OAAO,KAAK,UAAU,EAAE,KAAK,OAAO,CAAC,CAAC;AAC/D,UAAM,UAAoB,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC;AAC9D,WAAO,GAAG,MAAM,IAAI,OAAO;AAAA,EAC/B;AAAA,EACA,OAAO,OAAO,KAAK,SAAS;AACxB,QAAI,OAAO,QAAQ,UAAU;AACzB,YAAM,IAAI,WAAW,gCAAgC;AAAA,IACzD;AACA,UAAM,EAAE,GAAG,eAAe,GAAG,gBAAgB,GAAG,WAAW,OAAO,IAAI,IAAI,MAAM,GAAG;AACnF,QAAI,WAAW,KAAK,cAAc,IAAI;AAClC,YAAM,IAAI,WAAW,uBAAuB;AAAA,IAChD;AACA,QAAI;AACJ,QAAI;AACA,eAAS,KAAK,MAAM,QAAQ,OAAiB,OAAO,aAAa,CAAC,CAAC;AACnE,UAAI,OAAO,QAAQ;AACf,cAAM,IAAI,MAAM;AAAA,IACxB,QACM;AACF,YAAM,IAAI,WAAW,uBAAuB;AAAA,IAChD;AACA,UAAM,UAAU,uBAAW,QAAkB,OAAO,cAAc,GAAG,OAAO;AAC5E,WAAO,EAAE,SAAS,OAAO;AAAA,EAC7B;AACJ;;;AC/BA;;;ACAA,IAAAC,qBAAA;AAAA,SAAAA,oBAAA;AAAA,gBAAAC;AAAA,EAAA,cAAAC;AAAA;AAAA;AACO,IAAMC,UAAmB;AACzB,IAAMC,UAAmB;;;ADCzB,SAAS,sBAAsB,OAAO;AACzC,MAAI;AACJ,MAAI,OAAO,UAAU,UAAU;AAC3B,UAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,QAAI,MAAM,WAAW,KAAK,MAAM,WAAW,GAAG;AAC1C;AACA,OAAC,aAAa,IAAI;AAAA,IACtB;AAAA,EACJ,WACS,OAAO,UAAU,YAAY,OAAO;AACzC,QAAI,eAAe,OAAO;AACtB,sBAAgB,MAAM;AAAA,IAC1B,OACK;AACD,YAAM,IAAI,UAAU,2CAA2C;AAAA,IACnE;AAAA,EACJ;AACA,MAAI;AACA,QAAI,OAAO,kBAAkB,YAAY,CAAC,eAAe;AACrD,YAAM,IAAI,MAAM;AAAA,IACpB;AACA,UAAM,SAAS,KAAK,MAAM,QAAQ,OAAOC,QAAU,aAAa,CAAC,CAAC;AAClE,QAAI,CAAC,SAAS,MAAM,GAAG;AACnB,YAAM,IAAI,MAAM;AAAA,IACpB;AACA,WAAO;AAAA,EACX,QACM;AACF,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACtE;AACJ;;;AEjCA;AAIO,SAAS,UAAU,KAAK;AAC3B,MAAI,OAAO,QAAQ;AACf,UAAM,IAAI,WAAW,+DAA+D;AACxF,QAAM,EAAE,GAAG,SAAS,OAAO,IAAI,IAAI,MAAM,GAAG;AAC5C,MAAI,WAAW;AACX,UAAM,IAAI,WAAW,0DAA0D;AACnF,MAAI,WAAW;AACX,UAAM,IAAI,WAAW,aAAa;AACtC,MAAI,CAAC;AACD,UAAM,IAAI,WAAW,6BAA6B;AACtD,MAAI;AACJ,MAAI;AACA,cAAUC,QAAU,OAAO;AAAA,EAC/B,QACM;AACF,UAAM,IAAI,WAAW,wCAAwC;AAAA,EACjE;AACA,MAAI;AACJ,MAAI;AACA,aAAS,KAAK,MAAM,QAAQ,OAAO,OAAO,CAAC;AAAA,EAC/C,QACM;AACF,UAAM,IAAI,WAAW,6CAA6C;AAAA,EACtE;AACA,MAAI,CAAC,SAAS,MAAM;AAChB,UAAM,IAAI,WAAW,wBAAwB;AACjD,SAAO;AACX;;;AC/BA;;;ACAA;AAAA,SAAS,mBAAAC,kBAAiB,mBAAmBC,0BAAyB;AACtE,SAAS,aAAAC,kBAAiB;AAG1B,IAAM,WAAWC,WAAUC,kBAAiB;AAC5C,eAAsB,eAAe,KAAK,SAAS;AAC/C,MAAI;AACJ,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,eAAS,SAAS,IAAI,MAAM,EAAE,GAAG,EAAE;AACnC;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,eAAS,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AACrC;AAAA,IACJ;AACI,YAAM,IAAI,iBAAiB,8DAA8D;AAAA,EACjG;AACA,SAAOC,iBAAgB,eAAO,IAAI,WAAW,UAAU,CAAC,CAAC,CAAC;AAC9D;AACA,eAAsBC,iBAAgB,KAAK,SAAS;AAChD,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,UAAU;AACX,YAAM,gBAAgB,SAAS,iBAAiB;AAChD,UAAI,OAAO,kBAAkB,YAAY,gBAAgB,MAAM;AAC3D,cAAM,IAAI,iBAAiB,6FAA6F;AAAA,MAC5H;AACA,YAAM,UAAU,MAAM,SAAS,OAAO;AAAA,QAClC;AAAA,QACA,gBAAgB;AAAA,MACpB,CAAC;AACD,aAAO;AAAA,IACX;AAAA,IACA,KAAK;AACD,aAAO,SAAS,MAAM,EAAE,YAAY,QAAQ,CAAC;AAAA,IACjD,KAAK;AACD,aAAO,SAAS,MAAM,EAAE,YAAY,YAAY,CAAC;AAAA,IACrD,KAAK;AACD,aAAO,SAAS,MAAM,EAAE,YAAY,QAAQ,CAAC;AAAA,IACjD,KAAK;AACD,aAAO,SAAS,MAAM,EAAE,YAAY,QAAQ,CAAC;AAAA,IACjD,KAAK;AACD,aAAO,SAAS,SAAS;AAAA,IAC7B,KAAK,SAAS;AACV,cAAQ,SAAS,KAAK;AAAA,QAClB,KAAK;AAAA,QACL,KAAK;AACD,iBAAO,SAAS,SAAS;AAAA,QAC7B,KAAK;AACD,iBAAO,SAAS,OAAO;AAAA,QAC3B;AACI,gBAAM,IAAI,iBAAiB,oFAAoF;AAAA,MACvH;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,kBAAkB;AACnB,YAAM,MAAM,SAAS,OAAO;AAC5B,cAAQ,KAAK;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,iBAAO,SAAS,MAAM,EAAE,YAAY,IAAI,CAAC;AAAA,QAC7C,KAAK;AACD,iBAAO,SAAS,QAAQ;AAAA,QAC5B,KAAK;AACD,iBAAO,SAAS,MAAM;AAAA,QAC1B;AACI,gBAAM,IAAI,iBAAiB,wGAAwG;AAAA,MAC3I;AAAA,IACJ;AAAA,IACA;AACI,YAAM,IAAI,iBAAiB,8DAA8D;AAAA,EACjG;AACJ;;;ADjGA,eAAsBC,iBAAgB,KAAK,SAAS;AAChD,SAAOA,iBAAS,KAAK,OAAO;AAChC;;;AEHA;AACA,eAAsBC,gBAAe,KAAK,SAAS;AAC/C,SAAO,eAAS,KAAK,OAAO;AAChC;;;ACHA;;;ACAA;AAAA,IAAO,kBAAQ;;;ADCf,IAAOC,mBAAQ;", "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "message", "util", "webcrypto", "types", "types", "tag", "types", "<PERSON><PERSON><PERSON>", "KeyObject", "createDecipheriv", "KeyObject", "types", "<PERSON><PERSON><PERSON>", "createDecipheriv", "KeyObject", "KeyObject", "getNamedCurve", "KeyObject", "types", "KeyObject", "types", "promisify", "KeyObject", "p2s", "promisify", "KeyObject", "types", "p2s", "decrypt", "KeyObject", "KeyObject", "ensureKeyObject", "KeyObject", "types", "encrypt", "decrypt", "bitLength", "KeyObject", "<PERSON><PERSON><PERSON>", "KeyObject", "types", "<PERSON><PERSON><PERSON>", "createPrivateKey", "createPublicKey", "types", "createCipheriv", "KeyObject", "createCipheriv", "tag", "encrypt", "KeyObject", "types", "wrap", "unwrap", "tag", "bitLength", "decrypt", "p2s", "tag", "unwrap", "<PERSON><PERSON><PERSON><PERSON>", "tag", "tag", "KeyObject", "KeyObject", "types", "bitLength", "encrypt", "p2s", "wrap", "tag", "jwe", "crypto", "promisify", "constants", "KeyObject", "isJWK", "KeyObject", "constants", "crypto", "promisify", "KeyObject", "createSecretKey", "types", "createSecretKey", "KeyObject", "promisify", "sign", "promisify", "verify", "jwk", "get", "base64url_exports", "decode", "encode", "encode", "decode", "decode", "decode", "createSecretKey", "generateKeyPairCb", "promisify", "promisify", "generateKeyPairCb", "createSecretKey", "generateKeyPair", "generateKeyPair", "generateSecret", "runtime_default"]}