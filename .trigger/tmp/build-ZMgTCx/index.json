{"configPath": "/home/<USER>/workspace/indie/lingxiai-gemini/.trigger/tmp/build-ZMgTCx/workspace/indie/lingxiai-gemini/trigger.config.mjs", "tasks": [{"id": "process-match-request", "retry": {"maxAttempts": 3, "factor": 2, "minTimeoutInMs": 2000, "maxTimeoutInMs": 30000, "randomize": true}, "maxDuration": 300, "filePath": "src/trigger/matching/processMatchRequest.ts", "exportName": "processMatchRequest", "entryPoint": "/home/<USER>/workspace/indie/lingxiai-gemini/.trigger/tmp/build-ZMgTCx/workspace/indie/lingxiai-gemini/src/trigger/matching/processMatchRequest.mjs"}], "workerEntryPoint": "/home/<USER>/workspace/indie/lingxiai-gemini/.trigger/tmp/build-ZMgTCx/.npm/_npx/f51a09bd0abf5f10/node_modules/trigger.dev/dist/esm/entryPoints/dev-run-worker.mjs", "runtime": "node", "customConditions": []}