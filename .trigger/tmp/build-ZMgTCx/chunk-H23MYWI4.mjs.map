{"version": 3, "sources": ["../../../node_modules/dotenv/package.json", "../../../node_modules/dotenv/lib/main.js"], "sourcesContent": ["{\n  \"name\": \"dotenv\",\n  \"version\": \"16.5.0\",\n  \"description\": \"Loads environment variables from .env file\",\n  \"main\": \"lib/main.js\",\n  \"types\": \"lib/main.d.ts\",\n  \"exports\": {\n    \".\": {\n      \"types\": \"./lib/main.d.ts\",\n      \"require\": \"./lib/main.js\",\n      \"default\": \"./lib/main.js\"\n    },\n    \"./config\": \"./config.js\",\n    \"./config.js\": \"./config.js\",\n    \"./lib/env-options\": \"./lib/env-options.js\",\n    \"./lib/env-options.js\": \"./lib/env-options.js\",\n    \"./lib/cli-options\": \"./lib/cli-options.js\",\n    \"./lib/cli-options.js\": \"./lib/cli-options.js\",\n    \"./package.json\": \"./package.json\"\n  },\n  \"scripts\": {\n    \"dts-check\": \"tsc --project tests/types/tsconfig.json\",\n    \"lint\": \"standard\",\n    \"pretest\": \"npm run lint && npm run dts-check\",\n    \"test\": \"tap run --allow-empty-coverage --disable-coverage --timeout=60000\",\n    \"test:coverage\": \"tap run --show-full-coverage --timeout=60000 --coverage-report=lcov\",\n    \"prerelease\": \"npm test\",\n    \"release\": \"standard-version\"\n  },\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"git://github.com/motdotla/dotenv.git\"\n  },\n  \"homepage\": \"https://github.com/motdotla/dotenv#readme\",\n  \"funding\": \"https://dotenvx.com\",\n  \"keywords\": [\n    \"dotenv\",\n    \"env\",\n    \".env\",\n    \"environment\",\n    \"variables\",\n    \"config\",\n    \"settings\"\n  ],\n  \"readmeFilename\": \"README.md\",\n  \"license\": \"BSD-2-Clause\",\n  \"devDependencies\": {\n    \"@types/node\": \"^18.11.3\",\n    \"decache\": \"^4.6.2\",\n    \"sinon\": \"^14.0.1\",\n    \"standard\": \"^17.0.0\",\n    \"standard-version\": \"^9.5.0\",\n    \"tap\": \"^19.2.0\",\n    \"typescript\": \"^4.8.4\"\n  },\n  \"engines\": {\n    \"node\": \">=12\"\n  },\n  \"browser\": {\n    \"fs\": false\n  }\n}\n", "const fs = require('fs')\nconst path = require('path')\nconst os = require('os')\nconst crypto = require('crypto')\nconst packageJson = require('../package.json')\n\nconst version = packageJson.version\n\nconst LINE = /(?:^|^)\\s*(?:export\\s+)?([\\w.-]+)(?:\\s*=\\s*?|:\\s+?)(\\s*'(?:\\\\'|[^'])*'|\\s*\"(?:\\\\\"|[^\"])*\"|\\s*`(?:\\\\`|[^`])*`|[^#\\r\\n]+)?\\s*(?:#.*)?(?:$|$)/mg\n\n// Parse src into an Object\nfunction parse (src) {\n  const obj = {}\n\n  // Convert buffer to string\n  let lines = src.toString()\n\n  // Convert line breaks to same format\n  lines = lines.replace(/\\r\\n?/mg, '\\n')\n\n  let match\n  while ((match = LINE.exec(lines)) != null) {\n    const key = match[1]\n\n    // Default undefined or null to empty string\n    let value = (match[2] || '')\n\n    // Remove whitespace\n    value = value.trim()\n\n    // Check if double quoted\n    const maybeQuote = value[0]\n\n    // Remove surrounding quotes\n    value = value.replace(/^(['\"`])([\\s\\S]*)\\1$/mg, '$2')\n\n    // Expand newlines if double quoted\n    if (maybeQuote === '\"') {\n      value = value.replace(/\\\\n/g, '\\n')\n      value = value.replace(/\\\\r/g, '\\r')\n    }\n\n    // Add to object\n    obj[key] = value\n  }\n\n  return obj\n}\n\nfunction _parseVault (options) {\n  const vaultPath = _vaultPath(options)\n\n  // Parse .env.vault\n  const result = DotenvModule.configDotenv({ path: vaultPath })\n  if (!result.parsed) {\n    const err = new Error(`MISSING_DATA: Cannot parse ${vaultPath} for an unknown reason`)\n    err.code = 'MISSING_DATA'\n    throw err\n  }\n\n  // handle scenario for comma separated keys - for use with key rotation\n  // example: DOTENV_KEY=\"dotenv://:<EMAIL>/vault/.env.vault?environment=prod,dotenv://:<EMAIL>/vault/.env.vault?environment=prod\"\n  const keys = _dotenvKey(options).split(',')\n  const length = keys.length\n\n  let decrypted\n  for (let i = 0; i < length; i++) {\n    try {\n      // Get full key\n      const key = keys[i].trim()\n\n      // Get instructions for decrypt\n      const attrs = _instructions(result, key)\n\n      // Decrypt\n      decrypted = DotenvModule.decrypt(attrs.ciphertext, attrs.key)\n\n      break\n    } catch (error) {\n      // last key\n      if (i + 1 >= length) {\n        throw error\n      }\n      // try next key\n    }\n  }\n\n  // Parse decrypted .env string\n  return DotenvModule.parse(decrypted)\n}\n\nfunction _warn (message) {\n  console.log(`[dotenv@${version}][WARN] ${message}`)\n}\n\nfunction _debug (message) {\n  console.log(`[dotenv@${version}][DEBUG] ${message}`)\n}\n\nfunction _dotenvKey (options) {\n  // prioritize developer directly setting options.DOTENV_KEY\n  if (options && options.DOTENV_KEY && options.DOTENV_KEY.length > 0) {\n    return options.DOTENV_KEY\n  }\n\n  // secondary infra already contains a DOTENV_KEY environment variable\n  if (process.env.DOTENV_KEY && process.env.DOTENV_KEY.length > 0) {\n    return process.env.DOTENV_KEY\n  }\n\n  // fallback to empty string\n  return ''\n}\n\nfunction _instructions (result, dotenvKey) {\n  // Parse DOTENV_KEY. Format is a URI\n  let uri\n  try {\n    uri = new URL(dotenvKey)\n  } catch (error) {\n    if (error.code === 'ERR_INVALID_URL') {\n      const err = new Error('INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development')\n      err.code = 'INVALID_DOTENV_KEY'\n      throw err\n    }\n\n    throw error\n  }\n\n  // Get decrypt key\n  const key = uri.password\n  if (!key) {\n    const err = new Error('INVALID_DOTENV_KEY: Missing key part')\n    err.code = 'INVALID_DOTENV_KEY'\n    throw err\n  }\n\n  // Get environment\n  const environment = uri.searchParams.get('environment')\n  if (!environment) {\n    const err = new Error('INVALID_DOTENV_KEY: Missing environment part')\n    err.code = 'INVALID_DOTENV_KEY'\n    throw err\n  }\n\n  // Get ciphertext payload\n  const environmentKey = `DOTENV_VAULT_${environment.toUpperCase()}`\n  const ciphertext = result.parsed[environmentKey] // DOTENV_VAULT_PRODUCTION\n  if (!ciphertext) {\n    const err = new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${environmentKey} in your .env.vault file.`)\n    err.code = 'NOT_FOUND_DOTENV_ENVIRONMENT'\n    throw err\n  }\n\n  return { ciphertext, key }\n}\n\nfunction _vaultPath (options) {\n  let possibleVaultPath = null\n\n  if (options && options.path && options.path.length > 0) {\n    if (Array.isArray(options.path)) {\n      for (const filepath of options.path) {\n        if (fs.existsSync(filepath)) {\n          possibleVaultPath = filepath.endsWith('.vault') ? filepath : `${filepath}.vault`\n        }\n      }\n    } else {\n      possibleVaultPath = options.path.endsWith('.vault') ? options.path : `${options.path}.vault`\n    }\n  } else {\n    possibleVaultPath = path.resolve(process.cwd(), '.env.vault')\n  }\n\n  if (fs.existsSync(possibleVaultPath)) {\n    return possibleVaultPath\n  }\n\n  return null\n}\n\nfunction _resolveHome (envPath) {\n  return envPath[0] === '~' ? path.join(os.homedir(), envPath.slice(1)) : envPath\n}\n\nfunction _configVault (options) {\n  const debug = Boolean(options && options.debug)\n  if (debug) {\n    _debug('Loading env from encrypted .env.vault')\n  }\n\n  const parsed = DotenvModule._parseVault(options)\n\n  let processEnv = process.env\n  if (options && options.processEnv != null) {\n    processEnv = options.processEnv\n  }\n\n  DotenvModule.populate(processEnv, parsed, options)\n\n  return { parsed }\n}\n\nfunction configDotenv (options) {\n  const dotenvPath = path.resolve(process.cwd(), '.env')\n  let encoding = 'utf8'\n  const debug = Boolean(options && options.debug)\n\n  if (options && options.encoding) {\n    encoding = options.encoding\n  } else {\n    if (debug) {\n      _debug('No encoding is specified. UTF-8 is used by default')\n    }\n  }\n\n  let optionPaths = [dotenvPath] // default, look for .env\n  if (options && options.path) {\n    if (!Array.isArray(options.path)) {\n      optionPaths = [_resolveHome(options.path)]\n    } else {\n      optionPaths = [] // reset default\n      for (const filepath of options.path) {\n        optionPaths.push(_resolveHome(filepath))\n      }\n    }\n  }\n\n  // Build the parsed data in a temporary object (because we need to return it).  Once we have the final\n  // parsed data, we will combine it with process.env (or options.processEnv if provided).\n  let lastError\n  const parsedAll = {}\n  for (const path of optionPaths) {\n    try {\n      // Specifying an encoding returns a string instead of a buffer\n      const parsed = DotenvModule.parse(fs.readFileSync(path, { encoding }))\n\n      DotenvModule.populate(parsedAll, parsed, options)\n    } catch (e) {\n      if (debug) {\n        _debug(`Failed to load ${path} ${e.message}`)\n      }\n      lastError = e\n    }\n  }\n\n  let processEnv = process.env\n  if (options && options.processEnv != null) {\n    processEnv = options.processEnv\n  }\n\n  DotenvModule.populate(processEnv, parsedAll, options)\n\n  if (lastError) {\n    return { parsed: parsedAll, error: lastError }\n  } else {\n    return { parsed: parsedAll }\n  }\n}\n\n// Populates process.env from .env file\nfunction config (options) {\n  // fallback to original dotenv if DOTENV_KEY is not set\n  if (_dotenvKey(options).length === 0) {\n    return DotenvModule.configDotenv(options)\n  }\n\n  const vaultPath = _vaultPath(options)\n\n  // dotenvKey exists but .env.vault file does not exist\n  if (!vaultPath) {\n    _warn(`You set DOTENV_KEY but you are missing a .env.vault file at ${vaultPath}. Did you forget to build it?`)\n\n    return DotenvModule.configDotenv(options)\n  }\n\n  return DotenvModule._configVault(options)\n}\n\nfunction decrypt (encrypted, keyStr) {\n  const key = Buffer.from(keyStr.slice(-64), 'hex')\n  let ciphertext = Buffer.from(encrypted, 'base64')\n\n  const nonce = ciphertext.subarray(0, 12)\n  const authTag = ciphertext.subarray(-16)\n  ciphertext = ciphertext.subarray(12, -16)\n\n  try {\n    const aesgcm = crypto.createDecipheriv('aes-256-gcm', key, nonce)\n    aesgcm.setAuthTag(authTag)\n    return `${aesgcm.update(ciphertext)}${aesgcm.final()}`\n  } catch (error) {\n    const isRange = error instanceof RangeError\n    const invalidKeyLength = error.message === 'Invalid key length'\n    const decryptionFailed = error.message === 'Unsupported state or unable to authenticate data'\n\n    if (isRange || invalidKeyLength) {\n      const err = new Error('INVALID_DOTENV_KEY: It must be 64 characters long (or more)')\n      err.code = 'INVALID_DOTENV_KEY'\n      throw err\n    } else if (decryptionFailed) {\n      const err = new Error('DECRYPTION_FAILED: Please check your DOTENV_KEY')\n      err.code = 'DECRYPTION_FAILED'\n      throw err\n    } else {\n      throw error\n    }\n  }\n}\n\n// Populate process.env with parsed values\nfunction populate (processEnv, parsed, options = {}) {\n  const debug = Boolean(options && options.debug)\n  const override = Boolean(options && options.override)\n\n  if (typeof parsed !== 'object') {\n    const err = new Error('OBJECT_REQUIRED: Please check the processEnv argument being passed to populate')\n    err.code = 'OBJECT_REQUIRED'\n    throw err\n  }\n\n  // Set process.env\n  for (const key of Object.keys(parsed)) {\n    if (Object.prototype.hasOwnProperty.call(processEnv, key)) {\n      if (override === true) {\n        processEnv[key] = parsed[key]\n      }\n\n      if (debug) {\n        if (override === true) {\n          _debug(`\"${key}\" is already defined and WAS overwritten`)\n        } else {\n          _debug(`\"${key}\" is already defined and was NOT overwritten`)\n        }\n      }\n    } else {\n      processEnv[key] = parsed[key]\n    }\n  }\n}\n\nconst DotenvModule = {\n  configDotenv,\n  _configVault,\n  _parseVault,\n  config,\n  decrypt,\n  parse,\n  populate\n}\n\nmodule.exports.configDotenv = DotenvModule.configDotenv\nmodule.exports._configVault = DotenvModule._configVault\nmodule.exports._parseVault = DotenvModule._parseVault\nmodule.exports.config = DotenvModule.config\nmodule.exports.decrypt = DotenvModule.decrypt\nmodule.exports.parse = DotenvModule.parse\nmodule.exports.populate = DotenvModule.populate\n\nmodule.exports = DotenvModule\n"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA,MACE,MAAQ;AAAA,MACR,SAAW;AAAA,MACX,aAAe;AAAA,MACf,MAAQ;AAAA,MACR,OAAS;AAAA,MACT,SAAW;AAAA,QACT,KAAK;AAAA,UACH,OAAS;AAAA,UACT,SAAW;AAAA,UACX,SAAW;AAAA,QACb;AAAA,QACA,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,qBAAqB;AAAA,QACrB,wBAAwB;AAAA,QACxB,qBAAqB;AAAA,QACrB,wBAAwB;AAAA,QACxB,kBAAkB;AAAA,MACpB;AAAA,MACA,SAAW;AAAA,QACT,aAAa;AAAA,QACb,MAAQ;AAAA,QACR,SAAW;AAAA,QACX,MAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,YAAc;AAAA,QACd,SAAW;AAAA,MACb;AAAA,MACA,YAAc;AAAA,QACZ,MAAQ;AAAA,QACR,KAAO;AAAA,MACT;AAAA,MACA,UAAY;AAAA,MACZ,SAAW;AAAA,MACX,UAAY;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,gBAAkB;AAAA,MAClB,SAAW;AAAA,MACX,iBAAmB;AAAA,QACjB,eAAe;AAAA,QACf,SAAW;AAAA,QACX,OAAS;AAAA,QACT,UAAY;AAAA,QACZ,oBAAoB;AAAA,QACpB,KAAO;AAAA,QACP,YAAc;AAAA,MAChB;AAAA,MACA,SAAW;AAAA,QACT,MAAQ;AAAA,MACV;AAAA,MACA,SAAW;AAAA,QACT,IAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;;;AC7DA;AAAA;AAAA;AAAA,QAAM,KAAK,UAAQ,IAAI;AACvB,QAAM,OAAO,UAAQ,MAAM;AAC3B,QAAM,KAAK,UAAQ,IAAI;AACvB,QAAM,SAAS,UAAQ,QAAQ;AAC/B,QAAM,cAAc;AAEpB,QAAM,UAAU,YAAY;AAE5B,QAAM,OAAO;AAGb,aAAS,MAAO,KAAK;AACnB,YAAM,MAAM,CAAC;AAGb,UAAI,QAAQ,IAAI,SAAS;AAGzB,cAAQ,MAAM,QAAQ,WAAW,IAAI;AAErC,UAAI;AACJ,cAAQ,QAAQ,KAAK,KAAK,KAAK,MAAM,MAAM;AACzC,cAAM,MAAM,MAAM,CAAC;AAGnB,YAAI,QAAS,MAAM,CAAC,KAAK;AAGzB,gBAAQ,MAAM,KAAK;AAGnB,cAAM,aAAa,MAAM,CAAC;AAG1B,gBAAQ,MAAM,QAAQ,0BAA0B,IAAI;AAGpD,YAAI,eAAe,KAAK;AACtB,kBAAQ,MAAM,QAAQ,QAAQ,IAAI;AAClC,kBAAQ,MAAM,QAAQ,QAAQ,IAAI;AAAA,QACpC;AAGA,YAAI,GAAG,IAAI;AAAA,MACb;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,YAAa,SAAS;AAC7B,YAAM,YAAY,WAAW,OAAO;AAGpC,YAAM,SAAS,aAAa,aAAa,EAAE,MAAM,UAAU,CAAC;AAC5D,UAAI,CAAC,OAAO,QAAQ;AAClB,cAAM,MAAM,IAAI,MAAM,8BAA8B,SAAS,wBAAwB;AACrF,YAAI,OAAO;AACX,cAAM;AAAA,MACR;AAIA,YAAM,OAAO,WAAW,OAAO,EAAE,MAAM,GAAG;AAC1C,YAAM,SAAS,KAAK;AAEpB,UAAI;AACJ,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAI;AAEF,gBAAM,MAAM,KAAK,CAAC,EAAE,KAAK;AAGzB,gBAAM,QAAQ,cAAc,QAAQ,GAAG;AAGvC,sBAAY,aAAa,QAAQ,MAAM,YAAY,MAAM,GAAG;AAE5D;AAAA,QACF,SAAS,OAAO;AAEd,cAAI,IAAI,KAAK,QAAQ;AACnB,kBAAM;AAAA,UACR;AAAA,QAEF;AAAA,MACF;AAGA,aAAO,aAAa,MAAM,SAAS;AAAA,IACrC;AAEA,aAAS,MAAO,SAAS;AACvB,cAAQ,IAAI,WAAW,OAAO,WAAW,OAAO,EAAE;AAAA,IACpD;AAEA,aAAS,OAAQ,SAAS;AACxB,cAAQ,IAAI,WAAW,OAAO,YAAY,OAAO,EAAE;AAAA,IACrD;AAEA,aAAS,WAAY,SAAS;AAE5B,UAAI,WAAW,QAAQ,cAAc,QAAQ,WAAW,SAAS,GAAG;AAClE,eAAO,QAAQ;AAAA,MACjB;AAGA,UAAI,QAAQ,IAAI,cAAc,QAAQ,IAAI,WAAW,SAAS,GAAG;AAC/D,eAAO,QAAQ,IAAI;AAAA,MACrB;AAGA,aAAO;AAAA,IACT;AAEA,aAAS,cAAe,QAAQ,WAAW;AAEzC,UAAI;AACJ,UAAI;AACF,cAAM,IAAI,IAAI,SAAS;AAAA,MACzB,SAAS,OAAO;AACd,YAAI,MAAM,SAAS,mBAAmB;AACpC,gBAAM,MAAM,IAAI,MAAM,4IAA4I;AAClK,cAAI,OAAO;AACX,gBAAM;AAAA,QACR;AAEA,cAAM;AAAA,MACR;AAGA,YAAM,MAAM,IAAI;AAChB,UAAI,CAAC,KAAK;AACR,cAAM,MAAM,IAAI,MAAM,sCAAsC;AAC5D,YAAI,OAAO;AACX,cAAM;AAAA,MACR;AAGA,YAAM,cAAc,IAAI,aAAa,IAAI,aAAa;AACtD,UAAI,CAAC,aAAa;AAChB,cAAM,MAAM,IAAI,MAAM,8CAA8C;AACpE,YAAI,OAAO;AACX,cAAM;AAAA,MACR;AAGA,YAAM,iBAAiB,gBAAgB,YAAY,YAAY,CAAC;AAChE,YAAM,aAAa,OAAO,OAAO,cAAc;AAC/C,UAAI,CAAC,YAAY;AACf,cAAM,MAAM,IAAI,MAAM,2DAA2D,cAAc,2BAA2B;AAC1H,YAAI,OAAO;AACX,cAAM;AAAA,MACR;AAEA,aAAO,EAAE,YAAY,IAAI;AAAA,IAC3B;AAEA,aAAS,WAAY,SAAS;AAC5B,UAAI,oBAAoB;AAExB,UAAI,WAAW,QAAQ,QAAQ,QAAQ,KAAK,SAAS,GAAG;AACtD,YAAI,MAAM,QAAQ,QAAQ,IAAI,GAAG;AAC/B,qBAAW,YAAY,QAAQ,MAAM;AACnC,gBAAI,GAAG,WAAW,QAAQ,GAAG;AAC3B,kCAAoB,SAAS,SAAS,QAAQ,IAAI,WAAW,GAAG,QAAQ;AAAA,YAC1E;AAAA,UACF;AAAA,QACF,OAAO;AACL,8BAAoB,QAAQ,KAAK,SAAS,QAAQ,IAAI,QAAQ,OAAO,GAAG,QAAQ,IAAI;AAAA,QACtF;AAAA,MACF,OAAO;AACL,4BAAoB,KAAK,QAAQ,QAAQ,IAAI,GAAG,YAAY;AAAA,MAC9D;AAEA,UAAI,GAAG,WAAW,iBAAiB,GAAG;AACpC,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,aAAc,SAAS;AAC9B,aAAO,QAAQ,CAAC,MAAM,MAAM,KAAK,KAAK,GAAG,QAAQ,GAAG,QAAQ,MAAM,CAAC,CAAC,IAAI;AAAA,IAC1E;AAEA,aAAS,aAAc,SAAS;AAC9B,YAAM,QAAQ,QAAQ,WAAW,QAAQ,KAAK;AAC9C,UAAI,OAAO;AACT,eAAO,uCAAuC;AAAA,MAChD;AAEA,YAAM,SAAS,aAAa,YAAY,OAAO;AAE/C,UAAI,aAAa,QAAQ;AACzB,UAAI,WAAW,QAAQ,cAAc,MAAM;AACzC,qBAAa,QAAQ;AAAA,MACvB;AAEA,mBAAa,SAAS,YAAY,QAAQ,OAAO;AAEjD,aAAO,EAAE,OAAO;AAAA,IAClB;AAEA,aAAS,aAAc,SAAS;AAC9B,YAAM,aAAa,KAAK,QAAQ,QAAQ,IAAI,GAAG,MAAM;AACrD,UAAI,WAAW;AACf,YAAM,QAAQ,QAAQ,WAAW,QAAQ,KAAK;AAE9C,UAAI,WAAW,QAAQ,UAAU;AAC/B,mBAAW,QAAQ;AAAA,MACrB,OAAO;AACL,YAAI,OAAO;AACT,iBAAO,oDAAoD;AAAA,QAC7D;AAAA,MACF;AAEA,UAAI,cAAc,CAAC,UAAU;AAC7B,UAAI,WAAW,QAAQ,MAAM;AAC3B,YAAI,CAAC,MAAM,QAAQ,QAAQ,IAAI,GAAG;AAChC,wBAAc,CAAC,aAAa,QAAQ,IAAI,CAAC;AAAA,QAC3C,OAAO;AACL,wBAAc,CAAC;AACf,qBAAW,YAAY,QAAQ,MAAM;AACnC,wBAAY,KAAK,aAAa,QAAQ,CAAC;AAAA,UACzC;AAAA,QACF;AAAA,MACF;AAIA,UAAI;AACJ,YAAM,YAAY,CAAC;AACnB,iBAAWA,SAAQ,aAAa;AAC9B,YAAI;AAEF,gBAAM,SAAS,aAAa,MAAM,GAAG,aAAaA,OAAM,EAAE,SAAS,CAAC,CAAC;AAErE,uBAAa,SAAS,WAAW,QAAQ,OAAO;AAAA,QAClD,SAAS,GAAG;AACV,cAAI,OAAO;AACT,mBAAO,kBAAkBA,KAAI,IAAI,EAAE,OAAO,EAAE;AAAA,UAC9C;AACA,sBAAY;AAAA,QACd;AAAA,MACF;AAEA,UAAI,aAAa,QAAQ;AACzB,UAAI,WAAW,QAAQ,cAAc,MAAM;AACzC,qBAAa,QAAQ;AAAA,MACvB;AAEA,mBAAa,SAAS,YAAY,WAAW,OAAO;AAEpD,UAAI,WAAW;AACb,eAAO,EAAE,QAAQ,WAAW,OAAO,UAAU;AAAA,MAC/C,OAAO;AACL,eAAO,EAAE,QAAQ,UAAU;AAAA,MAC7B;AAAA,IACF;AAGA,aAAS,OAAQ,SAAS;AAExB,UAAI,WAAW,OAAO,EAAE,WAAW,GAAG;AACpC,eAAO,aAAa,aAAa,OAAO;AAAA,MAC1C;AAEA,YAAM,YAAY,WAAW,OAAO;AAGpC,UAAI,CAAC,WAAW;AACd,cAAM,+DAA+D,SAAS,+BAA+B;AAE7G,eAAO,aAAa,aAAa,OAAO;AAAA,MAC1C;AAEA,aAAO,aAAa,aAAa,OAAO;AAAA,IAC1C;AAEA,aAAS,QAAS,WAAW,QAAQ;AACnC,YAAM,MAAM,OAAO,KAAK,OAAO,MAAM,GAAG,GAAG,KAAK;AAChD,UAAI,aAAa,OAAO,KAAK,WAAW,QAAQ;AAEhD,YAAM,QAAQ,WAAW,SAAS,GAAG,EAAE;AACvC,YAAM,UAAU,WAAW,SAAS,GAAG;AACvC,mBAAa,WAAW,SAAS,IAAI,GAAG;AAExC,UAAI;AACF,cAAM,SAAS,OAAO,iBAAiB,eAAe,KAAK,KAAK;AAChE,eAAO,WAAW,OAAO;AACzB,eAAO,GAAG,OAAO,OAAO,UAAU,CAAC,GAAG,OAAO,MAAM,CAAC;AAAA,MACtD,SAAS,OAAO;AACd,cAAM,UAAU,iBAAiB;AACjC,cAAM,mBAAmB,MAAM,YAAY;AAC3C,cAAM,mBAAmB,MAAM,YAAY;AAE3C,YAAI,WAAW,kBAAkB;AAC/B,gBAAM,MAAM,IAAI,MAAM,6DAA6D;AACnF,cAAI,OAAO;AACX,gBAAM;AAAA,QACR,WAAW,kBAAkB;AAC3B,gBAAM,MAAM,IAAI,MAAM,iDAAiD;AACvE,cAAI,OAAO;AACX,gBAAM;AAAA,QACR,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAGA,aAAS,SAAU,YAAY,QAAQ,UAAU,CAAC,GAAG;AACnD,YAAM,QAAQ,QAAQ,WAAW,QAAQ,KAAK;AAC9C,YAAM,WAAW,QAAQ,WAAW,QAAQ,QAAQ;AAEpD,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM,MAAM,IAAI,MAAM,gFAAgF;AACtG,YAAI,OAAO;AACX,cAAM;AAAA,MACR;AAGA,iBAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACrC,YAAI,OAAO,UAAU,eAAe,KAAK,YAAY,GAAG,GAAG;AACzD,cAAI,aAAa,MAAM;AACrB,uBAAW,GAAG,IAAI,OAAO,GAAG;AAAA,UAC9B;AAEA,cAAI,OAAO;AACT,gBAAI,aAAa,MAAM;AACrB,qBAAO,IAAI,GAAG,0CAA0C;AAAA,YAC1D,OAAO;AACL,qBAAO,IAAI,GAAG,8CAA8C;AAAA,YAC9D;AAAA,UACF;AAAA,QACF,OAAO;AACL,qBAAW,GAAG,IAAI,OAAO,GAAG;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAEA,QAAM,eAAe;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,WAAO,QAAQ,eAAe,aAAa;AAC3C,WAAO,QAAQ,eAAe,aAAa;AAC3C,WAAO,QAAQ,cAAc,aAAa;AAC1C,WAAO,QAAQ,SAAS,aAAa;AACrC,WAAO,QAAQ,UAAU,aAAa;AACtC,WAAO,QAAQ,QAAQ,aAAa;AACpC,WAAO,QAAQ,WAAW,aAAa;AAEvC,WAAO,UAAU;AAAA;AAAA;", "names": ["path"]}