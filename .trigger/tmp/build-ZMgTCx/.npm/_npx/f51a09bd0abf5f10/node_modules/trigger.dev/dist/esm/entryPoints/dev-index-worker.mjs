import {
  BuildManifest,
  StandardTaskCatalog,
  TracingSDK,
  ZodSchemaParsedError,
  indexerToWorkerMessages,
  normalizeImportPath,
  o,
  require_source_map_support,
  sendMessageInCatalog,
  taskCatalog
} from "../../../../../../../../chunk-RVZAMPQI.mjs";
import {
  __toESM,
  init_esm
} from "../../../../../../../../chunk-CYQ63LDX.mjs";

// ../../../.npm/_npx/f51a09bd0abf5f10/node_modules/trigger.dev/dist/esm/entryPoints/dev-index-worker.js
init_esm();
var import_source_map_support = __toESM(require_source_map_support(), 1);
import { readFile } from "node:fs/promises";

// ../../../.npm/_npx/f51a09bd0abf5f10/node_modules/trigger.dev/dist/esm/indexing/registerTasks.js
init_esm();
async function registerTasks(buildManifest2) {
  const importErrors2 = [];
  for (const file of buildManifest2.files) {
    const [error, module] = await tryImport(file.out);
    if (error) {
      if (typeof error === "string") {
        importErrors2.push({
          file: file.entry,
          message: error
        });
      } else {
        importErrors2.push({
          file: file.entry,
          message: error.message,
          stack: error.stack,
          name: error.name
        });
      }
      continue;
    }
    for (const exportName of getExportNames(module)) {
      const task = module[exportName] ?? module.default?.[exportName];
      if (!task) {
        continue;
      }
      if (task[Symbol.for("trigger.dev/task")]) {
        if (taskCatalog.taskExists(task.id)) {
          taskCatalog.registerTaskFileMetadata(task.id, {
            exportName,
            filePath: file.entry,
            entryPoint: file.out
          });
        }
      }
    }
  }
  return importErrors2;
}
async function tryImport(path) {
  try {
    const module = await import(normalizeImportPath(path));
    return [null, module];
  } catch (error) {
    return [error, null];
  }
}
function getExportNames(module) {
  const exports = [];
  if (!module) {
    return exports;
  }
  const exportKeys = Object.keys(module);
  if (exportKeys.length === 0) {
    return exports;
  }
  if (exportKeys.length === 1 && exportKeys[0] === "default") {
    return Object.keys(module.default);
  }
  return exportKeys;
}

// ../../../.npm/_npx/f51a09bd0abf5f10/node_modules/trigger.dev/dist/esm/entryPoints/dev-index-worker.js
import_source_map_support.default.install({
  handleUncaughtExceptions: false,
  environment: "node",
  hookRequire: false
});
process.on("uncaughtException", function(error, origin) {
  if (error instanceof Error) {
    process.send && process.send({
      type: "UNCAUGHT_EXCEPTION",
      payload: {
        error: { name: error.name, message: error.message, stack: error.stack },
        origin
      },
      version: "v1"
    });
  } else {
    process.send && process.send({
      type: "UNCAUGHT_EXCEPTION",
      payload: {
        error: {
          name: "Error",
          message: typeof error === "string" ? error : JSON.stringify(error)
        },
        origin
      },
      version: "v1"
    });
  }
});
taskCatalog.setGlobalTaskCatalog(new StandardTaskCatalog());
async function importConfig(configPath) {
  const configModule = await import(normalizeImportPath(configPath));
  const config2 = configModule?.default ?? configModule?.config;
  return {
    config: config2,
    handleError: configModule?.handleError
  };
}
async function loadBuildManifest() {
  const manifestContents = await readFile(o.TRIGGER_BUILD_MANIFEST_PATH, "utf-8");
  const raw = JSON.parse(manifestContents);
  return BuildManifest.parse(raw);
}
async function bootstrap() {
  const buildManifest2 = await loadBuildManifest();
  const { config: config2 } = await importConfig(buildManifest2.configPath);
  const tracingSDK = new TracingSDK({
    url: o.OTEL_EXPORTER_OTLP_ENDPOINT ?? "http://0.0.0.0:4318",
    instrumentations: config2.instrumentations ?? [],
    diagLogLevel: o.OTEL_LOG_LEVEL ?? "none",
    forceFlushTimeoutMillis: 3e4
  });
  const importErrors2 = await registerTasks(buildManifest2);
  return {
    tracingSDK,
    config: config2,
    buildManifest: buildManifest2,
    importErrors: importErrors2
  };
}
var { buildManifest, importErrors, config } = await bootstrap();
var tasks = taskCatalog.listTaskManifests();
if (config.retries?.default) {
  tasks = tasks.map((task) => {
    if (!task.retry) {
      return {
        ...task,
        retry: config.retries?.default
      };
    }
    return task;
  });
}
if (typeof config.maxDuration === "number") {
  tasks = tasks.map((task) => {
    if (typeof task.maxDuration !== "number") {
      return {
        ...task,
        maxDuration: config.maxDuration
      };
    }
    return task;
  });
}
await sendMessageInCatalog(indexerToWorkerMessages, "INDEX_COMPLETE", {
  manifest: {
    tasks,
    configPath: buildManifest.configPath,
    runtime: buildManifest.runtime,
    workerEntryPoint: buildManifest.runWorkerEntryPoint,
    controllerEntryPoint: buildManifest.runControllerEntryPoint,
    loaderEntryPoint: buildManifest.loaderEntryPoint,
    customConditions: buildManifest.customConditions
  },
  importErrors
}, async (msg) => {
  process.send?.(msg);
}).catch((err) => {
  if (err instanceof ZodSchemaParsedError) {
    return sendMessageInCatalog(indexerToWorkerMessages, "TASKS_FAILED_TO_PARSE", { zodIssues: err.error.issues, tasks }, async (msg) => {
      await process.send?.(msg);
    });
  } else {
    console.error("Failed to send TASKS_READY message", err);
  }
  return;
});
await new Promise((resolve) => {
  setTimeout(() => {
    resolve();
  }, 10);
});
//# sourceMappingURL=dev-index-worker.mjs.map
