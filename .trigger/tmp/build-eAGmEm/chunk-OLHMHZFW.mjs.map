{"version": 3, "sources": ["../../../node_modules/src/index.ts", "../../../node_modules/src/sql/functions/aggregate.ts", "../../../node_modules/src/sql/functions/vector.ts"], "sourcesContent": ["export * from './alias.ts';\nexport * from './column-builder.ts';\nexport * from './column.ts';\nexport * from './entity.ts';\nexport * from './errors.ts';\nexport * from './expressions.ts';\nexport * from './logger.ts';\nexport * from './operations.ts';\nexport * from './query-promise.ts';\nexport * from './relations.ts';\nexport * from './sql/index.ts';\nexport * from './subquery.ts';\nexport * from './table.ts';\nexport * from './utils.ts';\nexport * from './view-common.ts';\n", "import { type AnyColumn, Column } from '~/column.ts';\nimport { is } from '~/entity.ts';\nimport { type SQL, sql, type SQLWrapper } from '../sql.ts';\n\n/**\n * Returns the number of values in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // Number employees with null values\n * db.select({ value: count() }).from(employees)\n * // Number of employees where `name` is not null\n * db.select({ value: count(employees.name) }).from(employees)\n * ```\n *\n * @see countDistinct to get the number of non-duplicate values in `expression`\n */\nexport function count(expression?: SQLWrapper): SQL<number> {\n\treturn sql`count(${expression || sql.raw('*')})`.mapWith(Number);\n}\n\n/**\n * Returns the number of non-duplicate values in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // Number of employees where `name` is distinct\n * db.select({ value: countDistinct(employees.name) }).from(employees)\n * ```\n *\n * @see count to get the number of values in `expression`, including duplicates\n */\nexport function countDistinct(expression: SQLWrapper): SQL<number> {\n\treturn sql`count(distinct ${expression})`.mapWith(Number);\n}\n\n/**\n * Returns the average (arithmetic mean) of all non-null values in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // Average salary of an employee\n * db.select({ value: avg(employees.salary) }).from(employees)\n * ```\n *\n * @see avgDistinct to get the average of all non-null and non-duplicate values in `expression`\n */\nexport function avg(expression: SQLWrapper): SQL<string | null> {\n\treturn sql`avg(${expression})`.mapWith(String);\n}\n\n/**\n * Returns the average (arithmetic mean) of all non-null and non-duplicate values in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // Average salary of an employee where `salary` is distinct\n * db.select({ value: avgDistinct(employees.salary) }).from(employees)\n * ```\n *\n * @see avg to get the average of all non-null values in `expression`, including duplicates\n */\nexport function avgDistinct(expression: SQLWrapper): SQL<string | null> {\n\treturn sql`avg(distinct ${expression})`.mapWith(String);\n}\n\n/**\n * Returns the sum of all non-null values in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // Sum of every employee's salary\n * db.select({ value: sum(employees.salary) }).from(employees)\n * ```\n *\n * @see sumDistinct to get the sum of all non-null and non-duplicate values in `expression`\n */\nexport function sum(expression: SQLWrapper): SQL<string | null> {\n\treturn sql`sum(${expression})`.mapWith(String);\n}\n\n/**\n * Returns the sum of all non-null and non-duplicate values in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // Sum of every employee's salary where `salary` is distinct (no duplicates)\n * db.select({ value: sumDistinct(employees.salary) }).from(employees)\n * ```\n *\n * @see sum to get the sum of all non-null values in `expression`, including duplicates\n */\nexport function sumDistinct(expression: SQLWrapper): SQL<string | null> {\n\treturn sql`sum(distinct ${expression})`.mapWith(String);\n}\n\n/**\n * Returns the maximum value in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // The employee with the highest salary\n * db.select({ value: max(employees.salary) }).from(employees)\n * ```\n */\nexport function max<T extends SQLWrapper>(expression: T): SQL<(T extends AnyColumn ? T['_']['data'] : string) | null> {\n\treturn sql`max(${expression})`.mapWith(is(expression, Column) ? expression : String) as any;\n}\n\n/**\n * Returns the minimum value in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // The employee with the lowest salary\n * db.select({ value: min(employees.salary) }).from(employees)\n * ```\n */\nexport function min<T extends SQLWrapper>(expression: T): SQL<(T extends AnyColumn ? T['_']['data'] : string) | null> {\n\treturn sql`min(${expression})`.mapWith(is(expression, Column) ? expression : String) as any;\n}\n", "import type { AnyColumn } from '~/column.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport { type SQL, sql, type SQLWrapper } from '../sql.ts';\n\nfunction toSql(value: number[] | string[]): string {\n\treturn JSON.stringify(value);\n}\n\n/**\n * Used in sorting and in querying, if used in sorting,\n * this specifies that the given column or expression should be sorted in an order\n * that minimizes the L2 distance to the given value.\n * If used in querying, this specifies that it should return the L2 distance\n * between the given column or expression and the given value.\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(l2Distance(cars.embedding, embedding));\n * ```\n *\n * ```ts\n * // Select distance of cars and embedding\n * // to the given embedding\n * db.select({distance: l2Distance(cars.embedding, embedding)}).from(cars)\n * ```\n */\nexport function l2Distance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <-> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <-> ${value}`;\n}\n\n/**\n * L1 distance is one of the possible distance measures between two probability distribution vectors and it is\n * calculated as the sum of the absolute differences.\n * The smaller the distance between the observed probability vectors, the higher the accuracy of the synthetic data\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(l1Distance(cars.embedding, embedding));\n * ```\n *\n * ```ts\n * // Select distance of cars and embedding\n * // to the given embedding\n * db.select({distance: l1Distance(cars.embedding, embedding)}).from(cars)\n * ```\n */\nexport function l1Distance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <+> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <+> ${value}`;\n}\n\n/**\n * Used in sorting and in querying, if used in sorting,\n * this specifies that the given column or expression should be sorted in an order\n * that minimizes the inner product distance to the given value.\n * If used in querying, this specifies that it should return the inner product distance\n * between the given column or expression and the given value.\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(innerProduct(cars.embedding, embedding));\n * ```\n *\n * ```ts\n * // Select distance of cars and embedding\n * // to the given embedding\n * db.select({ distance: innerProduct(cars.embedding, embedding) }).from(cars)\n * ```\n */\nexport function innerProduct(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <#> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <#> ${value}`;\n}\n\n/**\n * Used in sorting and in querying, if used in sorting,\n * this specifies that the given column or expression should be sorted in an order\n * that minimizes the cosine distance to the given value.\n * If used in querying, this specifies that it should return the cosine distance\n * between the given column or expression and the given value.\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(cosineDistance(cars.embedding, embedding));\n * ```\n *\n * ```ts\n * // Select distance of cars and embedding\n * // to the given embedding\n * db.select({distance: cosineDistance(cars.embedding, embedding)}).from(cars)\n * ```\n */\nexport function cosineDistance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <=> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <=> ${value}`;\n}\n\n/**\n * Hamming distance between two strings or vectors of equal length is the number of positions at which the\n * corresponding symbols are different. In other words, it measures the minimum number of\n * substitutions required to change one string into the other, or equivalently,\n * the minimum number of errors that could have transformed one string into the other\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(hammingDistance(cars.embedding, embedding));\n * ```\n */\nexport function hammingDistance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <~> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <~> ${value}`;\n}\n\n/**\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(jaccardDistance(cars.embedding, embedding));\n * ```\n */\nexport function jaccardDistance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <%> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <%> ${value}`;\n}\n"], "mappings": ";;;;;;;;;;AAAA;;;ACAA;AAkBO,SAAS,MAAM,YAAsC;AAC3D,SAAO,YAAY,cAAc,IAAI,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM;AAChE;AAcO,SAAS,cAAc,YAAqC;AAClE,SAAO,qBAAqB,UAAU,IAAI,QAAQ,MAAM;AACzD;AAcO,SAAS,IAAI,YAA4C;AAC/D,SAAO,UAAU,UAAU,IAAI,QAAQ,MAAM;AAC9C;AAcO,SAAS,YAAY,YAA4C;AACvE,SAAO,mBAAmB,UAAU,IAAI,QAAQ,MAAM;AACvD;AAcO,SAAS,IAAI,YAA4C;AAC/D,SAAO,UAAU,UAAU,IAAI,QAAQ,MAAM;AAC9C;AAcO,SAAS,YAAY,YAA4C;AACvE,SAAO,mBAAmB,UAAU,IAAI,QAAQ,MAAM;AACvD;AAYO,SAAS,IAA0B,YAA4E;AACrH,SAAO,UAAU,UAAU,IAAI,QAAQ,GAAG,YAAY,MAAM,IAAI,aAAa,MAAM;AACpF;AAYO,SAAS,IAA0B,YAA4E;AACrH,SAAO,UAAU,UAAU,IAAI,QAAQ,GAAG,YAAY,MAAM,IAAI,aAAa,MAAM;AACpF;;;AC9HA;AAEA,SAAS,MAAM,OAAoC;AAClD,SAAO,KAAK,UAAU,KAAK;AAC5B;AAwBO,SAAS,WACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAsBO,SAAS,WACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAwBO,SAAS,aACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAwBO,SAAS,eACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAiBO,SAAS,gBACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAYO,SAAS,gBACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;", "names": []}