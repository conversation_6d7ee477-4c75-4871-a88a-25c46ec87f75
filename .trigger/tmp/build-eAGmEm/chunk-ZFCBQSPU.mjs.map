{"version": 3, "sources": ["../../../node_modules/@langchain/core/dist/prompts/few_shot.js", "../../../node_modules/@langchain/core/dist/prompts/chat.js", "../../../node_modules/@langchain/core/dist/messages/index.js", "../../../node_modules/@langchain/core/dist/messages/transformers.js", "../../../node_modules/@langchain/core/dist/messages/modifier.js", "../../../node_modules/@langchain/core/dist/prompts/image.js", "../../../node_modules/@langchain/core/dist/prompts/dict.js"], "sourcesContent": ["import { BaseStringPromptTemplate } from \"./string.js\";\nimport { checkValidTemplate, renderTemplate, } from \"./template.js\";\nimport { PromptTemplate } from \"./prompt.js\";\nimport { BaseChatPromptTemplate, } from \"./chat.js\";\n/**\n * Prompt template that contains few-shot examples.\n * @augments BasePromptTemplate\n * @augments FewShotPromptTemplateInput\n * @example\n * ```typescript\n * const examplePrompt = PromptTemplate.fromTemplate(\n *   \"Input: {input}\\nOutput: {output}\",\n * );\n *\n * const exampleSelector = await SemanticSimilarityExampleSelector.fromExamples(\n *   [\n *     { input: \"happy\", output: \"sad\" },\n *     { input: \"tall\", output: \"short\" },\n *     { input: \"energetic\", output: \"lethargic\" },\n *     { input: \"sunny\", output: \"gloomy\" },\n *     { input: \"windy\", output: \"calm\" },\n *   ],\n *   new OpenAIEmbeddings(),\n *   HNSWLib,\n *   { k: 1 },\n * );\n *\n * const dynamicPrompt = new FewShotPromptTemplate({\n *   exampleSelector,\n *   examplePrompt,\n *   prefix: \"Give the antonym of every input\",\n *   suffix: \"Input: {adjective}\\nOutput:\",\n *   inputVariables: [\"adjective\"],\n * });\n *\n * // Format the dynamic prompt with the input 'rainy'\n * console.log(await dynamicPrompt.format({ adjective: \"rainy\" }));\n *\n * ```\n */\nexport class FewShotPromptTemplate extends BaseStringPromptTemplate {\n    constructor(input) {\n        super(input);\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: false\n        });\n        Object.defineProperty(this, \"examples\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"exampleSelector\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"examplePrompt\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"suffix\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\"\n        });\n        Object.defineProperty(this, \"exampleSeparator\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\\n\\n\"\n        });\n        Object.defineProperty(this, \"prefix\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\"\n        });\n        Object.defineProperty(this, \"templateFormat\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"f-string\"\n        });\n        Object.defineProperty(this, \"validateTemplate\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.assign(this, input);\n        if (this.examples !== undefined && this.exampleSelector !== undefined) {\n            throw new Error(\"Only one of 'examples' and 'example_selector' should be provided\");\n        }\n        if (this.examples === undefined && this.exampleSelector === undefined) {\n            throw new Error(\"One of 'examples' and 'example_selector' should be provided\");\n        }\n        if (this.validateTemplate) {\n            let totalInputVariables = this.inputVariables;\n            if (this.partialVariables) {\n                totalInputVariables = totalInputVariables.concat(Object.keys(this.partialVariables));\n            }\n            checkValidTemplate(this.prefix + this.suffix, this.templateFormat, totalInputVariables);\n        }\n    }\n    _getPromptType() {\n        return \"few_shot\";\n    }\n    static lc_name() {\n        return \"FewShotPromptTemplate\";\n    }\n    async getExamples(inputVariables) {\n        if (this.examples !== undefined) {\n            return this.examples;\n        }\n        if (this.exampleSelector !== undefined) {\n            return this.exampleSelector.selectExamples(inputVariables);\n        }\n        throw new Error(\"One of 'examples' and 'example_selector' should be provided\");\n    }\n    async partial(values) {\n        const newInputVariables = this.inputVariables.filter((iv) => !(iv in values));\n        const newPartialVariables = {\n            ...(this.partialVariables ?? {}),\n            ...values,\n        };\n        const promptDict = {\n            ...this,\n            inputVariables: newInputVariables,\n            partialVariables: newPartialVariables,\n        };\n        return new FewShotPromptTemplate(promptDict);\n    }\n    /**\n     * Formats the prompt with the given values.\n     * @param values The values to format the prompt with.\n     * @returns A promise that resolves to a string representing the formatted prompt.\n     */\n    async format(values) {\n        const allValues = await this.mergePartialAndUserVariables(values);\n        const examples = await this.getExamples(allValues);\n        const exampleStrings = await Promise.all(examples.map((example) => this.examplePrompt.format(example)));\n        const template = [this.prefix, ...exampleStrings, this.suffix].join(this.exampleSeparator);\n        return renderTemplate(template, this.templateFormat, allValues);\n    }\n    serialize() {\n        if (this.exampleSelector || !this.examples) {\n            throw new Error(\"Serializing an example selector is not currently supported\");\n        }\n        if (this.outputParser !== undefined) {\n            throw new Error(\"Serializing an output parser is not currently supported\");\n        }\n        return {\n            _type: this._getPromptType(),\n            input_variables: this.inputVariables,\n            example_prompt: this.examplePrompt.serialize(),\n            example_separator: this.exampleSeparator,\n            suffix: this.suffix,\n            prefix: this.prefix,\n            template_format: this.templateFormat,\n            examples: this.examples,\n        };\n    }\n    static async deserialize(data) {\n        const { example_prompt } = data;\n        if (!example_prompt) {\n            throw new Error(\"Missing example prompt\");\n        }\n        const examplePrompt = await PromptTemplate.deserialize(example_prompt);\n        let examples;\n        if (Array.isArray(data.examples)) {\n            examples = data.examples;\n        }\n        else {\n            throw new Error(\"Invalid examples format. Only list or string are supported.\");\n        }\n        return new FewShotPromptTemplate({\n            inputVariables: data.input_variables,\n            examplePrompt,\n            examples,\n            exampleSeparator: data.example_separator,\n            prefix: data.prefix,\n            suffix: data.suffix,\n            templateFormat: data.template_format,\n        });\n    }\n}\n/**\n * Chat prompt template that contains few-shot examples.\n * @augments BasePromptTemplateInput\n * @augments FewShotChatMessagePromptTemplateInput\n */\nexport class FewShotChatMessagePromptTemplate extends BaseChatPromptTemplate {\n    _getPromptType() {\n        return \"few_shot_chat\";\n    }\n    static lc_name() {\n        return \"FewShotChatMessagePromptTemplate\";\n    }\n    constructor(fields) {\n        super(fields);\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"examples\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"exampleSelector\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"examplePrompt\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"suffix\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\"\n        });\n        Object.defineProperty(this, \"exampleSeparator\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\\n\\n\"\n        });\n        Object.defineProperty(this, \"prefix\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\"\n        });\n        Object.defineProperty(this, \"templateFormat\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"f-string\"\n        });\n        Object.defineProperty(this, \"validateTemplate\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        this.examples = fields.examples;\n        this.examplePrompt = fields.examplePrompt;\n        this.exampleSeparator = fields.exampleSeparator ?? \"\\n\\n\";\n        this.exampleSelector = fields.exampleSelector;\n        this.prefix = fields.prefix ?? \"\";\n        this.suffix = fields.suffix ?? \"\";\n        this.templateFormat = fields.templateFormat ?? \"f-string\";\n        this.validateTemplate = fields.validateTemplate ?? true;\n        if (this.examples !== undefined && this.exampleSelector !== undefined) {\n            throw new Error(\"Only one of 'examples' and 'example_selector' should be provided\");\n        }\n        if (this.examples === undefined && this.exampleSelector === undefined) {\n            throw new Error(\"One of 'examples' and 'example_selector' should be provided\");\n        }\n        if (this.validateTemplate) {\n            let totalInputVariables = this.inputVariables;\n            if (this.partialVariables) {\n                totalInputVariables = totalInputVariables.concat(Object.keys(this.partialVariables));\n            }\n            checkValidTemplate(this.prefix + this.suffix, this.templateFormat, totalInputVariables);\n        }\n    }\n    async getExamples(inputVariables) {\n        if (this.examples !== undefined) {\n            return this.examples;\n        }\n        if (this.exampleSelector !== undefined) {\n            return this.exampleSelector.selectExamples(inputVariables);\n        }\n        throw new Error(\"One of 'examples' and 'example_selector' should be provided\");\n    }\n    /**\n     * Formats the list of values and returns a list of formatted messages.\n     * @param values The values to format the prompt with.\n     * @returns A promise that resolves to a string representing the formatted prompt.\n     */\n    async formatMessages(values) {\n        const allValues = await this.mergePartialAndUserVariables(values);\n        let examples = await this.getExamples(allValues);\n        examples = examples.map((example) => {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const result = {};\n            this.examplePrompt.inputVariables.forEach((inputVariable) => {\n                result[inputVariable] = example[inputVariable];\n            });\n            return result;\n        });\n        const messages = [];\n        for (const example of examples) {\n            const exampleMessages = await this.examplePrompt.formatMessages(example);\n            messages.push(...exampleMessages);\n        }\n        return messages;\n    }\n    /**\n     * Formats the prompt with the given values.\n     * @param values The values to format the prompt with.\n     * @returns A promise that resolves to a string representing the formatted prompt.\n     */\n    async format(values) {\n        const allValues = await this.mergePartialAndUserVariables(values);\n        const examples = await this.getExamples(allValues);\n        const exampleMessages = await Promise.all(examples.map((example) => this.examplePrompt.formatMessages(example)));\n        const exampleStrings = exampleMessages\n            .flat()\n            .map((message) => message.content);\n        const template = [this.prefix, ...exampleStrings, this.suffix].join(this.exampleSeparator);\n        return renderTemplate(template, this.templateFormat, allValues);\n    }\n    /**\n     * Partially formats the prompt with the given values.\n     * @param values The values to partially format the prompt with.\n     * @returns A promise that resolves to an instance of `FewShotChatMessagePromptTemplate` with the given values partially formatted.\n     */\n    async partial(values) {\n        const newInputVariables = this.inputVariables.filter((variable) => !(variable in values));\n        const newPartialVariables = {\n            ...(this.partialVariables ?? {}),\n            ...values,\n        };\n        const promptDict = {\n            ...this,\n            inputVariables: newInputVariables,\n            partialVariables: newPartialVariables,\n        };\n        return new FewShotChatMessagePromptTemplate(promptDict);\n    }\n}\n", "// Default generic \"any\" values are for backwards compatibility.\n// Replace with \"string\" when we are comfortable with a breaking change.\nimport { AIMessage, HumanMessage, SystemMessage, BaseMessage, ChatMessage, coerceMessageLikeToMessage, isBaseMessage, } from \"../messages/index.js\";\nimport { ChatPromptValue, } from \"../prompt_values.js\";\nimport { Runnable } from \"../runnables/base.js\";\nimport { BaseStringPromptTemplate } from \"./string.js\";\nimport { BasePromptTemplate, } from \"./base.js\";\nimport { PromptTemplate, } from \"./prompt.js\";\nimport { ImagePromptTemplate } from \"./image.js\";\nimport { parseFString, parseMustache, } from \"./template.js\";\nimport { addLangChainErrorFields } from \"../errors/index.js\";\nimport { DictPromptTemplate } from \"./dict.js\";\n/**\n * Abstract class that serves as a base for creating message prompt\n * templates. It defines how to format messages for different roles in a\n * conversation.\n */\nexport class BaseMessagePromptTemplate extends Runnable {\n    constructor() {\n        super(...arguments);\n        Object.defineProperty(this, \"lc_namespace\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: [\"langchain_core\", \"prompts\", \"chat\"]\n        });\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n    }\n    /**\n     * Calls the formatMessages method with the provided input and options.\n     * @param input Input for the formatMessages method\n     * @param options Optional BaseCallbackConfig\n     * @returns Formatted output messages\n     */\n    async invoke(input, options) {\n        return this._callWithConfig((input) => this.formatMessages(input), input, { ...options, runType: \"prompt\" });\n    }\n}\n/**\n * Class that represents a placeholder for messages in a chat prompt. It\n * extends the BaseMessagePromptTemplate.\n */\nexport class MessagesPlaceholder extends BaseMessagePromptTemplate {\n    static lc_name() {\n        return \"MessagesPlaceholder\";\n    }\n    constructor(fields) {\n        if (typeof fields === \"string\") {\n            // eslint-disable-next-line no-param-reassign\n            fields = { variableName: fields };\n        }\n        super(fields);\n        Object.defineProperty(this, \"variableName\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"optional\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.variableName = fields.variableName;\n        this.optional = fields.optional ?? false;\n    }\n    get inputVariables() {\n        return [this.variableName];\n    }\n    async formatMessages(values) {\n        const input = values[this.variableName];\n        if (this.optional && !input) {\n            return [];\n        }\n        else if (!input) {\n            const error = new Error(`Field \"${this.variableName}\" in prompt uses a MessagesPlaceholder, which expects an array of BaseMessages as an input value. Received: undefined`);\n            error.name = \"InputFormatError\";\n            throw error;\n        }\n        let formattedMessages;\n        try {\n            if (Array.isArray(input)) {\n                formattedMessages = input.map(coerceMessageLikeToMessage);\n            }\n            else {\n                formattedMessages = [coerceMessageLikeToMessage(input)];\n            }\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        }\n        catch (e) {\n            const readableInput = typeof input === \"string\" ? input : JSON.stringify(input, null, 2);\n            const error = new Error([\n                `Field \"${this.variableName}\" in prompt uses a MessagesPlaceholder, which expects an array of BaseMessages or coerceable values as input.`,\n                `Received value: ${readableInput}`,\n                `Additional message: ${e.message}`,\n            ].join(\"\\n\\n\"));\n            error.name = \"InputFormatError\";\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            error.lc_error_code = e.lc_error_code;\n            throw error;\n        }\n        return formattedMessages;\n    }\n}\n/**\n * Abstract class that serves as a base for creating message string prompt\n * templates. It extends the BaseMessagePromptTemplate.\n */\nexport class BaseMessageStringPromptTemplate extends BaseMessagePromptTemplate {\n    constructor(fields) {\n        if (!(\"prompt\" in fields)) {\n            // eslint-disable-next-line no-param-reassign\n            fields = { prompt: fields };\n        }\n        super(fields);\n        Object.defineProperty(this, \"prompt\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.prompt = fields.prompt;\n    }\n    get inputVariables() {\n        return this.prompt.inputVariables;\n    }\n    async formatMessages(values) {\n        return [await this.format(values)];\n    }\n}\n/**\n * Abstract class that serves as a base for creating chat prompt\n * templates. It extends the BasePromptTemplate.\n */\nexport class BaseChatPromptTemplate extends BasePromptTemplate {\n    constructor(input) {\n        super(input);\n    }\n    async format(values) {\n        return (await this.formatPromptValue(values)).toString();\n    }\n    async formatPromptValue(values) {\n        const resultMessages = await this.formatMessages(values);\n        return new ChatPromptValue(resultMessages);\n    }\n}\n/**\n * Class that represents a chat message prompt template. It extends the\n * BaseMessageStringPromptTemplate.\n */\nexport class ChatMessagePromptTemplate extends BaseMessageStringPromptTemplate {\n    static lc_name() {\n        return \"ChatMessagePromptTemplate\";\n    }\n    constructor(fields, role) {\n        if (!(\"prompt\" in fields)) {\n            // eslint-disable-next-line no-param-reassign, @typescript-eslint/no-non-null-assertion\n            fields = { prompt: fields, role: role };\n        }\n        super(fields);\n        Object.defineProperty(this, \"role\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.role = fields.role;\n    }\n    async format(values) {\n        return new ChatMessage(await this.prompt.format(values), this.role);\n    }\n    static fromTemplate(template, role, options) {\n        return new this(PromptTemplate.fromTemplate(template, {\n            templateFormat: options?.templateFormat,\n        }), role);\n    }\n}\nfunction isTextTemplateParam(param) {\n    if (param === null || typeof param !== \"object\" || Array.isArray(param)) {\n        return false;\n    }\n    return (Object.keys(param).length === 1 &&\n        \"text\" in param &&\n        typeof param.text === \"string\");\n}\nfunction isImageTemplateParam(param) {\n    if (param === null || typeof param !== \"object\" || Array.isArray(param)) {\n        return false;\n    }\n    return (\"image_url\" in param &&\n        (typeof param.image_url === \"string\" ||\n            (typeof param.image_url === \"object\" &&\n                param.image_url !== null &&\n                \"url\" in param.image_url &&\n                typeof param.image_url.url === \"string\")));\n}\nclass _StringImageMessagePromptTemplate extends BaseMessagePromptTemplate {\n    static _messageClass() {\n        throw new Error(\"Can not invoke _messageClass from inside _StringImageMessagePromptTemplate\");\n    }\n    constructor(\n    /** @TODO When we come up with a better way to type prompt templates, fix this */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    fields, additionalOptions) {\n        if (!(\"prompt\" in fields)) {\n            // eslint-disable-next-line no-param-reassign\n            fields = { prompt: fields };\n        }\n        super(fields);\n        Object.defineProperty(this, \"lc_namespace\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: [\"langchain_core\", \"prompts\", \"chat\"]\n        });\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"inputVariables\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: []\n        });\n        Object.defineProperty(this, \"additionalOptions\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: {}\n        });\n        Object.defineProperty(this, \"prompt\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"messageClass\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        // ChatMessage contains role field, others don't.\n        // Because of this, we have a separate class property for ChatMessage.\n        Object.defineProperty(this, \"chatMessageClass\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.prompt = fields.prompt;\n        if (Array.isArray(this.prompt)) {\n            let inputVariables = [];\n            this.prompt.forEach((prompt) => {\n                if (\"inputVariables\" in prompt) {\n                    inputVariables = inputVariables.concat(prompt.inputVariables);\n                }\n            });\n            this.inputVariables = inputVariables;\n        }\n        else {\n            this.inputVariables = this.prompt.inputVariables;\n        }\n        this.additionalOptions = additionalOptions ?? this.additionalOptions;\n    }\n    createMessage(content) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const constructor = this.constructor;\n        if (constructor._messageClass()) {\n            const MsgClass = constructor._messageClass();\n            return new MsgClass({ content });\n        }\n        else if (constructor.chatMessageClass) {\n            const MsgClass = constructor.chatMessageClass();\n            // Assuming ChatMessage constructor also takes a content argument\n            return new MsgClass({\n                content,\n                role: this.getRoleFromMessageClass(MsgClass.lc_name()),\n            });\n        }\n        else {\n            throw new Error(\"No message class defined\");\n        }\n    }\n    getRoleFromMessageClass(name) {\n        switch (name) {\n            case \"HumanMessage\":\n                return \"human\";\n            case \"AIMessage\":\n                return \"ai\";\n            case \"SystemMessage\":\n                return \"system\";\n            case \"ChatMessage\":\n                return \"chat\";\n            default:\n                throw new Error(\"Invalid message class name\");\n        }\n    }\n    static fromTemplate(template, additionalOptions) {\n        if (typeof template === \"string\") {\n            return new this(PromptTemplate.fromTemplate(template, additionalOptions));\n        }\n        const prompt = [];\n        for (const item of template) {\n            // handle string cases\n            if (typeof item === \"string\") {\n                prompt.push(PromptTemplate.fromTemplate(item, additionalOptions));\n            }\n            else if (item === null) {\n                // pass\n            }\n            else if (isTextTemplateParam(item)) {\n                let text = \"\";\n                if (typeof item.text === \"string\") {\n                    text = item.text ?? \"\";\n                }\n                const options = {\n                    ...additionalOptions,\n                    additionalContentFields: item,\n                };\n                prompt.push(PromptTemplate.fromTemplate(text, options));\n            }\n            else if (isImageTemplateParam(item)) {\n                let imgTemplate = item.image_url ?? \"\";\n                let imgTemplateObject;\n                let inputVariables = [];\n                if (typeof imgTemplate === \"string\") {\n                    let parsedTemplate;\n                    if (additionalOptions?.templateFormat === \"mustache\") {\n                        parsedTemplate = parseMustache(imgTemplate);\n                    }\n                    else {\n                        parsedTemplate = parseFString(imgTemplate);\n                    }\n                    const variables = parsedTemplate.flatMap((item) => item.type === \"variable\" ? [item.name] : []);\n                    if ((variables?.length ?? 0) > 0) {\n                        if (variables.length > 1) {\n                            throw new Error(`Only one format variable allowed per image template.\\nGot: ${variables}\\nFrom: ${imgTemplate}`);\n                        }\n                        inputVariables = [variables[0]];\n                    }\n                    else {\n                        inputVariables = [];\n                    }\n                    imgTemplate = { url: imgTemplate };\n                    imgTemplateObject = new ImagePromptTemplate({\n                        template: imgTemplate,\n                        inputVariables,\n                        templateFormat: additionalOptions?.templateFormat,\n                        additionalContentFields: item,\n                    });\n                }\n                else if (typeof imgTemplate === \"object\") {\n                    if (\"url\" in imgTemplate) {\n                        let parsedTemplate;\n                        if (additionalOptions?.templateFormat === \"mustache\") {\n                            parsedTemplate = parseMustache(imgTemplate.url);\n                        }\n                        else {\n                            parsedTemplate = parseFString(imgTemplate.url);\n                        }\n                        inputVariables = parsedTemplate.flatMap((item) => item.type === \"variable\" ? [item.name] : []);\n                    }\n                    else {\n                        inputVariables = [];\n                    }\n                    imgTemplateObject = new ImagePromptTemplate({\n                        template: imgTemplate,\n                        inputVariables,\n                        templateFormat: additionalOptions?.templateFormat,\n                        additionalContentFields: item,\n                    });\n                }\n                else {\n                    throw new Error(\"Invalid image template\");\n                }\n                prompt.push(imgTemplateObject);\n            }\n            else if (typeof item === \"object\") {\n                prompt.push(new DictPromptTemplate({\n                    template: item,\n                    templateFormat: additionalOptions?.templateFormat,\n                }));\n            }\n        }\n        return new this({ prompt, additionalOptions });\n    }\n    async format(input) {\n        // eslint-disable-next-line no-instanceof/no-instanceof\n        if (this.prompt instanceof BaseStringPromptTemplate) {\n            const text = await this.prompt.format(input);\n            return this.createMessage(text);\n        }\n        else {\n            const content = [];\n            for (const prompt of this.prompt) {\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                let inputs = {};\n                if (!(\"inputVariables\" in prompt)) {\n                    throw new Error(`Prompt ${prompt} does not have inputVariables defined.`);\n                }\n                for (const item of prompt.inputVariables) {\n                    if (!inputs) {\n                        inputs = { [item]: input[item] };\n                    }\n                    inputs = { ...inputs, [item]: input[item] };\n                }\n                // eslint-disable-next-line no-instanceof/no-instanceof\n                if (prompt instanceof BaseStringPromptTemplate) {\n                    const formatted = await prompt.format(inputs);\n                    let additionalContentFields;\n                    if (\"additionalContentFields\" in prompt) {\n                        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                        additionalContentFields = prompt.additionalContentFields;\n                    }\n                    content.push({\n                        ...additionalContentFields,\n                        type: \"text\",\n                        text: formatted,\n                    });\n                    /** @TODO replace this */\n                    // eslint-disable-next-line no-instanceof/no-instanceof\n                }\n                else if (prompt instanceof ImagePromptTemplate) {\n                    const formatted = await prompt.format(inputs);\n                    let additionalContentFields;\n                    if (\"additionalContentFields\" in prompt) {\n                        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                        additionalContentFields = prompt.additionalContentFields;\n                    }\n                    content.push({\n                        ...additionalContentFields,\n                        type: \"image_url\",\n                        image_url: formatted,\n                    });\n                    // eslint-disable-next-line no-instanceof/no-instanceof\n                }\n                else if (prompt instanceof DictPromptTemplate) {\n                    const formatted = await prompt.format(inputs);\n                    let additionalContentFields;\n                    if (\"additionalContentFields\" in prompt) {\n                        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                        additionalContentFields = prompt.additionalContentFields;\n                    }\n                    content.push({\n                        ...additionalContentFields,\n                        ...formatted,\n                    });\n                }\n            }\n            return this.createMessage(content);\n        }\n    }\n    async formatMessages(values) {\n        return [await this.format(values)];\n    }\n}\n/**\n * Class that represents a human message prompt template. It extends the\n * BaseMessageStringPromptTemplate.\n * @example\n * ```typescript\n * const message = HumanMessagePromptTemplate.fromTemplate(\"{text}\");\n * const formatted = await message.format({ text: \"Hello world!\" });\n *\n * const chatPrompt = ChatPromptTemplate.fromMessages([message]);\n * const formattedChatPrompt = await chatPrompt.invoke({\n *   text: \"Hello world!\",\n * });\n * ```\n */\nexport class HumanMessagePromptTemplate extends _StringImageMessagePromptTemplate {\n    static _messageClass() {\n        return HumanMessage;\n    }\n    static lc_name() {\n        return \"HumanMessagePromptTemplate\";\n    }\n}\n/**\n * Class that represents an AI message prompt template. It extends the\n * BaseMessageStringPromptTemplate.\n */\nexport class AIMessagePromptTemplate extends _StringImageMessagePromptTemplate {\n    static _messageClass() {\n        return AIMessage;\n    }\n    static lc_name() {\n        return \"AIMessagePromptTemplate\";\n    }\n}\n/**\n * Class that represents a system message prompt template. It extends the\n * BaseMessageStringPromptTemplate.\n * @example\n * ```typescript\n * const message = SystemMessagePromptTemplate.fromTemplate(\"{text}\");\n * const formatted = await message.format({ text: \"Hello world!\" });\n *\n * const chatPrompt = ChatPromptTemplate.fromMessages([message]);\n * const formattedChatPrompt = await chatPrompt.invoke({\n *   text: \"Hello world!\",\n * });\n * ```\n */\nexport class SystemMessagePromptTemplate extends _StringImageMessagePromptTemplate {\n    static _messageClass() {\n        return SystemMessage;\n    }\n    static lc_name() {\n        return \"SystemMessagePromptTemplate\";\n    }\n}\nfunction _isBaseMessagePromptTemplate(baseMessagePromptTemplateLike) {\n    return (typeof baseMessagePromptTemplateLike\n        .formatMessages === \"function\");\n}\nfunction _coerceMessagePromptTemplateLike(messagePromptTemplateLike, extra) {\n    if (_isBaseMessagePromptTemplate(messagePromptTemplateLike) ||\n        isBaseMessage(messagePromptTemplateLike)) {\n        return messagePromptTemplateLike;\n    }\n    if (Array.isArray(messagePromptTemplateLike) &&\n        messagePromptTemplateLike[0] === \"placeholder\") {\n        const messageContent = messagePromptTemplateLike[1];\n        if (extra?.templateFormat === \"mustache\" &&\n            typeof messageContent === \"string\" &&\n            messageContent.slice(0, 2) === \"{{\" &&\n            messageContent.slice(-2) === \"}}\") {\n            const variableName = messageContent.slice(2, -2);\n            return new MessagesPlaceholder({ variableName, optional: true });\n        }\n        else if (typeof messageContent === \"string\" &&\n            messageContent[0] === \"{\" &&\n            messageContent[messageContent.length - 1] === \"}\") {\n            const variableName = messageContent.slice(1, -1);\n            return new MessagesPlaceholder({ variableName, optional: true });\n        }\n        throw new Error(`Invalid placeholder template for format ${extra?.templateFormat ?? `\"f-string\"`}: \"${messagePromptTemplateLike[1]}\". Expected a variable name surrounded by ${extra?.templateFormat === \"mustache\" ? \"double\" : \"single\"} curly braces.`);\n    }\n    const message = coerceMessageLikeToMessage(messagePromptTemplateLike);\n    let templateData;\n    if (typeof message.content === \"string\") {\n        templateData = message.content;\n    }\n    else {\n        // Assuming message.content is an array of complex objects, transform it.\n        templateData = message.content.map((item) => {\n            if (\"text\" in item) {\n                return { ...item, text: item.text };\n            }\n            else if (\"image_url\" in item) {\n                return { ...item, image_url: item.image_url };\n            }\n            else {\n                return item;\n            }\n        });\n    }\n    if (message._getType() === \"human\") {\n        return HumanMessagePromptTemplate.fromTemplate(templateData, extra);\n    }\n    else if (message._getType() === \"ai\") {\n        return AIMessagePromptTemplate.fromTemplate(templateData, extra);\n    }\n    else if (message._getType() === \"system\") {\n        return SystemMessagePromptTemplate.fromTemplate(templateData, extra);\n    }\n    else if (ChatMessage.isInstance(message)) {\n        return ChatMessagePromptTemplate.fromTemplate(message.content, message.role, extra);\n    }\n    else {\n        throw new Error(`Could not coerce message prompt template from input. Received message type: \"${message._getType()}\".`);\n    }\n}\nfunction isMessagesPlaceholder(x) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return x.constructor.lc_name() === \"MessagesPlaceholder\";\n}\n/**\n * Class that represents a chat prompt. It extends the\n * BaseChatPromptTemplate and uses an array of BaseMessagePromptTemplate\n * instances to format a series of messages for a conversation.\n * @example\n * ```typescript\n * const message = SystemMessagePromptTemplate.fromTemplate(\"{text}\");\n * const chatPrompt = ChatPromptTemplate.fromMessages([\n *   [\"ai\", \"You are a helpful assistant.\"],\n *   message,\n * ]);\n * const formattedChatPrompt = await chatPrompt.invoke({\n *   text: \"Hello world!\",\n * });\n * ```\n */\nexport class ChatPromptTemplate extends BaseChatPromptTemplate {\n    static lc_name() {\n        return \"ChatPromptTemplate\";\n    }\n    get lc_aliases() {\n        return {\n            promptMessages: \"messages\",\n        };\n    }\n    constructor(input) {\n        super(input);\n        Object.defineProperty(this, \"promptMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"validateTemplate\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"templateFormat\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"f-string\"\n        });\n        // If input is mustache and validateTemplate is not defined, set it to false\n        if (input.templateFormat === \"mustache\" &&\n            input.validateTemplate === undefined) {\n            this.validateTemplate = false;\n        }\n        Object.assign(this, input);\n        if (this.validateTemplate) {\n            const inputVariablesMessages = new Set();\n            for (const promptMessage of this.promptMessages) {\n                // eslint-disable-next-line no-instanceof/no-instanceof\n                if (promptMessage instanceof BaseMessage)\n                    continue;\n                for (const inputVariable of promptMessage.inputVariables) {\n                    inputVariablesMessages.add(inputVariable);\n                }\n            }\n            const totalInputVariables = this.inputVariables;\n            const inputVariablesInstance = new Set(this.partialVariables\n                ? totalInputVariables.concat(Object.keys(this.partialVariables))\n                : totalInputVariables);\n            const difference = new Set([...inputVariablesInstance].filter((x) => !inputVariablesMessages.has(x)));\n            if (difference.size > 0) {\n                throw new Error(`Input variables \\`${[\n                    ...difference,\n                ]}\\` are not used in any of the prompt messages.`);\n            }\n            const otherDifference = new Set([...inputVariablesMessages].filter((x) => !inputVariablesInstance.has(x)));\n            if (otherDifference.size > 0) {\n                throw new Error(`Input variables \\`${[\n                    ...otherDifference,\n                ]}\\` are used in prompt messages but not in the prompt template.`);\n            }\n        }\n    }\n    _getPromptType() {\n        return \"chat\";\n    }\n    async _parseImagePrompts(message, inputValues) {\n        if (typeof message.content === \"string\") {\n            return message;\n        }\n        const formattedMessageContent = await Promise.all(message.content.map(async (item) => {\n            if (item.type !== \"image_url\") {\n                return item;\n            }\n            let imageUrl = \"\";\n            if (typeof item.image_url === \"string\") {\n                imageUrl = item.image_url;\n            }\n            else {\n                imageUrl = item.image_url.url;\n            }\n            const promptTemplatePlaceholder = PromptTemplate.fromTemplate(imageUrl, {\n                templateFormat: this.templateFormat,\n            });\n            const formattedUrl = await promptTemplatePlaceholder.format(inputValues);\n            if (typeof item.image_url !== \"string\" && \"url\" in item.image_url) {\n                // eslint-disable-next-line no-param-reassign\n                item.image_url.url = formattedUrl;\n            }\n            else {\n                // eslint-disable-next-line no-param-reassign\n                item.image_url = formattedUrl;\n            }\n            return item;\n        }));\n        // eslint-disable-next-line no-param-reassign\n        message.content = formattedMessageContent;\n        return message;\n    }\n    async formatMessages(values) {\n        const allValues = await this.mergePartialAndUserVariables(values);\n        let resultMessages = [];\n        for (const promptMessage of this.promptMessages) {\n            // eslint-disable-next-line no-instanceof/no-instanceof\n            if (promptMessage instanceof BaseMessage) {\n                resultMessages.push(await this._parseImagePrompts(promptMessage, allValues));\n            }\n            else {\n                const inputValues = promptMessage.inputVariables.reduce((acc, inputVariable) => {\n                    if (!(inputVariable in allValues) &&\n                        !(isMessagesPlaceholder(promptMessage) && promptMessage.optional)) {\n                        const error = addLangChainErrorFields(new Error(`Missing value for input variable \\`${inputVariable.toString()}\\``), \"INVALID_PROMPT_INPUT\");\n                        throw error;\n                    }\n                    acc[inputVariable] = allValues[inputVariable];\n                    return acc;\n                }, {});\n                const message = await promptMessage.formatMessages(inputValues);\n                resultMessages = resultMessages.concat(message);\n            }\n        }\n        return resultMessages;\n    }\n    async partial(values) {\n        // This is implemented in a way it doesn't require making\n        // BaseMessagePromptTemplate aware of .partial()\n        const newInputVariables = this.inputVariables.filter((iv) => !(iv in values));\n        const newPartialVariables = {\n            ...(this.partialVariables ?? {}),\n            ...values,\n        };\n        const promptDict = {\n            ...this,\n            inputVariables: newInputVariables,\n            partialVariables: newPartialVariables,\n        };\n        return new ChatPromptTemplate(promptDict);\n    }\n    static fromTemplate(template, options) {\n        const prompt = PromptTemplate.fromTemplate(template, options);\n        const humanTemplate = new HumanMessagePromptTemplate({ prompt });\n        return this.fromMessages([humanTemplate]);\n    }\n    /**\n     * Create a chat model-specific prompt from individual chat messages\n     * or message-like tuples.\n     * @param promptMessages Messages to be passed to the chat model\n     * @returns A new ChatPromptTemplate\n     */\n    static fromMessages(promptMessages, extra) {\n        const flattenedMessages = promptMessages.reduce((acc, promptMessage) => acc.concat(\n        // eslint-disable-next-line no-instanceof/no-instanceof\n        promptMessage instanceof ChatPromptTemplate\n            ? promptMessage.promptMessages\n            : [\n                _coerceMessagePromptTemplateLike(promptMessage, extra),\n            ]), []);\n        const flattenedPartialVariables = promptMessages.reduce((acc, promptMessage) => \n        // eslint-disable-next-line no-instanceof/no-instanceof\n        promptMessage instanceof ChatPromptTemplate\n            ? Object.assign(acc, promptMessage.partialVariables)\n            : acc, Object.create(null));\n        const inputVariables = new Set();\n        for (const promptMessage of flattenedMessages) {\n            // eslint-disable-next-line no-instanceof/no-instanceof\n            if (promptMessage instanceof BaseMessage)\n                continue;\n            for (const inputVariable of promptMessage.inputVariables) {\n                if (inputVariable in flattenedPartialVariables) {\n                    continue;\n                }\n                inputVariables.add(inputVariable);\n            }\n        }\n        return new this({\n            ...extra,\n            inputVariables: [...inputVariables],\n            promptMessages: flattenedMessages,\n            partialVariables: flattenedPartialVariables,\n            templateFormat: extra?.templateFormat,\n        });\n    }\n    /** @deprecated Renamed to .fromMessages */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    static fromPromptMessages(promptMessages) {\n        return this.fromMessages(promptMessages);\n    }\n}\n", "export * from \"./ai.js\";\nexport * from \"./base.js\";\nexport * from \"./chat.js\";\nexport * from \"./function.js\";\nexport * from \"./human.js\";\nexport * from \"./system.js\";\nexport * from \"./utils.js\";\nexport * from \"./transformers.js\";\nexport * from \"./modifier.js\";\nexport * from \"./content_blocks.js\";\n// TODO: Use a star export when we deprecate the\n// existing \"ToolCall\" type in \"base.js\".\nexport { ToolMessage, ToolMessageChunk, isToolMessage, isToolMessageChunk, } from \"./tool.js\";\n", "import { RunnableLambda } from \"../runnables/base.js\";\nimport { AIMessage, AIMessageChunk } from \"./ai.js\";\nimport { isBaseMessageChunk, } from \"./base.js\";\nimport { ChatMessage, ChatMessageChunk, } from \"./chat.js\";\nimport { FunctionMessage, FunctionMessageChunk, } from \"./function.js\";\nimport { HumanMessage, HumanMessageChunk } from \"./human.js\";\nimport { RemoveMessage } from \"./modifier.js\";\nimport { SystemMessage, SystemMessageChunk } from \"./system.js\";\nimport { ToolMessage, ToolMessageChunk, } from \"./tool.js\";\nimport { convertToChunk } from \"./utils.js\";\nconst _isMessageType = (msg, types) => {\n    const typesAsStrings = [\n        ...new Set(types?.map((t) => {\n            if (typeof t === \"string\") {\n                return t;\n            }\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const instantiatedMsgClass = new t({});\n            if (!(\"getType\" in instantiatedMsgClass) ||\n                typeof instantiatedMsgClass.getType !== \"function\") {\n                throw new Error(\"Invalid type provided.\");\n            }\n            return instantiatedMsgClass.getType();\n        })),\n    ];\n    const msgType = msg.getType();\n    return typesAsStrings.some((t) => t === msgType);\n};\nexport function filterMessages(messagesOrOptions, options) {\n    if (Array.isArray(messagesOrOptions)) {\n        return _filterMessages(messagesOrOptions, options);\n    }\n    return RunnableLambda.from((input) => {\n        return _filterMessages(input, messagesOrOptions);\n    });\n}\nfunction _filterMessages(messages, options = {}) {\n    const { includeNames, excludeNames, includeTypes, excludeTypes, includeIds, excludeIds, } = options;\n    const filtered = [];\n    for (const msg of messages) {\n        if (excludeNames && msg.name && excludeNames.includes(msg.name)) {\n            continue;\n        }\n        else if (excludeTypes && _isMessageType(msg, excludeTypes)) {\n            continue;\n        }\n        else if (excludeIds && msg.id && excludeIds.includes(msg.id)) {\n            continue;\n        }\n        // default to inclusion when no inclusion criteria given.\n        if (!(includeTypes || includeIds || includeNames)) {\n            filtered.push(msg);\n        }\n        else if (includeNames &&\n            msg.name &&\n            includeNames.some((iName) => iName === msg.name)) {\n            filtered.push(msg);\n        }\n        else if (includeTypes && _isMessageType(msg, includeTypes)) {\n            filtered.push(msg);\n        }\n        else if (includeIds && msg.id && includeIds.some((id) => id === msg.id)) {\n            filtered.push(msg);\n        }\n    }\n    return filtered;\n}\nexport function mergeMessageRuns(messages) {\n    if (Array.isArray(messages)) {\n        return _mergeMessageRuns(messages);\n    }\n    return RunnableLambda.from(_mergeMessageRuns);\n}\nfunction _mergeMessageRuns(messages) {\n    if (!messages.length) {\n        return [];\n    }\n    const merged = [];\n    for (const msg of messages) {\n        const curr = msg;\n        const last = merged.pop();\n        if (!last) {\n            merged.push(curr);\n        }\n        else if (curr.getType() === \"tool\" ||\n            !(curr.getType() === last.getType())) {\n            merged.push(last, curr);\n        }\n        else {\n            const lastChunk = convertToChunk(last);\n            const currChunk = convertToChunk(curr);\n            const mergedChunks = lastChunk.concat(currChunk);\n            if (typeof lastChunk.content === \"string\" &&\n                typeof currChunk.content === \"string\") {\n                mergedChunks.content = `${lastChunk.content}\\n${currChunk.content}`;\n            }\n            merged.push(_chunkToMsg(mergedChunks));\n        }\n    }\n    return merged;\n}\nexport function trimMessages(messagesOrOptions, options) {\n    if (Array.isArray(messagesOrOptions)) {\n        const messages = messagesOrOptions;\n        if (!options) {\n            throw new Error(\"Options parameter is required when providing messages.\");\n        }\n        return _trimMessagesHelper(messages, options);\n    }\n    else {\n        const trimmerOptions = messagesOrOptions;\n        return RunnableLambda.from((input) => _trimMessagesHelper(input, trimmerOptions)).withConfig({\n            runName: \"trim_messages\",\n        });\n    }\n}\nasync function _trimMessagesHelper(messages, options) {\n    const { maxTokens, tokenCounter, strategy = \"last\", allowPartial = false, endOn, startOn, includeSystem = false, textSplitter, } = options;\n    if (startOn && strategy === \"first\") {\n        throw new Error(\"`startOn` should only be specified if `strategy` is 'last'.\");\n    }\n    if (includeSystem && strategy === \"first\") {\n        throw new Error(\"`includeSystem` should only be specified if `strategy` is 'last'.\");\n    }\n    let listTokenCounter;\n    if (\"getNumTokens\" in tokenCounter) {\n        listTokenCounter = async (msgs) => {\n            const tokenCounts = await Promise.all(msgs.map((msg) => tokenCounter.getNumTokens(msg.content)));\n            return tokenCounts.reduce((sum, count) => sum + count, 0);\n        };\n    }\n    else {\n        listTokenCounter = async (msgs) => tokenCounter(msgs);\n    }\n    let textSplitterFunc = defaultTextSplitter;\n    if (textSplitter) {\n        if (\"splitText\" in textSplitter) {\n            textSplitterFunc = textSplitter.splitText;\n        }\n        else {\n            textSplitterFunc = async (text) => textSplitter(text);\n        }\n    }\n    if (strategy === \"first\") {\n        return _firstMaxTokens(messages, {\n            maxTokens,\n            tokenCounter: listTokenCounter,\n            textSplitter: textSplitterFunc,\n            partialStrategy: allowPartial ? \"first\" : undefined,\n            endOn,\n        });\n    }\n    else if (strategy === \"last\") {\n        return _lastMaxTokens(messages, {\n            maxTokens,\n            tokenCounter: listTokenCounter,\n            textSplitter: textSplitterFunc,\n            allowPartial,\n            includeSystem,\n            startOn,\n            endOn,\n        });\n    }\n    else {\n        throw new Error(`Unrecognized strategy: '${strategy}'. Must be one of 'first' or 'last'.`);\n    }\n}\nasync function _firstMaxTokens(messages, options) {\n    const { maxTokens, tokenCounter, textSplitter, partialStrategy, endOn } = options;\n    let messagesCopy = [...messages];\n    let idx = 0;\n    for (let i = 0; i < messagesCopy.length; i += 1) {\n        const remainingMessages = i > 0 ? messagesCopy.slice(0, -i) : messagesCopy;\n        if ((await tokenCounter(remainingMessages)) <= maxTokens) {\n            idx = messagesCopy.length - i;\n            break;\n        }\n    }\n    if (idx < messagesCopy.length - 1 && partialStrategy) {\n        let includedPartial = false;\n        if (Array.isArray(messagesCopy[idx].content)) {\n            const excluded = messagesCopy[idx];\n            if (typeof excluded.content === \"string\") {\n                throw new Error(\"Expected content to be an array.\");\n            }\n            const numBlock = excluded.content.length;\n            const reversedContent = partialStrategy === \"last\"\n                ? [...excluded.content].reverse()\n                : excluded.content;\n            for (let i = 1; i <= numBlock; i += 1) {\n                const partialContent = partialStrategy === \"first\"\n                    ? reversedContent.slice(0, i)\n                    : reversedContent.slice(-i);\n                const fields = Object.fromEntries(Object.entries(excluded).filter(([k]) => k !== \"type\" && !k.startsWith(\"lc_\")));\n                const updatedMessage = _switchTypeToMessage(excluded.getType(), {\n                    ...fields,\n                    content: partialContent,\n                });\n                const slicedMessages = [...messagesCopy.slice(0, idx), updatedMessage];\n                if ((await tokenCounter(slicedMessages)) <= maxTokens) {\n                    messagesCopy = slicedMessages;\n                    idx += 1;\n                    includedPartial = true;\n                }\n                else {\n                    break;\n                }\n            }\n            if (includedPartial && partialStrategy === \"last\") {\n                excluded.content = [...reversedContent].reverse();\n            }\n        }\n        if (!includedPartial) {\n            const excluded = messagesCopy[idx];\n            let text;\n            if (Array.isArray(excluded.content) &&\n                excluded.content.some((block) => typeof block === \"string\" || block.type === \"text\")) {\n                const textBlock = excluded.content.find((block) => block.type === \"text\" && block.text);\n                text = textBlock?.text;\n            }\n            else if (typeof excluded.content === \"string\") {\n                text = excluded.content;\n            }\n            if (text) {\n                const splitTexts = await textSplitter(text);\n                const numSplits = splitTexts.length;\n                if (partialStrategy === \"last\") {\n                    splitTexts.reverse();\n                }\n                for (let _ = 0; _ < numSplits - 1; _ += 1) {\n                    splitTexts.pop();\n                    excluded.content = splitTexts.join(\"\");\n                    if ((await tokenCounter([...messagesCopy.slice(0, idx), excluded])) <=\n                        maxTokens) {\n                        if (partialStrategy === \"last\") {\n                            excluded.content = [...splitTexts].reverse().join(\"\");\n                        }\n                        messagesCopy = [...messagesCopy.slice(0, idx), excluded];\n                        idx += 1;\n                        break;\n                    }\n                }\n            }\n        }\n    }\n    if (endOn) {\n        const endOnArr = Array.isArray(endOn) ? endOn : [endOn];\n        while (idx > 0 && !_isMessageType(messagesCopy[idx - 1], endOnArr)) {\n            idx -= 1;\n        }\n    }\n    return messagesCopy.slice(0, idx);\n}\nasync function _lastMaxTokens(messages, options) {\n    const { allowPartial = false, includeSystem = false, endOn, startOn, ...rest } = options;\n    // Create a copy of messages to avoid mutation\n    let messagesCopy = messages.map((message) => {\n        const fields = Object.fromEntries(Object.entries(message).filter(([k]) => k !== \"type\" && !k.startsWith(\"lc_\")));\n        return _switchTypeToMessage(message.getType(), fields, isBaseMessageChunk(message));\n    });\n    if (endOn) {\n        const endOnArr = Array.isArray(endOn) ? endOn : [endOn];\n        while (messagesCopy.length > 0 &&\n            !_isMessageType(messagesCopy[messagesCopy.length - 1], endOnArr)) {\n            messagesCopy = messagesCopy.slice(0, -1);\n        }\n    }\n    const swappedSystem = includeSystem && messagesCopy[0]?.getType() === \"system\";\n    let reversed_ = swappedSystem\n        ? messagesCopy.slice(0, 1).concat(messagesCopy.slice(1).reverse())\n        : messagesCopy.reverse();\n    reversed_ = await _firstMaxTokens(reversed_, {\n        ...rest,\n        partialStrategy: allowPartial ? \"last\" : undefined,\n        endOn: startOn,\n    });\n    if (swappedSystem) {\n        return [reversed_[0], ...reversed_.slice(1).reverse()];\n    }\n    else {\n        return reversed_.reverse();\n    }\n}\nconst _MSG_CHUNK_MAP = {\n    human: {\n        message: HumanMessage,\n        messageChunk: HumanMessageChunk,\n    },\n    ai: {\n        message: AIMessage,\n        messageChunk: AIMessageChunk,\n    },\n    system: {\n        message: SystemMessage,\n        messageChunk: SystemMessageChunk,\n    },\n    developer: {\n        message: SystemMessage,\n        messageChunk: SystemMessageChunk,\n    },\n    tool: {\n        message: ToolMessage,\n        messageChunk: ToolMessageChunk,\n    },\n    function: {\n        message: FunctionMessage,\n        messageChunk: FunctionMessageChunk,\n    },\n    generic: {\n        message: ChatMessage,\n        messageChunk: ChatMessageChunk,\n    },\n    remove: {\n        message: RemoveMessage,\n        messageChunk: RemoveMessage, // RemoveMessage does not have a chunk class.\n    },\n};\nfunction _switchTypeToMessage(messageType, fields, returnChunk) {\n    let chunk;\n    let msg;\n    switch (messageType) {\n        case \"human\":\n            if (returnChunk) {\n                chunk = new HumanMessageChunk(fields);\n            }\n            else {\n                msg = new HumanMessage(fields);\n            }\n            break;\n        case \"ai\":\n            if (returnChunk) {\n                let aiChunkFields = {\n                    ...fields,\n                };\n                if (\"tool_calls\" in aiChunkFields) {\n                    aiChunkFields = {\n                        ...aiChunkFields,\n                        tool_call_chunks: aiChunkFields.tool_calls?.map((tc) => ({\n                            ...tc,\n                            type: \"tool_call_chunk\",\n                            index: undefined,\n                            args: JSON.stringify(tc.args),\n                        })),\n                    };\n                }\n                chunk = new AIMessageChunk(aiChunkFields);\n            }\n            else {\n                msg = new AIMessage(fields);\n            }\n            break;\n        case \"system\":\n            if (returnChunk) {\n                chunk = new SystemMessageChunk(fields);\n            }\n            else {\n                msg = new SystemMessage(fields);\n            }\n            break;\n        case \"developer\":\n            if (returnChunk) {\n                chunk = new SystemMessageChunk({\n                    ...fields,\n                    additional_kwargs: {\n                        ...fields.additional_kwargs,\n                        __openai_role__: \"developer\",\n                    },\n                });\n            }\n            else {\n                msg = new SystemMessage({\n                    ...fields,\n                    additional_kwargs: {\n                        ...fields.additional_kwargs,\n                        __openai_role__: \"developer\",\n                    },\n                });\n            }\n            break;\n        case \"tool\":\n            if (\"tool_call_id\" in fields) {\n                if (returnChunk) {\n                    chunk = new ToolMessageChunk(fields);\n                }\n                else {\n                    msg = new ToolMessage(fields);\n                }\n            }\n            else {\n                throw new Error(\"Can not convert ToolMessage to ToolMessageChunk if 'tool_call_id' field is not defined.\");\n            }\n            break;\n        case \"function\":\n            if (returnChunk) {\n                chunk = new FunctionMessageChunk(fields);\n            }\n            else {\n                if (!fields.name) {\n                    throw new Error(\"FunctionMessage must have a 'name' field\");\n                }\n                msg = new FunctionMessage(fields);\n            }\n            break;\n        case \"generic\":\n            if (\"role\" in fields) {\n                if (returnChunk) {\n                    chunk = new ChatMessageChunk(fields);\n                }\n                else {\n                    msg = new ChatMessage(fields);\n                }\n            }\n            else {\n                throw new Error(\"Can not convert ChatMessage to ChatMessageChunk if 'role' field is not defined.\");\n            }\n            break;\n        default:\n            throw new Error(`Unrecognized message type ${messageType}`);\n    }\n    if (returnChunk && chunk) {\n        return chunk;\n    }\n    if (msg) {\n        return msg;\n    }\n    throw new Error(`Unrecognized message type ${messageType}`);\n}\nfunction _chunkToMsg(chunk) {\n    const chunkType = chunk.getType();\n    let msg;\n    const fields = Object.fromEntries(Object.entries(chunk).filter(([k]) => ![\"type\", \"tool_call_chunks\"].includes(k) && !k.startsWith(\"lc_\")));\n    if (chunkType in _MSG_CHUNK_MAP) {\n        msg = _switchTypeToMessage(chunkType, fields);\n    }\n    if (!msg) {\n        throw new Error(`Unrecognized message chunk class ${chunkType}. Supported classes are ${Object.keys(_MSG_CHUNK_MAP)}`);\n    }\n    return msg;\n}\n/**\n * The default text splitter function that splits text by newlines.\n *\n * @param {string} text\n * @returns A promise that resolves to an array of strings split by newlines.\n */\nexport function defaultTextSplitter(text) {\n    const splits = text.split(\"\\n\");\n    return Promise.resolve([\n        ...splits.slice(0, -1).map((s) => `${s}\\n`),\n        splits[splits.length - 1],\n    ]);\n}\n", "import { BaseMessage } from \"./base.js\";\n/**\n * Message responsible for deleting other messages.\n */\nexport class RemoveMessage extends BaseMessage {\n    constructor(fields) {\n        super({\n            ...fields,\n            content: \"\",\n        });\n        /**\n         * The ID of the message to remove.\n         */\n        Object.defineProperty(this, \"id\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.id = fields.id;\n    }\n    _getType() {\n        return \"remove\";\n    }\n    get _printableFields() {\n        return {\n            ...super._printableFields,\n            id: this.id,\n        };\n    }\n}\n", "import { ImagePromptValue } from \"../prompt_values.js\";\nimport { BasePromptTemplate, } from \"./base.js\";\nimport { checkValidTemplate, renderTemplate, } from \"./template.js\";\n/**\n * An image prompt template for a multimodal model.\n */\nexport class ImagePromptTemplate extends BasePromptTemplate {\n    static lc_name() {\n        return \"ImagePromptTemplate\";\n    }\n    constructor(input) {\n        super(input);\n        Object.defineProperty(this, \"lc_namespace\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: [\"langchain_core\", \"prompts\", \"image\"]\n        });\n        Object.defineProperty(this, \"template\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"templateFormat\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"f-string\"\n        });\n        Object.defineProperty(this, \"validateTemplate\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        /**\n         * Additional fields which should be included inside\n         * the message content array if using a complex message\n         * content.\n         */\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Object.defineProperty(this, \"additionalContentFields\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.template = input.template;\n        this.templateFormat = input.templateFormat ?? this.templateFormat;\n        this.validateTemplate = input.validateTemplate ?? this.validateTemplate;\n        this.additionalContentFields = input.additionalContentFields;\n        if (this.validateTemplate) {\n            let totalInputVariables = this.inputVariables;\n            if (this.partialVariables) {\n                totalInputVariables = totalInputVariables.concat(Object.keys(this.partialVariables));\n            }\n            checkValidTemplate([\n                { type: \"image_url\", image_url: this.template },\n            ], this.templateFormat, totalInputVariables);\n        }\n    }\n    _getPromptType() {\n        return \"prompt\";\n    }\n    /**\n     * Partially applies values to the prompt template.\n     * @param values The values to be partially applied to the prompt template.\n     * @returns A new instance of ImagePromptTemplate with the partially applied values.\n     */\n    async partial(values) {\n        const newInputVariables = this.inputVariables.filter((iv) => !(iv in values));\n        const newPartialVariables = {\n            ...(this.partialVariables ?? {}),\n            ...values,\n        };\n        const promptDict = {\n            ...this,\n            inputVariables: newInputVariables,\n            partialVariables: newPartialVariables,\n        };\n        return new ImagePromptTemplate(promptDict);\n    }\n    /**\n     * Formats the prompt template with the provided values.\n     * @param values The values to be used to format the prompt template.\n     * @returns A promise that resolves to a string which is the formatted prompt.\n     */\n    async format(values) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const formatted = {};\n        for (const [key, value] of Object.entries(this.template)) {\n            if (typeof value === \"string\") {\n                formatted[key] = renderTemplate(value, this.templateFormat, values);\n            }\n            else {\n                formatted[key] = value;\n            }\n        }\n        const url = values.url || formatted.url;\n        const detail = values.detail || formatted.detail;\n        if (!url) {\n            throw new Error(\"Must provide either an image URL.\");\n        }\n        if (typeof url !== \"string\") {\n            throw new Error(\"url must be a string.\");\n        }\n        const output = { url };\n        if (detail) {\n            output.detail = detail;\n        }\n        return output;\n    }\n    /**\n     * Formats the prompt given the input values and returns a formatted\n     * prompt value.\n     * @param values The input values to format the prompt.\n     * @returns A Promise that resolves to a formatted prompt value.\n     */\n    async formatPromptValue(values) {\n        const formattedPrompt = await this.format(values);\n        return new ImagePromptValue(formattedPrompt);\n    }\n}\n", "import { Runnable } from \"../runnables/base.js\";\nimport { parseTemplate, renderTemplate } from \"./template.js\";\nexport class DictPromptTemplate extends Runnable {\n    static lc_name() {\n        return \"DictPromptTemplate\";\n    }\n    constructor(fields) {\n        const templateFormat = fields.templateFormat ?? \"f-string\";\n        const inputVariables = _getInputVariables(fields.template, templateFormat);\n        super({ inputVariables, ...fields });\n        Object.defineProperty(this, \"lc_namespace\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: [\"langchain_core\", \"prompts\", \"dict\"]\n        });\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"template\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"templateFormat\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"inputVariables\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.template = fields.template;\n        this.templateFormat = templateFormat;\n        this.inputVariables = inputVariables;\n    }\n    async format(values) {\n        return _insertInputVariables(this.template, values, this.templateFormat);\n    }\n    async invoke(values) {\n        return await this._callWithConfig(this.format.bind(this), values, {\n            runType: \"prompt\",\n        });\n    }\n}\nfunction _getInputVariables(template, templateFormat) {\n    const inputVariables = [];\n    for (const v of Object.values(template)) {\n        if (typeof v === \"string\") {\n            parseTemplate(v, templateFormat).forEach((t) => {\n                if (t.type === \"variable\") {\n                    inputVariables.push(t.name);\n                }\n            });\n        }\n        else if (Array.isArray(v)) {\n            for (const x of v) {\n                if (typeof x === \"string\") {\n                    parseTemplate(x, templateFormat).forEach((t) => {\n                        if (t.type === \"variable\") {\n                            inputVariables.push(t.name);\n                        }\n                    });\n                }\n                else if (typeof x === \"object\") {\n                    inputVariables.push(..._getInputVariables(x, templateFormat));\n                }\n            }\n        }\n        else if (typeof v === \"object\" && v !== null) {\n            inputVariables.push(..._getInputVariables(v, templateFormat));\n        }\n    }\n    return Array.from(new Set(inputVariables));\n}\nfunction _insertInputVariables(template, inputs, templateFormat) {\n    const formatted = {};\n    for (const [k, v] of Object.entries(template)) {\n        if (typeof v === \"string\") {\n            formatted[k] = renderTemplate(v, templateFormat, inputs);\n        }\n        else if (Array.isArray(v)) {\n            const formattedV = [];\n            for (const x of v) {\n                if (typeof x === \"string\") {\n                    formattedV.push(renderTemplate(x, templateFormat, inputs));\n                }\n                else if (typeof x === \"object\") {\n                    formattedV.push(_insertInputVariables(x, inputs, templateFormat));\n                }\n            }\n            formatted[k] = formattedV;\n        }\n        else if (typeof v === \"object\" && v !== null) {\n            formatted[k] = _insertInputVariables(v, inputs, templateFormat);\n        }\n        else {\n            formatted[k] = v;\n        }\n    }\n    return formatted;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;ACAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;;;ACAA;AAIO,IAAM,gBAAN,cAA4B,YAAY;AAAA,EAC3C,YAAY,QAAQ;AAChB,UAAM;AAAA,MACF,GAAG;AAAA,MACH,SAAS;AAAA,IACb,CAAC;AAID,WAAO,eAAe,MAAM,MAAM;AAAA,MAC9B,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,SAAK,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,WAAW;AACP,WAAO;AAAA,EACX;AAAA,EACA,IAAI,mBAAmB;AACnB,WAAO;AAAA,MACH,GAAG,MAAM;AAAA,MACT,IAAI,KAAK;AAAA,IACb;AAAA,EACJ;AACJ;;;ADpBA,IAAM,iBAAiB,CAAC,KAAK,UAAU;AACnC,QAAM,iBAAiB;AAAA,IACnB,GAAG,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM;AACzB,UAAI,OAAO,MAAM,UAAU;AACvB,eAAO;AAAA,MACX;AAEA,YAAM,uBAAuB,IAAI,EAAE,CAAC,CAAC;AACrC,UAAI,EAAE,aAAa,yBACf,OAAO,qBAAqB,YAAY,YAAY;AACpD,cAAM,IAAI,MAAM,wBAAwB;AAAA,MAC5C;AACA,aAAO,qBAAqB,QAAQ;AAAA,IACxC,CAAC,CAAC;AAAA,EACN;AACA,QAAM,UAAU,IAAI,QAAQ;AAC5B,SAAO,eAAe,KAAK,CAAC,MAAM,MAAM,OAAO;AACnD;AACO,SAAS,eAAe,mBAAmB,SAAS;AACvD,MAAI,MAAM,QAAQ,iBAAiB,GAAG;AAClC,WAAO,gBAAgB,mBAAmB,OAAO;AAAA,EACrD;AACA,SAAO,eAAe,KAAK,CAAC,UAAU;AAClC,WAAO,gBAAgB,OAAO,iBAAiB;AAAA,EACnD,CAAC;AACL;AACA,SAAS,gBAAgB,UAAU,UAAU,CAAC,GAAG;AAC7C,QAAM,EAAE,cAAc,cAAc,cAAc,cAAc,YAAY,WAAY,IAAI;AAC5F,QAAM,WAAW,CAAC;AAClB,aAAW,OAAO,UAAU;AACxB,QAAI,gBAAgB,IAAI,QAAQ,aAAa,SAAS,IAAI,IAAI,GAAG;AAC7D;AAAA,IACJ,WACS,gBAAgB,eAAe,KAAK,YAAY,GAAG;AACxD;AAAA,IACJ,WACS,cAAc,IAAI,MAAM,WAAW,SAAS,IAAI,EAAE,GAAG;AAC1D;AAAA,IACJ;AAEA,QAAI,EAAE,gBAAgB,cAAc,eAAe;AAC/C,eAAS,KAAK,GAAG;AAAA,IACrB,WACS,gBACL,IAAI,QACJ,aAAa,KAAK,CAAC,UAAU,UAAU,IAAI,IAAI,GAAG;AAClD,eAAS,KAAK,GAAG;AAAA,IACrB,WACS,gBAAgB,eAAe,KAAK,YAAY,GAAG;AACxD,eAAS,KAAK,GAAG;AAAA,IACrB,WACS,cAAc,IAAI,MAAM,WAAW,KAAK,CAAC,OAAO,OAAO,IAAI,EAAE,GAAG;AACrE,eAAS,KAAK,GAAG;AAAA,IACrB;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,iBAAiB,UAAU;AACvC,MAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,WAAO,kBAAkB,QAAQ;AAAA,EACrC;AACA,SAAO,eAAe,KAAK,iBAAiB;AAChD;AACA,SAAS,kBAAkB,UAAU;AACjC,MAAI,CAAC,SAAS,QAAQ;AAClB,WAAO,CAAC;AAAA,EACZ;AACA,QAAM,SAAS,CAAC;AAChB,aAAW,OAAO,UAAU;AACxB,UAAM,OAAO;AACb,UAAM,OAAO,OAAO,IAAI;AACxB,QAAI,CAAC,MAAM;AACP,aAAO,KAAK,IAAI;AAAA,IACpB,WACS,KAAK,QAAQ,MAAM,UACxB,EAAE,KAAK,QAAQ,MAAM,KAAK,QAAQ,IAAI;AACtC,aAAO,KAAK,MAAM,IAAI;AAAA,IAC1B,OACK;AACD,YAAM,YAAY,eAAe,IAAI;AACrC,YAAM,YAAY,eAAe,IAAI;AACrC,YAAM,eAAe,UAAU,OAAO,SAAS;AAC/C,UAAI,OAAO,UAAU,YAAY,YAC7B,OAAO,UAAU,YAAY,UAAU;AACvC,qBAAa,UAAU,GAAG,UAAU,OAAO;AAAA,EAAK,UAAU,OAAO;AAAA,MACrE;AACA,aAAO,KAAK,YAAY,YAAY,CAAC;AAAA,IACzC;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,aAAa,mBAAmB,SAAS;AACrD,MAAI,MAAM,QAAQ,iBAAiB,GAAG;AAClC,UAAM,WAAW;AACjB,QAAI,CAAC,SAAS;AACV,YAAM,IAAI,MAAM,wDAAwD;AAAA,IAC5E;AACA,WAAO,oBAAoB,UAAU,OAAO;AAAA,EAChD,OACK;AACD,UAAM,iBAAiB;AACvB,WAAO,eAAe,KAAK,CAAC,UAAU,oBAAoB,OAAO,cAAc,CAAC,EAAE,WAAW;AAAA,MACzF,SAAS;AAAA,IACb,CAAC;AAAA,EACL;AACJ;AACA,eAAe,oBAAoB,UAAU,SAAS;AAClD,QAAM,EAAE,WAAW,cAAc,WAAW,QAAQ,eAAe,OAAO,OAAO,SAAS,gBAAgB,OAAO,aAAc,IAAI;AACnI,MAAI,WAAW,aAAa,SAAS;AACjC,UAAM,IAAI,MAAM,6DAA6D;AAAA,EACjF;AACA,MAAI,iBAAiB,aAAa,SAAS;AACvC,UAAM,IAAI,MAAM,mEAAmE;AAAA,EACvF;AACA,MAAI;AACJ,MAAI,kBAAkB,cAAc;AAChC,uBAAmB,OAAO,SAAS;AAC/B,YAAM,cAAc,MAAM,QAAQ,IAAI,KAAK,IAAI,CAAC,QAAQ,aAAa,aAAa,IAAI,OAAO,CAAC,CAAC;AAC/F,aAAO,YAAY,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC;AAAA,IAC5D;AAAA,EACJ,OACK;AACD,uBAAmB,OAAO,SAAS,aAAa,IAAI;AAAA,EACxD;AACA,MAAI,mBAAmB;AACvB,MAAI,cAAc;AACd,QAAI,eAAe,cAAc;AAC7B,yBAAmB,aAAa;AAAA,IACpC,OACK;AACD,yBAAmB,OAAO,SAAS,aAAa,IAAI;AAAA,IACxD;AAAA,EACJ;AACA,MAAI,aAAa,SAAS;AACtB,WAAO,gBAAgB,UAAU;AAAA,MAC7B;AAAA,MACA,cAAc;AAAA,MACd,cAAc;AAAA,MACd,iBAAiB,eAAe,UAAU;AAAA,MAC1C;AAAA,IACJ,CAAC;AAAA,EACL,WACS,aAAa,QAAQ;AAC1B,WAAO,eAAe,UAAU;AAAA,MAC5B;AAAA,MACA,cAAc;AAAA,MACd,cAAc;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL,OACK;AACD,UAAM,IAAI,MAAM,2BAA2B,QAAQ,sCAAsC;AAAA,EAC7F;AACJ;AACA,eAAe,gBAAgB,UAAU,SAAS;AAC9C,QAAM,EAAE,WAAW,cAAc,cAAc,iBAAiB,MAAM,IAAI;AAC1E,MAAI,eAAe,CAAC,GAAG,QAAQ;AAC/B,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK,GAAG;AAC7C,UAAM,oBAAoB,IAAI,IAAI,aAAa,MAAM,GAAG,CAAC,CAAC,IAAI;AAC9D,QAAK,MAAM,aAAa,iBAAiB,KAAM,WAAW;AACtD,YAAM,aAAa,SAAS;AAC5B;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,MAAM,aAAa,SAAS,KAAK,iBAAiB;AAClD,QAAI,kBAAkB;AACtB,QAAI,MAAM,QAAQ,aAAa,GAAG,EAAE,OAAO,GAAG;AAC1C,YAAM,WAAW,aAAa,GAAG;AACjC,UAAI,OAAO,SAAS,YAAY,UAAU;AACtC,cAAM,IAAI,MAAM,kCAAkC;AAAA,MACtD;AACA,YAAM,WAAW,SAAS,QAAQ;AAClC,YAAM,kBAAkB,oBAAoB,SACtC,CAAC,GAAG,SAAS,OAAO,EAAE,QAAQ,IAC9B,SAAS;AACf,eAAS,IAAI,GAAG,KAAK,UAAU,KAAK,GAAG;AACnC,cAAM,iBAAiB,oBAAoB,UACrC,gBAAgB,MAAM,GAAG,CAAC,IAC1B,gBAAgB,MAAM,CAAC,CAAC;AAC9B,cAAM,SAAS,OAAO,YAAY,OAAO,QAAQ,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,MAAM,UAAU,CAAC,EAAE,WAAW,KAAK,CAAC,CAAC;AAChH,cAAM,iBAAiB,qBAAqB,SAAS,QAAQ,GAAG;AAAA,UAC5D,GAAG;AAAA,UACH,SAAS;AAAA,QACb,CAAC;AACD,cAAM,iBAAiB,CAAC,GAAG,aAAa,MAAM,GAAG,GAAG,GAAG,cAAc;AACrE,YAAK,MAAM,aAAa,cAAc,KAAM,WAAW;AACnD,yBAAe;AACf,iBAAO;AACP,4BAAkB;AAAA,QACtB,OACK;AACD;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,mBAAmB,oBAAoB,QAAQ;AAC/C,iBAAS,UAAU,CAAC,GAAG,eAAe,EAAE,QAAQ;AAAA,MACpD;AAAA,IACJ;AACA,QAAI,CAAC,iBAAiB;AAClB,YAAM,WAAW,aAAa,GAAG;AACjC,UAAI;AACJ,UAAI,MAAM,QAAQ,SAAS,OAAO,KAC9B,SAAS,QAAQ,KAAK,CAAC,UAAU,OAAO,UAAU,YAAY,MAAM,SAAS,MAAM,GAAG;AACtF,cAAM,YAAY,SAAS,QAAQ,KAAK,CAAC,UAAU,MAAM,SAAS,UAAU,MAAM,IAAI;AACtF,eAAO,WAAW;AAAA,MACtB,WACS,OAAO,SAAS,YAAY,UAAU;AAC3C,eAAO,SAAS;AAAA,MACpB;AACA,UAAI,MAAM;AACN,cAAM,aAAa,MAAM,aAAa,IAAI;AAC1C,cAAM,YAAY,WAAW;AAC7B,YAAI,oBAAoB,QAAQ;AAC5B,qBAAW,QAAQ;AAAA,QACvB;AACA,iBAAS,IAAI,GAAG,IAAI,YAAY,GAAG,KAAK,GAAG;AACvC,qBAAW,IAAI;AACf,mBAAS,UAAU,WAAW,KAAK,EAAE;AACrC,cAAK,MAAM,aAAa,CAAC,GAAG,aAAa,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,KAC7D,WAAW;AACX,gBAAI,oBAAoB,QAAQ;AAC5B,uBAAS,UAAU,CAAC,GAAG,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE;AAAA,YACxD;AACA,2BAAe,CAAC,GAAG,aAAa,MAAM,GAAG,GAAG,GAAG,QAAQ;AACvD,mBAAO;AACP;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,OAAO;AACP,UAAM,WAAW,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AACtD,WAAO,MAAM,KAAK,CAAC,eAAe,aAAa,MAAM,CAAC,GAAG,QAAQ,GAAG;AAChE,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO,aAAa,MAAM,GAAG,GAAG;AACpC;AACA,eAAe,eAAe,UAAU,SAAS;AAC7C,QAAM,EAAE,eAAe,OAAO,gBAAgB,OAAO,OAAO,SAAS,GAAG,KAAK,IAAI;AAEjF,MAAI,eAAe,SAAS,IAAI,CAAC,YAAY;AACzC,UAAM,SAAS,OAAO,YAAY,OAAO,QAAQ,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,MAAM,UAAU,CAAC,EAAE,WAAW,KAAK,CAAC,CAAC;AAC/G,WAAO,qBAAqB,QAAQ,QAAQ,GAAG,QAAQ,mBAAmB,OAAO,CAAC;AAAA,EACtF,CAAC;AACD,MAAI,OAAO;AACP,UAAM,WAAW,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AACtD,WAAO,aAAa,SAAS,KACzB,CAAC,eAAe,aAAa,aAAa,SAAS,CAAC,GAAG,QAAQ,GAAG;AAClE,qBAAe,aAAa,MAAM,GAAG,EAAE;AAAA,IAC3C;AAAA,EACJ;AACA,QAAM,gBAAgB,iBAAiB,aAAa,CAAC,GAAG,QAAQ,MAAM;AACtE,MAAI,YAAY,gBACV,aAAa,MAAM,GAAG,CAAC,EAAE,OAAO,aAAa,MAAM,CAAC,EAAE,QAAQ,CAAC,IAC/D,aAAa,QAAQ;AAC3B,cAAY,MAAM,gBAAgB,WAAW;AAAA,IACzC,GAAG;AAAA,IACH,iBAAiB,eAAe,SAAS;AAAA,IACzC,OAAO;AAAA,EACX,CAAC;AACD,MAAI,eAAe;AACf,WAAO,CAAC,UAAU,CAAC,GAAG,GAAG,UAAU,MAAM,CAAC,EAAE,QAAQ,CAAC;AAAA,EACzD,OACK;AACD,WAAO,UAAU,QAAQ;AAAA,EAC7B;AACJ;AACA,IAAM,iBAAiB;AAAA,EACnB,OAAO;AAAA,IACH,SAAS;AAAA,IACT,cAAc;AAAA,EAClB;AAAA,EACA,IAAI;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,EAClB;AAAA,EACA,QAAQ;AAAA,IACJ,SAAS;AAAA,IACT,cAAc;AAAA,EAClB;AAAA,EACA,WAAW;AAAA,IACP,SAAS;AAAA,IACT,cAAc;AAAA,EAClB;AAAA,EACA,MAAM;AAAA,IACF,SAAS;AAAA,IACT,cAAc;AAAA,EAClB;AAAA,EACA,UAAU;AAAA,IACN,SAAS;AAAA,IACT,cAAc;AAAA,EAClB;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,IACT,cAAc;AAAA,EAClB;AAAA,EACA,QAAQ;AAAA,IACJ,SAAS;AAAA,IACT,cAAc;AAAA;AAAA,EAClB;AACJ;AACA,SAAS,qBAAqB,aAAa,QAAQ,aAAa;AAC5D,MAAI;AACJ,MAAI;AACJ,UAAQ,aAAa;AAAA,IACjB,KAAK;AACD,UAAI,aAAa;AACb,gBAAQ,IAAI,kBAAkB,MAAM;AAAA,MACxC,OACK;AACD,cAAM,IAAI,aAAa,MAAM;AAAA,MACjC;AACA;AAAA,IACJ,KAAK;AACD,UAAI,aAAa;AACb,YAAI,gBAAgB;AAAA,UAChB,GAAG;AAAA,QACP;AACA,YAAI,gBAAgB,eAAe;AAC/B,0BAAgB;AAAA,YACZ,GAAG;AAAA,YACH,kBAAkB,cAAc,YAAY,IAAI,CAAC,QAAQ;AAAA,cACrD,GAAG;AAAA,cACH,MAAM;AAAA,cACN,OAAO;AAAA,cACP,MAAM,KAAK,UAAU,GAAG,IAAI;AAAA,YAChC,EAAE;AAAA,UACN;AAAA,QACJ;AACA,gBAAQ,IAAI,eAAe,aAAa;AAAA,MAC5C,OACK;AACD,cAAM,IAAI,UAAU,MAAM;AAAA,MAC9B;AACA;AAAA,IACJ,KAAK;AACD,UAAI,aAAa;AACb,gBAAQ,IAAI,mBAAmB,MAAM;AAAA,MACzC,OACK;AACD,cAAM,IAAI,cAAc,MAAM;AAAA,MAClC;AACA;AAAA,IACJ,KAAK;AACD,UAAI,aAAa;AACb,gBAAQ,IAAI,mBAAmB;AAAA,UAC3B,GAAG;AAAA,UACH,mBAAmB;AAAA,YACf,GAAG,OAAO;AAAA,YACV,iBAAiB;AAAA,UACrB;AAAA,QACJ,CAAC;AAAA,MACL,OACK;AACD,cAAM,IAAI,cAAc;AAAA,UACpB,GAAG;AAAA,UACH,mBAAmB;AAAA,YACf,GAAG,OAAO;AAAA,YACV,iBAAiB;AAAA,UACrB;AAAA,QACJ,CAAC;AAAA,MACL;AACA;AAAA,IACJ,KAAK;AACD,UAAI,kBAAkB,QAAQ;AAC1B,YAAI,aAAa;AACb,kBAAQ,IAAI,iBAAiB,MAAM;AAAA,QACvC,OACK;AACD,gBAAM,IAAI,YAAY,MAAM;AAAA,QAChC;AAAA,MACJ,OACK;AACD,cAAM,IAAI,MAAM,yFAAyF;AAAA,MAC7G;AACA;AAAA,IACJ,KAAK;AACD,UAAI,aAAa;AACb,gBAAQ,IAAI,qBAAqB,MAAM;AAAA,MAC3C,OACK;AACD,YAAI,CAAC,OAAO,MAAM;AACd,gBAAM,IAAI,MAAM,0CAA0C;AAAA,QAC9D;AACA,cAAM,IAAI,gBAAgB,MAAM;AAAA,MACpC;AACA;AAAA,IACJ,KAAK;AACD,UAAI,UAAU,QAAQ;AAClB,YAAI,aAAa;AACb,kBAAQ,IAAI,iBAAiB,MAAM;AAAA,QACvC,OACK;AACD,gBAAM,IAAI,YAAY,MAAM;AAAA,QAChC;AAAA,MACJ,OACK;AACD,cAAM,IAAI,MAAM,iFAAiF;AAAA,MACrG;AACA;AAAA,IACJ;AACI,YAAM,IAAI,MAAM,6BAA6B,WAAW,EAAE;AAAA,EAClE;AACA,MAAI,eAAe,OAAO;AACtB,WAAO;AAAA,EACX;AACA,MAAI,KAAK;AACL,WAAO;AAAA,EACX;AACA,QAAM,IAAI,MAAM,6BAA6B,WAAW,EAAE;AAC9D;AACA,SAAS,YAAY,OAAO;AACxB,QAAM,YAAY,MAAM,QAAQ;AAChC,MAAI;AACJ,QAAM,SAAS,OAAO,YAAY,OAAO,QAAQ,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,kBAAkB,EAAE,SAAS,CAAC,KAAK,CAAC,EAAE,WAAW,KAAK,CAAC,CAAC;AAC1I,MAAI,aAAa,gBAAgB;AAC7B,UAAM,qBAAqB,WAAW,MAAM;AAAA,EAChD;AACA,MAAI,CAAC,KAAK;AACN,UAAM,IAAI,MAAM,oCAAoC,SAAS,2BAA2B,OAAO,KAAK,cAAc,CAAC,EAAE;AAAA,EACzH;AACA,SAAO;AACX;AAOO,SAAS,oBAAoB,MAAM;AACtC,QAAM,SAAS,KAAK,MAAM,IAAI;AAC9B,SAAO,QAAQ,QAAQ;AAAA,IACnB,GAAG,OAAO,MAAM,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC;AAAA,CAAI;AAAA,IAC1C,OAAO,OAAO,SAAS,CAAC;AAAA,EAC5B,CAAC;AACL;;;AEncA;AAMO,IAAM,sBAAN,MAAM,6BAA4B,mBAAmB;AAAA,EACxD,OAAO,UAAU;AACb,WAAO;AAAA,EACX;AAAA,EACA,YAAY,OAAO;AACf,UAAM,KAAK;AACX,WAAO,eAAe,MAAM,gBAAgB;AAAA,MACxC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO,CAAC,kBAAkB,WAAW,OAAO;AAAA,IAChD,CAAC;AACD,WAAO,eAAe,MAAM,YAAY;AAAA,MACpC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,kBAAkB;AAAA,MAC1C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,oBAAoB;AAAA,MAC5C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AAOD,WAAO,eAAe,MAAM,2BAA2B;AAAA,MACnD,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,SAAK,WAAW,MAAM;AACtB,SAAK,iBAAiB,MAAM,kBAAkB,KAAK;AACnD,SAAK,mBAAmB,MAAM,oBAAoB,KAAK;AACvD,SAAK,0BAA0B,MAAM;AACrC,QAAI,KAAK,kBAAkB;AACvB,UAAI,sBAAsB,KAAK;AAC/B,UAAI,KAAK,kBAAkB;AACvB,8BAAsB,oBAAoB,OAAO,OAAO,KAAK,KAAK,gBAAgB,CAAC;AAAA,MACvF;AACA,yBAAmB;AAAA,QACf,EAAE,MAAM,aAAa,WAAW,KAAK,SAAS;AAAA,MAClD,GAAG,KAAK,gBAAgB,mBAAmB;AAAA,IAC/C;AAAA,EACJ;AAAA,EACA,iBAAiB;AACb,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ,QAAQ;AAClB,UAAM,oBAAoB,KAAK,eAAe,OAAO,CAAC,OAAO,EAAE,MAAM,OAAO;AAC5E,UAAM,sBAAsB;AAAA,MACxB,GAAI,KAAK,oBAAoB,CAAC;AAAA,MAC9B,GAAG;AAAA,IACP;AACA,UAAM,aAAa;AAAA,MACf,GAAG;AAAA,MACH,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,IACtB;AACA,WAAO,IAAI,qBAAoB,UAAU;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,OAAO,QAAQ;AAEjB,UAAM,YAAY,CAAC;AACnB,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,QAAQ,GAAG;AACtD,UAAI,OAAO,UAAU,UAAU;AAC3B,kBAAU,GAAG,IAAI,eAAe,OAAO,KAAK,gBAAgB,MAAM;AAAA,MACtE,OACK;AACD,kBAAU,GAAG,IAAI;AAAA,MACrB;AAAA,IACJ;AACA,UAAM,MAAM,OAAO,OAAO,UAAU;AACpC,UAAM,SAAS,OAAO,UAAU,UAAU;AAC1C,QAAI,CAAC,KAAK;AACN,YAAM,IAAI,MAAM,mCAAmC;AAAA,IACvD;AACA,QAAI,OAAO,QAAQ,UAAU;AACzB,YAAM,IAAI,MAAM,uBAAuB;AAAA,IAC3C;AACA,UAAM,SAAS,EAAE,IAAI;AACrB,QAAI,QAAQ;AACR,aAAO,SAAS;AAAA,IACpB;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,kBAAkB,QAAQ;AAC5B,UAAM,kBAAkB,MAAM,KAAK,OAAO,MAAM;AAChD,WAAO,IAAI,iBAAiB,eAAe;AAAA,EAC/C;AACJ;;;AC3HA;AAEO,IAAM,qBAAN,cAAiC,SAAS;AAAA,EAC7C,OAAO,UAAU;AACb,WAAO;AAAA,EACX;AAAA,EACA,YAAY,QAAQ;AAChB,UAAM,iBAAiB,OAAO,kBAAkB;AAChD,UAAM,iBAAiB,mBAAmB,OAAO,UAAU,cAAc;AACzE,UAAM,EAAE,gBAAgB,GAAG,OAAO,CAAC;AACnC,WAAO,eAAe,MAAM,gBAAgB;AAAA,MACxC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO,CAAC,kBAAkB,WAAW,MAAM;AAAA,IAC/C,CAAC;AACD,WAAO,eAAe,MAAM,mBAAmB;AAAA,MAC3C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,YAAY;AAAA,MACpC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,kBAAkB;AAAA,MAC1C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,kBAAkB;AAAA,MAC1C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,SAAK,WAAW,OAAO;AACvB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,MAAM,OAAO,QAAQ;AACjB,WAAO,sBAAsB,KAAK,UAAU,QAAQ,KAAK,cAAc;AAAA,EAC3E;AAAA,EACA,MAAM,OAAO,QAAQ;AACjB,WAAO,MAAM,KAAK,gBAAgB,KAAK,OAAO,KAAK,IAAI,GAAG,QAAQ;AAAA,MAC9D,SAAS;AAAA,IACb,CAAC;AAAA,EACL;AACJ;AACA,SAAS,mBAAmB,UAAU,gBAAgB;AAClD,QAAM,iBAAiB,CAAC;AACxB,aAAW,KAAK,OAAO,OAAO,QAAQ,GAAG;AACrC,QAAI,OAAO,MAAM,UAAU;AACvB,oBAAc,GAAG,cAAc,EAAE,QAAQ,CAAC,MAAM;AAC5C,YAAI,EAAE,SAAS,YAAY;AACvB,yBAAe,KAAK,EAAE,IAAI;AAAA,QAC9B;AAAA,MACJ,CAAC;AAAA,IACL,WACS,MAAM,QAAQ,CAAC,GAAG;AACvB,iBAAW,KAAK,GAAG;AACf,YAAI,OAAO,MAAM,UAAU;AACvB,wBAAc,GAAG,cAAc,EAAE,QAAQ,CAAC,MAAM;AAC5C,gBAAI,EAAE,SAAS,YAAY;AACvB,6BAAe,KAAK,EAAE,IAAI;AAAA,YAC9B;AAAA,UACJ,CAAC;AAAA,QACL,WACS,OAAO,MAAM,UAAU;AAC5B,yBAAe,KAAK,GAAG,mBAAmB,GAAG,cAAc,CAAC;AAAA,QAChE;AAAA,MACJ;AAAA,IACJ,WACS,OAAO,MAAM,YAAY,MAAM,MAAM;AAC1C,qBAAe,KAAK,GAAG,mBAAmB,GAAG,cAAc,CAAC;AAAA,IAChE;AAAA,EACJ;AACA,SAAO,MAAM,KAAK,IAAI,IAAI,cAAc,CAAC;AAC7C;AACA,SAAS,sBAAsB,UAAU,QAAQ,gBAAgB;AAC7D,QAAM,YAAY,CAAC;AACnB,aAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,QAAQ,GAAG;AAC3C,QAAI,OAAO,MAAM,UAAU;AACvB,gBAAU,CAAC,IAAI,eAAe,GAAG,gBAAgB,MAAM;AAAA,IAC3D,WACS,MAAM,QAAQ,CAAC,GAAG;AACvB,YAAM,aAAa,CAAC;AACpB,iBAAW,KAAK,GAAG;AACf,YAAI,OAAO,MAAM,UAAU;AACvB,qBAAW,KAAK,eAAe,GAAG,gBAAgB,MAAM,CAAC;AAAA,QAC7D,WACS,OAAO,MAAM,UAAU;AAC5B,qBAAW,KAAK,sBAAsB,GAAG,QAAQ,cAAc,CAAC;AAAA,QACpE;AAAA,MACJ;AACA,gBAAU,CAAC,IAAI;AAAA,IACnB,WACS,OAAO,MAAM,YAAY,MAAM,MAAM;AAC1C,gBAAU,CAAC,IAAI,sBAAsB,GAAG,QAAQ,cAAc;AAAA,IAClE,OACK;AACD,gBAAU,CAAC,IAAI;AAAA,IACnB;AAAA,EACJ;AACA,SAAO;AACX;;;AL5FO,IAAM,4BAAN,cAAwC,SAAS;AAAA,EACpD,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,WAAO,eAAe,MAAM,gBAAgB;AAAA,MACxC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO,CAAC,kBAAkB,WAAW,MAAM;AAAA,IAC/C,CAAC;AACD,WAAO,eAAe,MAAM,mBAAmB;AAAA,MAC3C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO,OAAO,SAAS;AACzB,WAAO,KAAK,gBAAgB,CAACA,WAAU,KAAK,eAAeA,MAAK,GAAG,OAAO,EAAE,GAAG,SAAS,SAAS,SAAS,CAAC;AAAA,EAC/G;AACJ;AAKO,IAAM,sBAAN,cAAkC,0BAA0B;AAAA,EAC/D,OAAO,UAAU;AACb,WAAO;AAAA,EACX;AAAA,EACA,YAAY,QAAQ;AAChB,QAAI,OAAO,WAAW,UAAU;AAE5B,eAAS,EAAE,cAAc,OAAO;AAAA,IACpC;AACA,UAAM,MAAM;AACZ,WAAO,eAAe,MAAM,gBAAgB;AAAA,MACxC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,YAAY;AAAA,MACpC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,SAAK,eAAe,OAAO;AAC3B,SAAK,WAAW,OAAO,YAAY;AAAA,EACvC;AAAA,EACA,IAAI,iBAAiB;AACjB,WAAO,CAAC,KAAK,YAAY;AAAA,EAC7B;AAAA,EACA,MAAM,eAAe,QAAQ;AACzB,UAAM,QAAQ,OAAO,KAAK,YAAY;AACtC,QAAI,KAAK,YAAY,CAAC,OAAO;AACzB,aAAO,CAAC;AAAA,IACZ,WACS,CAAC,OAAO;AACb,YAAM,QAAQ,IAAI,MAAM,UAAU,KAAK,YAAY,uHAAuH;AAC1K,YAAM,OAAO;AACb,YAAM;AAAA,IACV;AACA,QAAI;AACJ,QAAI;AACA,UAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,4BAAoB,MAAM,IAAI,0BAA0B;AAAA,MAC5D,OACK;AACD,4BAAoB,CAAC,2BAA2B,KAAK,CAAC;AAAA,MAC1D;AAAA,IAEJ,SACO,GAAG;AACN,YAAM,gBAAgB,OAAO,UAAU,WAAW,QAAQ,KAAK,UAAU,OAAO,MAAM,CAAC;AACvF,YAAM,QAAQ,IAAI,MAAM;AAAA,QACpB,UAAU,KAAK,YAAY;AAAA,QAC3B,mBAAmB,aAAa;AAAA,QAChC,uBAAuB,EAAE,OAAO;AAAA,MACpC,EAAE,KAAK,MAAM,CAAC;AACd,YAAM,OAAO;AAEb,YAAM,gBAAgB,EAAE;AACxB,YAAM;AAAA,IACV;AACA,WAAO;AAAA,EACX;AACJ;AAKO,IAAM,kCAAN,cAA8C,0BAA0B;AAAA,EAC3E,YAAY,QAAQ;AAChB,QAAI,EAAE,YAAY,SAAS;AAEvB,eAAS,EAAE,QAAQ,OAAO;AAAA,IAC9B;AACA,UAAM,MAAM;AACZ,WAAO,eAAe,MAAM,UAAU;AAAA,MAClC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,SAAK,SAAS,OAAO;AAAA,EACzB;AAAA,EACA,IAAI,iBAAiB;AACjB,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA,EACA,MAAM,eAAe,QAAQ;AACzB,WAAO,CAAC,MAAM,KAAK,OAAO,MAAM,CAAC;AAAA,EACrC;AACJ;AAKO,IAAM,yBAAN,cAAqC,mBAAmB;AAAA,EAC3D,YAAY,OAAO;AACf,UAAM,KAAK;AAAA,EACf;AAAA,EACA,MAAM,OAAO,QAAQ;AACjB,YAAQ,MAAM,KAAK,kBAAkB,MAAM,GAAG,SAAS;AAAA,EAC3D;AAAA,EACA,MAAM,kBAAkB,QAAQ;AAC5B,UAAM,iBAAiB,MAAM,KAAK,eAAe,MAAM;AACvD,WAAO,IAAI,gBAAgB,cAAc;AAAA,EAC7C;AACJ;AAKO,IAAM,4BAAN,cAAwC,gCAAgC;AAAA,EAC3E,OAAO,UAAU;AACb,WAAO;AAAA,EACX;AAAA,EACA,YAAY,QAAQ,MAAM;AACtB,QAAI,EAAE,YAAY,SAAS;AAEvB,eAAS,EAAE,QAAQ,QAAQ,KAAW;AAAA,IAC1C;AACA,UAAM,MAAM;AACZ,WAAO,eAAe,MAAM,QAAQ;AAAA,MAChC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,SAAK,OAAO,OAAO;AAAA,EACvB;AAAA,EACA,MAAM,OAAO,QAAQ;AACjB,WAAO,IAAI,YAAY,MAAM,KAAK,OAAO,OAAO,MAAM,GAAG,KAAK,IAAI;AAAA,EACtE;AAAA,EACA,OAAO,aAAa,UAAU,MAAM,SAAS;AACzC,WAAO,IAAI,KAAK,eAAe,aAAa,UAAU;AAAA,MAClD,gBAAgB,SAAS;AAAA,IAC7B,CAAC,GAAG,IAAI;AAAA,EACZ;AACJ;AACA,SAAS,oBAAoB,OAAO;AAChC,MAAI,UAAU,QAAQ,OAAO,UAAU,YAAY,MAAM,QAAQ,KAAK,GAAG;AACrE,WAAO;AAAA,EACX;AACA,SAAQ,OAAO,KAAK,KAAK,EAAE,WAAW,KAClC,UAAU,SACV,OAAO,MAAM,SAAS;AAC9B;AACA,SAAS,qBAAqB,OAAO;AACjC,MAAI,UAAU,QAAQ,OAAO,UAAU,YAAY,MAAM,QAAQ,KAAK,GAAG;AACrE,WAAO;AAAA,EACX;AACA,SAAQ,eAAe,UAClB,OAAO,MAAM,cAAc,YACvB,OAAO,MAAM,cAAc,YACxB,MAAM,cAAc,QACpB,SAAS,MAAM,aACf,OAAO,MAAM,UAAU,QAAQ;AAC/C;AACA,IAAM,oCAAN,cAAgD,0BAA0B;AAAA,EACtE,OAAO,gBAAgB;AACnB,UAAM,IAAI,MAAM,4EAA4E;AAAA,EAChG;AAAA,EACA,YAGA,QAAQ,mBAAmB;AACvB,QAAI,EAAE,YAAY,SAAS;AAEvB,eAAS,EAAE,QAAQ,OAAO;AAAA,IAC9B;AACA,UAAM,MAAM;AACZ,WAAO,eAAe,MAAM,gBAAgB;AAAA,MACxC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO,CAAC,kBAAkB,WAAW,MAAM;AAAA,IAC/C,CAAC;AACD,WAAO,eAAe,MAAM,mBAAmB;AAAA,MAC3C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,kBAAkB;AAAA,MAC1C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO,CAAC;AAAA,IACZ,CAAC;AACD,WAAO,eAAe,MAAM,qBAAqB;AAAA,MAC7C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO,CAAC;AAAA,IACZ,CAAC;AACD,WAAO,eAAe,MAAM,UAAU;AAAA,MAClC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,gBAAgB;AAAA,MACxC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AAGD,WAAO,eAAe,MAAM,oBAAoB;AAAA,MAC5C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,SAAK,SAAS,OAAO;AACrB,QAAI,MAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,UAAI,iBAAiB,CAAC;AACtB,WAAK,OAAO,QAAQ,CAAC,WAAW;AAC5B,YAAI,oBAAoB,QAAQ;AAC5B,2BAAiB,eAAe,OAAO,OAAO,cAAc;AAAA,QAChE;AAAA,MACJ,CAAC;AACD,WAAK,iBAAiB;AAAA,IAC1B,OACK;AACD,WAAK,iBAAiB,KAAK,OAAO;AAAA,IACtC;AACA,SAAK,oBAAoB,qBAAqB,KAAK;AAAA,EACvD;AAAA,EACA,cAAc,SAAS;AAEnB,UAAM,cAAc,KAAK;AACzB,QAAI,YAAY,cAAc,GAAG;AAC7B,YAAM,WAAW,YAAY,cAAc;AAC3C,aAAO,IAAI,SAAS,EAAE,QAAQ,CAAC;AAAA,IACnC,WACS,YAAY,kBAAkB;AACnC,YAAM,WAAW,YAAY,iBAAiB;AAE9C,aAAO,IAAI,SAAS;AAAA,QAChB;AAAA,QACA,MAAM,KAAK,wBAAwB,SAAS,QAAQ,CAAC;AAAA,MACzD,CAAC;AAAA,IACL,OACK;AACD,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC9C;AAAA,EACJ;AAAA,EACA,wBAAwB,MAAM;AAC1B,YAAQ,MAAM;AAAA,MACV,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX;AACI,cAAM,IAAI,MAAM,4BAA4B;AAAA,IACpD;AAAA,EACJ;AAAA,EACA,OAAO,aAAa,UAAU,mBAAmB;AAC7C,QAAI,OAAO,aAAa,UAAU;AAC9B,aAAO,IAAI,KAAK,eAAe,aAAa,UAAU,iBAAiB,CAAC;AAAA,IAC5E;AACA,UAAM,SAAS,CAAC;AAChB,eAAW,QAAQ,UAAU;AAEzB,UAAI,OAAO,SAAS,UAAU;AAC1B,eAAO,KAAK,eAAe,aAAa,MAAM,iBAAiB,CAAC;AAAA,MACpE,WACS,SAAS,MAAM;AAAA,MAExB,WACS,oBAAoB,IAAI,GAAG;AAChC,YAAI,OAAO;AACX,YAAI,OAAO,KAAK,SAAS,UAAU;AAC/B,iBAAO,KAAK,QAAQ;AAAA,QACxB;AACA,cAAM,UAAU;AAAA,UACZ,GAAG;AAAA,UACH,yBAAyB;AAAA,QAC7B;AACA,eAAO,KAAK,eAAe,aAAa,MAAM,OAAO,CAAC;AAAA,MAC1D,WACS,qBAAqB,IAAI,GAAG;AACjC,YAAI,cAAc,KAAK,aAAa;AACpC,YAAI;AACJ,YAAI,iBAAiB,CAAC;AACtB,YAAI,OAAO,gBAAgB,UAAU;AACjC,cAAI;AACJ,cAAI,mBAAmB,mBAAmB,YAAY;AAClD,6BAAiB,cAAc,WAAW;AAAA,UAC9C,OACK;AACD,6BAAiB,aAAa,WAAW;AAAA,UAC7C;AACA,gBAAM,YAAY,eAAe,QAAQ,CAACC,UAASA,MAAK,SAAS,aAAa,CAACA,MAAK,IAAI,IAAI,CAAC,CAAC;AAC9F,eAAK,WAAW,UAAU,KAAK,GAAG;AAC9B,gBAAI,UAAU,SAAS,GAAG;AACtB,oBAAM,IAAI,MAAM;AAAA,OAA8D,SAAS;AAAA,QAAW,WAAW,EAAE;AAAA,YACnH;AACA,6BAAiB,CAAC,UAAU,CAAC,CAAC;AAAA,UAClC,OACK;AACD,6BAAiB,CAAC;AAAA,UACtB;AACA,wBAAc,EAAE,KAAK,YAAY;AACjC,8BAAoB,IAAI,oBAAoB;AAAA,YACxC,UAAU;AAAA,YACV;AAAA,YACA,gBAAgB,mBAAmB;AAAA,YACnC,yBAAyB;AAAA,UAC7B,CAAC;AAAA,QACL,WACS,OAAO,gBAAgB,UAAU;AACtC,cAAI,SAAS,aAAa;AACtB,gBAAI;AACJ,gBAAI,mBAAmB,mBAAmB,YAAY;AAClD,+BAAiB,cAAc,YAAY,GAAG;AAAA,YAClD,OACK;AACD,+BAAiB,aAAa,YAAY,GAAG;AAAA,YACjD;AACA,6BAAiB,eAAe,QAAQ,CAACA,UAASA,MAAK,SAAS,aAAa,CAACA,MAAK,IAAI,IAAI,CAAC,CAAC;AAAA,UACjG,OACK;AACD,6BAAiB,CAAC;AAAA,UACtB;AACA,8BAAoB,IAAI,oBAAoB;AAAA,YACxC,UAAU;AAAA,YACV;AAAA,YACA,gBAAgB,mBAAmB;AAAA,YACnC,yBAAyB;AAAA,UAC7B,CAAC;AAAA,QACL,OACK;AACD,gBAAM,IAAI,MAAM,wBAAwB;AAAA,QAC5C;AACA,eAAO,KAAK,iBAAiB;AAAA,MACjC,WACS,OAAO,SAAS,UAAU;AAC/B,eAAO,KAAK,IAAI,mBAAmB;AAAA,UAC/B,UAAU;AAAA,UACV,gBAAgB,mBAAmB;AAAA,QACvC,CAAC,CAAC;AAAA,MACN;AAAA,IACJ;AACA,WAAO,IAAI,KAAK,EAAE,QAAQ,kBAAkB,CAAC;AAAA,EACjD;AAAA,EACA,MAAM,OAAO,OAAO;AAEhB,QAAI,KAAK,kBAAkB,0BAA0B;AACjD,YAAM,OAAO,MAAM,KAAK,OAAO,OAAO,KAAK;AAC3C,aAAO,KAAK,cAAc,IAAI;AAAA,IAClC,OACK;AACD,YAAM,UAAU,CAAC;AACjB,iBAAW,UAAU,KAAK,QAAQ;AAE9B,YAAI,SAAS,CAAC;AACd,YAAI,EAAE,oBAAoB,SAAS;AAC/B,gBAAM,IAAI,MAAM,UAAU,MAAM,wCAAwC;AAAA,QAC5E;AACA,mBAAW,QAAQ,OAAO,gBAAgB;AACtC,cAAI,CAAC,QAAQ;AACT,qBAAS,EAAE,CAAC,IAAI,GAAG,MAAM,IAAI,EAAE;AAAA,UACnC;AACA,mBAAS,EAAE,GAAG,QAAQ,CAAC,IAAI,GAAG,MAAM,IAAI,EAAE;AAAA,QAC9C;AAEA,YAAI,kBAAkB,0BAA0B;AAC5C,gBAAM,YAAY,MAAM,OAAO,OAAO,MAAM;AAC5C,cAAI;AACJ,cAAI,6BAA6B,QAAQ;AAErC,sCAA0B,OAAO;AAAA,UACrC;AACA,kBAAQ,KAAK;AAAA,YACT,GAAG;AAAA,YACH,MAAM;AAAA,YACN,MAAM;AAAA,UACV,CAAC;AAAA,QAGL,WACS,kBAAkB,qBAAqB;AAC5C,gBAAM,YAAY,MAAM,OAAO,OAAO,MAAM;AAC5C,cAAI;AACJ,cAAI,6BAA6B,QAAQ;AAErC,sCAA0B,OAAO;AAAA,UACrC;AACA,kBAAQ,KAAK;AAAA,YACT,GAAG;AAAA,YACH,MAAM;AAAA,YACN,WAAW;AAAA,UACf,CAAC;AAAA,QAEL,WACS,kBAAkB,oBAAoB;AAC3C,gBAAM,YAAY,MAAM,OAAO,OAAO,MAAM;AAC5C,cAAI;AACJ,cAAI,6BAA6B,QAAQ;AAErC,sCAA0B,OAAO;AAAA,UACrC;AACA,kBAAQ,KAAK;AAAA,YACT,GAAG;AAAA,YACH,GAAG;AAAA,UACP,CAAC;AAAA,QACL;AAAA,MACJ;AACA,aAAO,KAAK,cAAc,OAAO;AAAA,IACrC;AAAA,EACJ;AAAA,EACA,MAAM,eAAe,QAAQ;AACzB,WAAO,CAAC,MAAM,KAAK,OAAO,MAAM,CAAC;AAAA,EACrC;AACJ;AAeO,IAAM,6BAAN,cAAyC,kCAAkC;AAAA,EAC9E,OAAO,gBAAgB;AACnB,WAAO;AAAA,EACX;AAAA,EACA,OAAO,UAAU;AACb,WAAO;AAAA,EACX;AACJ;AAKO,IAAM,0BAAN,cAAsC,kCAAkC;AAAA,EAC3E,OAAO,gBAAgB;AACnB,WAAO;AAAA,EACX;AAAA,EACA,OAAO,UAAU;AACb,WAAO;AAAA,EACX;AACJ;AAeO,IAAM,8BAAN,cAA0C,kCAAkC;AAAA,EAC/E,OAAO,gBAAgB;AACnB,WAAO;AAAA,EACX;AAAA,EACA,OAAO,UAAU;AACb,WAAO;AAAA,EACX;AACJ;AACA,SAAS,6BAA6B,+BAA+B;AACjE,SAAQ,OAAO,8BACV,mBAAmB;AAC5B;AACA,SAAS,iCAAiC,2BAA2B,OAAO;AACxE,MAAI,6BAA6B,yBAAyB,KACtD,cAAc,yBAAyB,GAAG;AAC1C,WAAO;AAAA,EACX;AACA,MAAI,MAAM,QAAQ,yBAAyB,KACvC,0BAA0B,CAAC,MAAM,eAAe;AAChD,UAAM,iBAAiB,0BAA0B,CAAC;AAClD,QAAI,OAAO,mBAAmB,cAC1B,OAAO,mBAAmB,YAC1B,eAAe,MAAM,GAAG,CAAC,MAAM,QAC/B,eAAe,MAAM,EAAE,MAAM,MAAM;AACnC,YAAM,eAAe,eAAe,MAAM,GAAG,EAAE;AAC/C,aAAO,IAAI,oBAAoB,EAAE,cAAc,UAAU,KAAK,CAAC;AAAA,IACnE,WACS,OAAO,mBAAmB,YAC/B,eAAe,CAAC,MAAM,OACtB,eAAe,eAAe,SAAS,CAAC,MAAM,KAAK;AACnD,YAAM,eAAe,eAAe,MAAM,GAAG,EAAE;AAC/C,aAAO,IAAI,oBAAoB,EAAE,cAAc,UAAU,KAAK,CAAC;AAAA,IACnE;AACA,UAAM,IAAI,MAAM,2CAA2C,OAAO,kBAAkB,YAAY,MAAM,0BAA0B,CAAC,CAAC,6CAA6C,OAAO,mBAAmB,aAAa,WAAW,QAAQ,gBAAgB;AAAA,EAC7P;AACA,QAAM,UAAU,2BAA2B,yBAAyB;AACpE,MAAI;AACJ,MAAI,OAAO,QAAQ,YAAY,UAAU;AACrC,mBAAe,QAAQ;AAAA,EAC3B,OACK;AAED,mBAAe,QAAQ,QAAQ,IAAI,CAAC,SAAS;AACzC,UAAI,UAAU,MAAM;AAChB,eAAO,EAAE,GAAG,MAAM,MAAM,KAAK,KAAK;AAAA,MACtC,WACS,eAAe,MAAM;AAC1B,eAAO,EAAE,GAAG,MAAM,WAAW,KAAK,UAAU;AAAA,MAChD,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ,CAAC;AAAA,EACL;AACA,MAAI,QAAQ,SAAS,MAAM,SAAS;AAChC,WAAO,2BAA2B,aAAa,cAAc,KAAK;AAAA,EACtE,WACS,QAAQ,SAAS,MAAM,MAAM;AAClC,WAAO,wBAAwB,aAAa,cAAc,KAAK;AAAA,EACnE,WACS,QAAQ,SAAS,MAAM,UAAU;AACtC,WAAO,4BAA4B,aAAa,cAAc,KAAK;AAAA,EACvE,WACS,YAAY,WAAW,OAAO,GAAG;AACtC,WAAO,0BAA0B,aAAa,QAAQ,SAAS,QAAQ,MAAM,KAAK;AAAA,EACtF,OACK;AACD,UAAM,IAAI,MAAM,gFAAgF,QAAQ,SAAS,CAAC,IAAI;AAAA,EAC1H;AACJ;AACA,SAAS,sBAAsB,GAAG;AAE9B,SAAO,EAAE,YAAY,QAAQ,MAAM;AACvC;AAiBO,IAAM,qBAAN,MAAM,4BAA2B,uBAAuB;AAAA,EAC3D,OAAO,UAAU;AACb,WAAO;AAAA,EACX;AAAA,EACA,IAAI,aAAa;AACb,WAAO;AAAA,MACH,gBAAgB;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,YAAY,OAAO;AACf,UAAM,KAAK;AACX,WAAO,eAAe,MAAM,kBAAkB;AAAA,MAC1C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,oBAAoB;AAAA,MAC5C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,kBAAkB;AAAA,MAC1C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AAED,QAAI,MAAM,mBAAmB,cACzB,MAAM,qBAAqB,QAAW;AACtC,WAAK,mBAAmB;AAAA,IAC5B;AACA,WAAO,OAAO,MAAM,KAAK;AACzB,QAAI,KAAK,kBAAkB;AACvB,YAAM,yBAAyB,oBAAI,IAAI;AACvC,iBAAW,iBAAiB,KAAK,gBAAgB;AAE7C,YAAI,yBAAyB;AACzB;AACJ,mBAAW,iBAAiB,cAAc,gBAAgB;AACtD,iCAAuB,IAAI,aAAa;AAAA,QAC5C;AAAA,MACJ;AACA,YAAM,sBAAsB,KAAK;AACjC,YAAM,yBAAyB,IAAI,IAAI,KAAK,mBACtC,oBAAoB,OAAO,OAAO,KAAK,KAAK,gBAAgB,CAAC,IAC7D,mBAAmB;AACzB,YAAM,aAAa,IAAI,IAAI,CAAC,GAAG,sBAAsB,EAAE,OAAO,CAAC,MAAM,CAAC,uBAAuB,IAAI,CAAC,CAAC,CAAC;AACpG,UAAI,WAAW,OAAO,GAAG;AACrB,cAAM,IAAI,MAAM,qBAAqB;AAAA,UACjC,GAAG;AAAA,QACP,CAAC,gDAAgD;AAAA,MACrD;AACA,YAAM,kBAAkB,IAAI,IAAI,CAAC,GAAG,sBAAsB,EAAE,OAAO,CAAC,MAAM,CAAC,uBAAuB,IAAI,CAAC,CAAC,CAAC;AACzG,UAAI,gBAAgB,OAAO,GAAG;AAC1B,cAAM,IAAI,MAAM,qBAAqB;AAAA,UACjC,GAAG;AAAA,QACP,CAAC,gEAAgE;AAAA,MACrE;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,iBAAiB;AACb,WAAO;AAAA,EACX;AAAA,EACA,MAAM,mBAAmB,SAAS,aAAa;AAC3C,QAAI,OAAO,QAAQ,YAAY,UAAU;AACrC,aAAO;AAAA,IACX;AACA,UAAM,0BAA0B,MAAM,QAAQ,IAAI,QAAQ,QAAQ,IAAI,OAAO,SAAS;AAClF,UAAI,KAAK,SAAS,aAAa;AAC3B,eAAO;AAAA,MACX;AACA,UAAI,WAAW;AACf,UAAI,OAAO,KAAK,cAAc,UAAU;AACpC,mBAAW,KAAK;AAAA,MACpB,OACK;AACD,mBAAW,KAAK,UAAU;AAAA,MAC9B;AACA,YAAM,4BAA4B,eAAe,aAAa,UAAU;AAAA,QACpE,gBAAgB,KAAK;AAAA,MACzB,CAAC;AACD,YAAM,eAAe,MAAM,0BAA0B,OAAO,WAAW;AACvE,UAAI,OAAO,KAAK,cAAc,YAAY,SAAS,KAAK,WAAW;AAE/D,aAAK,UAAU,MAAM;AAAA,MACzB,OACK;AAED,aAAK,YAAY;AAAA,MACrB;AACA,aAAO;AAAA,IACX,CAAC,CAAC;AAEF,YAAQ,UAAU;AAClB,WAAO;AAAA,EACX;AAAA,EACA,MAAM,eAAe,QAAQ;AACzB,UAAM,YAAY,MAAM,KAAK,6BAA6B,MAAM;AAChE,QAAI,iBAAiB,CAAC;AACtB,eAAW,iBAAiB,KAAK,gBAAgB;AAE7C,UAAI,yBAAyB,aAAa;AACtC,uBAAe,KAAK,MAAM,KAAK,mBAAmB,eAAe,SAAS,CAAC;AAAA,MAC/E,OACK;AACD,cAAM,cAAc,cAAc,eAAe,OAAO,CAAC,KAAK,kBAAkB;AAC5E,cAAI,EAAE,iBAAiB,cACnB,EAAE,sBAAsB,aAAa,KAAK,cAAc,WAAW;AACnE,kBAAM,QAAQ,wBAAwB,IAAI,MAAM,sCAAsC,cAAc,SAAS,CAAC,IAAI,GAAG,sBAAsB;AAC3I,kBAAM;AAAA,UACV;AACA,cAAI,aAAa,IAAI,UAAU,aAAa;AAC5C,iBAAO;AAAA,QACX,GAAG,CAAC,CAAC;AACL,cAAM,UAAU,MAAM,cAAc,eAAe,WAAW;AAC9D,yBAAiB,eAAe,OAAO,OAAO;AAAA,MAClD;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,MAAM,QAAQ,QAAQ;AAGlB,UAAM,oBAAoB,KAAK,eAAe,OAAO,CAAC,OAAO,EAAE,MAAM,OAAO;AAC5E,UAAM,sBAAsB;AAAA,MACxB,GAAI,KAAK,oBAAoB,CAAC;AAAA,MAC9B,GAAG;AAAA,IACP;AACA,UAAM,aAAa;AAAA,MACf,GAAG;AAAA,MACH,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,IACtB;AACA,WAAO,IAAI,oBAAmB,UAAU;AAAA,EAC5C;AAAA,EACA,OAAO,aAAa,UAAU,SAAS;AACnC,UAAM,SAAS,eAAe,aAAa,UAAU,OAAO;AAC5D,UAAM,gBAAgB,IAAI,2BAA2B,EAAE,OAAO,CAAC;AAC/D,WAAO,KAAK,aAAa,CAAC,aAAa,CAAC;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,aAAa,gBAAgB,OAAO;AACvC,UAAM,oBAAoB,eAAe,OAAO,CAAC,KAAK,kBAAkB,IAAI;AAAA;AAAA,MAE5E,yBAAyB,sBACnB,cAAc,iBACd;AAAA,QACE,iCAAiC,eAAe,KAAK;AAAA,MACzD;AAAA,IAAC,GAAG,CAAC,CAAC;AACV,UAAM,4BAA4B,eAAe,OAAO,CAAC,KAAK;AAAA;AAAA,MAE9D,yBAAyB,sBACnB,OAAO,OAAO,KAAK,cAAc,gBAAgB,IACjD;AAAA,OAAK,uBAAO,OAAO,IAAI,CAAC;AAC9B,UAAM,iBAAiB,oBAAI,IAAI;AAC/B,eAAW,iBAAiB,mBAAmB;AAE3C,UAAI,yBAAyB;AACzB;AACJ,iBAAW,iBAAiB,cAAc,gBAAgB;AACtD,YAAI,iBAAiB,2BAA2B;AAC5C;AAAA,QACJ;AACA,uBAAe,IAAI,aAAa;AAAA,MACpC;AAAA,IACJ;AACA,WAAO,IAAI,KAAK;AAAA,MACZ,GAAG;AAAA,MACH,gBAAgB,CAAC,GAAG,cAAc;AAAA,MAClC,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,gBAAgB,OAAO;AAAA,IAC3B,CAAC;AAAA,EACL;AAAA;AAAA;AAAA,EAGA,OAAO,mBAAmB,gBAAgB;AACtC,WAAO,KAAK,aAAa,cAAc;AAAA,EAC3C;AACJ;;;AD/uBO,IAAM,wBAAN,MAAM,+BAA8B,yBAAyB;AAAA,EAChE,YAAY,OAAO;AACf,UAAM,KAAK;AACX,WAAO,eAAe,MAAM,mBAAmB;AAAA,MAC3C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,YAAY;AAAA,MACpC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,mBAAmB;AAAA,MAC3C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,iBAAiB;AAAA,MACzC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,UAAU;AAAA,MAClC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,oBAAoB;AAAA,MAC5C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,UAAU;AAAA,MAClC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,kBAAkB;AAAA,MAC1C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,oBAAoB;AAAA,MAC5C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,OAAO,MAAM,KAAK;AACzB,QAAI,KAAK,aAAa,UAAa,KAAK,oBAAoB,QAAW;AACnE,YAAM,IAAI,MAAM,kEAAkE;AAAA,IACtF;AACA,QAAI,KAAK,aAAa,UAAa,KAAK,oBAAoB,QAAW;AACnE,YAAM,IAAI,MAAM,6DAA6D;AAAA,IACjF;AACA,QAAI,KAAK,kBAAkB;AACvB,UAAI,sBAAsB,KAAK;AAC/B,UAAI,KAAK,kBAAkB;AACvB,8BAAsB,oBAAoB,OAAO,OAAO,KAAK,KAAK,gBAAgB,CAAC;AAAA,MACvF;AACA,yBAAmB,KAAK,SAAS,KAAK,QAAQ,KAAK,gBAAgB,mBAAmB;AAAA,IAC1F;AAAA,EACJ;AAAA,EACA,iBAAiB;AACb,WAAO;AAAA,EACX;AAAA,EACA,OAAO,UAAU;AACb,WAAO;AAAA,EACX;AAAA,EACA,MAAM,YAAY,gBAAgB;AAC9B,QAAI,KAAK,aAAa,QAAW;AAC7B,aAAO,KAAK;AAAA,IAChB;AACA,QAAI,KAAK,oBAAoB,QAAW;AACpC,aAAO,KAAK,gBAAgB,eAAe,cAAc;AAAA,IAC7D;AACA,UAAM,IAAI,MAAM,6DAA6D;AAAA,EACjF;AAAA,EACA,MAAM,QAAQ,QAAQ;AAClB,UAAM,oBAAoB,KAAK,eAAe,OAAO,CAAC,OAAO,EAAE,MAAM,OAAO;AAC5E,UAAM,sBAAsB;AAAA,MACxB,GAAI,KAAK,oBAAoB,CAAC;AAAA,MAC9B,GAAG;AAAA,IACP;AACA,UAAM,aAAa;AAAA,MACf,GAAG;AAAA,MACH,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,IACtB;AACA,WAAO,IAAI,uBAAsB,UAAU;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,OAAO,QAAQ;AACjB,UAAM,YAAY,MAAM,KAAK,6BAA6B,MAAM;AAChE,UAAM,WAAW,MAAM,KAAK,YAAY,SAAS;AACjD,UAAM,iBAAiB,MAAM,QAAQ,IAAI,SAAS,IAAI,CAAC,YAAY,KAAK,cAAc,OAAO,OAAO,CAAC,CAAC;AACtG,UAAM,WAAW,CAAC,KAAK,QAAQ,GAAG,gBAAgB,KAAK,MAAM,EAAE,KAAK,KAAK,gBAAgB;AACzF,WAAO,eAAe,UAAU,KAAK,gBAAgB,SAAS;AAAA,EAClE;AAAA,EACA,YAAY;AACR,QAAI,KAAK,mBAAmB,CAAC,KAAK,UAAU;AACxC,YAAM,IAAI,MAAM,4DAA4D;AAAA,IAChF;AACA,QAAI,KAAK,iBAAiB,QAAW;AACjC,YAAM,IAAI,MAAM,yDAAyD;AAAA,IAC7E;AACA,WAAO;AAAA,MACH,OAAO,KAAK,eAAe;AAAA,MAC3B,iBAAiB,KAAK;AAAA,MACtB,gBAAgB,KAAK,cAAc,UAAU;AAAA,MAC7C,mBAAmB,KAAK;AAAA,MACxB,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,MACb,iBAAiB,KAAK;AAAA,MACtB,UAAU,KAAK;AAAA,IACnB;AAAA,EACJ;AAAA,EACA,aAAa,YAAY,MAAM;AAC3B,UAAM,EAAE,eAAe,IAAI;AAC3B,QAAI,CAAC,gBAAgB;AACjB,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC5C;AACA,UAAM,gBAAgB,MAAM,eAAe,YAAY,cAAc;AACrE,QAAI;AACJ,QAAI,MAAM,QAAQ,KAAK,QAAQ,GAAG;AAC9B,iBAAW,KAAK;AAAA,IACpB,OACK;AACD,YAAM,IAAI,MAAM,6DAA6D;AAAA,IACjF;AACA,WAAO,IAAI,uBAAsB;AAAA,MAC7B,gBAAgB,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,MACA,kBAAkB,KAAK;AAAA,MACvB,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,MACb,gBAAgB,KAAK;AAAA,IACzB,CAAC;AAAA,EACL;AACJ;AAMO,IAAM,mCAAN,MAAM,0CAAyC,uBAAuB;AAAA,EACzE,iBAAiB;AACb,WAAO;AAAA,EACX;AAAA,EACA,OAAO,UAAU;AACb,WAAO;AAAA,EACX;AAAA,EACA,YAAY,QAAQ;AAChB,UAAM,MAAM;AACZ,WAAO,eAAe,MAAM,mBAAmB;AAAA,MAC3C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,YAAY;AAAA,MACpC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,mBAAmB;AAAA,MAC3C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,iBAAiB;AAAA,MACzC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,UAAU;AAAA,MAClC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,oBAAoB;AAAA,MAC5C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,UAAU;AAAA,MAClC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,kBAAkB;AAAA,MAC1C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,WAAO,eAAe,MAAM,oBAAoB;AAAA,MAC5C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,SAAK,WAAW,OAAO;AACvB,SAAK,gBAAgB,OAAO;AAC5B,SAAK,mBAAmB,OAAO,oBAAoB;AACnD,SAAK,kBAAkB,OAAO;AAC9B,SAAK,SAAS,OAAO,UAAU;AAC/B,SAAK,SAAS,OAAO,UAAU;AAC/B,SAAK,iBAAiB,OAAO,kBAAkB;AAC/C,SAAK,mBAAmB,OAAO,oBAAoB;AACnD,QAAI,KAAK,aAAa,UAAa,KAAK,oBAAoB,QAAW;AACnE,YAAM,IAAI,MAAM,kEAAkE;AAAA,IACtF;AACA,QAAI,KAAK,aAAa,UAAa,KAAK,oBAAoB,QAAW;AACnE,YAAM,IAAI,MAAM,6DAA6D;AAAA,IACjF;AACA,QAAI,KAAK,kBAAkB;AACvB,UAAI,sBAAsB,KAAK;AAC/B,UAAI,KAAK,kBAAkB;AACvB,8BAAsB,oBAAoB,OAAO,OAAO,KAAK,KAAK,gBAAgB,CAAC;AAAA,MACvF;AACA,yBAAmB,KAAK,SAAS,KAAK,QAAQ,KAAK,gBAAgB,mBAAmB;AAAA,IAC1F;AAAA,EACJ;AAAA,EACA,MAAM,YAAY,gBAAgB;AAC9B,QAAI,KAAK,aAAa,QAAW;AAC7B,aAAO,KAAK;AAAA,IAChB;AACA,QAAI,KAAK,oBAAoB,QAAW;AACpC,aAAO,KAAK,gBAAgB,eAAe,cAAc;AAAA,IAC7D;AACA,UAAM,IAAI,MAAM,6DAA6D;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,eAAe,QAAQ;AACzB,UAAM,YAAY,MAAM,KAAK,6BAA6B,MAAM;AAChE,QAAI,WAAW,MAAM,KAAK,YAAY,SAAS;AAC/C,eAAW,SAAS,IAAI,CAAC,YAAY;AAEjC,YAAM,SAAS,CAAC;AAChB,WAAK,cAAc,eAAe,QAAQ,CAAC,kBAAkB;AACzD,eAAO,aAAa,IAAI,QAAQ,aAAa;AAAA,MACjD,CAAC;AACD,aAAO;AAAA,IACX,CAAC;AACD,UAAM,WAAW,CAAC;AAClB,eAAW,WAAW,UAAU;AAC5B,YAAM,kBAAkB,MAAM,KAAK,cAAc,eAAe,OAAO;AACvE,eAAS,KAAK,GAAG,eAAe;AAAA,IACpC;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,OAAO,QAAQ;AACjB,UAAM,YAAY,MAAM,KAAK,6BAA6B,MAAM;AAChE,UAAM,WAAW,MAAM,KAAK,YAAY,SAAS;AACjD,UAAM,kBAAkB,MAAM,QAAQ,IAAI,SAAS,IAAI,CAAC,YAAY,KAAK,cAAc,eAAe,OAAO,CAAC,CAAC;AAC/G,UAAM,iBAAiB,gBAClB,KAAK,EACL,IAAI,CAAC,YAAY,QAAQ,OAAO;AACrC,UAAM,WAAW,CAAC,KAAK,QAAQ,GAAG,gBAAgB,KAAK,MAAM,EAAE,KAAK,KAAK,gBAAgB;AACzF,WAAO,eAAe,UAAU,KAAK,gBAAgB,SAAS;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ,QAAQ;AAClB,UAAM,oBAAoB,KAAK,eAAe,OAAO,CAAC,aAAa,EAAE,YAAY,OAAO;AACxF,UAAM,sBAAsB;AAAA,MACxB,GAAI,KAAK,oBAAoB,CAAC;AAAA,MAC9B,GAAG;AAAA,IACP;AACA,UAAM,aAAa;AAAA,MACf,GAAG;AAAA,MACH,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,IACtB;AACA,WAAO,IAAI,kCAAiC,UAAU;AAAA,EAC1D;AACJ;", "names": ["input", "item"]}