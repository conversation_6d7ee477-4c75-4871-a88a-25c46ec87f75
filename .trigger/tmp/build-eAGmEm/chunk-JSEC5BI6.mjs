import {
  boolean,
  integer,
  jsonb,
  pgTable,
  text,
  timestamp,
  uuid
} from "./chunk-2VGBRW4L.mjs";
import {
  __export,
  init_esm
} from "./chunk-CYQ63LDX.mjs";

// src/lib/db/schema.ts
var schema_exports = {};
__export(schema_exports, {
  aiAgentFeedback: () => aiAgentFeedback,
  conversations: () => conversations,
  matchCandidates: () => matchCandidates,
  matchQueue: () => matchQueue,
  matchRequests: () => matchRequests,
  matches: () => matches,
  messages: () => messages,
  userProfiles: () => userProfiles,
  userSessions: () => userSessions,
  users: () => users
});
init_esm();
var users = pgTable("users", {
  id: uuid("id").primaryKey().defaultRandom(),
  email: text("email").notNull().unique(),
  name: text("name"),
  avatar: text("avatar"),
  bio: text("bio"),
  age: integer("age"),
  gender: text("gender"),
  location: text("location"),
  interests: jsonb("interests").$type().default([]),
  personalityTraits: jsonb("personality_traits").$type(),
  personalitySummary: text("personality_summary"),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var userProfiles = pgTable("user_profiles", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").references(() => users.id).notNull(),
  selfDescription: text("self_description"),
  lookingFor: text("looking_for"),
  relationshipGoals: text("relationship_goals"),
  lifestyle: jsonb("lifestyle").$type(),
  values: jsonb("values").$type().default([]),
  photos: jsonb("photos").$type().default([]),
  preferences: jsonb("preferences").$type(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var matches = pgTable("matches", {
  id: uuid("id").primaryKey().defaultRandom(),
  user1Id: uuid("user1_id").references(() => users.id).notNull(),
  user2Id: uuid("user2_id").references(() => users.id).notNull(),
  compatibilityScore: integer("compatibility_score"),
  aiAnalysis: jsonb("ai_analysis").$type(),
  conversationSimulation: jsonb("conversation_simulation").$type(),
  status: text("status").default("pending"),
  // pending, mutual_like, rejected
  user1Liked: boolean("user1_liked").default(false),
  user2Liked: boolean("user2_liked").default(false),
  user1Viewed: boolean("user1_viewed").default(false),
  user2Viewed: boolean("user2_viewed").default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var aiAgentFeedback = pgTable("ai_agent_feedback", {
  id: uuid("id").primaryKey().defaultRandom(),
  matchId: uuid("match_id").references(() => matches.id).notNull(),
  userId: uuid("user_id").references(() => users.id).notNull(),
  feedbackType: text("feedback_type").notNull(),
  // 'like', 'dislike', 'accurate', 'inaccurate'
  feedbackText: text("feedback_text"),
  rating: integer("rating"),
  // 1-5 scale
  aspectRated: text("aspect_rated"),
  // 'personality', 'conversation', 'compatibility'
  createdAt: timestamp("created_at").defaultNow()
});
var conversations = pgTable("conversations", {
  id: uuid("id").primaryKey().defaultRandom(),
  matchId: uuid("match_id").references(() => matches.id).notNull(),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var messages = pgTable("messages", {
  id: uuid("id").primaryKey().defaultRandom(),
  conversationId: uuid("conversation_id").references(() => conversations.id).notNull(),
  senderId: uuid("sender_id").references(() => users.id).notNull(),
  content: text("content").notNull(),
  messageType: text("message_type").default("text"),
  // text, image, etc.
  isRead: boolean("is_read").default(false),
  createdAt: timestamp("created_at").defaultNow()
});
var userSessions = pgTable("user_sessions", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").references(() => users.id).notNull(),
  sessionToken: text("session_token").notNull().unique(),
  expiresAt: timestamp("expires_at").notNull(),
  createdAt: timestamp("created_at").defaultNow()
});
var matchQueue = pgTable("match_queue", {
  id: uuid("id").primaryKey().defaultRandom(),
  matchRequestId: uuid("match_request_id").notNull().unique(),
  requesterId: uuid("requester_id").references(() => users.id).notNull(),
  status: text("status").default("pending"),
  // pending, processing, completed, failed
  attempts: integer("attempts").default(0),
  createdAt: timestamp("created_at").defaultNow()
});
var matchRequests = pgTable("match_requests", {
  id: uuid("id").primaryKey().defaultRandom(),
  requesterId: uuid("requester_id").references(() => users.id).notNull(),
  status: text("status").default("processing"),
  // processing, completed, failed
  errorMessage: text("error_message"),
  createdAt: timestamp("created_at").defaultNow()
});
var matchCandidates = pgTable("match_candidates", {
  id: uuid("id").primaryKey().defaultRandom(),
  requestId: uuid("request_id").references(() => matchRequests.id).notNull(),
  candidateId: uuid("candidate_id").references(() => users.id).notNull(),
  rank: integer("rank").notNull(),
  // 1 to 5 (1 = 首席推荐)
  // 兼容性分析数据
  compatibilityScore: integer("compatibility_score").notNull().default(0),
  // 0-100 分数
  reasoning: text("reasoning"),
  // 兼容性推理
  highlights: jsonb("highlights"),
  // 关系亮点数组
  challenges: jsonb("challenges"),
  // 潜在挑战数组
  personalitySummary: text("personality_summary"),
  // 候选人人格摘要
  // 首席推荐的额外数据 (rank = 1 时才有)
  relationshipInsight: jsonb("relationship_insight"),
  // 关系洞察
  conversationSimulation: jsonb("conversation_simulation"),
  // 对话模拟
  datePlan: jsonb("date_plan"),
  // 约会计划
  // 用户决策
  userDecision: text("user_decision").default("pending"),
  // pending, liked, skipped, mutual_liked
  createdAt: timestamp("created_at").defaultNow()
});

export {
  users,
  userProfiles,
  matches,
  aiAgentFeedback,
  conversations,
  messages,
  userSessions,
  matchQueue,
  matchRequests,
  matchCandidates,
  schema_exports
};
//# sourceMappingURL=chunk-JSEC5BI6.mjs.map
