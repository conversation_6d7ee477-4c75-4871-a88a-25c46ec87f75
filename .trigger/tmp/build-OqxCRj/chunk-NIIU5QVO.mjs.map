{"version": 3, "sources": ["../../../node_modules/dotenv/package.json", "../../../node_modules/dotenv/lib/main.js", "../../../src/lib/db/index.ts", "../../../node_modules/src/postgres-js/driver.ts", "../../../node_modules/postgres/src/index.js", "../../../node_modules/postgres/src/types.js", "../../../node_modules/postgres/src/query.js", "../../../node_modules/postgres/src/errors.js", "../../../node_modules/postgres/src/connection.js", "../../../node_modules/postgres/src/result.js", "../../../node_modules/postgres/src/queue.js", "../../../node_modules/postgres/src/bytes.js", "../../../node_modules/postgres/src/subscribe.js", "../../../node_modules/postgres/src/large.js", "../../../node_modules/src/pg-core/db.ts", "../../../node_modules/src/pg-core/query-builders/delete.ts", "../../../node_modules/src/pg-core/query-builders/insert.ts", "../../../node_modules/src/pg-core/query-builders/query-builder.ts", "../../../node_modules/src/pg-core/dialect.ts", "../../../node_modules/src/casing.ts", "../../../node_modules/src/pg-core/view-base.ts", "../../../node_modules/src/selection-proxy.ts", "../../../node_modules/src/pg-core/query-builders/select.ts", "../../../node_modules/src/query-builders/query-builder.ts", "../../../node_modules/src/pg-core/query-builders/refresh-materialized-view.ts", "../../../node_modules/src/pg-core/query-builders/update.ts", "../../../node_modules/src/pg-core/query-builders/count.ts", "../../../node_modules/src/pg-core/query-builders/query.ts", "../../../node_modules/src/pg-core/query-builders/raw.ts", "../../../node_modules/src/postgres-js/session.ts", "../../../node_modules/src/pg-core/session.ts", "../../../src/lib/utils/env.ts"], "sourcesContent": ["{\n  \"name\": \"dotenv\",\n  \"version\": \"16.5.0\",\n  \"description\": \"Loads environment variables from .env file\",\n  \"main\": \"lib/main.js\",\n  \"types\": \"lib/main.d.ts\",\n  \"exports\": {\n    \".\": {\n      \"types\": \"./lib/main.d.ts\",\n      \"require\": \"./lib/main.js\",\n      \"default\": \"./lib/main.js\"\n    },\n    \"./config\": \"./config.js\",\n    \"./config.js\": \"./config.js\",\n    \"./lib/env-options\": \"./lib/env-options.js\",\n    \"./lib/env-options.js\": \"./lib/env-options.js\",\n    \"./lib/cli-options\": \"./lib/cli-options.js\",\n    \"./lib/cli-options.js\": \"./lib/cli-options.js\",\n    \"./package.json\": \"./package.json\"\n  },\n  \"scripts\": {\n    \"dts-check\": \"tsc --project tests/types/tsconfig.json\",\n    \"lint\": \"standard\",\n    \"pretest\": \"npm run lint && npm run dts-check\",\n    \"test\": \"tap run --allow-empty-coverage --disable-coverage --timeout=60000\",\n    \"test:coverage\": \"tap run --show-full-coverage --timeout=60000 --coverage-report=lcov\",\n    \"prerelease\": \"npm test\",\n    \"release\": \"standard-version\"\n  },\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"git://github.com/motdotla/dotenv.git\"\n  },\n  \"homepage\": \"https://github.com/motdotla/dotenv#readme\",\n  \"funding\": \"https://dotenvx.com\",\n  \"keywords\": [\n    \"dotenv\",\n    \"env\",\n    \".env\",\n    \"environment\",\n    \"variables\",\n    \"config\",\n    \"settings\"\n  ],\n  \"readmeFilename\": \"README.md\",\n  \"license\": \"BSD-2-Clause\",\n  \"devDependencies\": {\n    \"@types/node\": \"^18.11.3\",\n    \"decache\": \"^4.6.2\",\n    \"sinon\": \"^14.0.1\",\n    \"standard\": \"^17.0.0\",\n    \"standard-version\": \"^9.5.0\",\n    \"tap\": \"^19.2.0\",\n    \"typescript\": \"^4.8.4\"\n  },\n  \"engines\": {\n    \"node\": \">=12\"\n  },\n  \"browser\": {\n    \"fs\": false\n  }\n}\n", "const fs = require('fs')\nconst path = require('path')\nconst os = require('os')\nconst crypto = require('crypto')\nconst packageJson = require('../package.json')\n\nconst version = packageJson.version\n\nconst LINE = /(?:^|^)\\s*(?:export\\s+)?([\\w.-]+)(?:\\s*=\\s*?|:\\s+?)(\\s*'(?:\\\\'|[^'])*'|\\s*\"(?:\\\\\"|[^\"])*\"|\\s*`(?:\\\\`|[^`])*`|[^#\\r\\n]+)?\\s*(?:#.*)?(?:$|$)/mg\n\n// Parse src into an Object\nfunction parse (src) {\n  const obj = {}\n\n  // Convert buffer to string\n  let lines = src.toString()\n\n  // Convert line breaks to same format\n  lines = lines.replace(/\\r\\n?/mg, '\\n')\n\n  let match\n  while ((match = LINE.exec(lines)) != null) {\n    const key = match[1]\n\n    // Default undefined or null to empty string\n    let value = (match[2] || '')\n\n    // Remove whitespace\n    value = value.trim()\n\n    // Check if double quoted\n    const maybeQuote = value[0]\n\n    // Remove surrounding quotes\n    value = value.replace(/^(['\"`])([\\s\\S]*)\\1$/mg, '$2')\n\n    // Expand newlines if double quoted\n    if (maybeQuote === '\"') {\n      value = value.replace(/\\\\n/g, '\\n')\n      value = value.replace(/\\\\r/g, '\\r')\n    }\n\n    // Add to object\n    obj[key] = value\n  }\n\n  return obj\n}\n\nfunction _parseVault (options) {\n  const vaultPath = _vaultPath(options)\n\n  // Parse .env.vault\n  const result = DotenvModule.configDotenv({ path: vaultPath })\n  if (!result.parsed) {\n    const err = new Error(`MISSING_DATA: Cannot parse ${vaultPath} for an unknown reason`)\n    err.code = 'MISSING_DATA'\n    throw err\n  }\n\n  // handle scenario for comma separated keys - for use with key rotation\n  // example: DOTENV_KEY=\"dotenv://:<EMAIL>/vault/.env.vault?environment=prod,dotenv://:<EMAIL>/vault/.env.vault?environment=prod\"\n  const keys = _dotenvKey(options).split(',')\n  const length = keys.length\n\n  let decrypted\n  for (let i = 0; i < length; i++) {\n    try {\n      // Get full key\n      const key = keys[i].trim()\n\n      // Get instructions for decrypt\n      const attrs = _instructions(result, key)\n\n      // Decrypt\n      decrypted = DotenvModule.decrypt(attrs.ciphertext, attrs.key)\n\n      break\n    } catch (error) {\n      // last key\n      if (i + 1 >= length) {\n        throw error\n      }\n      // try next key\n    }\n  }\n\n  // Parse decrypted .env string\n  return DotenvModule.parse(decrypted)\n}\n\nfunction _warn (message) {\n  console.log(`[dotenv@${version}][WARN] ${message}`)\n}\n\nfunction _debug (message) {\n  console.log(`[dotenv@${version}][DEBUG] ${message}`)\n}\n\nfunction _dotenvKey (options) {\n  // prioritize developer directly setting options.DOTENV_KEY\n  if (options && options.DOTENV_KEY && options.DOTENV_KEY.length > 0) {\n    return options.DOTENV_KEY\n  }\n\n  // secondary infra already contains a DOTENV_KEY environment variable\n  if (process.env.DOTENV_KEY && process.env.DOTENV_KEY.length > 0) {\n    return process.env.DOTENV_KEY\n  }\n\n  // fallback to empty string\n  return ''\n}\n\nfunction _instructions (result, dotenvKey) {\n  // Parse DOTENV_KEY. Format is a URI\n  let uri\n  try {\n    uri = new URL(dotenvKey)\n  } catch (error) {\n    if (error.code === 'ERR_INVALID_URL') {\n      const err = new Error('INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development')\n      err.code = 'INVALID_DOTENV_KEY'\n      throw err\n    }\n\n    throw error\n  }\n\n  // Get decrypt key\n  const key = uri.password\n  if (!key) {\n    const err = new Error('INVALID_DOTENV_KEY: Missing key part')\n    err.code = 'INVALID_DOTENV_KEY'\n    throw err\n  }\n\n  // Get environment\n  const environment = uri.searchParams.get('environment')\n  if (!environment) {\n    const err = new Error('INVALID_DOTENV_KEY: Missing environment part')\n    err.code = 'INVALID_DOTENV_KEY'\n    throw err\n  }\n\n  // Get ciphertext payload\n  const environmentKey = `DOTENV_VAULT_${environment.toUpperCase()}`\n  const ciphertext = result.parsed[environmentKey] // DOTENV_VAULT_PRODUCTION\n  if (!ciphertext) {\n    const err = new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${environmentKey} in your .env.vault file.`)\n    err.code = 'NOT_FOUND_DOTENV_ENVIRONMENT'\n    throw err\n  }\n\n  return { ciphertext, key }\n}\n\nfunction _vaultPath (options) {\n  let possibleVaultPath = null\n\n  if (options && options.path && options.path.length > 0) {\n    if (Array.isArray(options.path)) {\n      for (const filepath of options.path) {\n        if (fs.existsSync(filepath)) {\n          possibleVaultPath = filepath.endsWith('.vault') ? filepath : `${filepath}.vault`\n        }\n      }\n    } else {\n      possibleVaultPath = options.path.endsWith('.vault') ? options.path : `${options.path}.vault`\n    }\n  } else {\n    possibleVaultPath = path.resolve(process.cwd(), '.env.vault')\n  }\n\n  if (fs.existsSync(possibleVaultPath)) {\n    return possibleVaultPath\n  }\n\n  return null\n}\n\nfunction _resolveHome (envPath) {\n  return envPath[0] === '~' ? path.join(os.homedir(), envPath.slice(1)) : envPath\n}\n\nfunction _configVault (options) {\n  const debug = Boolean(options && options.debug)\n  if (debug) {\n    _debug('Loading env from encrypted .env.vault')\n  }\n\n  const parsed = DotenvModule._parseVault(options)\n\n  let processEnv = process.env\n  if (options && options.processEnv != null) {\n    processEnv = options.processEnv\n  }\n\n  DotenvModule.populate(processEnv, parsed, options)\n\n  return { parsed }\n}\n\nfunction configDotenv (options) {\n  const dotenvPath = path.resolve(process.cwd(), '.env')\n  let encoding = 'utf8'\n  const debug = Boolean(options && options.debug)\n\n  if (options && options.encoding) {\n    encoding = options.encoding\n  } else {\n    if (debug) {\n      _debug('No encoding is specified. UTF-8 is used by default')\n    }\n  }\n\n  let optionPaths = [dotenvPath] // default, look for .env\n  if (options && options.path) {\n    if (!Array.isArray(options.path)) {\n      optionPaths = [_resolveHome(options.path)]\n    } else {\n      optionPaths = [] // reset default\n      for (const filepath of options.path) {\n        optionPaths.push(_resolveHome(filepath))\n      }\n    }\n  }\n\n  // Build the parsed data in a temporary object (because we need to return it).  Once we have the final\n  // parsed data, we will combine it with process.env (or options.processEnv if provided).\n  let lastError\n  const parsedAll = {}\n  for (const path of optionPaths) {\n    try {\n      // Specifying an encoding returns a string instead of a buffer\n      const parsed = DotenvModule.parse(fs.readFileSync(path, { encoding }))\n\n      DotenvModule.populate(parsedAll, parsed, options)\n    } catch (e) {\n      if (debug) {\n        _debug(`Failed to load ${path} ${e.message}`)\n      }\n      lastError = e\n    }\n  }\n\n  let processEnv = process.env\n  if (options && options.processEnv != null) {\n    processEnv = options.processEnv\n  }\n\n  DotenvModule.populate(processEnv, parsedAll, options)\n\n  if (lastError) {\n    return { parsed: parsedAll, error: lastError }\n  } else {\n    return { parsed: parsedAll }\n  }\n}\n\n// Populates process.env from .env file\nfunction config (options) {\n  // fallback to original dotenv if DOTENV_KEY is not set\n  if (_dotenvKey(options).length === 0) {\n    return DotenvModule.configDotenv(options)\n  }\n\n  const vaultPath = _vaultPath(options)\n\n  // dotenvKey exists but .env.vault file does not exist\n  if (!vaultPath) {\n    _warn(`You set DOTENV_KEY but you are missing a .env.vault file at ${vaultPath}. Did you forget to build it?`)\n\n    return DotenvModule.configDotenv(options)\n  }\n\n  return DotenvModule._configVault(options)\n}\n\nfunction decrypt (encrypted, keyStr) {\n  const key = Buffer.from(keyStr.slice(-64), 'hex')\n  let ciphertext = Buffer.from(encrypted, 'base64')\n\n  const nonce = ciphertext.subarray(0, 12)\n  const authTag = ciphertext.subarray(-16)\n  ciphertext = ciphertext.subarray(12, -16)\n\n  try {\n    const aesgcm = crypto.createDecipheriv('aes-256-gcm', key, nonce)\n    aesgcm.setAuthTag(authTag)\n    return `${aesgcm.update(ciphertext)}${aesgcm.final()}`\n  } catch (error) {\n    const isRange = error instanceof RangeError\n    const invalidKeyLength = error.message === 'Invalid key length'\n    const decryptionFailed = error.message === 'Unsupported state or unable to authenticate data'\n\n    if (isRange || invalidKeyLength) {\n      const err = new Error('INVALID_DOTENV_KEY: It must be 64 characters long (or more)')\n      err.code = 'INVALID_DOTENV_KEY'\n      throw err\n    } else if (decryptionFailed) {\n      const err = new Error('DECRYPTION_FAILED: Please check your DOTENV_KEY')\n      err.code = 'DECRYPTION_FAILED'\n      throw err\n    } else {\n      throw error\n    }\n  }\n}\n\n// Populate process.env with parsed values\nfunction populate (processEnv, parsed, options = {}) {\n  const debug = Boolean(options && options.debug)\n  const override = Boolean(options && options.override)\n\n  if (typeof parsed !== 'object') {\n    const err = new Error('OBJECT_REQUIRED: Please check the processEnv argument being passed to populate')\n    err.code = 'OBJECT_REQUIRED'\n    throw err\n  }\n\n  // Set process.env\n  for (const key of Object.keys(parsed)) {\n    if (Object.prototype.hasOwnProperty.call(processEnv, key)) {\n      if (override === true) {\n        processEnv[key] = parsed[key]\n      }\n\n      if (debug) {\n        if (override === true) {\n          _debug(`\"${key}\" is already defined and WAS overwritten`)\n        } else {\n          _debug(`\"${key}\" is already defined and was NOT overwritten`)\n        }\n      }\n    } else {\n      processEnv[key] = parsed[key]\n    }\n  }\n}\n\nconst DotenvModule = {\n  configDotenv,\n  _configVault,\n  _parseVault,\n  config,\n  decrypt,\n  parse,\n  populate\n}\n\nmodule.exports.configDotenv = DotenvModule.configDotenv\nmodule.exports._configVault = DotenvModule._configVault\nmodule.exports._parseVault = DotenvModule._parseVault\nmodule.exports.config = DotenvModule.config\nmodule.exports.decrypt = DotenvModule.decrypt\nmodule.exports.parse = DotenvModule.parse\nmodule.exports.populate = DotenvModule.populate\n\nmodule.exports = DotenvModule\n", "import { drizzle } from 'drizzle-orm/postgres-js';\nimport postgres from 'postgres';\nimport * as schema from './schema';\nimport { ensureEnvLoaded, getOptionalEnv } from '../utils/env';\n\n// 确保环境变量被加载\nensureEnvLoaded();\n\n// Create the connection\nconst connectionString = getOptionalEnv('DATABASE_URL');\n\nlet db: ReturnType<typeof drizzle>;\n\nif (connectionString) {\n  // Create postgres client with better configuration for Supabase\n  const client = postgres(connectionString, {\n    max: 1,\n    idle_timeout: 20,\n    connect_timeout: 10,\n    ssl: 'require',\n    prepare: false,\n  });\n\n  // Create drizzle instance\n  db = drizzle(client, { schema });\n} else {\n  // Mock database for build time - create a minimal mock\n  const mockClient = {} as any;\n  db = drizzle(mockClient, { schema });\n}\n\nexport { db };\n\n// Export schema for use in other files\nexport * from './schema';\n", "import pgClient, { type Options, type PostgresType, type Sql } from 'postgres';\nimport { entityKind } from '~/entity.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase } from '~/pg-core/db.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { type DrizzleConfig, isConfig } from '~/utils.ts';\nimport type { PostgresJsQueryResultHKT } from './session.ts';\nimport { PostgresJsSession } from './session.ts';\n\nexport class PostgresJsDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends PgDatabase<PostgresJsQueryResultHKT, TSchema> {\n\tstatic override readonly [entityKind]: string = 'PostgresJsDatabase';\n}\n\nfunction construct<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: Sql,\n\tconfig: DrizzleConfig<TSchema> = {},\n): PostgresJsDatabase<TSchema> & {\n\t$client: Sql;\n} {\n\tconst transparentParser = (val: any) => val;\n\n\t// Override postgres.js default date parsers: https://github.com/porsager/postgres/discussions/761\n\tfor (const type of ['1184', '1082', '1083', '1114']) {\n\t\tclient.options.parsers[type as any] = transparentParser;\n\t\tclient.options.serializers[type as any] = transparentParser;\n\t}\n\tclient.options.serializers['114'] = transparentParser;\n\tclient.options.serializers['3802'] = transparentParser;\n\n\tconst dialect = new PgDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new PostgresJsSession(client, dialect, schema, { logger });\n\tconst db = new PostgresJsDatabase(dialect, session, schema as any) as PostgresJsDatabase<TSchema>;\n\t(<any> db).$client = client;\n\n\treturn db as any;\n}\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends Sql = Sql,\n>(\n\t...params: [\n\t\tTClient | string,\n\t] | [\n\t\tTClient | string,\n\t\tDrizzleConfig<TSchema>,\n\t] | [\n\t\t(\n\t\t\t& DrizzleConfig<TSchema>\n\t\t\t& ({\n\t\t\t\tconnection: string | ({ url?: string } & Options<Record<string, PostgresType>>);\n\t\t\t} | {\n\t\t\t\tclient: TClient;\n\t\t\t})\n\t\t),\n\t]\n): PostgresJsDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\tif (typeof params[0] === 'string') {\n\t\tconst instance = pgClient(params[0] as string);\n\n\t\treturn construct(instance, params[1]) as any;\n\t}\n\n\tif (isConfig(params[0])) {\n\t\tconst { connection, client, ...drizzleConfig } = params[0] as {\n\t\t\tconnection?: { url?: string } & Options<Record<string, PostgresType>>;\n\t\t\tclient?: TClient;\n\t\t} & DrizzleConfig<TSchema>;\n\n\t\tif (client) return construct(client, drizzleConfig) as any;\n\n\t\tif (typeof connection === 'object' && connection.url !== undefined) {\n\t\t\tconst { url, ...config } = connection;\n\n\t\t\tconst instance = pgClient(url, config);\n\t\t\treturn construct(instance, drizzleConfig) as any;\n\t\t}\n\n\t\tconst instance = pgClient(connection);\n\t\treturn construct(instance, drizzleConfig) as any;\n\t}\n\n\treturn construct(params[0] as TClient, params[1] as DrizzleConfig<TSchema> | undefined) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig?: DrizzleConfig<TSchema>,\n\t): PostgresJsDatabase<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({} as any, config) as any;\n\t}\n}\n", "import os from 'os'\nimport fs from 'fs'\n\nimport {\n  mergeUserTypes,\n  inferType,\n  Parameter,\n  Identifier,\n  Builder,\n  toPascal,\n  pascal,\n  toCamel,\n  camel,\n  toKebab,\n  kebab,\n  fromPascal,\n  fromCamel,\n  fromKebab\n} from './types.js'\n\nimport Connection from './connection.js'\nimport { Query, CLOSE } from './query.js'\nimport Queue from './queue.js'\nimport { Errors, PostgresError } from './errors.js'\nimport Subscribe from './subscribe.js'\nimport largeObject from './large.js'\n\nObject.assign(Postgres, {\n  PostgresError,\n  toPascal,\n  pascal,\n  toCamel,\n  camel,\n  toKebab,\n  kebab,\n  fromPascal,\n  fromCamel,\n  fromKebab,\n  BigInt: {\n    to: 20,\n    from: [20],\n    parse: x => BigInt(x), // eslint-disable-line\n    serialize: x => x.toString()\n  }\n})\n\nexport default Postgres\n\nfunction Postgres(a, b) {\n  const options = parseOptions(a, b)\n      , subscribe = options.no_subscribe || Subscribe(Postgres, { ...options })\n\n  let ending = false\n\n  const queries = Queue()\n      , connecting = Queue()\n      , reserved = Queue()\n      , closed = Queue()\n      , ended = Queue()\n      , open = Queue()\n      , busy = Queue()\n      , full = Queue()\n      , queues = { connecting, reserved, closed, ended, open, busy, full }\n\n  const connections = [...Array(options.max)].map(() => Connection(options, queues, { onopen, onend, onclose }))\n\n  const sql = Sql(handler)\n\n  Object.assign(sql, {\n    get parameters() { return options.parameters },\n    largeObject: largeObject.bind(null, sql),\n    subscribe,\n    CLOSE,\n    END: CLOSE,\n    PostgresError,\n    options,\n    reserve,\n    listen,\n    begin,\n    close,\n    end\n  })\n\n  return sql\n\n  function Sql(handler) {\n    handler.debug = options.debug\n\n    Object.entries(options.types).reduce((acc, [name, type]) => {\n      acc[name] = (x) => new Parameter(x, type.to)\n      return acc\n    }, typed)\n\n    Object.assign(sql, {\n      types: typed,\n      typed,\n      unsafe,\n      notify,\n      array,\n      json,\n      file\n    })\n\n    return sql\n\n    function typed(value, type) {\n      return new Parameter(value, type)\n    }\n\n    function sql(strings, ...args) {\n      const query = strings && Array.isArray(strings.raw)\n        ? new Query(strings, args, handler, cancel)\n        : typeof strings === 'string' && !args.length\n          ? new Identifier(options.transform.column.to ? options.transform.column.to(strings) : strings)\n          : new Builder(strings, args)\n      return query\n    }\n\n    function unsafe(string, args = [], options = {}) {\n      arguments.length === 2 && !Array.isArray(args) && (options = args, args = [])\n      const query = new Query([string], args, handler, cancel, {\n        prepare: false,\n        ...options,\n        simple: 'simple' in options ? options.simple : args.length === 0\n      })\n      return query\n    }\n\n    function file(path, args = [], options = {}) {\n      arguments.length === 2 && !Array.isArray(args) && (options = args, args = [])\n      const query = new Query([], args, (query) => {\n        fs.readFile(path, 'utf8', (err, string) => {\n          if (err)\n            return query.reject(err)\n\n          query.strings = [string]\n          handler(query)\n        })\n      }, cancel, {\n        ...options,\n        simple: 'simple' in options ? options.simple : args.length === 0\n      })\n      return query\n    }\n  }\n\n  async function listen(name, fn, onlisten) {\n    const listener = { fn, onlisten }\n\n    const sql = listen.sql || (listen.sql = Postgres({\n      ...options,\n      max: 1,\n      idle_timeout: null,\n      max_lifetime: null,\n      fetch_types: false,\n      onclose() {\n        Object.entries(listen.channels).forEach(([name, { listeners }]) => {\n          delete listen.channels[name]\n          Promise.all(listeners.map(l => listen(name, l.fn, l.onlisten).catch(() => { /* noop */ })))\n        })\n      },\n      onnotify(c, x) {\n        c in listen.channels && listen.channels[c].listeners.forEach(l => l.fn(x))\n      }\n    }))\n\n    const channels = listen.channels || (listen.channels = {})\n        , exists = name in channels\n\n    if (exists) {\n      channels[name].listeners.push(listener)\n      const result = await channels[name].result\n      listener.onlisten && listener.onlisten()\n      return { state: result.state, unlisten }\n    }\n\n    channels[name] = { result: sql`listen ${\n      sql.unsafe('\"' + name.replace(/\"/g, '\"\"') + '\"')\n    }`, listeners: [listener] }\n    const result = await channels[name].result\n    listener.onlisten && listener.onlisten()\n    return { state: result.state, unlisten }\n\n    async function unlisten() {\n      if (name in channels === false)\n        return\n\n      channels[name].listeners = channels[name].listeners.filter(x => x !== listener)\n      if (channels[name].listeners.length)\n        return\n\n      delete channels[name]\n      return sql`unlisten ${\n        sql.unsafe('\"' + name.replace(/\"/g, '\"\"') + '\"')\n      }`\n    }\n  }\n\n  async function notify(channel, payload) {\n    return await sql`select pg_notify(${ channel }, ${ '' + payload })`\n  }\n\n  async function reserve() {\n    const queue = Queue()\n    const c = open.length\n      ? open.shift()\n      : await new Promise((resolve, reject) => {\n        const query = { reserve: resolve, reject }\n        queries.push(query)\n        closed.length && connect(closed.shift(), query)\n      })\n\n    move(c, reserved)\n    c.reserved = () => queue.length\n      ? c.execute(queue.shift())\n      : move(c, reserved)\n    c.reserved.release = true\n\n    const sql = Sql(handler)\n    sql.release = () => {\n      c.reserved = null\n      onopen(c)\n    }\n\n    return sql\n\n    function handler(q) {\n      c.queue === full\n        ? queue.push(q)\n        : c.execute(q) || move(c, full)\n    }\n  }\n\n  async function begin(options, fn) {\n    !fn && (fn = options, options = '')\n    const queries = Queue()\n    let savepoints = 0\n      , connection\n      , prepare = null\n\n    try {\n      await sql.unsafe('begin ' + options.replace(/[^a-z ]/ig, ''), [], { onexecute }).execute()\n      return await Promise.race([\n        scope(connection, fn),\n        new Promise((_, reject) => connection.onclose = reject)\n      ])\n    } catch (error) {\n      throw error\n    }\n\n    async function scope(c, fn, name) {\n      const sql = Sql(handler)\n      sql.savepoint = savepoint\n      sql.prepare = x => prepare = x.replace(/[^a-z0-9$-_. ]/gi)\n      let uncaughtError\n        , result\n\n      name && await sql`savepoint ${ sql(name) }`\n      try {\n        result = await new Promise((resolve, reject) => {\n          const x = fn(sql)\n          Promise.resolve(Array.isArray(x) ? Promise.all(x) : x).then(resolve, reject)\n        })\n\n        if (uncaughtError)\n          throw uncaughtError\n      } catch (e) {\n        await (name\n          ? sql`rollback to ${ sql(name) }`\n          : sql`rollback`\n        )\n        throw e instanceof PostgresError && e.code === '25P02' && uncaughtError || e\n      }\n\n      if (!name) {\n        prepare\n          ? await sql`prepare transaction '${ sql.unsafe(prepare) }'`\n          : await sql`commit`\n      }\n\n      return result\n\n      function savepoint(name, fn) {\n        if (name && Array.isArray(name.raw))\n          return savepoint(sql => sql.apply(sql, arguments))\n\n        arguments.length === 1 && (fn = name, name = null)\n        return scope(c, fn, 's' + savepoints++ + (name ? '_' + name : ''))\n      }\n\n      function handler(q) {\n        q.catch(e => uncaughtError || (uncaughtError = e))\n        c.queue === full\n          ? queries.push(q)\n          : c.execute(q) || move(c, full)\n      }\n    }\n\n    function onexecute(c) {\n      connection = c\n      move(c, reserved)\n      c.reserved = () => queries.length\n        ? c.execute(queries.shift())\n        : move(c, reserved)\n    }\n  }\n\n  function move(c, queue) {\n    c.queue.remove(c)\n    queue.push(c)\n    c.queue = queue\n    queue === open\n      ? c.idleTimer.start()\n      : c.idleTimer.cancel()\n    return c\n  }\n\n  function json(x) {\n    return new Parameter(x, 3802)\n  }\n\n  function array(x, type) {\n    if (!Array.isArray(x))\n      return array(Array.from(arguments))\n\n    return new Parameter(x, type || (x.length ? inferType(x) || 25 : 0), options.shared.typeArrayMap)\n  }\n\n  function handler(query) {\n    if (ending)\n      return query.reject(Errors.connection('CONNECTION_ENDED', options, options))\n\n    if (open.length)\n      return go(open.shift(), query)\n\n    if (closed.length)\n      return connect(closed.shift(), query)\n\n    busy.length\n      ? go(busy.shift(), query)\n      : queries.push(query)\n  }\n\n  function go(c, query) {\n    return c.execute(query)\n      ? move(c, busy)\n      : move(c, full)\n  }\n\n  function cancel(query) {\n    return new Promise((resolve, reject) => {\n      query.state\n        ? query.active\n          ? Connection(options).cancel(query.state, resolve, reject)\n          : query.cancelled = { resolve, reject }\n        : (\n          queries.remove(query),\n          query.cancelled = true,\n          query.reject(Errors.generic('57014', 'canceling statement due to user request')),\n          resolve()\n        )\n    })\n  }\n\n  async function end({ timeout = null } = {}) {\n    if (ending)\n      return ending\n\n    await 1\n    let timer\n    return ending = Promise.race([\n      new Promise(r => timeout !== null && (timer = setTimeout(destroy, timeout * 1000, r))),\n      Promise.all(connections.map(c => c.end()).concat(\n        listen.sql ? listen.sql.end({ timeout: 0 }) : [],\n        subscribe.sql ? subscribe.sql.end({ timeout: 0 }) : []\n      ))\n    ]).then(() => clearTimeout(timer))\n  }\n\n  async function close() {\n    await Promise.all(connections.map(c => c.end()))\n  }\n\n  async function destroy(resolve) {\n    await Promise.all(connections.map(c => c.terminate()))\n    while (queries.length)\n      queries.shift().reject(Errors.connection('CONNECTION_DESTROYED', options))\n    resolve()\n  }\n\n  function connect(c, query) {\n    move(c, connecting)\n    c.connect(query)\n    return c\n  }\n\n  function onend(c) {\n    move(c, ended)\n  }\n\n  function onopen(c) {\n    if (queries.length === 0)\n      return move(c, open)\n\n    let max = Math.ceil(queries.length / (connecting.length + 1))\n      , ready = true\n\n    while (ready && queries.length && max-- > 0) {\n      const query = queries.shift()\n      if (query.reserve)\n        return query.reserve(c)\n\n      ready = c.execute(query)\n    }\n\n    ready\n      ? move(c, busy)\n      : move(c, full)\n  }\n\n  function onclose(c, e) {\n    move(c, closed)\n    c.reserved = null\n    c.onclose && (c.onclose(e), c.onclose = null)\n    options.onclose && options.onclose(c.id)\n    queries.length && connect(c, queries.shift())\n  }\n}\n\nfunction parseOptions(a, b) {\n  if (a && a.shared)\n    return a\n\n  const env = process.env // eslint-disable-line\n      , o = (!a || typeof a === 'string' ? b : a) || {}\n      , { url, multihost } = parseUrl(a)\n      , query = [...url.searchParams].reduce((a, [b, c]) => (a[b] = c, a), {})\n      , host = o.hostname || o.host || multihost || url.hostname || env.PGHOST || 'localhost'\n      , port = o.port || url.port || env.PGPORT || 5432\n      , user = o.user || o.username || url.username || env.PGUSERNAME || env.PGUSER || osUsername()\n\n  o.no_prepare && (o.prepare = false)\n  query.sslmode && (query.ssl = query.sslmode, delete query.sslmode)\n  'timeout' in o && (console.log('The timeout option is deprecated, use idle_timeout instead'), o.idle_timeout = o.timeout) // eslint-disable-line\n  query.sslrootcert === 'system' && (query.ssl = 'verify-full')\n\n  const ints = ['idle_timeout', 'connect_timeout', 'max_lifetime', 'max_pipeline', 'backoff', 'keep_alive']\n  const defaults = {\n    max             : 10,\n    ssl             : false,\n    idle_timeout    : null,\n    connect_timeout : 30,\n    max_lifetime    : max_lifetime,\n    max_pipeline    : 100,\n    backoff         : backoff,\n    keep_alive      : 60,\n    prepare         : true,\n    debug           : false,\n    fetch_types     : true,\n    publications    : 'alltables',\n    target_session_attrs: null\n  }\n\n  return {\n    host            : Array.isArray(host) ? host : host.split(',').map(x => x.split(':')[0]),\n    port            : Array.isArray(port) ? port : host.split(',').map(x => parseInt(x.split(':')[1] || port)),\n    path            : o.path || host.indexOf('/') > -1 && host + '/.s.PGSQL.' + port,\n    database        : o.database || o.db || (url.pathname || '').slice(1) || env.PGDATABASE || user,\n    user            : user,\n    pass            : o.pass || o.password || url.password || env.PGPASSWORD || '',\n    ...Object.entries(defaults).reduce(\n      (acc, [k, d]) => {\n        const value = k in o ? o[k] : k in query\n          ? (query[k] === 'disable' || query[k] === 'false' ? false : query[k])\n          : env['PG' + k.toUpperCase()] || d\n        acc[k] = typeof value === 'string' && ints.includes(k)\n          ? +value\n          : value\n        return acc\n      },\n      {}\n    ),\n    connection      : {\n      application_name: env.PGAPPNAME || 'postgres.js',\n      ...o.connection,\n      ...Object.entries(query).reduce((acc, [k, v]) => (k in defaults || (acc[k] = v), acc), {})\n    },\n    types           : o.types || {},\n    target_session_attrs: tsa(o, url, env),\n    onnotice        : o.onnotice,\n    onnotify        : o.onnotify,\n    onclose         : o.onclose,\n    onparameter     : o.onparameter,\n    socket          : o.socket,\n    transform       : parseTransform(o.transform || { undefined: undefined }),\n    parameters      : {},\n    shared          : { retries: 0, typeArrayMap: {} },\n    ...mergeUserTypes(o.types)\n  }\n}\n\nfunction tsa(o, url, env) {\n  const x = o.target_session_attrs || url.searchParams.get('target_session_attrs') || env.PGTARGETSESSIONATTRS\n  if (!x || ['read-write', 'read-only', 'primary', 'standby', 'prefer-standby'].includes(x))\n    return x\n\n  throw new Error('target_session_attrs ' + x + ' is not supported')\n}\n\nfunction backoff(retries) {\n  return (0.5 + Math.random() / 2) * Math.min(3 ** retries / 100, 20)\n}\n\nfunction max_lifetime() {\n  return 60 * (30 + Math.random() * 30)\n}\n\nfunction parseTransform(x) {\n  return {\n    undefined: x.undefined,\n    column: {\n      from: typeof x.column === 'function' ? x.column : x.column && x.column.from,\n      to: x.column && x.column.to\n    },\n    value: {\n      from: typeof x.value === 'function' ? x.value : x.value && x.value.from,\n      to: x.value && x.value.to\n    },\n    row: {\n      from: typeof x.row === 'function' ? x.row : x.row && x.row.from,\n      to: x.row && x.row.to\n    }\n  }\n}\n\nfunction parseUrl(url) {\n  if (!url || typeof url !== 'string')\n    return { url: { searchParams: new Map() } }\n\n  let host = url\n  host = host.slice(host.indexOf('://') + 3).split(/[?/]/)[0]\n  host = decodeURIComponent(host.slice(host.indexOf('@') + 1))\n\n  const urlObj = new URL(url.replace(host, host.split(',')[0]))\n\n  return {\n    url: {\n      username: decodeURIComponent(urlObj.username),\n      password: decodeURIComponent(urlObj.password),\n      host: urlObj.host,\n      hostname: urlObj.hostname,\n      port: urlObj.port,\n      pathname: urlObj.pathname,\n      searchParams: urlObj.searchParams\n    },\n    multihost: host.indexOf(',') > -1 && host\n  }\n}\n\nfunction osUsername() {\n  try {\n    return os.userInfo().username // eslint-disable-line\n  } catch (_) {\n    return process.env.USERNAME || process.env.USER || process.env.LOGNAME  // eslint-disable-line\n  }\n}\n", "import { Query } from './query.js'\nimport { Errors } from './errors.js'\n\nexport const types = {\n  string: {\n    to: 25,\n    from: null,             // defaults to string\n    serialize: x => '' + x\n  },\n  number: {\n    to: 0,\n    from: [21, 23, 26, 700, 701],\n    serialize: x => '' + x,\n    parse: x => +x\n  },\n  json: {\n    to: 114,\n    from: [114, 3802],\n    serialize: x => JSON.stringify(x),\n    parse: x => JSON.parse(x)\n  },\n  boolean: {\n    to: 16,\n    from: 16,\n    serialize: x => x === true ? 't' : 'f',\n    parse: x => x === 't'\n  },\n  date: {\n    to: 1184,\n    from: [1082, 1114, 1184],\n    serialize: x => (x instanceof Date ? x : new Date(x)).toISOString(),\n    parse: x => new Date(x)\n  },\n  bytea: {\n    to: 17,\n    from: 17,\n    serialize: x => '\\\\x' + Buffer.from(x).toString('hex'),\n    parse: x => Buffer.from(x.slice(2), 'hex')\n  }\n}\n\nclass NotTagged { then() { notTagged() } catch() { notTagged() } finally() { notTagged() }}\n\nexport class Identifier extends NotTagged {\n  constructor(value) {\n    super()\n    this.value = escapeIdentifier(value)\n  }\n}\n\nexport class Parameter extends NotTagged {\n  constructor(value, type, array) {\n    super()\n    this.value = value\n    this.type = type\n    this.array = array\n  }\n}\n\nexport class Builder extends NotTagged {\n  constructor(first, rest) {\n    super()\n    this.first = first\n    this.rest = rest\n  }\n\n  build(before, parameters, types, options) {\n    const keyword = builders.map(([x, fn]) => ({ fn, i: before.search(x) })).sort((a, b) => a.i - b.i).pop()\n    return keyword.i === -1\n      ? escapeIdentifiers(this.first, options)\n      : keyword.fn(this.first, this.rest, parameters, types, options)\n  }\n}\n\nexport function handleValue(x, parameters, types, options) {\n  let value = x instanceof Parameter ? x.value : x\n  if (value === undefined) {\n    x instanceof Parameter\n      ? x.value = options.transform.undefined\n      : value = x = options.transform.undefined\n\n    if (value === undefined)\n      throw Errors.generic('UNDEFINED_VALUE', 'Undefined values are not allowed')\n  }\n\n  return '$' + (types.push(\n    x instanceof Parameter\n      ? (parameters.push(x.value), x.array\n        ? x.array[x.type || inferType(x.value)] || x.type || firstIsString(x.value)\n        : x.type\n      )\n      : (parameters.push(x), inferType(x))\n  ))\n}\n\nconst defaultHandlers = typeHandlers(types)\n\nexport function stringify(q, string, value, parameters, types, options) { // eslint-disable-line\n  for (let i = 1; i < q.strings.length; i++) {\n    string += (stringifyValue(string, value, parameters, types, options)) + q.strings[i]\n    value = q.args[i]\n  }\n\n  return string\n}\n\nfunction stringifyValue(string, value, parameters, types, o) {\n  return (\n    value instanceof Builder ? value.build(string, parameters, types, o) :\n    value instanceof Query ? fragment(value, parameters, types, o) :\n    value instanceof Identifier ? value.value :\n    value && value[0] instanceof Query ? value.reduce((acc, x) => acc + ' ' + fragment(x, parameters, types, o), '') :\n    handleValue(value, parameters, types, o)\n  )\n}\n\nfunction fragment(q, parameters, types, options) {\n  q.fragment = true\n  return stringify(q, q.strings[0], q.args[0], parameters, types, options)\n}\n\nfunction valuesBuilder(first, parameters, types, columns, options) {\n  return first.map(row =>\n    '(' + columns.map(column =>\n      stringifyValue('values', row[column], parameters, types, options)\n    ).join(',') + ')'\n  ).join(',')\n}\n\nfunction values(first, rest, parameters, types, options) {\n  const multi = Array.isArray(first[0])\n  const columns = rest.length ? rest.flat() : Object.keys(multi ? first[0] : first)\n  return valuesBuilder(multi ? first : [first], parameters, types, columns, options)\n}\n\nfunction select(first, rest, parameters, types, options) {\n  typeof first === 'string' && (first = [first].concat(rest))\n  if (Array.isArray(first))\n    return escapeIdentifiers(first, options)\n\n  let value\n  const columns = rest.length ? rest.flat() : Object.keys(first)\n  return columns.map(x => {\n    value = first[x]\n    return (\n      value instanceof Query ? fragment(value, parameters, types, options) :\n      value instanceof Identifier ? value.value :\n      handleValue(value, parameters, types, options)\n    ) + ' as ' + escapeIdentifier(options.transform.column.to ? options.transform.column.to(x) : x)\n  }).join(',')\n}\n\nconst builders = Object.entries({\n  values,\n  in: (...xs) => {\n    const x = values(...xs)\n    return x === '()' ? '(null)' : x\n  },\n  select,\n  as: select,\n  returning: select,\n  '\\\\(': select,\n\n  update(first, rest, parameters, types, options) {\n    return (rest.length ? rest.flat() : Object.keys(first)).map(x =>\n      escapeIdentifier(options.transform.column.to ? options.transform.column.to(x) : x) +\n      '=' + stringifyValue('values', first[x], parameters, types, options)\n    )\n  },\n\n  insert(first, rest, parameters, types, options) {\n    const columns = rest.length ? rest.flat() : Object.keys(Array.isArray(first) ? first[0] : first)\n    return '(' + escapeIdentifiers(columns, options) + ')values' +\n    valuesBuilder(Array.isArray(first) ? first : [first], parameters, types, columns, options)\n  }\n}).map(([x, fn]) => ([new RegExp('((?:^|[\\\\s(])' + x + '(?:$|[\\\\s(]))(?![\\\\s\\\\S]*\\\\1)', 'i'), fn]))\n\nfunction notTagged() {\n  throw Errors.generic('NOT_TAGGED_CALL', 'Query not called as a tagged template literal')\n}\n\nexport const serializers = defaultHandlers.serializers\nexport const parsers = defaultHandlers.parsers\n\nexport const END = {}\n\nfunction firstIsString(x) {\n  if (Array.isArray(x))\n    return firstIsString(x[0])\n  return typeof x === 'string' ? 1009 : 0\n}\n\nexport const mergeUserTypes = function(types) {\n  const user = typeHandlers(types || {})\n  return {\n    serializers: Object.assign({}, serializers, user.serializers),\n    parsers: Object.assign({}, parsers, user.parsers)\n  }\n}\n\nfunction typeHandlers(types) {\n  return Object.keys(types).reduce((acc, k) => {\n    types[k].from && [].concat(types[k].from).forEach(x => acc.parsers[x] = types[k].parse)\n    if (types[k].serialize) {\n      acc.serializers[types[k].to] = types[k].serialize\n      types[k].from && [].concat(types[k].from).forEach(x => acc.serializers[x] = types[k].serialize)\n    }\n    return acc\n  }, { parsers: {}, serializers: {} })\n}\n\nfunction escapeIdentifiers(xs, { transform: { column } }) {\n  return xs.map(x => escapeIdentifier(column.to ? column.to(x) : x)).join(',')\n}\n\nexport const escapeIdentifier = function escape(str) {\n  return '\"' + str.replace(/\"/g, '\"\"').replace(/\\./g, '\".\"') + '\"'\n}\n\nexport const inferType = function inferType(x) {\n  return (\n    x instanceof Parameter ? x.type :\n    x instanceof Date ? 1184 :\n    x instanceof Uint8Array ? 17 :\n    (x === true || x === false) ? 16 :\n    typeof x === 'bigint' ? 20 :\n    Array.isArray(x) ? inferType(x[0]) :\n    0\n  )\n}\n\nconst escapeBackslash = /\\\\/g\nconst escapeQuote = /\"/g\n\nfunction arrayEscape(x) {\n  return x\n    .replace(escapeBackslash, '\\\\\\\\')\n    .replace(escapeQuote, '\\\\\"')\n}\n\nexport const arraySerializer = function arraySerializer(xs, serializer, options, typarray) {\n  if (Array.isArray(xs) === false)\n    return xs\n\n  if (!xs.length)\n    return '{}'\n\n  const first = xs[0]\n  // Only _box (1020) has the ';' delimiter for arrays, all other types use the ',' delimiter\n  const delimiter = typarray === 1020 ? ';' : ','\n\n  if (Array.isArray(first) && !first.type)\n    return '{' + xs.map(x => arraySerializer(x, serializer, options, typarray)).join(delimiter) + '}'\n\n  return '{' + xs.map(x => {\n    if (x === undefined) {\n      x = options.transform.undefined\n      if (x === undefined)\n        throw Errors.generic('UNDEFINED_VALUE', 'Undefined values are not allowed')\n    }\n\n    return x === null\n      ? 'null'\n      : '\"' + arrayEscape(serializer ? serializer(x.type ? x.value : x) : '' + x) + '\"'\n  }).join(delimiter) + '}'\n}\n\nconst arrayParserState = {\n  i: 0,\n  char: null,\n  str: '',\n  quoted: false,\n  last: 0\n}\n\nexport const arrayParser = function arrayParser(x, parser, typarray) {\n  arrayParserState.i = arrayParserState.last = 0\n  return arrayParserLoop(arrayParserState, x, parser, typarray)\n}\n\nfunction arrayParserLoop(s, x, parser, typarray) {\n  const xs = []\n  // Only _box (1020) has the ';' delimiter for arrays, all other types use the ',' delimiter\n  const delimiter = typarray === 1020 ? ';' : ','\n  for (; s.i < x.length; s.i++) {\n    s.char = x[s.i]\n    if (s.quoted) {\n      if (s.char === '\\\\') {\n        s.str += x[++s.i]\n      } else if (s.char === '\"') {\n        xs.push(parser ? parser(s.str) : s.str)\n        s.str = ''\n        s.quoted = x[s.i + 1] === '\"'\n        s.last = s.i + 2\n      } else {\n        s.str += s.char\n      }\n    } else if (s.char === '\"') {\n      s.quoted = true\n    } else if (s.char === '{') {\n      s.last = ++s.i\n      xs.push(arrayParserLoop(s, x, parser, typarray))\n    } else if (s.char === '}') {\n      s.quoted = false\n      s.last < s.i && xs.push(parser ? parser(x.slice(s.last, s.i)) : x.slice(s.last, s.i))\n      s.last = s.i + 1\n      break\n    } else if (s.char === delimiter && s.p !== '}' && s.p !== '\"') {\n      xs.push(parser ? parser(x.slice(s.last, s.i)) : x.slice(s.last, s.i))\n      s.last = s.i + 1\n    }\n    s.p = s.char\n  }\n  s.last < s.i && xs.push(parser ? parser(x.slice(s.last, s.i + 1)) : x.slice(s.last, s.i + 1))\n  return xs\n}\n\nexport const toCamel = x => {\n  let str = x[0]\n  for (let i = 1; i < x.length; i++)\n    str += x[i] === '_' ? x[++i].toUpperCase() : x[i]\n  return str\n}\n\nexport const toPascal = x => {\n  let str = x[0].toUpperCase()\n  for (let i = 1; i < x.length; i++)\n    str += x[i] === '_' ? x[++i].toUpperCase() : x[i]\n  return str\n}\n\nexport const toKebab = x => x.replace(/_/g, '-')\n\nexport const fromCamel = x => x.replace(/([A-Z])/g, '_$1').toLowerCase()\nexport const fromPascal = x => (x.slice(0, 1) + x.slice(1).replace(/([A-Z])/g, '_$1')).toLowerCase()\nexport const fromKebab = x => x.replace(/-/g, '_')\n\nfunction createJsonTransform(fn) {\n  return function jsonTransform(x, column) {\n    return typeof x === 'object' && x !== null && (column.type === 114 || column.type === 3802)\n      ? Array.isArray(x)\n        ? x.map(x => jsonTransform(x, column))\n        : Object.entries(x).reduce((acc, [k, v]) => Object.assign(acc, { [fn(k)]: jsonTransform(v, column) }), {})\n      : x\n  }\n}\n\ntoCamel.column = { from: toCamel }\ntoCamel.value = { from: createJsonTransform(toCamel) }\nfromCamel.column = { to: fromCamel }\n\nexport const camel = { ...toCamel }\ncamel.column.to = fromCamel\n\ntoPascal.column = { from: toPascal }\ntoPascal.value = { from: createJsonTransform(toPascal) }\nfromPascal.column = { to: fromPascal }\n\nexport const pascal = { ...toPascal }\npascal.column.to = fromPascal\n\ntoKebab.column = { from: toKebab }\ntoKebab.value = { from: createJsonTransform(toKebab) }\nfromKebab.column = { to: fromKebab }\n\nexport const kebab = { ...toKebab }\nkebab.column.to = fromKebab\n", "const originCache = new Map()\n    , originStackCache = new Map()\n    , originError = Symbol('OriginError')\n\nexport const CLOSE = {}\nexport class Query extends Promise {\n  constructor(strings, args, handler, canceller, options = {}) {\n    let resolve\n      , reject\n\n    super((a, b) => {\n      resolve = a\n      reject = b\n    })\n\n    this.tagged = Array.isArray(strings.raw)\n    this.strings = strings\n    this.args = args\n    this.handler = handler\n    this.canceller = canceller\n    this.options = options\n\n    this.state = null\n    this.statement = null\n\n    this.resolve = x => (this.active = false, resolve(x))\n    this.reject = x => (this.active = false, reject(x))\n\n    this.active = false\n    this.cancelled = null\n    this.executed = false\n    this.signature = ''\n\n    this[originError] = this.handler.debug\n      ? new Error()\n      : this.tagged && cachedError(this.strings)\n  }\n\n  get origin() {\n    return (this.handler.debug\n      ? this[originError].stack\n      : this.tagged && originStackCache.has(this.strings)\n        ? originStackCache.get(this.strings)\n        : originStackCache.set(this.strings, this[originError].stack).get(this.strings)\n    ) || ''\n  }\n\n  static get [Symbol.species]() {\n    return Promise\n  }\n\n  cancel() {\n    return this.canceller && (this.canceller(this), this.canceller = null)\n  }\n\n  simple() {\n    this.options.simple = true\n    this.options.prepare = false\n    return this\n  }\n\n  async readable() {\n    this.simple()\n    this.streaming = true\n    return this\n  }\n\n  async writable() {\n    this.simple()\n    this.streaming = true\n    return this\n  }\n\n  cursor(rows = 1, fn) {\n    this.options.simple = false\n    if (typeof rows === 'function') {\n      fn = rows\n      rows = 1\n    }\n\n    this.cursorRows = rows\n\n    if (typeof fn === 'function')\n      return (this.cursorFn = fn, this)\n\n    let prev\n    return {\n      [Symbol.asyncIterator]: () => ({\n        next: () => {\n          if (this.executed && !this.active)\n            return { done: true }\n\n          prev && prev()\n          const promise = new Promise((resolve, reject) => {\n            this.cursorFn = value => {\n              resolve({ value, done: false })\n              return new Promise(r => prev = r)\n            }\n            this.resolve = () => (this.active = false, resolve({ done: true }))\n            this.reject = x => (this.active = false, reject(x))\n          })\n          this.execute()\n          return promise\n        },\n        return() {\n          prev && prev(CLOSE)\n          return { done: true }\n        }\n      })\n    }\n  }\n\n  describe() {\n    this.options.simple = false\n    this.onlyDescribe = this.options.prepare = true\n    return this\n  }\n\n  stream() {\n    throw new Error('.stream has been renamed to .forEach')\n  }\n\n  forEach(fn) {\n    this.forEachFn = fn\n    this.handle()\n    return this\n  }\n\n  raw() {\n    this.isRaw = true\n    return this\n  }\n\n  values() {\n    this.isRaw = 'values'\n    return this\n  }\n\n  async handle() {\n    !this.executed && (this.executed = true) && await 1 && this.handler(this)\n  }\n\n  execute() {\n    this.handle()\n    return this\n  }\n\n  then() {\n    this.handle()\n    return super.then.apply(this, arguments)\n  }\n\n  catch() {\n    this.handle()\n    return super.catch.apply(this, arguments)\n  }\n\n  finally() {\n    this.handle()\n    return super.finally.apply(this, arguments)\n  }\n}\n\nfunction cachedError(xs) {\n  if (originCache.has(xs))\n    return originCache.get(xs)\n\n  const x = Error.stackTraceLimit\n  Error.stackTraceLimit = 4\n  originCache.set(xs, new Error())\n  Error.stackTraceLimit = x\n  return originCache.get(xs)\n}\n", "export class PostgresError extends Error {\n  constructor(x) {\n    super(x.message)\n    this.name = this.constructor.name\n    Object.assign(this, x)\n  }\n}\n\nexport const Errors = {\n  connection,\n  postgres,\n  generic,\n  notSupported\n}\n\nfunction connection(x, options, socket) {\n  const { host, port } = socket || options\n  const error = Object.assign(\n    new Error(('write ' + x + ' ' + (options.path || (host + ':' + port)))),\n    {\n      code: x,\n      errno: x,\n      address: options.path || host\n    }, options.path ? {} : { port: port }\n  )\n  Error.captureStackTrace(error, connection)\n  return error\n}\n\nfunction postgres(x) {\n  const error = new PostgresError(x)\n  Error.captureStackTrace(error, postgres)\n  return error\n}\n\nfunction generic(code, message) {\n  const error = Object.assign(new Error(code + ': ' + message), { code })\n  Error.captureStackTrace(error, generic)\n  return error\n}\n\n/* c8 ignore next 10 */\nfunction notSupported(x) {\n  const error = Object.assign(\n    new Error(x + ' (B) is not supported'),\n    {\n      code: 'MESSAGE_NOT_SUPPORTED',\n      name: x\n    }\n  )\n  Error.captureStackTrace(error, notSupported)\n  return error\n}\n", "import net from 'net'\nimport tls from 'tls'\nimport crypto from 'crypto'\nimport Stream from 'stream'\nimport { performance } from 'perf_hooks'\n\nimport { stringify, handleValue, arrayParser, arraySerializer } from './types.js'\nimport { Errors } from './errors.js'\nimport Result from './result.js'\nimport Queue from './queue.js'\nimport { Query, CLOSE } from './query.js'\nimport b from './bytes.js'\n\nexport default Connection\n\nlet uid = 1\n\nconst Sync = b().S().end()\n    , Flush = b().H().end()\n    , SSLRequest = b().i32(8).i32(80877103).end(8)\n    , ExecuteUnnamed = Buffer.concat([b().E().str(b.N).i32(0).end(), Sync])\n    , DescribeUnnamed = b().D().str('S').str(b.N).end()\n    , noop = () => { /* noop */ }\n\nconst retryRoutines = new Set([\n  'FetchPreparedStatement',\n  'RevalidateCachedQuery',\n  'transformAssignedExpr'\n])\n\nconst errorFields = {\n  83  : 'severity_local',    // S\n  86  : 'severity',          // V\n  67  : 'code',              // C\n  77  : 'message',           // M\n  68  : 'detail',            // D\n  72  : 'hint',              // H\n  80  : 'position',          // P\n  112 : 'internal_position', // p\n  113 : 'internal_query',    // q\n  87  : 'where',             // W\n  115 : 'schema_name',       // s\n  116 : 'table_name',        // t\n  99  : 'column_name',       // c\n  100 : 'data type_name',    // d\n  110 : 'constraint_name',   // n\n  70  : 'file',              // F\n  76  : 'line',              // L\n  82  : 'routine'            // R\n}\n\nfunction Connection(options, queues = {}, { onopen = noop, onend = noop, onclose = noop } = {}) {\n  const {\n    ssl,\n    max,\n    user,\n    host,\n    port,\n    database,\n    parsers,\n    transform,\n    onnotice,\n    onnotify,\n    onparameter,\n    max_pipeline,\n    keep_alive,\n    backoff,\n    target_session_attrs\n  } = options\n\n  const sent = Queue()\n      , id = uid++\n      , backend = { pid: null, secret: null }\n      , idleTimer = timer(end, options.idle_timeout)\n      , lifeTimer = timer(end, options.max_lifetime)\n      , connectTimer = timer(connectTimedOut, options.connect_timeout)\n\n  let socket = null\n    , cancelMessage\n    , result = new Result()\n    , incoming = Buffer.alloc(0)\n    , needsTypes = options.fetch_types\n    , backendParameters = {}\n    , statements = {}\n    , statementId = Math.random().toString(36).slice(2)\n    , statementCount = 1\n    , closedDate = 0\n    , remaining = 0\n    , hostIndex = 0\n    , retries = 0\n    , length = 0\n    , delay = 0\n    , rows = 0\n    , serverSignature = null\n    , nextWriteTimer = null\n    , terminated = false\n    , incomings = null\n    , results = null\n    , initial = null\n    , ending = null\n    , stream = null\n    , chunk = null\n    , ended = null\n    , nonce = null\n    , query = null\n    , final = null\n\n  const connection = {\n    queue: queues.closed,\n    idleTimer,\n    connect(query) {\n      initial = query\n      reconnect()\n    },\n    terminate,\n    execute,\n    cancel,\n    end,\n    count: 0,\n    id\n  }\n\n  queues.closed && queues.closed.push(connection)\n\n  return connection\n\n  async function createSocket() {\n    let x\n    try {\n      x = options.socket\n        ? (await Promise.resolve(options.socket(options)))\n        : new net.Socket()\n    } catch (e) {\n      error(e)\n      return\n    }\n    x.on('error', error)\n    x.on('close', closed)\n    x.on('drain', drain)\n    return x\n  }\n\n  async function cancel({ pid, secret }, resolve, reject) {\n    try {\n      cancelMessage = b().i32(16).i32(80877102).i32(pid).i32(secret).end(16)\n      await connect()\n      socket.once('error', reject)\n      socket.once('close', resolve)\n    } catch (error) {\n      reject(error)\n    }\n  }\n\n  function execute(q) {\n    if (terminated)\n      return queryError(q, Errors.connection('CONNECTION_DESTROYED', options))\n\n    if (q.cancelled)\n      return\n\n    try {\n      q.state = backend\n      query\n        ? sent.push(q)\n        : (query = q, query.active = true)\n\n      build(q)\n      return write(toBuffer(q))\n        && !q.describeFirst\n        && !q.cursorFn\n        && sent.length < max_pipeline\n        && (!q.options.onexecute || q.options.onexecute(connection))\n    } catch (error) {\n      sent.length === 0 && write(Sync)\n      errored(error)\n      return true\n    }\n  }\n\n  function toBuffer(q) {\n    if (q.parameters.length >= 65534)\n      throw Errors.generic('MAX_PARAMETERS_EXCEEDED', 'Max number of parameters (65534) exceeded')\n\n    return q.options.simple\n      ? b().Q().str(q.statement.string + b.N).end()\n      : q.describeFirst\n        ? Buffer.concat([describe(q), Flush])\n        : q.prepare\n          ? q.prepared\n            ? prepared(q)\n            : Buffer.concat([describe(q), prepared(q)])\n          : unnamed(q)\n  }\n\n  function describe(q) {\n    return Buffer.concat([\n      Parse(q.statement.string, q.parameters, q.statement.types, q.statement.name),\n      Describe('S', q.statement.name)\n    ])\n  }\n\n  function prepared(q) {\n    return Buffer.concat([\n      Bind(q.parameters, q.statement.types, q.statement.name, q.cursorName),\n      q.cursorFn\n        ? Execute('', q.cursorRows)\n        : ExecuteUnnamed\n    ])\n  }\n\n  function unnamed(q) {\n    return Buffer.concat([\n      Parse(q.statement.string, q.parameters, q.statement.types),\n      DescribeUnnamed,\n      prepared(q)\n    ])\n  }\n\n  function build(q) {\n    const parameters = []\n        , types = []\n\n    const string = stringify(q, q.strings[0], q.args[0], parameters, types, options)\n\n    !q.tagged && q.args.forEach(x => handleValue(x, parameters, types, options))\n\n    q.prepare = options.prepare && ('prepare' in q.options ? q.options.prepare : true)\n    q.string = string\n    q.signature = q.prepare && types + string\n    q.onlyDescribe && (delete statements[q.signature])\n    q.parameters = q.parameters || parameters\n    q.prepared = q.prepare && q.signature in statements\n    q.describeFirst = q.onlyDescribe || (parameters.length && !q.prepared)\n    q.statement = q.prepared\n      ? statements[q.signature]\n      : { string, types, name: q.prepare ? statementId + statementCount++ : '' }\n\n    typeof options.debug === 'function' && options.debug(id, string, parameters, types)\n  }\n\n  function write(x, fn) {\n    chunk = chunk ? Buffer.concat([chunk, x]) : Buffer.from(x)\n    if (fn || chunk.length >= 1024)\n      return nextWrite(fn)\n    nextWriteTimer === null && (nextWriteTimer = setImmediate(nextWrite))\n    return true\n  }\n\n  function nextWrite(fn) {\n    const x = socket.write(chunk, fn)\n    nextWriteTimer !== null && clearImmediate(nextWriteTimer)\n    chunk = nextWriteTimer = null\n    return x\n  }\n\n  function connectTimedOut() {\n    errored(Errors.connection('CONNECT_TIMEOUT', options, socket))\n    socket.destroy()\n  }\n\n  async function secure() {\n    write(SSLRequest)\n    const canSSL = await new Promise(r => socket.once('data', x => r(x[0] === 83))) // S\n\n    if (!canSSL && ssl === 'prefer')\n      return connected()\n\n    socket.removeAllListeners()\n    socket = tls.connect({\n      socket,\n      servername: net.isIP(socket.host) ? undefined : socket.host,\n      ...(ssl === 'require' || ssl === 'allow' || ssl === 'prefer'\n        ? { rejectUnauthorized: false }\n        : ssl === 'verify-full'\n          ? {}\n          : typeof ssl === 'object'\n            ? ssl\n            : {}\n      )\n    })\n    socket.on('secureConnect', connected)\n    socket.on('error', error)\n    socket.on('close', closed)\n    socket.on('drain', drain)\n  }\n\n  /* c8 ignore next 3 */\n  function drain() {\n    !query && onopen(connection)\n  }\n\n  function data(x) {\n    if (incomings) {\n      incomings.push(x)\n      remaining -= x.length\n      if (remaining > 0)\n        return\n    }\n\n    incoming = incomings\n      ? Buffer.concat(incomings, length - remaining)\n      : incoming.length === 0\n        ? x\n        : Buffer.concat([incoming, x], incoming.length + x.length)\n\n    while (incoming.length > 4) {\n      length = incoming.readUInt32BE(1)\n      if (length >= incoming.length) {\n        remaining = length - incoming.length\n        incomings = [incoming]\n        break\n      }\n\n      try {\n        handle(incoming.subarray(0, length + 1))\n      } catch (e) {\n        query && (query.cursorFn || query.describeFirst) && write(Sync)\n        errored(e)\n      }\n      incoming = incoming.subarray(length + 1)\n      remaining = 0\n      incomings = null\n    }\n  }\n\n  async function connect() {\n    terminated = false\n    backendParameters = {}\n    socket || (socket = await createSocket())\n\n    if (!socket)\n      return\n\n    connectTimer.start()\n\n    if (options.socket)\n      return ssl ? secure() : connected()\n\n    socket.on('connect', ssl ? secure : connected)\n\n    if (options.path)\n      return socket.connect(options.path)\n\n    socket.ssl = ssl\n    socket.connect(port[hostIndex], host[hostIndex])\n    socket.host = host[hostIndex]\n    socket.port = port[hostIndex]\n\n    hostIndex = (hostIndex + 1) % port.length\n  }\n\n  function reconnect() {\n    setTimeout(connect, closedDate ? closedDate + delay - performance.now() : 0)\n  }\n\n  function connected() {\n    try {\n      statements = {}\n      needsTypes = options.fetch_types\n      statementId = Math.random().toString(36).slice(2)\n      statementCount = 1\n      lifeTimer.start()\n      socket.on('data', data)\n      keep_alive && socket.setKeepAlive && socket.setKeepAlive(true, 1000 * keep_alive)\n      const s = StartupMessage()\n      write(s)\n    } catch (err) {\n      error(err)\n    }\n  }\n\n  function error(err) {\n    if (connection.queue === queues.connecting && options.host[retries + 1])\n      return\n\n    errored(err)\n    while (sent.length)\n      queryError(sent.shift(), err)\n  }\n\n  function errored(err) {\n    stream && (stream.destroy(err), stream = null)\n    query && queryError(query, err)\n    initial && (queryError(initial, err), initial = null)\n  }\n\n  function queryError(query, err) {\n    if (query.reserve)\n      return query.reject(err)\n\n    if (!err || typeof err !== 'object')\n      err = new Error(err)\n\n    'query' in err || 'parameters' in err || Object.defineProperties(err, {\n      stack: { value: err.stack + query.origin.replace(/.*\\n/, '\\n'), enumerable: options.debug },\n      query: { value: query.string, enumerable: options.debug },\n      parameters: { value: query.parameters, enumerable: options.debug },\n      args: { value: query.args, enumerable: options.debug },\n      types: { value: query.statement && query.statement.types, enumerable: options.debug }\n    })\n    query.reject(err)\n  }\n\n  function end() {\n    return ending || (\n      !connection.reserved && onend(connection),\n      !connection.reserved && !initial && !query && sent.length === 0\n        ? (terminate(), new Promise(r => socket && socket.readyState !== 'closed' ? socket.once('close', r) : r()))\n        : ending = new Promise(r => ended = r)\n    )\n  }\n\n  function terminate() {\n    terminated = true\n    if (stream || query || initial || sent.length)\n      error(Errors.connection('CONNECTION_DESTROYED', options))\n\n    clearImmediate(nextWriteTimer)\n    if (socket) {\n      socket.removeListener('data', data)\n      socket.removeListener('connect', connected)\n      socket.readyState === 'open' && socket.end(b().X().end())\n    }\n    ended && (ended(), ending = ended = null)\n  }\n\n  async function closed(hadError) {\n    incoming = Buffer.alloc(0)\n    remaining = 0\n    incomings = null\n    clearImmediate(nextWriteTimer)\n    socket.removeListener('data', data)\n    socket.removeListener('connect', connected)\n    idleTimer.cancel()\n    lifeTimer.cancel()\n    connectTimer.cancel()\n\n    socket.removeAllListeners()\n    socket = null\n\n    if (initial)\n      return reconnect()\n\n    !hadError && (query || sent.length) && error(Errors.connection('CONNECTION_CLOSED', options, socket))\n    closedDate = performance.now()\n    hadError && options.shared.retries++\n    delay = (typeof backoff === 'function' ? backoff(options.shared.retries) : backoff) * 1000\n    onclose(connection, Errors.connection('CONNECTION_CLOSED', options, socket))\n  }\n\n  /* Handlers */\n  function handle(xs, x = xs[0]) {\n    (\n      x === 68 ? DataRow :                   // D\n      x === 100 ? CopyData :                 // d\n      x === 65 ? NotificationResponse :      // A\n      x === 83 ? ParameterStatus :           // S\n      x === 90 ? ReadyForQuery :             // Z\n      x === 67 ? CommandComplete :           // C\n      x === 50 ? BindComplete :              // 2\n      x === 49 ? ParseComplete :             // 1\n      x === 116 ? ParameterDescription :     // t\n      x === 84 ? RowDescription :            // T\n      x === 82 ? Authentication :            // R\n      x === 110 ? NoData :                   // n\n      x === 75 ? BackendKeyData :            // K\n      x === 69 ? ErrorResponse :             // E\n      x === 115 ? PortalSuspended :          // s\n      x === 51 ? CloseComplete :             // 3\n      x === 71 ? CopyInResponse :            // G\n      x === 78 ? NoticeResponse :            // N\n      x === 72 ? CopyOutResponse :           // H\n      x === 99 ? CopyDone :                  // c\n      x === 73 ? EmptyQueryResponse :        // I\n      x === 86 ? FunctionCallResponse :      // V\n      x === 118 ? NegotiateProtocolVersion : // v\n      x === 87 ? CopyBothResponse :          // W\n      /* c8 ignore next */\n      UnknownMessage\n    )(xs)\n  }\n\n  function DataRow(x) {\n    let index = 7\n    let length\n    let column\n    let value\n\n    const row = query.isRaw ? new Array(query.statement.columns.length) : {}\n    for (let i = 0; i < query.statement.columns.length; i++) {\n      column = query.statement.columns[i]\n      length = x.readInt32BE(index)\n      index += 4\n\n      value = length === -1\n        ? null\n        : query.isRaw === true\n          ? x.subarray(index, index += length)\n          : column.parser === undefined\n            ? x.toString('utf8', index, index += length)\n            : column.parser.array === true\n              ? column.parser(x.toString('utf8', index + 1, index += length))\n              : column.parser(x.toString('utf8', index, index += length))\n\n      query.isRaw\n        ? (row[i] = query.isRaw === true\n          ? value\n          : transform.value.from ? transform.value.from(value, column) : value)\n        : (row[column.name] = transform.value.from ? transform.value.from(value, column) : value)\n    }\n\n    query.forEachFn\n      ? query.forEachFn(transform.row.from ? transform.row.from(row) : row, result)\n      : (result[rows++] = transform.row.from ? transform.row.from(row) : row)\n  }\n\n  function ParameterStatus(x) {\n    const [k, v] = x.toString('utf8', 5, x.length - 1).split(b.N)\n    backendParameters[k] = v\n    if (options.parameters[k] !== v) {\n      options.parameters[k] = v\n      onparameter && onparameter(k, v)\n    }\n  }\n\n  function ReadyForQuery(x) {\n    query && query.options.simple && query.resolve(results || result)\n    query = results = null\n    result = new Result()\n    connectTimer.cancel()\n\n    if (initial) {\n      if (target_session_attrs) {\n        if (!backendParameters.in_hot_standby || !backendParameters.default_transaction_read_only)\n          return fetchState()\n        else if (tryNext(target_session_attrs, backendParameters))\n          return terminate()\n      }\n\n      if (needsTypes) {\n        initial.reserve && (initial = null)\n        return fetchArrayTypes()\n      }\n\n      initial && !initial.reserve && execute(initial)\n      options.shared.retries = retries = 0\n      initial = null\n      return\n    }\n\n    while (sent.length && (query = sent.shift()) && (query.active = true, query.cancelled))\n      Connection(options).cancel(query.state, query.cancelled.resolve, query.cancelled.reject)\n\n    if (query)\n      return // Consider opening if able and sent.length < 50\n\n    connection.reserved\n      ? !connection.reserved.release && x[5] === 73 // I\n        ? ending\n          ? terminate()\n          : (connection.reserved = null, onopen(connection))\n        : connection.reserved()\n      : ending\n        ? terminate()\n        : onopen(connection)\n  }\n\n  function CommandComplete(x) {\n    rows = 0\n\n    for (let i = x.length - 1; i > 0; i--) {\n      if (x[i] === 32 && x[i + 1] < 58 && result.count === null)\n        result.count = +x.toString('utf8', i + 1, x.length - 1)\n      if (x[i - 1] >= 65) {\n        result.command = x.toString('utf8', 5, i)\n        result.state = backend\n        break\n      }\n    }\n\n    final && (final(), final = null)\n\n    if (result.command === 'BEGIN' && max !== 1 && !connection.reserved)\n      return errored(Errors.generic('UNSAFE_TRANSACTION', 'Only use sql.begin, sql.reserved or max: 1'))\n\n    if (query.options.simple)\n      return BindComplete()\n\n    if (query.cursorFn) {\n      result.count && query.cursorFn(result)\n      write(Sync)\n    }\n\n    query.resolve(result)\n  }\n\n  function ParseComplete() {\n    query.parsing = false\n  }\n\n  function BindComplete() {\n    !result.statement && (result.statement = query.statement)\n    result.columns = query.statement.columns\n  }\n\n  function ParameterDescription(x) {\n    const length = x.readUInt16BE(5)\n\n    for (let i = 0; i < length; ++i)\n      !query.statement.types[i] && (query.statement.types[i] = x.readUInt32BE(7 + i * 4))\n\n    query.prepare && (statements[query.signature] = query.statement)\n    query.describeFirst && !query.onlyDescribe && (write(prepared(query)), query.describeFirst = false)\n  }\n\n  function RowDescription(x) {\n    if (result.command) {\n      results = results || [result]\n      results.push(result = new Result())\n      result.count = null\n      query.statement.columns = null\n    }\n\n    const length = x.readUInt16BE(5)\n    let index = 7\n    let start\n\n    query.statement.columns = Array(length)\n\n    for (let i = 0; i < length; ++i) {\n      start = index\n      while (x[index++] !== 0);\n      const table = x.readUInt32BE(index)\n      const number = x.readUInt16BE(index + 4)\n      const type = x.readUInt32BE(index + 6)\n      query.statement.columns[i] = {\n        name: transform.column.from\n          ? transform.column.from(x.toString('utf8', start, index - 1))\n          : x.toString('utf8', start, index - 1),\n        parser: parsers[type],\n        table,\n        number,\n        type\n      }\n      index += 18\n    }\n\n    result.statement = query.statement\n    if (query.onlyDescribe)\n      return (query.resolve(query.statement), write(Sync))\n  }\n\n  async function Authentication(x, type = x.readUInt32BE(5)) {\n    (\n      type === 3 ? AuthenticationCleartextPassword :\n      type === 5 ? AuthenticationMD5Password :\n      type === 10 ? SASL :\n      type === 11 ? SASLContinue :\n      type === 12 ? SASLFinal :\n      type !== 0 ? UnknownAuth :\n      noop\n    )(x, type)\n  }\n\n  /* c8 ignore next 5 */\n  async function AuthenticationCleartextPassword() {\n    const payload = await Pass()\n    write(\n      b().p().str(payload).z(1).end()\n    )\n  }\n\n  async function AuthenticationMD5Password(x) {\n    const payload = 'md5' + (\n      await md5(\n        Buffer.concat([\n          Buffer.from(await md5((await Pass()) + user)),\n          x.subarray(9)\n        ])\n      )\n    )\n    write(\n      b().p().str(payload).z(1).end()\n    )\n  }\n\n  async function SASL() {\n    nonce = (await crypto.randomBytes(18)).toString('base64')\n    b().p().str('SCRAM-SHA-256' + b.N)\n    const i = b.i\n    write(b.inc(4).str('n,,n=*,r=' + nonce).i32(b.i - i - 4, i).end())\n  }\n\n  async function SASLContinue(x) {\n    const res = x.toString('utf8', 9).split(',').reduce((acc, x) => (acc[x[0]] = x.slice(2), acc), {})\n\n    const saltedPassword = await crypto.pbkdf2Sync(\n      await Pass(),\n      Buffer.from(res.s, 'base64'),\n      parseInt(res.i), 32,\n      'sha256'\n    )\n\n    const clientKey = await hmac(saltedPassword, 'Client Key')\n\n    const auth = 'n=*,r=' + nonce + ','\n               + 'r=' + res.r + ',s=' + res.s + ',i=' + res.i\n               + ',c=biws,r=' + res.r\n\n    serverSignature = (await hmac(await hmac(saltedPassword, 'Server Key'), auth)).toString('base64')\n\n    const payload = 'c=biws,r=' + res.r + ',p=' + xor(\n      clientKey, Buffer.from(await hmac(await sha256(clientKey), auth))\n    ).toString('base64')\n\n    write(\n      b().p().str(payload).end()\n    )\n  }\n\n  function SASLFinal(x) {\n    if (x.toString('utf8', 9).split(b.N, 1)[0].slice(2) === serverSignature)\n      return\n    /* c8 ignore next 5 */\n    errored(Errors.generic('SASL_SIGNATURE_MISMATCH', 'The server did not return the correct signature'))\n    socket.destroy()\n  }\n\n  function Pass() {\n    return Promise.resolve(typeof options.pass === 'function'\n      ? options.pass()\n      : options.pass\n    )\n  }\n\n  function NoData() {\n    result.statement = query.statement\n    result.statement.columns = []\n    if (query.onlyDescribe)\n      return (query.resolve(query.statement), write(Sync))\n  }\n\n  function BackendKeyData(x) {\n    backend.pid = x.readUInt32BE(5)\n    backend.secret = x.readUInt32BE(9)\n  }\n\n  async function fetchArrayTypes() {\n    needsTypes = false\n    const types = await new Query([`\n      select b.oid, b.typarray\n      from pg_catalog.pg_type a\n      left join pg_catalog.pg_type b on b.oid = a.typelem\n      where a.typcategory = 'A'\n      group by b.oid, b.typarray\n      order by b.oid\n    `], [], execute)\n    types.forEach(({ oid, typarray }) => addArrayType(oid, typarray))\n  }\n\n  function addArrayType(oid, typarray) {\n    if (!!options.parsers[typarray] && !!options.serializers[typarray]) return\n    const parser = options.parsers[oid]\n    options.shared.typeArrayMap[oid] = typarray\n    options.parsers[typarray] = (xs) => arrayParser(xs, parser, typarray)\n    options.parsers[typarray].array = true\n    options.serializers[typarray] = (xs) => arraySerializer(xs, options.serializers[oid], options, typarray)\n  }\n\n  function tryNext(x, xs) {\n    return (\n      (x === 'read-write' && xs.default_transaction_read_only === 'on') ||\n      (x === 'read-only' && xs.default_transaction_read_only === 'off') ||\n      (x === 'primary' && xs.in_hot_standby === 'on') ||\n      (x === 'standby' && xs.in_hot_standby === 'off') ||\n      (x === 'prefer-standby' && xs.in_hot_standby === 'off' && options.host[retries])\n    )\n  }\n\n  function fetchState() {\n    const query = new Query([`\n      show transaction_read_only;\n      select pg_catalog.pg_is_in_recovery()\n    `], [], execute, null, { simple: true })\n    query.resolve = ([[a], [b]]) => {\n      backendParameters.default_transaction_read_only = a.transaction_read_only\n      backendParameters.in_hot_standby = b.pg_is_in_recovery ? 'on' : 'off'\n    }\n    query.execute()\n  }\n\n  function ErrorResponse(x) {\n    query && (query.cursorFn || query.describeFirst) && write(Sync)\n    const error = Errors.postgres(parseError(x))\n    query && query.retried\n      ? errored(query.retried)\n      : query && query.prepared && retryRoutines.has(error.routine)\n        ? retry(query, error)\n        : errored(error)\n  }\n\n  function retry(q, error) {\n    delete statements[q.signature]\n    q.retried = error\n    execute(q)\n  }\n\n  function NotificationResponse(x) {\n    if (!onnotify)\n      return\n\n    let index = 9\n    while (x[index++] !== 0);\n    onnotify(\n      x.toString('utf8', 9, index - 1),\n      x.toString('utf8', index, x.length - 1)\n    )\n  }\n\n  async function PortalSuspended() {\n    try {\n      const x = await Promise.resolve(query.cursorFn(result))\n      rows = 0\n      x === CLOSE\n        ? write(Close(query.portal))\n        : (result = new Result(), write(Execute('', query.cursorRows)))\n    } catch (err) {\n      write(Sync)\n      query.reject(err)\n    }\n  }\n\n  function CloseComplete() {\n    result.count && query.cursorFn(result)\n    query.resolve(result)\n  }\n\n  function CopyInResponse() {\n    stream = new Stream.Writable({\n      autoDestroy: true,\n      write(chunk, encoding, callback) {\n        socket.write(b().d().raw(chunk).end(), callback)\n      },\n      destroy(error, callback) {\n        callback(error)\n        socket.write(b().f().str(error + b.N).end())\n        stream = null\n      },\n      final(callback) {\n        socket.write(b().c().end())\n        final = callback\n      }\n    })\n    query.resolve(stream)\n  }\n\n  function CopyOutResponse() {\n    stream = new Stream.Readable({\n      read() { socket.resume() }\n    })\n    query.resolve(stream)\n  }\n\n  /* c8 ignore next 3 */\n  function CopyBothResponse() {\n    stream = new Stream.Duplex({\n      autoDestroy: true,\n      read() { socket.resume() },\n      /* c8 ignore next 11 */\n      write(chunk, encoding, callback) {\n        socket.write(b().d().raw(chunk).end(), callback)\n      },\n      destroy(error, callback) {\n        callback(error)\n        socket.write(b().f().str(error + b.N).end())\n        stream = null\n      },\n      final(callback) {\n        socket.write(b().c().end())\n        final = callback\n      }\n    })\n    query.resolve(stream)\n  }\n\n  function CopyData(x) {\n    stream && (stream.push(x.subarray(5)) || socket.pause())\n  }\n\n  function CopyDone() {\n    stream && stream.push(null)\n    stream = null\n  }\n\n  function NoticeResponse(x) {\n    onnotice\n      ? onnotice(parseError(x))\n      : console.log(parseError(x)) // eslint-disable-line\n\n  }\n\n  /* c8 ignore next 3 */\n  function EmptyQueryResponse() {\n    /* noop */\n  }\n\n  /* c8 ignore next 3 */\n  function FunctionCallResponse() {\n    errored(Errors.notSupported('FunctionCallResponse'))\n  }\n\n  /* c8 ignore next 3 */\n  function NegotiateProtocolVersion() {\n    errored(Errors.notSupported('NegotiateProtocolVersion'))\n  }\n\n  /* c8 ignore next 3 */\n  function UnknownMessage(x) {\n    console.error('Postgres.js : Unknown Message:', x[0]) // eslint-disable-line\n  }\n\n  /* c8 ignore next 3 */\n  function UnknownAuth(x, type) {\n    console.error('Postgres.js : Unknown Auth:', type) // eslint-disable-line\n  }\n\n  /* Messages */\n  function Bind(parameters, types, statement = '', portal = '') {\n    let prev\n      , type\n\n    b().B().str(portal + b.N).str(statement + b.N).i16(0).i16(parameters.length)\n\n    parameters.forEach((x, i) => {\n      if (x === null)\n        return b.i32(0xFFFFFFFF)\n\n      type = types[i]\n      parameters[i] = x = type in options.serializers\n        ? options.serializers[type](x)\n        : '' + x\n\n      prev = b.i\n      b.inc(4).str(x).i32(b.i - prev - 4, prev)\n    })\n\n    b.i16(0)\n\n    return b.end()\n  }\n\n  function Parse(str, parameters, types, name = '') {\n    b().P().str(name + b.N).str(str + b.N).i16(parameters.length)\n    parameters.forEach((x, i) => b.i32(types[i] || 0))\n    return b.end()\n  }\n\n  function Describe(x, name = '') {\n    return b().D().str(x).str(name + b.N).end()\n  }\n\n  function Execute(portal = '', rows = 0) {\n    return Buffer.concat([\n      b().E().str(portal + b.N).i32(rows).end(),\n      Flush\n    ])\n  }\n\n  function Close(portal = '') {\n    return Buffer.concat([\n      b().C().str('P').str(portal + b.N).end(),\n      b().S().end()\n    ])\n  }\n\n  function StartupMessage() {\n    return cancelMessage || b().inc(4).i16(3).z(2).str(\n      Object.entries(Object.assign({\n        user,\n        database,\n        client_encoding: 'UTF8'\n      },\n        options.connection\n      )).filter(([, v]) => v).map(([k, v]) => k + b.N + v).join(b.N)\n    ).z(2).end(0)\n  }\n\n}\n\nfunction parseError(x) {\n  const error = {}\n  let start = 5\n  for (let i = 5; i < x.length - 1; i++) {\n    if (x[i] === 0) {\n      error[errorFields[x[start]]] = x.toString('utf8', start + 1, i)\n      start = i + 1\n    }\n  }\n  return error\n}\n\nfunction md5(x) {\n  return crypto.createHash('md5').update(x).digest('hex')\n}\n\nfunction hmac(key, x) {\n  return crypto.createHmac('sha256', key).update(x).digest()\n}\n\nfunction sha256(x) {\n  return crypto.createHash('sha256').update(x).digest()\n}\n\nfunction xor(a, b) {\n  const length = Math.max(a.length, b.length)\n  const buffer = Buffer.allocUnsafe(length)\n  for (let i = 0; i < length; i++)\n    buffer[i] = a[i] ^ b[i]\n  return buffer\n}\n\nfunction timer(fn, seconds) {\n  seconds = typeof seconds === 'function' ? seconds() : seconds\n  if (!seconds)\n    return { cancel: noop, start: noop }\n\n  let timer\n  return {\n    cancel() {\n      timer && (clearTimeout(timer), timer = null)\n    },\n    start() {\n      timer && clearTimeout(timer)\n      timer = setTimeout(done, seconds * 1000, arguments)\n    }\n  }\n\n  function done(args) {\n    fn.apply(null, args)\n    timer = null\n  }\n}\n", "export default class Result extends Array {\n  constructor() {\n    super()\n    Object.defineProperties(this, {\n      count: { value: null, writable: true },\n      state: { value: null, writable: true },\n      command: { value: null, writable: true },\n      columns: { value: null, writable: true },\n      statement: { value: null, writable: true }\n    })\n  }\n\n  static get [Symbol.species]() {\n    return Array\n  }\n}\n", "export default Queue\n\nfunction Queue(initial = []) {\n  let xs = initial.slice()\n  let index = 0\n\n  return {\n    get length() {\n      return xs.length - index\n    },\n    remove: (x) => {\n      const index = xs.indexOf(x)\n      return index === -1\n        ? null\n        : (xs.splice(index, 1), x)\n    },\n    push: (x) => (xs.push(x), x),\n    shift: () => {\n      const out = xs[index++]\n\n      if (index === xs.length) {\n        index = 0\n        xs = []\n      } else {\n        xs[index - 1] = undefined\n      }\n\n      return out\n    }\n  }\n}\n", "const size = 256\nlet buffer = Buffer.allocUnsafe(size)\n\nconst messages = 'BCcDdEFfHPpQSX'.split('').reduce((acc, x) => {\n  const v = x.charCodeAt(0)\n  acc[x] = () => {\n    buffer[0] = v\n    b.i = 5\n    return b\n  }\n  return acc\n}, {})\n\nconst b = Object.assign(reset, messages, {\n  N: String.fromCharCode(0),\n  i: 0,\n  inc(x) {\n    b.i += x\n    return b\n  },\n  str(x) {\n    const length = Buffer.byteLength(x)\n    fit(length)\n    b.i += buffer.write(x, b.i, length, 'utf8')\n    return b\n  },\n  i16(x) {\n    fit(2)\n    buffer.writeUInt16BE(x, b.i)\n    b.i += 2\n    return b\n  },\n  i32(x, i) {\n    if (i || i === 0) {\n      buffer.writeUInt32BE(x, i)\n      return b\n    }\n    fit(4)\n    buffer.writeUInt32BE(x, b.i)\n    b.i += 4\n    return b\n  },\n  z(x) {\n    fit(x)\n    buffer.fill(0, b.i, b.i + x)\n    b.i += x\n    return b\n  },\n  raw(x) {\n    buffer = Buffer.concat([buffer.subarray(0, b.i), x])\n    b.i = buffer.length\n    return b\n  },\n  end(at = 1) {\n    buffer.writeUInt32BE(b.i - at, at)\n    const out = buffer.subarray(0, b.i)\n    b.i = 0\n    buffer = Buffer.allocUnsafe(size)\n    return out\n  }\n})\n\nexport default b\n\nfunction fit(x) {\n  if (buffer.length - b.i < x) {\n    const prev = buffer\n        , length = prev.length\n\n    buffer = Buffer.allocUnsafe(length + (length >> 1) + x)\n    prev.copy(buffer)\n  }\n}\n\nfunction reset() {\n  b.i = 0\n  return b\n}\n", "const noop = () => { /* noop */ }\n\nexport default function Subscribe(postgres, options) {\n  const subscribers = new Map()\n      , slot = 'postgresjs_' + Math.random().toString(36).slice(2)\n      , state = {}\n\n  let connection\n    , stream\n    , ended = false\n\n  const sql = subscribe.sql = postgres({\n    ...options,\n    transform: { column: {}, value: {}, row: {} },\n    max: 1,\n    fetch_types: false,\n    idle_timeout: null,\n    max_lifetime: null,\n    connection: {\n      ...options.connection,\n      replication: 'database'\n    },\n    onclose: async function() {\n      if (ended)\n        return\n      stream = null\n      state.pid = state.secret = undefined\n      connected(await init(sql, slot, options.publications))\n      subscribers.forEach(event => event.forEach(({ onsubscribe }) => onsubscribe()))\n    },\n    no_subscribe: true\n  })\n\n  const end = sql.end\n      , close = sql.close\n\n  sql.end = async() => {\n    ended = true\n    stream && (await new Promise(r => (stream.once('close', r), stream.end())))\n    return end()\n  }\n\n  sql.close = async() => {\n    stream && (await new Promise(r => (stream.once('close', r), stream.end())))\n    return close()\n  }\n\n  return subscribe\n\n  async function subscribe(event, fn, onsubscribe = noop, onerror = noop) {\n    event = parseEvent(event)\n\n    if (!connection)\n      connection = init(sql, slot, options.publications)\n\n    const subscriber = { fn, onsubscribe }\n    const fns = subscribers.has(event)\n      ? subscribers.get(event).add(subscriber)\n      : subscribers.set(event, new Set([subscriber])).get(event)\n\n    const unsubscribe = () => {\n      fns.delete(subscriber)\n      fns.size === 0 && subscribers.delete(event)\n    }\n\n    return connection.then(x => {\n      connected(x)\n      onsubscribe()\n      stream && stream.on('error', onerror)\n      return { unsubscribe, state, sql }\n    })\n  }\n\n  function connected(x) {\n    stream = x.stream\n    state.pid = x.state.pid\n    state.secret = x.state.secret\n  }\n\n  async function init(sql, slot, publications) {\n    if (!publications)\n      throw new Error('Missing publication names')\n\n    const xs = await sql.unsafe(\n      `CREATE_REPLICATION_SLOT ${ slot } TEMPORARY LOGICAL pgoutput NOEXPORT_SNAPSHOT`\n    )\n\n    const [x] = xs\n\n    const stream = await sql.unsafe(\n      `START_REPLICATION SLOT ${ slot } LOGICAL ${\n        x.consistent_point\n      } (proto_version '1', publication_names '${ publications }')`\n    ).writable()\n\n    const state = {\n      lsn: Buffer.concat(x.consistent_point.split('/').map(x => Buffer.from(('00000000' + x).slice(-8), 'hex')))\n    }\n\n    stream.on('data', data)\n    stream.on('error', error)\n    stream.on('close', sql.close)\n\n    return { stream, state: xs.state }\n\n    function error(e) {\n      console.error('Unexpected error during logical streaming - reconnecting', e) // eslint-disable-line\n    }\n\n    function data(x) {\n      if (x[0] === 0x77) {\n        parse(x.subarray(25), state, sql.options.parsers, handle, options.transform)\n      } else if (x[0] === 0x6b && x[17]) {\n        state.lsn = x.subarray(1, 9)\n        pong()\n      }\n    }\n\n    function handle(a, b) {\n      const path = b.relation.schema + '.' + b.relation.table\n      call('*', a, b)\n      call('*:' + path, a, b)\n      b.relation.keys.length && call('*:' + path + '=' + b.relation.keys.map(x => a[x.name]), a, b)\n      call(b.command, a, b)\n      call(b.command + ':' + path, a, b)\n      b.relation.keys.length && call(b.command + ':' + path + '=' + b.relation.keys.map(x => a[x.name]), a, b)\n    }\n\n    function pong() {\n      const x = Buffer.alloc(34)\n      x[0] = 'r'.charCodeAt(0)\n      x.fill(state.lsn, 1)\n      x.writeBigInt64BE(BigInt(Date.now() - Date.UTC(2000, 0, 1)) * BigInt(1000), 25)\n      stream.write(x)\n    }\n  }\n\n  function call(x, a, b) {\n    subscribers.has(x) && subscribers.get(x).forEach(({ fn }) => fn(a, b, x))\n  }\n}\n\nfunction Time(x) {\n  return new Date(Date.UTC(2000, 0, 1) + Number(x / BigInt(1000)))\n}\n\nfunction parse(x, state, parsers, handle, transform) {\n  const char = (acc, [k, v]) => (acc[k.charCodeAt(0)] = v, acc)\n\n  Object.entries({\n    R: x => {  // Relation\n      let i = 1\n      const r = state[x.readUInt32BE(i)] = {\n        schema: x.toString('utf8', i += 4, i = x.indexOf(0, i)) || 'pg_catalog',\n        table: x.toString('utf8', i + 1, i = x.indexOf(0, i + 1)),\n        columns: Array(x.readUInt16BE(i += 2)),\n        keys: []\n      }\n      i += 2\n\n      let columnIndex = 0\n        , column\n\n      while (i < x.length) {\n        column = r.columns[columnIndex++] = {\n          key: x[i++],\n          name: transform.column.from\n            ? transform.column.from(x.toString('utf8', i, i = x.indexOf(0, i)))\n            : x.toString('utf8', i, i = x.indexOf(0, i)),\n          type: x.readUInt32BE(i += 1),\n          parser: parsers[x.readUInt32BE(i)],\n          atttypmod: x.readUInt32BE(i += 4)\n        }\n\n        column.key && r.keys.push(column)\n        i += 4\n      }\n    },\n    Y: () => { /* noop */ }, // Type\n    O: () => { /* noop */ }, // Origin\n    B: x => { // Begin\n      state.date = Time(x.readBigInt64BE(9))\n      state.lsn = x.subarray(1, 9)\n    },\n    I: x => { // Insert\n      let i = 1\n      const relation = state[x.readUInt32BE(i)]\n      const { row } = tuples(x, relation.columns, i += 7, transform)\n\n      handle(row, {\n        command: 'insert',\n        relation\n      })\n    },\n    D: x => { // Delete\n      let i = 1\n      const relation = state[x.readUInt32BE(i)]\n      i += 4\n      const key = x[i] === 75\n      handle(key || x[i] === 79\n        ? tuples(x, relation.columns, i += 3, transform).row\n        : null\n      , {\n        command: 'delete',\n        relation,\n        key\n      })\n    },\n    U: x => { // Update\n      let i = 1\n      const relation = state[x.readUInt32BE(i)]\n      i += 4\n      const key = x[i] === 75\n      const xs = key || x[i] === 79\n        ? tuples(x, relation.columns, i += 3, transform)\n        : null\n\n      xs && (i = xs.i)\n\n      const { row } = tuples(x, relation.columns, i + 3, transform)\n\n      handle(row, {\n        command: 'update',\n        relation,\n        key,\n        old: xs && xs.row\n      })\n    },\n    T: () => { /* noop */ }, // Truncate,\n    C: () => { /* noop */ }  // Commit\n  }).reduce(char, {})[x[0]](x)\n}\n\nfunction tuples(x, columns, xi, transform) {\n  let type\n    , column\n    , value\n\n  const row = transform.raw ? new Array(columns.length) : {}\n  for (let i = 0; i < columns.length; i++) {\n    type = x[xi++]\n    column = columns[i]\n    value = type === 110 // n\n      ? null\n      : type === 117 // u\n        ? undefined\n        : column.parser === undefined\n          ? x.toString('utf8', xi + 4, xi += 4 + x.readUInt32BE(xi))\n          : column.parser.array === true\n            ? column.parser(x.toString('utf8', xi + 5, xi += 4 + x.readUInt32BE(xi)))\n            : column.parser(x.toString('utf8', xi + 4, xi += 4 + x.readUInt32BE(xi)))\n\n    transform.raw\n      ? (row[i] = transform.raw === true\n        ? value\n        : transform.value.from ? transform.value.from(value, column) : value)\n      : (row[column.name] = transform.value.from\n        ? transform.value.from(value, column)\n        : value\n      )\n  }\n\n  return { i: xi, row: transform.row.from ? transform.row.from(row) : row }\n}\n\nfunction parseEvent(x) {\n  const xs = x.match(/^(\\*|insert|update|delete)?:?([^.]+?\\.?[^=]+)?=?(.+)?/i) || []\n\n  if (!xs)\n    throw new Error('Malformed subscribe pattern: ' + x)\n\n  const [, command, path, key] = xs\n\n  return (command || '*')\n       + (path ? ':' + (path.indexOf('.') === -1 ? 'public.' + path : path) : '')\n       + (key ? '=' + key : '')\n}\n", "import Stream from 'stream'\n\nexport default function largeObject(sql, oid, mode = 0x00020000 | 0x00040000) {\n  return new Promise(async(resolve, reject) => {\n    await sql.begin(async sql => {\n      let finish\n      !oid && ([{ oid }] = await sql`select lo_creat(-1) as oid`)\n      const [{ fd }] = await sql`select lo_open(${ oid }, ${ mode }) as fd`\n\n      const lo = {\n        writable,\n        readable,\n        close     : () => sql`select lo_close(${ fd })`.then(finish),\n        tell      : () => sql`select lo_tell64(${ fd })`,\n        read      : (x) => sql`select loread(${ fd }, ${ x }) as data`,\n        write     : (x) => sql`select lowrite(${ fd }, ${ x })`,\n        truncate  : (x) => sql`select lo_truncate64(${ fd }, ${ x })`,\n        seek      : (x, whence = 0) => sql`select lo_lseek64(${ fd }, ${ x }, ${ whence })`,\n        size      : () => sql`\n          select\n            lo_lseek64(${ fd }, location, 0) as position,\n            seek.size\n          from (\n            select\n              lo_lseek64($1, 0, 2) as size,\n              tell.location\n            from (select lo_tell64($1) as location) tell\n          ) seek\n        `\n      }\n\n      resolve(lo)\n\n      return new Promise(async r => finish = r)\n\n      async function readable({\n        highWaterMark = 2048 * 8,\n        start = 0,\n        end = Infinity\n      } = {}) {\n        let max = end - start\n        start && await lo.seek(start)\n        return new Stream.Readable({\n          highWaterMark,\n          async read(size) {\n            const l = size > max ? size - max : size\n            max -= size\n            const [{ data }] = await lo.read(l)\n            this.push(data)\n            if (data.length < size)\n              this.push(null)\n          }\n        })\n      }\n\n      async function writable({\n        highWaterMark = 2048 * 8,\n        start = 0\n      } = {}) {\n        start && await lo.seek(start)\n        return new Stream.Writable({\n          highWaterMark,\n          write(chunk, encoding, callback) {\n            lo.write(chunk).then(() => callback(), callback)\n          }\n        })\n      }\n    }).catch(reject)\n  })\n}\n", "import { entityKind } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport {\n\tPgDeleteBase,\n\tPgInsertBuilder,\n\tPgSelectBuilder,\n\tPgUpdateBuilder,\n\tQueryBuilder,\n} from '~/pg-core/query-builders/index.ts';\nimport type {\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPgTransaction,\n\tPgTransactionConfig,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgTable } from '~/pg-core/table.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { ExtractTablesWithRelations, RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport { type ColumnsSelection, type SQL, sql, type SQLWrapper } from '~/sql/sql.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport type { DrizzleTypeError } from '~/utils.ts';\nimport type { PgColumn } from './columns/index.ts';\nimport { PgCountBuilder } from './query-builders/count.ts';\nimport { RelationalQueryBuilder } from './query-builders/query.ts';\nimport { PgRaw } from './query-builders/raw.ts';\nimport { PgRefreshMaterializedView } from './query-builders/refresh-materialized-view.ts';\nimport type { SelectedFields } from './query-builders/select.types.ts';\nimport type { WithSubqueryWithSelection } from './subquery.ts';\nimport type { PgViewBase } from './view-base.ts';\nimport type { PgMaterializedView } from './view.ts';\n\nexport class PgDatabase<\n\tTQueryResult extends PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = ExtractTablesWithRelations<TFullSchema>,\n> {\n\tstatic readonly [entityKind]: string = 'PgDatabase';\n\n\tdeclare readonly _: {\n\t\treadonly schema: TSchema | undefined;\n\t\treadonly fullSchema: TFullSchema;\n\t\treadonly tableNamesMap: Record<string, string>;\n\t\treadonly session: PgSession<TQueryResult, TFullSchema, TSchema>;\n\t};\n\n\tquery: TFullSchema extends Record<string, never>\n\t\t? DrizzleTypeError<'Seems like the schema generic is missing - did you forget to add it to your DB type?'>\n\t\t: {\n\t\t\t[K in keyof TSchema]: RelationalQueryBuilder<TSchema, TSchema[K]>;\n\t\t};\n\n\tconstructor(\n\t\t/** @internal */\n\t\treadonly dialect: PgDialect,\n\t\t/** @internal */\n\t\treadonly session: PgSession<any, any, any>,\n\t\tschema: RelationalSchemaConfig<TSchema> | undefined,\n\t) {\n\t\tthis._ = schema\n\t\t\t? {\n\t\t\t\tschema: schema.schema,\n\t\t\t\tfullSchema: schema.fullSchema as TFullSchema,\n\t\t\t\ttableNamesMap: schema.tableNamesMap,\n\t\t\t\tsession,\n\t\t\t}\n\t\t\t: {\n\t\t\t\tschema: undefined,\n\t\t\t\tfullSchema: {} as TFullSchema,\n\t\t\t\ttableNamesMap: {},\n\t\t\t\tsession,\n\t\t\t};\n\t\tthis.query = {} as typeof this['query'];\n\t\tif (this._.schema) {\n\t\t\tfor (const [tableName, columns] of Object.entries(this._.schema)) {\n\t\t\t\t(this.query as PgDatabase<TQueryResult, Record<string, any>>['query'])[tableName] = new RelationalQueryBuilder(\n\t\t\t\t\tschema!.fullSchema,\n\t\t\t\t\tthis._.schema,\n\t\t\t\t\tthis._.tableNamesMap,\n\t\t\t\t\tschema!.fullSchema[tableName] as PgTable,\n\t\t\t\t\tcolumns,\n\t\t\t\t\tdialect,\n\t\t\t\t\tsession,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Creates a subquery that defines a temporary named result set as a CTE.\n\t *\n\t * It is useful for breaking down complex queries into simpler parts and for reusing the result set in subsequent parts of the query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n\t *\n\t * @param alias The alias for the subquery.\n\t *\n\t * Failure to provide an alias will result in a DrizzleTypeError, preventing the subquery from being referenced in other queries.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Create a subquery with alias 'sq' and use it in the select query\n\t * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n\t *\n\t * const result = await db.with(sq).select().from(sq);\n\t * ```\n\t *\n\t * To select arbitrary SQL values as fields in a CTE and reference them in other CTEs or in the main query, you need to add aliases to them:\n\t *\n\t * ```ts\n\t * // Select an arbitrary SQL value as a field in a CTE and reference it in the main query\n\t * const sq = db.$with('sq').as(db.select({\n\t *   name: sql<string>`upper(${users.name})`.as('name'),\n\t * })\n\t * .from(users));\n\t *\n\t * const result = await db.with(sq).select({ name: sq.name }).from(sq);\n\t * ```\n\t */\n\t$with<TAlias extends string>(alias: TAlias) {\n\t\tconst self = this;\n\t\treturn {\n\t\t\tas<TSelection extends ColumnsSelection>(\n\t\t\t\tqb: TypedQueryBuilder<TSelection> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelection>),\n\t\t\t): WithSubqueryWithSelection<TSelection, TAlias> {\n\t\t\t\tif (typeof qb === 'function') {\n\t\t\t\t\tqb = qb(new QueryBuilder(self.dialect));\n\t\t\t\t}\n\n\t\t\t\treturn new Proxy(\n\t\t\t\t\tnew WithSubquery(qb.getSQL(), qb.getSelectedFields() as SelectedFields, alias, true),\n\t\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t\t) as WithSubqueryWithSelection<TSelection, TAlias>;\n\t\t\t},\n\t\t};\n\t}\n\n\t$count(\n\t\tsource: PgTable | PgViewBase | SQL | SQLWrapper,\n\t\tfilters?: SQL<unknown>,\n\t) {\n\t\treturn new PgCountBuilder({ source, filters, session: this.session });\n\t}\n\n\t/**\n\t * Incorporates a previously defined CTE (using `$with`) into the main query.\n\t *\n\t * This method allows the main query to reference a temporary named result set.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n\t *\n\t * @param queries The CTEs to incorporate into the main query.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Define a subquery 'sq' as a CTE using $with\n\t * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n\t *\n\t * // Incorporate the CTE 'sq' into the main query and select from it\n\t * const result = await db.with(sq).select().from(sq);\n\t * ```\n\t */\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\t/**\n\t\t * Creates a select query.\n\t\t *\n\t\t * Calling this method with no arguments will select all columns from the table. Pass a selection object to specify the columns you want to select.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select}\n\t\t *\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Select all columns and all rows from the 'cars' table\n\t\t * const allCars: Car[] = await db.select().from(cars);\n\t\t *\n\t\t * // Select specific columns and all rows from the 'cars' table\n\t\t * const carsIdsAndBrands: { id: number; brand: string }[] = await db.select({\n\t\t *   id: cars.id,\n\t\t *   brand: cars.brand\n\t\t * })\n\t\t *   .from(cars);\n\t\t * ```\n\t\t *\n\t\t * Like in SQL, you can use arbitrary expressions as selection fields, not just table columns:\n\t\t *\n\t\t * ```ts\n\t\t * // Select specific columns along with expression and all rows from the 'cars' table\n\t\t * const carsIdsAndLowerNames: { id: number; lowerBrand: string }[] = await db.select({\n\t\t *   id: cars.id,\n\t\t *   lowerBrand: sql<string>`lower(${cars.brand})`,\n\t\t * })\n\t\t *   .from(cars);\n\t\t * ```\n\t\t */\n\t\tfunction select(): PgSelectBuilder<undefined>;\n\t\tfunction select<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\t\tfunction select(fields?: SelectedFields): PgSelectBuilder<SelectedFields | undefined> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Adds `distinct` expression to the select query.\n\t\t *\n\t\t * Calling this method will return only unique values. When multiple columns are selected, it returns rows with unique combinations of values in these columns.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t\t *\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t * ```ts\n\t\t * // Select all unique rows from the 'cars' table\n\t\t * await db.selectDistinct()\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.id, cars.brand, cars.color);\n\t\t *\n\t\t * // Select all unique brands from the 'cars' table\n\t\t * await db.selectDistinct({ brand: cars.brand })\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.brand);\n\t\t * ```\n\t\t */\n\t\tfunction selectDistinct(): PgSelectBuilder<undefined>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\t\tfunction selectDistinct(fields?: SelectedFields): PgSelectBuilder<SelectedFields | undefined> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Adds `distinct on` expression to the select query.\n\t\t *\n\t\t * Calling this method will specify how the unique rows are determined.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t\t *\n\t\t * @param on The expression defining uniqueness.\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t * ```ts\n\t\t * // Select the first row for each unique brand from the 'cars' table\n\t\t * await db.selectDistinctOn([cars.brand])\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.brand);\n\t\t *\n\t\t * // Selects the first occurrence of each unique car brand along with its color from the 'cars' table\n\t\t * await db.selectDistinctOn([cars.brand], { brand: cars.brand, color: cars.color })\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.brand, cars.color);\n\t\t * ```\n\t\t */\n\t\tfunction selectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined>;\n\t\tfunction selectDistinctOn<TSelection extends SelectedFields>(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields: TSelection,\n\t\t): PgSelectBuilder<TSelection>;\n\t\tfunction selectDistinctOn(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields?: SelectedFields,\n\t\t): PgSelectBuilder<SelectedFields | undefined> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t\tdistinct: { on },\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Creates an update query.\n\t\t *\n\t\t * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.\n\t\t *\n\t\t * Use `.set()` method to specify which values to update.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t\t *\n\t\t * @param table The table to update.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Update all rows in the 'cars' table\n\t\t * await db.update(cars).set({ color: 'red' });\n\t\t *\n\t\t * // Update rows with filters and conditions\n\t\t * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));\n\t\t *\n\t\t * // Update with returning clause\n\t\t * const updatedCar: Car[] = await db.update(cars)\n\t\t *   .set({ color: 'red' })\n\t\t *   .where(eq(cars.id, 1))\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction update<TTable extends PgTable>(table: TTable): PgUpdateBuilder<TTable, TQueryResult> {\n\t\t\treturn new PgUpdateBuilder(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\t/**\n\t\t * Creates an insert query.\n\t\t *\n\t\t * Calling this method will create new rows in a table. Use `.values()` method to specify which values to insert.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/insert}\n\t\t *\n\t\t * @param table The table to insert into.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Insert one row\n\t\t * await db.insert(cars).values({ brand: 'BMW' });\n\t\t *\n\t\t * // Insert multiple rows\n\t\t * await db.insert(cars).values([{ brand: 'BMW' }, { brand: 'Porsche' }]);\n\t\t *\n\t\t * // Insert with returning clause\n\t\t * const insertedCar: Car[] = await db.insert(cars)\n\t\t *   .values({ brand: 'BMW' })\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction insert<TTable extends PgTable>(table: TTable): PgInsertBuilder<TTable, TQueryResult> {\n\t\t\treturn new PgInsertBuilder(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\t/**\n\t\t * Creates a delete query.\n\t\t *\n\t\t * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t\t *\n\t\t * @param table The table to delete from.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Delete all rows in the 'cars' table\n\t\t * await db.delete(cars);\n\t\t *\n\t\t * // Delete rows with filters and conditions\n\t\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t\t *\n\t\t * // Delete with returning clause\n\t\t * const deletedCar: Car[] = await db.delete(cars)\n\t\t *   .where(eq(cars.id, 1))\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction delete_<TTable extends PgTable>(table: TTable): PgDeleteBase<TTable, TQueryResult> {\n\t\t\treturn new PgDeleteBase(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\treturn { select, selectDistinct, selectDistinctOn, update, insert, delete: delete_ };\n\t}\n\n\t/**\n\t * Creates a select query.\n\t *\n\t * Calling this method with no arguments will select all columns from the table. Pass a selection object to specify the columns you want to select.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select}\n\t *\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all columns and all rows from the 'cars' table\n\t * const allCars: Car[] = await db.select().from(cars);\n\t *\n\t * // Select specific columns and all rows from the 'cars' table\n\t * const carsIdsAndBrands: { id: number; brand: string }[] = await db.select({\n\t *   id: cars.id,\n\t *   brand: cars.brand\n\t * })\n\t *   .from(cars);\n\t * ```\n\t *\n\t * Like in SQL, you can use arbitrary expressions as selection fields, not just table columns:\n\t *\n\t * ```ts\n\t * // Select specific columns along with expression and all rows from the 'cars' table\n\t * const carsIdsAndLowerNames: { id: number; lowerBrand: string }[] = await db.select({\n\t *   id: cars.id,\n\t *   lowerBrand: sql<string>`lower(${cars.brand})`,\n\t * })\n\t *   .from(cars);\n\t * ```\n\t */\n\tselect(): PgSelectBuilder<undefined>;\n\tselect<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\tselect(fields?: SelectedFields): PgSelectBuilder<SelectedFields | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t});\n\t}\n\n\t/**\n\t * Adds `distinct` expression to the select query.\n\t *\n\t * Calling this method will return only unique values. When multiple columns are selected, it returns rows with unique combinations of values in these columns.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t *\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t * ```ts\n\t * // Select all unique rows from the 'cars' table\n\t * await db.selectDistinct()\n\t *   .from(cars)\n\t *   .orderBy(cars.id, cars.brand, cars.color);\n\t *\n\t * // Select all unique brands from the 'cars' table\n\t * await db.selectDistinct({ brand: cars.brand })\n\t *   .from(cars)\n\t *   .orderBy(cars.brand);\n\t * ```\n\t */\n\tselectDistinct(): PgSelectBuilder<undefined>;\n\tselectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\tselectDistinct(fields?: SelectedFields): PgSelectBuilder<SelectedFields | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\t/**\n\t * Adds `distinct on` expression to the select query.\n\t *\n\t * Calling this method will specify how the unique rows are determined.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t *\n\t * @param on The expression defining uniqueness.\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t * ```ts\n\t * // Select the first row for each unique brand from the 'cars' table\n\t * await db.selectDistinctOn([cars.brand])\n\t *   .from(cars)\n\t *   .orderBy(cars.brand);\n\t *\n\t * // Selects the first occurrence of each unique car brand along with its color from the 'cars' table\n\t * await db.selectDistinctOn([cars.brand], { brand: cars.brand, color: cars.color })\n\t *   .from(cars)\n\t *   .orderBy(cars.brand, cars.color);\n\t * ```\n\t */\n\tselectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined>;\n\tselectDistinctOn<TSelection extends SelectedFields>(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields: TSelection,\n\t): PgSelectBuilder<TSelection>;\n\tselectDistinctOn(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields?: SelectedFields,\n\t): PgSelectBuilder<SelectedFields | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t\tdistinct: { on },\n\t\t});\n\t}\n\n\t/**\n\t * Creates an update query.\n\t *\n\t * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.\n\t *\n\t * Use `.set()` method to specify which values to update.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t *\n\t * @param table The table to update.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Update all rows in the 'cars' table\n\t * await db.update(cars).set({ color: 'red' });\n\t *\n\t * // Update rows with filters and conditions\n\t * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));\n\t *\n\t * // Update with returning clause\n\t * const updatedCar: Car[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.id, 1))\n\t *   .returning();\n\t * ```\n\t */\n\tupdate<TTable extends PgTable>(table: TTable): PgUpdateBuilder<TTable, TQueryResult> {\n\t\treturn new PgUpdateBuilder(table, this.session, this.dialect);\n\t}\n\n\t/**\n\t * Creates an insert query.\n\t *\n\t * Calling this method will create new rows in a table. Use `.values()` method to specify which values to insert.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert}\n\t *\n\t * @param table The table to insert into.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Insert one row\n\t * await db.insert(cars).values({ brand: 'BMW' });\n\t *\n\t * // Insert multiple rows\n\t * await db.insert(cars).values([{ brand: 'BMW' }, { brand: 'Porsche' }]);\n\t *\n\t * // Insert with returning clause\n\t * const insertedCar: Car[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning();\n\t * ```\n\t */\n\tinsert<TTable extends PgTable>(table: TTable): PgInsertBuilder<TTable, TQueryResult> {\n\t\treturn new PgInsertBuilder(table, this.session, this.dialect);\n\t}\n\n\t/**\n\t * Creates a delete query.\n\t *\n\t * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t *\n\t * @param table The table to delete from.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Delete all rows in the 'cars' table\n\t * await db.delete(cars);\n\t *\n\t * // Delete rows with filters and conditions\n\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t *\n\t * // Delete with returning clause\n\t * const deletedCar: Car[] = await db.delete(cars)\n\t *   .where(eq(cars.id, 1))\n\t *   .returning();\n\t * ```\n\t */\n\tdelete<TTable extends PgTable>(table: TTable): PgDeleteBase<TTable, TQueryResult> {\n\t\treturn new PgDeleteBase(table, this.session, this.dialect);\n\t}\n\n\trefreshMaterializedView<TView extends PgMaterializedView>(view: TView): PgRefreshMaterializedView<TQueryResult> {\n\t\treturn new PgRefreshMaterializedView(view, this.session, this.dialect);\n\t}\n\n\tprotected authToken?: string;\n\n\texecute<TRow extends Record<string, unknown> = Record<string, unknown>>(\n\t\tquery: SQLWrapper | string,\n\t): PgRaw<PgQueryResultKind<TQueryResult, TRow>> {\n\t\tconst sequel = typeof query === 'string' ? sql.raw(query) : query.getSQL();\n\t\tconst builtQuery = this.dialect.sqlToQuery(sequel);\n\t\tconst prepared = this.session.prepareQuery<\n\t\t\tPreparedQueryConfig & { execute: PgQueryResultKind<TQueryResult, TRow> }\n\t\t>(\n\t\t\tbuiltQuery,\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tfalse,\n\t\t);\n\t\treturn new PgRaw(\n\t\t\t() => prepared.execute(undefined, this.authToken),\n\t\t\tsequel,\n\t\t\tbuiltQuery,\n\t\t\t(result) => prepared.mapResult(result, true),\n\t\t);\n\t}\n\n\ttransaction<T>(\n\t\ttransaction: (tx: PgTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig,\n\t): Promise<T> {\n\t\treturn this.session.transaction(transaction, config);\n\t}\n}\n\nexport type PgWithReplicas<Q> = Q & { $primary: Q };\n\nexport const withReplicas = <\n\tHKT extends PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n\tQ extends PgDatabase<\n\t\tHKT,\n\t\tTFullSchema,\n\t\tTSchema extends Record<string, unknown> ? ExtractTablesWithRelations<TFullSchema> : TSchema\n\t>,\n>(\n\tprimary: Q,\n\treplicas: [Q, ...Q[]],\n\tgetReplica: (replicas: Q[]) => Q = () => replicas[Math.floor(Math.random() * replicas.length)]!,\n): PgWithReplicas<Q> => {\n\tconst select: Q['select'] = (...args: []) => getReplica(replicas).select(...args);\n\tconst selectDistinct: Q['selectDistinct'] = (...args: []) => getReplica(replicas).selectDistinct(...args);\n\tconst selectDistinctOn: Q['selectDistinctOn'] = (...args: [any]) => getReplica(replicas).selectDistinctOn(...args);\n\tconst $with: Q['with'] = (...args: any) => getReplica(replicas).with(...args);\n\n\tconst update: Q['update'] = (...args: [any]) => primary.update(...args);\n\tconst insert: Q['insert'] = (...args: [any]) => primary.insert(...args);\n\tconst $delete: Q['delete'] = (...args: [any]) => primary.delete(...args);\n\tconst execute: Q['execute'] = (...args: [any]) => primary.execute(...args);\n\tconst transaction: Q['transaction'] = (...args: [any]) => primary.transaction(...args);\n\tconst refreshMaterializedView: Q['refreshMaterializedView'] = (...args: [any]) =>\n\t\tprimary.refreshMaterializedView(...args);\n\n\treturn {\n\t\t...primary,\n\t\tupdate,\n\t\tinsert,\n\t\tdelete: $delete,\n\t\texecute,\n\t\ttransaction,\n\t\trefreshMaterializedView,\n\t\t$primary: primary,\n\t\tselect,\n\t\tselectDistinct,\n\t\tselectDistinctOn,\n\t\twith: $with,\n\t\tget query() {\n\t\t\treturn getReplica(replicas).query;\n\t\t},\n\t};\n};\n", "import { entityKind } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgTable } from '~/pg-core/table.ts';\nimport type { SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { tracer } from '~/tracing.ts';\nimport { orderSelectedFields } from '~/utils.ts';\nimport type { PgColumn } from '../columns/common.ts';\nimport type { SelectedFieldsFlat, SelectedFieldsOrdered } from './select.types.ts';\n\nexport type PgDeleteWithout<\n\tT extends AnyPgDeleteBase,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T\n\t: Omit<\n\t\tPgDeleteBase<\n\t\t\tT['_']['table'],\n\t\t\tT['_']['queryResult'],\n\t\t\tT['_']['returning'],\n\t\t\tTDynamic,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>,\n\t\tT['_']['excludedMethods'] | K\n\t>;\n\nexport type PgDelete<\n\tTTable extends PgTable = PgTable,\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n> = PgDeleteBase<TTable, TQueryResult, TReturning, true, never>;\n\nexport interface PgDeleteConfig {\n\twhere?: SQL | undefined;\n\ttable: PgTable;\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type PgDeleteReturningAll<\n\tT extends AnyPgDeleteBase,\n\tTDynamic extends boolean,\n> = PgDeleteWithout<\n\tPgDeleteBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['table']['$inferSelect'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgDeleteReturning<\n\tT extends AnyPgDeleteBase,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFieldsFlat,\n> = PgDeleteWithout<\n\tPgDeleteBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tSelectResultFields<TSelectedFields>,\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgDeletePrepare<T extends AnyPgDeleteBase> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? PgQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type PgDeleteDynamic<T extends AnyPgDeleteBase> = PgDelete<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['returning']\n>;\n\nexport type AnyPgDeleteBase = PgDeleteBase<any, any, any, any, any>;\n\nexport interface PgDeleteBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tQueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\tdialect: 'pg';\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class PgDeleteBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\t\tSQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'PgDelete';\n\n\tprivate config: PgDeleteConfig;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, withList };\n\t}\n\n\t/**\n\t * Adds a `where` clause to the query.\n\t *\n\t * Calling this method will delete only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t *\n\t * @param where the `where` clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be deleted.\n\t *\n\t * ```ts\n\t * // Delete all cars with green color\n\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.delete(cars).where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Delete all BMW cars with a green color\n\t * await db.delete(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Delete all cars with the green or blue color\n\t * await db.delete(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): PgDeleteWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the deleted rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete#delete-with-return}\n\t *\n\t * @example\n\t * ```ts\n\t * // Delete all cars with the green color and return all fields\n\t * const deletedCars: Car[] = await db.delete(cars)\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning();\n\t *\n\t * // Delete all cars with the green color and return only their id and brand fields\n\t * const deletedCarsIdsAndBrands: { id: number, brand: string }[] = await db.delete(cars)\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning({ id: cars.id, brand: cars.brand });\n\t * ```\n\t */\n\treturning(): PgDeleteReturningAll<this, TDynamic>;\n\treturning<TSelectedFields extends SelectedFieldsFlat>(\n\t\tfields: TSelectedFields,\n\t): PgDeleteReturning<this, TDynamic, TSelectedFields>;\n\treturning(\n\t\tfields: SelectedFieldsFlat = this.config.table[Table.Symbol.Columns],\n\t): PgDeleteReturning<this, TDynamic, any> {\n\t\tthis.config.returning = orderSelectedFields<PgColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildDeleteQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgDeletePrepare<this> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery<\n\t\t\t\tPreparedQueryConfig & {\n\t\t\t\t\texecute: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t\t\t\t}\n\t\t\t>(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true);\n\t\t});\n\t}\n\n\tprepare(name: string): PgDeletePrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate authToken?: string;\n\t/** @internal */\n\tsetToken(token: string) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues, this.authToken);\n\t\t});\n\t};\n\n\t$dynamic(): PgDeleteDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n", "import { entityKind, is } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type { IndexColumn } from '~/pg-core/indexes.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgTable, TableConfig } from '~/pg-core/table.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Placeholder, Query, SQLWrapper } from '~/sql/sql.ts';\nimport { Param, SQL, sql } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport type { InferInsertModel } from '~/table.ts';\nimport { Columns, Table } from '~/table.ts';\nimport { tracer } from '~/tracing.ts';\nimport { haveSameKeys, mapUpdateSet, orderSelectedFields } from '~/utils.ts';\nimport type { AnyPgColumn, PgColumn } from '../columns/common.ts';\nimport { QueryBuilder } from './query-builder.ts';\nimport type { SelectedFieldsFlat, SelectedFieldsOrdered } from './select.types.ts';\nimport type { PgUpdateSetSource } from './update.ts';\n\nexport interface PgInsertConfig<TTable extends PgTable = PgTable> {\n\ttable: TTable;\n\tvalues: Record<string, Param | SQL>[] | PgInsertSelectQueryBuilder<TTable> | SQL;\n\twithList?: Subquery[];\n\tonConflict?: SQL;\n\treturning?: SelectedFieldsOrdered;\n\tselect?: boolean;\n\toverridingSystemValue_?: boolean;\n}\n\nexport type PgInsertValue<TTable extends PgTable<TableConfig>, OverrideT extends boolean = false> =\n\t& {\n\t\t[Key in keyof InferInsertModel<TTable, { dbColumnNames: false; override: OverrideT }>]:\n\t\t\t| InferInsertModel<TTable, { dbColumnNames: false; override: OverrideT }>[Key]\n\t\t\t| SQL\n\t\t\t| Placeholder;\n\t}\n\t& {};\n\nexport type PgInsertSelectQueryBuilder<TTable extends PgTable> = TypedQueryBuilder<\n\t{ [K in keyof TTable['$inferInsert']]: AnyPgColumn | SQL | SQL.Aliased | TTable['$inferInsert'][K] }\n>;\n\nexport class PgInsertBuilder<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tOverrideT extends boolean = false,\n> {\n\tstatic readonly [entityKind]: string = 'PgInsertBuilder';\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\tprivate withList?: Subquery[],\n\t\tprivate overridingSystemValue_?: boolean,\n\t) {}\n\n\tprivate authToken?: string;\n\t/** @internal */\n\tsetToken(token: string) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\toverridingSystemValue(): Omit<PgInsertBuilder<TTable, TQueryResult, true>, 'overridingSystemValue'> {\n\t\tthis.overridingSystemValue_ = true;\n\t\treturn this as any;\n\t}\n\n\tvalues(value: PgInsertValue<TTable, OverrideT>): PgInsertBase<TTable, TQueryResult>;\n\tvalues(values: PgInsertValue<TTable, OverrideT>[]): PgInsertBase<TTable, TQueryResult>;\n\tvalues(\n\t\tvalues: PgInsertValue<TTable, OverrideT> | PgInsertValue<TTable, OverrideT>[],\n\t): PgInsertBase<TTable, TQueryResult> {\n\t\tvalues = Array.isArray(values) ? values : [values];\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('values() must be called with at least one value');\n\t\t}\n\t\tconst mappedValues = values.map((entry) => {\n\t\t\tconst result: Record<string, Param | SQL> = {};\n\t\t\tconst cols = this.table[Table.Symbol.Columns];\n\t\t\tfor (const colKey of Object.keys(entry)) {\n\t\t\t\tconst colValue = entry[colKey as keyof typeof entry];\n\t\t\t\tresult[colKey] = is(colValue, SQL) ? colValue : new Param(colValue, cols[colKey]);\n\t\t\t}\n\t\t\treturn result;\n\t\t});\n\n\t\treturn this.authToken === undefined\n\t\t\t? new PgInsertBase(\n\t\t\t\tthis.table,\n\t\t\t\tmappedValues,\n\t\t\t\tthis.session,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.withList,\n\t\t\t\tfalse,\n\t\t\t\tthis.overridingSystemValue_,\n\t\t\t)\n\t\t\t: new PgInsertBase(\n\t\t\t\tthis.table,\n\t\t\t\tmappedValues,\n\t\t\t\tthis.session,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.withList,\n\t\t\t\tfalse,\n\t\t\t\tthis.overridingSystemValue_,\n\t\t\t).setToken(this.authToken) as any;\n\t}\n\n\tselect(selectQuery: (qb: QueryBuilder) => PgInsertSelectQueryBuilder<TTable>): PgInsertBase<TTable, TQueryResult>;\n\tselect(selectQuery: (qb: QueryBuilder) => SQL): PgInsertBase<TTable, TQueryResult>;\n\tselect(selectQuery: SQL): PgInsertBase<TTable, TQueryResult>;\n\tselect(selectQuery: PgInsertSelectQueryBuilder<TTable>): PgInsertBase<TTable, TQueryResult>;\n\tselect(\n\t\tselectQuery:\n\t\t\t| SQL\n\t\t\t| PgInsertSelectQueryBuilder<TTable>\n\t\t\t| ((qb: QueryBuilder) => PgInsertSelectQueryBuilder<TTable> | SQL),\n\t): PgInsertBase<TTable, TQueryResult> {\n\t\tconst select = typeof selectQuery === 'function' ? selectQuery(new QueryBuilder()) : selectQuery;\n\n\t\tif (\n\t\t\t!is(select, SQL)\n\t\t\t&& !haveSameKeys(this.table[Columns], select._.selectedFields)\n\t\t) {\n\t\t\tthrow new Error(\n\t\t\t\t'Insert select error: selected fields are not the same or are in a different order compared to the table definition',\n\t\t\t);\n\t\t}\n\n\t\treturn new PgInsertBase(this.table, select, this.session, this.dialect, this.withList, true);\n\t}\n}\n\nexport type PgInsertWithout<T extends AnyPgInsert, TDynamic extends boolean, K extends keyof T & string> =\n\tTDynamic extends true ? T\n\t\t: Omit<\n\t\t\tPgInsertBase<\n\t\t\t\tT['_']['table'],\n\t\t\t\tT['_']['queryResult'],\n\t\t\t\tT['_']['returning'],\n\t\t\t\tTDynamic,\n\t\t\t\tT['_']['excludedMethods'] | K\n\t\t\t>,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>;\n\nexport type PgInsertReturning<\n\tT extends AnyPgInsert,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFieldsFlat,\n> = PgInsertBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tSelectResultFields<TSelectedFields>,\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\nexport type PgInsertReturningAll<T extends AnyPgInsert, TDynamic extends boolean> = PgInsertBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['table']['$inferSelect'],\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\nexport interface PgInsertOnConflictDoUpdateConfig<T extends AnyPgInsert> {\n\ttarget: IndexColumn | IndexColumn[];\n\t/** @deprecated use either `targetWhere` or `setWhere` */\n\twhere?: SQL;\n\t// TODO: add tests for targetWhere and setWhere\n\ttargetWhere?: SQL;\n\tsetWhere?: SQL;\n\tset: PgUpdateSetSource<T['_']['table']>;\n}\n\nexport type PgInsertPrepare<T extends AnyPgInsert> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? PgQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type PgInsertDynamic<T extends AnyPgInsert> = PgInsert<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['returning']\n>;\n\nexport type AnyPgInsert = PgInsertBase<any, any, any, any, any>;\n\nexport type PgInsert<\n\tTTable extends PgTable = PgTable,\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n> = PgInsertBase<TTable, TQueryResult, TReturning, true, never>;\n\nexport interface PgInsertBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tQueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class PgInsertBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\t\tSQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'PgInsert';\n\n\tprivate config: PgInsertConfig<TTable>;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tvalues: PgInsertConfig['values'],\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\twithList?: Subquery[],\n\t\tselect?: boolean,\n\t\toverridingSystemValue_?: boolean,\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, values: values as any, withList, select, overridingSystemValue_ };\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the inserted rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#insert-returning}\n\t *\n\t * @example\n\t * ```ts\n\t * // Insert one row and return all fields\n\t * const insertedCar: Car[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning();\n\t *\n\t * // Insert one row and return only the id\n\t * const insertedCarId: { id: number }[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning({ id: cars.id });\n\t * ```\n\t */\n\treturning(): PgInsertWithout<PgInsertReturningAll<this, TDynamic>, TDynamic, 'returning'>;\n\treturning<TSelectedFields extends SelectedFieldsFlat>(\n\t\tfields: TSelectedFields,\n\t): PgInsertWithout<PgInsertReturning<this, TDynamic, TSelectedFields>, TDynamic, 'returning'>;\n\treturning(\n\t\tfields: SelectedFieldsFlat = this.config.table[Table.Symbol.Columns],\n\t): PgInsertWithout<AnyPgInsert, TDynamic, 'returning'> {\n\t\tthis.config.returning = orderSelectedFields<PgColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `on conflict do nothing` clause to the query.\n\t *\n\t * Calling this method simply avoids inserting a row as its alternative action.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#on-conflict-do-nothing}\n\t *\n\t * @param config The `target` and `where` clauses.\n\t *\n\t * @example\n\t * ```ts\n\t * // Insert one row and cancel the insert if there's a conflict\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoNothing();\n\t *\n\t * // Explicitly specify conflict target\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoNothing({ target: cars.id });\n\t * ```\n\t */\n\tonConflictDoNothing(\n\t\tconfig: { target?: IndexColumn | IndexColumn[]; where?: SQL } = {},\n\t): PgInsertWithout<this, TDynamic, 'onConflictDoNothing' | 'onConflictDoUpdate'> {\n\t\tif (config.target === undefined) {\n\t\t\tthis.config.onConflict = sql`do nothing`;\n\t\t} else {\n\t\t\tlet targetColumn = '';\n\t\t\ttargetColumn = Array.isArray(config.target)\n\t\t\t\t? config.target.map((it) => this.dialect.escapeName(this.dialect.casing.getColumnCasing(it))).join(',')\n\t\t\t\t: this.dialect.escapeName(this.dialect.casing.getColumnCasing(config.target));\n\n\t\t\tconst whereSql = config.where ? sql` where ${config.where}` : undefined;\n\t\t\tthis.config.onConflict = sql`(${sql.raw(targetColumn)})${whereSql} do nothing`;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `on conflict do update` clause to the query.\n\t *\n\t * Calling this method will update the existing row that conflicts with the row proposed for insertion as its alternative action.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#upserts-and-conflicts}\n\t *\n\t * @param config The `target`, `set` and `where` clauses.\n\t *\n\t * @example\n\t * ```ts\n\t * // Update the row if there's a conflict\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoUpdate({\n\t *     target: cars.id,\n\t *     set: { brand: 'Porsche' }\n\t *   });\n\t *\n\t * // Upsert with 'where' clause\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoUpdate({\n\t *     target: cars.id,\n\t *     set: { brand: 'newBMW' },\n\t *     targetWhere: sql`${cars.createdAt} > '2023-01-01'::date`,\n\t *   });\n\t * ```\n\t */\n\tonConflictDoUpdate(\n\t\tconfig: PgInsertOnConflictDoUpdateConfig<this>,\n\t): PgInsertWithout<this, TDynamic, 'onConflictDoNothing' | 'onConflictDoUpdate'> {\n\t\tif (config.where && (config.targetWhere || config.setWhere)) {\n\t\t\tthrow new Error(\n\t\t\t\t'You cannot use both \"where\" and \"targetWhere\"/\"setWhere\" at the same time - \"where\" is deprecated, use \"targetWhere\" or \"setWhere\" instead.',\n\t\t\t);\n\t\t}\n\t\tconst whereSql = config.where ? sql` where ${config.where}` : undefined;\n\t\tconst targetWhereSql = config.targetWhere ? sql` where ${config.targetWhere}` : undefined;\n\t\tconst setWhereSql = config.setWhere ? sql` where ${config.setWhere}` : undefined;\n\t\tconst setSql = this.dialect.buildUpdateSet(this.config.table, mapUpdateSet(this.config.table, config.set));\n\t\tlet targetColumn = '';\n\t\ttargetColumn = Array.isArray(config.target)\n\t\t\t? config.target.map((it) => this.dialect.escapeName(this.dialect.casing.getColumnCasing(it))).join(',')\n\t\t\t: this.dialect.escapeName(this.dialect.casing.getColumnCasing(config.target));\n\t\tthis.config.onConflict = sql`(${\n\t\t\tsql.raw(targetColumn)\n\t\t})${targetWhereSql} do update set ${setSql}${whereSql}${setWhereSql}`;\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildInsertQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgInsertPrepare<this> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery<\n\t\t\t\tPreparedQueryConfig & {\n\t\t\t\t\texecute: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t\t\t\t}\n\t\t\t>(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true);\n\t\t});\n\t}\n\n\tprepare(name: string): PgInsertPrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate authToken?: string;\n\t/** @internal */\n\tsetToken(token: string) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues, this.authToken);\n\t\t});\n\t};\n\n\t$dynamic(): PgInsertDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n", "import { entityKind, is } from '~/entity.ts';\nimport type { PgDialectConfig } from '~/pg-core/dialect.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, SQLWrapper } from '~/sql/sql.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport type { PgColumn } from '../columns/index.ts';\nimport type { WithSubqueryWithSelection } from '../subquery.ts';\nimport { PgSelectBuilder } from './select.ts';\nimport type { SelectedFields } from './select.types.ts';\n\nexport class QueryBuilder {\n\tstatic readonly [entityKind]: string = 'PgQueryBuilder';\n\n\tprivate dialect: PgDialect | undefined;\n\tprivate dialectConfig: PgDialectConfig | undefined;\n\n\tconstructor(dialect?: PgDialect | PgDialectConfig) {\n\t\tthis.dialect = is(dialect, PgDialect) ? dialect : undefined;\n\t\tthis.dialectConfig = is(dialect, PgDialect) ? undefined : dialect;\n\t}\n\n\t$with<TAlias extends string>(alias: TAlias) {\n\t\tconst queryBuilder = this;\n\n\t\treturn {\n\t\t\tas<TSelection extends ColumnsSelection>(\n\t\t\t\tqb: TypedQueryBuilder<TSelection> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelection>),\n\t\t\t): WithSubqueryWithSelection<TSelection, TAlias> {\n\t\t\t\tif (typeof qb === 'function') {\n\t\t\t\t\tqb = qb(queryBuilder);\n\t\t\t\t}\n\n\t\t\t\treturn new Proxy(\n\t\t\t\t\tnew WithSubquery(qb.getSQL(), qb.getSelectedFields() as SelectedFields, alias, true),\n\t\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t\t) as WithSubqueryWithSelection<TSelection, TAlias>;\n\t\t\t},\n\t\t};\n\t}\n\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\tfunction select(): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): PgSelectBuilder<TSelection | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinct(): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction selectDistinct(fields?: SelectedFields): PgSelectBuilder<SelectedFields | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction selectDistinctOn<TSelection extends SelectedFields>(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields: TSelection,\n\t\t): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction selectDistinctOn(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields?: SelectedFields,\n\t\t): PgSelectBuilder<SelectedFields | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\tdistinct: { on },\n\t\t\t});\n\t\t}\n\n\t\treturn { select, selectDistinct, selectDistinctOn };\n\t}\n\n\tselect(): PgSelectBuilder<undefined, 'qb'>;\n\tselect<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\tselect<TSelection extends SelectedFields>(fields?: TSelection): PgSelectBuilder<TSelection | undefined, 'qb'> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t});\n\t}\n\n\tselectDistinct(): PgSelectBuilder<undefined>;\n\tselectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\tselectDistinct(fields?: SelectedFields): PgSelectBuilder<SelectedFields | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\tselectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined>;\n\tselectDistinctOn<TSelection extends SelectedFields>(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields: TSelection,\n\t): PgSelectBuilder<TSelection>;\n\tselectDistinctOn(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields?: SelectedFields,\n\t): PgSelectBuilder<SelectedFields | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: { on },\n\t\t});\n\t}\n\n\t// Lazy load dialect to avoid circular dependency\n\tprivate getDialect() {\n\t\tif (!this.dialect) {\n\t\t\tthis.dialect = new PgDialect(this.dialectConfig);\n\t\t}\n\n\t\treturn this.dialect;\n\t}\n}\n", "import { aliasedTable, aliasedTableColumn, mapColumnsInAliasedSQLToAlias, mapColumnsInSQLToAlias } from '~/alias.ts';\nimport { CasingCache } from '~/casing.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport { DrizzleError } from '~/errors.ts';\nimport type { MigrationConfig, MigrationMeta } from '~/migrator.ts';\nimport {\n\tPgColumn,\n\tPgDate,\n\tPgDateString,\n\tPgJson,\n\tPgJsonb,\n\tPgNumeric,\n\tPgTime,\n\tPgTimestamp,\n\tPgTimestampString,\n\tPgUUID,\n} from '~/pg-core/columns/index.ts';\nimport type {\n\tAnyPgSelectQueryBuilder,\n\tPgDeleteConfig,\n\tPgInsertConfig,\n\tPgSelectJoinConfig,\n\tPgUpdateConfig,\n} from '~/pg-core/query-builders/index.ts';\nimport type { PgSelectConfig, SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport { PgTable } from '~/pg-core/table.ts';\nimport {\n\ttype BuildRelationalQueryResult,\n\ttype DBQueryConfig,\n\tgetOperators,\n\tgetOrderByOperators,\n\tMany,\n\tnormalizeRelation,\n\tOne,\n\ttype Relation,\n\ttype TableRelationalConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { and, eq, View } from '~/sql/index.ts';\nimport {\n\ttype DriverValueEncoder,\n\ttype Name,\n\tParam,\n\ttype QueryTypingsValue,\n\ttype QueryWithTypings,\n\tSQL,\n\tsql,\n\ttype SQLChunk,\n} from '~/sql/sql.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { getTableName, getTableUniqueName, Table } from '~/table.ts';\nimport { type Casing, orderSelectedFields, type UpdateSet } from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type { PgSession } from './session.ts';\nimport { PgViewBase } from './view-base.ts';\nimport type { PgMaterializedView } from './view.ts';\n\nexport interface PgDialectConfig {\n\tcasing?: Casing;\n}\n\nexport class PgDialect {\n\tstatic readonly [entityKind]: string = 'PgDialect';\n\n\t/** @internal */\n\treadonly casing: CasingCache;\n\n\tconstructor(config?: PgDialectConfig) {\n\t\tthis.casing = new CasingCache(config?.casing);\n\t}\n\n\tasync migrate(migrations: MigrationMeta[], session: PgSession, config: string | MigrationConfig): Promise<void> {\n\t\tconst migrationsTable = typeof config === 'string'\n\t\t\t? '__drizzle_migrations'\n\t\t\t: config.migrationsTable ?? '__drizzle_migrations';\n\t\tconst migrationsSchema = typeof config === 'string' ? 'drizzle' : config.migrationsSchema ?? 'drizzle';\n\t\tconst migrationTableCreate = sql`\n\t\t\tCREATE TABLE IF NOT EXISTS ${sql.identifier(migrationsSchema)}.${sql.identifier(migrationsTable)} (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at bigint\n\t\t\t)\n\t\t`;\n\t\tawait session.execute(sql`CREATE SCHEMA IF NOT EXISTS ${sql.identifier(migrationsSchema)}`);\n\t\tawait session.execute(migrationTableCreate);\n\n\t\tconst dbMigrations = await session.all<{ id: number; hash: string; created_at: string }>(\n\t\t\tsql`select id, hash, created_at from ${sql.identifier(migrationsSchema)}.${\n\t\t\t\tsql.identifier(migrationsTable)\n\t\t\t} order by created_at desc limit 1`,\n\t\t);\n\n\t\tconst lastDbMigration = dbMigrations[0];\n\t\tawait session.transaction(async (tx) => {\n\t\t\tfor await (const migration of migrations) {\n\t\t\t\tif (\n\t\t\t\t\t!lastDbMigration\n\t\t\t\t\t|| Number(lastDbMigration.created_at) < migration.folderMillis\n\t\t\t\t) {\n\t\t\t\t\tfor (const stmt of migration.sql) {\n\t\t\t\t\t\tawait tx.execute(sql.raw(stmt));\n\t\t\t\t\t}\n\t\t\t\t\tawait tx.execute(\n\t\t\t\t\t\tsql`insert into ${sql.identifier(migrationsSchema)}.${\n\t\t\t\t\t\t\tsql.identifier(migrationsTable)\n\t\t\t\t\t\t} (\"hash\", \"created_at\") values(${migration.hash}, ${migration.folderMillis})`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n\n\tescapeName(name: string): string {\n\t\treturn `\"${name}\"`;\n\t}\n\n\tescapeParam(num: number): string {\n\t\treturn `$${num + 1}`;\n\t}\n\n\tescapeString(str: string): string {\n\t\treturn `'${str.replace(/'/g, \"''\")}'`;\n\t}\n\n\tprivate buildWithCTE(queries: Subquery[] | undefined): SQL | undefined {\n\t\tif (!queries?.length) return undefined;\n\n\t\tconst withSqlChunks = [sql`with `];\n\t\tfor (const [i, w] of queries.entries()) {\n\t\t\twithSqlChunks.push(sql`${sql.identifier(w._.alias)} as (${w._.sql})`);\n\t\t\tif (i < queries.length - 1) {\n\t\t\t\twithSqlChunks.push(sql`, `);\n\t\t\t}\n\t\t}\n\t\twithSqlChunks.push(sql` `);\n\t\treturn sql.join(withSqlChunks);\n\t}\n\n\tbuildDeleteQuery({ table, where, returning, withList }: PgDeleteConfig): SQL {\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}`\n\t\t\t: undefined;\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\treturn sql`${withSql}delete from ${table}${whereSql}${returningSql}`;\n\t}\n\n\tbuildUpdateSet(table: PgTable, set: UpdateSet): SQL {\n\t\tconst tableColumns = table[Table.Symbol.Columns];\n\n\t\tconst columnNames = Object.keys(tableColumns).filter((colName) =>\n\t\t\tset[colName] !== undefined || tableColumns[colName]?.onUpdateFn !== undefined\n\t\t);\n\n\t\tconst setSize = columnNames.length;\n\t\treturn sql.join(columnNames.flatMap((colName, i) => {\n\t\t\tconst col = tableColumns[colName]!;\n\n\t\t\tconst value = set[colName] ?? sql.param(col.onUpdateFn!(), col);\n\t\t\tconst res = sql`${sql.identifier(this.casing.getColumnCasing(col))} = ${value}`;\n\n\t\t\tif (i < setSize - 1) {\n\t\t\t\treturn [res, sql.raw(', ')];\n\t\t\t}\n\t\t\treturn [res];\n\t\t}));\n\t}\n\n\tbuildUpdateQuery({ table, set, where, returning, withList, from, joins }: PgUpdateConfig): SQL {\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst tableName = table[PgTable.Symbol.Name];\n\t\tconst tableSchema = table[PgTable.Symbol.Schema];\n\t\tconst origTableName = table[PgTable.Symbol.OriginalName];\n\t\tconst alias = tableName === origTableName ? undefined : tableName;\n\t\tconst tableSql = sql`${tableSchema ? sql`${sql.identifier(tableSchema)}.` : undefined}${\n\t\t\tsql.identifier(origTableName)\n\t\t}${alias && sql` ${sql.identifier(alias)}`}`;\n\n\t\tconst setSql = this.buildUpdateSet(table, set);\n\n\t\tconst fromSql = from && sql.join([sql.raw(' from '), this.buildFromTable(from)]);\n\n\t\tconst joinsSql = this.buildJoins(joins);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: !from })}`\n\t\t\t: undefined;\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\treturn sql`${withSql}update ${tableSql} set ${setSql}${fromSql}${joinsSql}${whereSql}${returningSql}`;\n\t}\n\n\t/**\n\t * Builds selection SQL with provided fields/expressions\n\t *\n\t * Examples:\n\t *\n\t * `select <selection> from`\n\t *\n\t * `insert ... returning <selection>`\n\t *\n\t * If `isSingleTable` is true, then columns won't be prefixed with table name\n\t */\n\tprivate buildSelection(\n\t\tfields: SelectedFieldsOrdered,\n\t\t{ isSingleTable = false }: { isSingleTable?: boolean } = {},\n\t): SQL {\n\t\tconst columnsLen = fields.length;\n\n\t\tconst chunks = fields\n\t\t\t.flatMap(({ field }, i) => {\n\t\t\t\tconst chunk: SQLChunk[] = [];\n\n\t\t\t\tif (is(field, SQL.Aliased) && field.isSelectionField) {\n\t\t\t\t\tchunk.push(sql.identifier(field.fieldAlias));\n\t\t\t\t} else if (is(field, SQL.Aliased) || is(field, SQL)) {\n\t\t\t\t\tconst query = is(field, SQL.Aliased) ? field.sql : field;\n\n\t\t\t\t\tif (isSingleTable) {\n\t\t\t\t\t\tchunk.push(\n\t\t\t\t\t\t\tnew SQL(\n\t\t\t\t\t\t\t\tquery.queryChunks.map((c) => {\n\t\t\t\t\t\t\t\t\tif (is(c, PgColumn)) {\n\t\t\t\t\t\t\t\t\t\treturn sql.identifier(this.casing.getColumnCasing(c));\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn c;\n\t\t\t\t\t\t\t\t}),\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tchunk.push(query);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (is(field, SQL.Aliased)) {\n\t\t\t\t\t\tchunk.push(sql` as ${sql.identifier(field.fieldAlias)}`);\n\t\t\t\t\t}\n\t\t\t\t} else if (is(field, Column)) {\n\t\t\t\t\tif (isSingleTable) {\n\t\t\t\t\t\tchunk.push(sql.identifier(this.casing.getColumnCasing(field)));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tchunk.push(field);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (i < columnsLen - 1) {\n\t\t\t\t\tchunk.push(sql`, `);\n\t\t\t\t}\n\n\t\t\t\treturn chunk;\n\t\t\t});\n\n\t\treturn sql.join(chunks);\n\t}\n\n\tprivate buildJoins(joins: PgSelectJoinConfig[] | undefined): SQL | undefined {\n\t\tif (!joins || joins.length === 0) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tconst joinsArray: SQL[] = [];\n\n\t\tfor (const [index, joinMeta] of joins.entries()) {\n\t\t\tif (index === 0) {\n\t\t\t\tjoinsArray.push(sql` `);\n\t\t\t}\n\t\t\tconst table = joinMeta.table;\n\t\t\tconst lateralSql = joinMeta.lateral ? sql` lateral` : undefined;\n\n\t\t\tif (is(table, PgTable)) {\n\t\t\t\tconst tableName = table[PgTable.Symbol.Name];\n\t\t\t\tconst tableSchema = table[PgTable.Symbol.Schema];\n\t\t\t\tconst origTableName = table[PgTable.Symbol.OriginalName];\n\t\t\t\tconst alias = tableName === origTableName ? undefined : joinMeta.alias;\n\t\t\t\tjoinsArray.push(\n\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${\n\t\t\t\t\t\ttableSchema ? sql`${sql.identifier(tableSchema)}.` : undefined\n\t\t\t\t\t}${sql.identifier(origTableName)}${alias && sql` ${sql.identifier(alias)}`} on ${joinMeta.on}`,\n\t\t\t\t);\n\t\t\t} else if (is(table, View)) {\n\t\t\t\tconst viewName = table[ViewBaseConfig].name;\n\t\t\t\tconst viewSchema = table[ViewBaseConfig].schema;\n\t\t\t\tconst origViewName = table[ViewBaseConfig].originalName;\n\t\t\t\tconst alias = viewName === origViewName ? undefined : joinMeta.alias;\n\t\t\t\tjoinsArray.push(\n\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${\n\t\t\t\t\t\tviewSchema ? sql`${sql.identifier(viewSchema)}.` : undefined\n\t\t\t\t\t}${sql.identifier(origViewName)}${alias && sql` ${sql.identifier(alias)}`} on ${joinMeta.on}`,\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tjoinsArray.push(\n\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${table} on ${joinMeta.on}`,\n\t\t\t\t);\n\t\t\t}\n\t\t\tif (index < joins.length - 1) {\n\t\t\t\tjoinsArray.push(sql` `);\n\t\t\t}\n\t\t}\n\n\t\treturn sql.join(joinsArray);\n\t}\n\n\tprivate buildFromTable(\n\t\ttable: SQL | Subquery | PgViewBase | PgTable | undefined,\n\t): SQL | Subquery | PgViewBase | PgTable | undefined {\n\t\tif (is(table, Table) && table[Table.Symbol.OriginalName] !== table[Table.Symbol.Name]) {\n\t\t\tlet fullName = sql`${sql.identifier(table[Table.Symbol.OriginalName])}`;\n\t\t\tif (table[Table.Symbol.Schema]) {\n\t\t\t\tfullName = sql`${sql.identifier(table[Table.Symbol.Schema]!)}.${fullName}`;\n\t\t\t}\n\t\t\treturn sql`${fullName} ${sql.identifier(table[Table.Symbol.Name])}`;\n\t\t}\n\n\t\treturn table;\n\t}\n\n\tbuildSelectQuery(\n\t\t{\n\t\t\twithList,\n\t\t\tfields,\n\t\t\tfieldsFlat,\n\t\t\twhere,\n\t\t\thaving,\n\t\t\ttable,\n\t\t\tjoins,\n\t\t\torderBy,\n\t\t\tgroupBy,\n\t\t\tlimit,\n\t\t\toffset,\n\t\t\tlockingClause,\n\t\t\tdistinct,\n\t\t\tsetOperators,\n\t\t}: PgSelectConfig,\n\t): SQL {\n\t\tconst fieldsList = fieldsFlat ?? orderSelectedFields<PgColumn>(fields);\n\t\tfor (const f of fieldsList) {\n\t\t\tif (\n\t\t\t\tis(f.field, Column)\n\t\t\t\t&& getTableName(f.field.table)\n\t\t\t\t\t!== (is(table, Subquery)\n\t\t\t\t\t\t? table._.alias\n\t\t\t\t\t\t: is(table, PgViewBase)\n\t\t\t\t\t\t? table[ViewBaseConfig].name\n\t\t\t\t\t\t: is(table, SQL)\n\t\t\t\t\t\t? undefined\n\t\t\t\t\t\t: getTableName(table))\n\t\t\t\t&& !((table) =>\n\t\t\t\t\tjoins?.some(({ alias }) =>\n\t\t\t\t\t\talias === (table[Table.Symbol.IsAlias] ? getTableName(table) : table[Table.Symbol.BaseName])\n\t\t\t\t\t))(f.field.table)\n\t\t\t) {\n\t\t\t\tconst tableName = getTableName(f.field.table);\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Your \"${\n\t\t\t\t\t\tf.path.join('->')\n\t\t\t\t\t}\" field references a column \"${tableName}\".\"${f.field.name}\", but the table \"${tableName}\" is not part of the query! Did you forget to join it?`,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tconst isSingleTable = !joins || joins.length === 0;\n\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tlet distinctSql: SQL | undefined;\n\t\tif (distinct) {\n\t\t\tdistinctSql = distinct === true ? sql` distinct` : sql` distinct on (${sql.join(distinct.on, sql`, `)})`;\n\t\t}\n\n\t\tconst selection = this.buildSelection(fieldsList, { isSingleTable });\n\n\t\tconst tableSql = this.buildFromTable(table);\n\n\t\tconst joinsSql = this.buildJoins(joins);\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\tconst havingSql = having ? sql` having ${having}` : undefined;\n\n\t\tlet orderBySql;\n\t\tif (orderBy && orderBy.length > 0) {\n\t\t\torderBySql = sql` order by ${sql.join(orderBy, sql`, `)}`;\n\t\t}\n\n\t\tlet groupBySql;\n\t\tif (groupBy && groupBy.length > 0) {\n\t\t\tgroupBySql = sql` group by ${sql.join(groupBy, sql`, `)}`;\n\t\t}\n\n\t\tconst limitSql = typeof limit === 'object' || (typeof limit === 'number' && limit >= 0)\n\t\t\t? sql` limit ${limit}`\n\t\t\t: undefined;\n\n\t\tconst offsetSql = offset ? sql` offset ${offset}` : undefined;\n\n\t\tconst lockingClauseSql = sql.empty();\n\t\tif (lockingClause) {\n\t\t\tconst clauseSql = sql` for ${sql.raw(lockingClause.strength)}`;\n\t\t\tif (lockingClause.config.of) {\n\t\t\t\tclauseSql.append(\n\t\t\t\t\tsql` of ${\n\t\t\t\t\t\tsql.join(\n\t\t\t\t\t\t\tArray.isArray(lockingClause.config.of) ? lockingClause.config.of : [lockingClause.config.of],\n\t\t\t\t\t\t\tsql`, `,\n\t\t\t\t\t\t)\n\t\t\t\t\t}`,\n\t\t\t\t);\n\t\t\t}\n\t\t\tif (lockingClause.config.noWait) {\n\t\t\t\tclauseSql.append(sql` no wait`);\n\t\t\t} else if (lockingClause.config.skipLocked) {\n\t\t\t\tclauseSql.append(sql` skip locked`);\n\t\t\t}\n\t\t\tlockingClauseSql.append(clauseSql);\n\t\t}\n\t\tconst finalQuery =\n\t\t\tsql`${withSql}select${distinctSql} ${selection} from ${tableSql}${joinsSql}${whereSql}${groupBySql}${havingSql}${orderBySql}${limitSql}${offsetSql}${lockingClauseSql}`;\n\n\t\tif (setOperators.length > 0) {\n\t\t\treturn this.buildSetOperations(finalQuery, setOperators);\n\t\t}\n\n\t\treturn finalQuery;\n\t}\n\n\tbuildSetOperations(leftSelect: SQL, setOperators: PgSelectConfig['setOperators']): SQL {\n\t\tconst [setOperator, ...rest] = setOperators;\n\n\t\tif (!setOperator) {\n\t\t\tthrow new Error('Cannot pass undefined values to any set operator');\n\t\t}\n\n\t\tif (rest.length === 0) {\n\t\t\treturn this.buildSetOperationQuery({ leftSelect, setOperator });\n\t\t}\n\n\t\t// Some recursive magic here\n\t\treturn this.buildSetOperations(\n\t\t\tthis.buildSetOperationQuery({ leftSelect, setOperator }),\n\t\t\trest,\n\t\t);\n\t}\n\n\tbuildSetOperationQuery({\n\t\tleftSelect,\n\t\tsetOperator: { type, isAll, rightSelect, limit, orderBy, offset },\n\t}: { leftSelect: SQL; setOperator: PgSelectConfig['setOperators'][number] }): SQL {\n\t\tconst leftChunk = sql`(${leftSelect.getSQL()}) `;\n\t\tconst rightChunk = sql`(${rightSelect.getSQL()})`;\n\n\t\tlet orderBySql;\n\t\tif (orderBy && orderBy.length > 0) {\n\t\t\tconst orderByValues: (SQL<unknown> | Name)[] = [];\n\n\t\t\t// The next bit is necessary because the sql operator replaces ${table.column} with `table`.`column`\n\t\t\t// which is invalid Sql syntax, Table from one of the SELECTs cannot be used in global ORDER clause\n\t\t\tfor (const singleOrderBy of orderBy) {\n\t\t\t\tif (is(singleOrderBy, PgColumn)) {\n\t\t\t\t\torderByValues.push(sql.identifier(singleOrderBy.name));\n\t\t\t\t} else if (is(singleOrderBy, SQL)) {\n\t\t\t\t\tfor (let i = 0; i < singleOrderBy.queryChunks.length; i++) {\n\t\t\t\t\t\tconst chunk = singleOrderBy.queryChunks[i];\n\n\t\t\t\t\t\tif (is(chunk, PgColumn)) {\n\t\t\t\t\t\t\tsingleOrderBy.queryChunks[i] = sql.identifier(chunk.name);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\torderByValues.push(sql`${singleOrderBy}`);\n\t\t\t\t} else {\n\t\t\t\t\torderByValues.push(sql`${singleOrderBy}`);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\torderBySql = sql` order by ${sql.join(orderByValues, sql`, `)} `;\n\t\t}\n\n\t\tconst limitSql = typeof limit === 'object' || (typeof limit === 'number' && limit >= 0)\n\t\t\t? sql` limit ${limit}`\n\t\t\t: undefined;\n\n\t\tconst operatorChunk = sql.raw(`${type} ${isAll ? 'all ' : ''}`);\n\n\t\tconst offsetSql = offset ? sql` offset ${offset}` : undefined;\n\n\t\treturn sql`${leftChunk}${operatorChunk}${rightChunk}${orderBySql}${limitSql}${offsetSql}`;\n\t}\n\n\tbuildInsertQuery(\n\t\t{ table, values: valuesOrSelect, onConflict, returning, withList, select, overridingSystemValue_ }: PgInsertConfig,\n\t): SQL {\n\t\tconst valuesSqlList: ((SQLChunk | SQL)[] | SQL)[] = [];\n\t\tconst columns: Record<string, PgColumn> = table[Table.Symbol.Columns];\n\n\t\tconst colEntries: [string, PgColumn][] = Object.entries(columns).filter(([_, col]) => !col.shouldDisableInsert());\n\n\t\tconst insertOrder = colEntries.map(\n\t\t\t([, column]) => sql.identifier(this.casing.getColumnCasing(column)),\n\t\t);\n\n\t\tif (select) {\n\t\t\tconst select = valuesOrSelect as AnyPgSelectQueryBuilder | SQL;\n\n\t\t\tif (is(select, SQL)) {\n\t\t\t\tvaluesSqlList.push(select);\n\t\t\t} else {\n\t\t\t\tvaluesSqlList.push(select.getSQL());\n\t\t\t}\n\t\t} else {\n\t\t\tconst values = valuesOrSelect as Record<string, Param | SQL>[];\n\t\t\tvaluesSqlList.push(sql.raw('values '));\n\n\t\t\tfor (const [valueIndex, value] of values.entries()) {\n\t\t\t\tconst valueList: (SQLChunk | SQL)[] = [];\n\t\t\t\tfor (const [fieldName, col] of colEntries) {\n\t\t\t\t\tconst colValue = value[fieldName];\n\t\t\t\t\tif (colValue === undefined || (is(colValue, Param) && colValue.value === undefined)) {\n\t\t\t\t\t\t// eslint-disable-next-line unicorn/no-negated-condition\n\t\t\t\t\t\tif (col.defaultFn !== undefined) {\n\t\t\t\t\t\t\tconst defaultFnResult = col.defaultFn();\n\t\t\t\t\t\t\tconst defaultValue = is(defaultFnResult, SQL) ? defaultFnResult : sql.param(defaultFnResult, col);\n\t\t\t\t\t\t\tvalueList.push(defaultValue);\n\t\t\t\t\t\t\t// eslint-disable-next-line unicorn/no-negated-condition\n\t\t\t\t\t\t} else if (!col.default && col.onUpdateFn !== undefined) {\n\t\t\t\t\t\t\tconst onUpdateFnResult = col.onUpdateFn();\n\t\t\t\t\t\t\tconst newValue = is(onUpdateFnResult, SQL) ? onUpdateFnResult : sql.param(onUpdateFnResult, col);\n\t\t\t\t\t\t\tvalueList.push(newValue);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tvalueList.push(sql`default`);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvalueList.push(colValue);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvaluesSqlList.push(valueList);\n\t\t\t\tif (valueIndex < values.length - 1) {\n\t\t\t\t\tvaluesSqlList.push(sql`, `);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst valuesSql = sql.join(valuesSqlList);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}`\n\t\t\t: undefined;\n\n\t\tconst onConflictSql = onConflict ? sql` on conflict ${onConflict}` : undefined;\n\n\t\tconst overridingSql = overridingSystemValue_ === true ? sql`overriding system value ` : undefined;\n\n\t\treturn sql`${withSql}insert into ${table} ${insertOrder} ${overridingSql}${valuesSql}${onConflictSql}${returningSql}`;\n\t}\n\n\tbuildRefreshMaterializedViewQuery(\n\t\t{ view, concurrently, withNoData }: { view: PgMaterializedView; concurrently?: boolean; withNoData?: boolean },\n\t): SQL {\n\t\tconst concurrentlySql = concurrently ? sql` concurrently` : undefined;\n\t\tconst withNoDataSql = withNoData ? sql` with no data` : undefined;\n\n\t\treturn sql`refresh materialized view${concurrentlySql} ${view}${withNoDataSql}`;\n\t}\n\n\tprepareTyping(encoder: DriverValueEncoder<unknown, unknown>): QueryTypingsValue {\n\t\tif (is(encoder, PgJsonb) || is(encoder, PgJson)) {\n\t\t\treturn 'json';\n\t\t} else if (is(encoder, PgNumeric)) {\n\t\t\treturn 'decimal';\n\t\t} else if (is(encoder, PgTime)) {\n\t\t\treturn 'time';\n\t\t} else if (is(encoder, PgTimestamp) || is(encoder, PgTimestampString)) {\n\t\t\treturn 'timestamp';\n\t\t} else if (is(encoder, PgDate) || is(encoder, PgDateString)) {\n\t\t\treturn 'date';\n\t\t} else if (is(encoder, PgUUID)) {\n\t\t\treturn 'uuid';\n\t\t} else {\n\t\t\treturn 'none';\n\t\t}\n\t}\n\n\tsqlToQuery(sql: SQL, invokeSource?: 'indexes' | undefined): QueryWithTypings {\n\t\treturn sql.toQuery({\n\t\t\tcasing: this.casing,\n\t\t\tescapeName: this.escapeName,\n\t\t\tescapeParam: this.escapeParam,\n\t\t\tescapeString: this.escapeString,\n\t\t\tprepareTyping: this.prepareTyping,\n\t\t\tinvokeSource,\n\t\t});\n\t}\n\n\t// buildRelationalQueryWithPK({\n\t// \tfullSchema,\n\t// \tschema,\n\t// \ttableNamesMap,\n\t// \ttable,\n\t// \ttableConfig,\n\t// \tqueryConfig: config,\n\t// \ttableAlias,\n\t// \tisRoot = false,\n\t// \tjoinOn,\n\t// }: {\n\t// \tfullSchema: Record<string, unknown>;\n\t// \tschema: TablesRelationalConfig;\n\t// \ttableNamesMap: Record<string, string>;\n\t// \ttable: PgTable;\n\t// \ttableConfig: TableRelationalConfig;\n\t// \tqueryConfig: true | DBQueryConfig<'many', true>;\n\t// \ttableAlias: string;\n\t// \tisRoot?: boolean;\n\t// \tjoinOn?: SQL;\n\t// }): BuildRelationalQueryResult<PgTable, PgColumn> {\n\t// \t// For { \"<relation>\": true }, return a table with selection of all columns\n\t// \tif (config === true) {\n\t// \t\tconst selectionEntries = Object.entries(tableConfig.columns);\n\t// \t\tconst selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = selectionEntries.map((\n\t// \t\t\t[key, value],\n\t// \t\t) => ({\n\t// \t\t\tdbKey: value.name,\n\t// \t\t\ttsKey: key,\n\t// \t\t\tfield: value as PgColumn,\n\t// \t\t\trelationTableTsKey: undefined,\n\t// \t\t\tisJson: false,\n\t// \t\t\tselection: [],\n\t// \t\t}));\n\n\t// \t\treturn {\n\t// \t\t\ttableTsKey: tableConfig.tsName,\n\t// \t\t\tsql: table,\n\t// \t\t\tselection,\n\t// \t\t};\n\t// \t}\n\n\t// \t// let selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = [];\n\t// \t// let selectionForBuild = selection;\n\n\t// \tconst aliasedColumns = Object.fromEntries(\n\t// \t\tObject.entries(tableConfig.columns).map(([key, value]) => [key, aliasedTableColumn(value, tableAlias)]),\n\t// \t);\n\n\t// \tconst aliasedRelations = Object.fromEntries(\n\t// \t\tObject.entries(tableConfig.relations).map(([key, value]) => [key, aliasedRelation(value, tableAlias)]),\n\t// \t);\n\n\t// \tconst aliasedFields = Object.assign({}, aliasedColumns, aliasedRelations);\n\n\t// \tlet where, hasUserDefinedWhere;\n\t// \tif (config.where) {\n\t// \t\tconst whereSql = typeof config.where === 'function' ? config.where(aliasedFields, operators) : config.where;\n\t// \t\twhere = whereSql && mapColumnsInSQLToAlias(whereSql, tableAlias);\n\t// \t\thasUserDefinedWhere = !!where;\n\t// \t}\n\t// \twhere = and(joinOn, where);\n\n\t// \t// const fieldsSelection: { tsKey: string; value: PgColumn | SQL.Aliased; isExtra?: boolean }[] = [];\n\t// \tlet joins: Join[] = [];\n\t// \tlet selectedColumns: string[] = [];\n\n\t// \t// Figure out which columns to select\n\t// \tif (config.columns) {\n\t// \t\tlet isIncludeMode = false;\n\n\t// \t\tfor (const [field, value] of Object.entries(config.columns)) {\n\t// \t\t\tif (value === undefined) {\n\t// \t\t\t\tcontinue;\n\t// \t\t\t}\n\n\t// \t\t\tif (field in tableConfig.columns) {\n\t// \t\t\t\tif (!isIncludeMode && value === true) {\n\t// \t\t\t\t\tisIncludeMode = true;\n\t// \t\t\t\t}\n\t// \t\t\t\tselectedColumns.push(field);\n\t// \t\t\t}\n\t// \t\t}\n\n\t// \t\tif (selectedColumns.length > 0) {\n\t// \t\t\tselectedColumns = isIncludeMode\n\t// \t\t\t\t? selectedColumns.filter((c) => config.columns?.[c] === true)\n\t// \t\t\t\t: Object.keys(tableConfig.columns).filter((key) => !selectedColumns.includes(key));\n\t// \t\t}\n\t// \t} else {\n\t// \t\t// Select all columns if selection is not specified\n\t// \t\tselectedColumns = Object.keys(tableConfig.columns);\n\t// \t}\n\n\t// \t// for (const field of selectedColumns) {\n\t// \t// \tconst column = tableConfig.columns[field]! as PgColumn;\n\t// \t// \tfieldsSelection.push({ tsKey: field, value: column });\n\t// \t// }\n\n\t// \tlet initiallySelectedRelations: {\n\t// \t\ttsKey: string;\n\t// \t\tqueryConfig: true | DBQueryConfig<'many', false>;\n\t// \t\trelation: Relation;\n\t// \t}[] = [];\n\n\t// \t// let selectedRelations: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = [];\n\n\t// \t// Figure out which relations to select\n\t// \tif (config.with) {\n\t// \t\tinitiallySelectedRelations = Object.entries(config.with)\n\t// \t\t\t.filter((entry): entry is [typeof entry[0], NonNullable<typeof entry[1]>] => !!entry[1])\n\t// \t\t\t.map(([tsKey, queryConfig]) => ({ tsKey, queryConfig, relation: tableConfig.relations[tsKey]! }));\n\t// \t}\n\n\t// \tconst manyRelations = initiallySelectedRelations.filter((r) =>\n\t// \t\tis(r.relation, Many)\n\t// \t\t&& (schema[tableNamesMap[r.relation.referencedTable[Table.Symbol.Name]]!]?.primaryKey.length ?? 0) > 0\n\t// \t);\n\t// \t// If this is the last Many relation (or there are no Many relations), we are on the innermost subquery level\n\t// \tconst isInnermostQuery = manyRelations.length < 2;\n\n\t// \tconst selectedExtras: {\n\t// \t\ttsKey: string;\n\t// \t\tvalue: SQL.Aliased;\n\t// \t}[] = [];\n\n\t// \t// Figure out which extras to select\n\t// \tif (isInnermostQuery && config.extras) {\n\t// \t\tconst extras = typeof config.extras === 'function'\n\t// \t\t\t? config.extras(aliasedFields, { sql })\n\t// \t\t\t: config.extras;\n\t// \t\tfor (const [tsKey, value] of Object.entries(extras)) {\n\t// \t\t\tselectedExtras.push({\n\t// \t\t\t\ttsKey,\n\t// \t\t\t\tvalue: mapColumnsInAliasedSQLToAlias(value, tableAlias),\n\t// \t\t\t});\n\t// \t\t}\n\t// \t}\n\n\t// \t// Transform `fieldsSelection` into `selection`\n\t// \t// `fieldsSelection` shouldn't be used after this point\n\t// \t// for (const { tsKey, value, isExtra } of fieldsSelection) {\n\t// \t// \tselection.push({\n\t// \t// \t\tdbKey: is(value, SQL.Aliased) ? value.fieldAlias : tableConfig.columns[tsKey]!.name,\n\t// \t// \t\ttsKey,\n\t// \t// \t\tfield: is(value, Column) ? aliasedTableColumn(value, tableAlias) : value,\n\t// \t// \t\trelationTableTsKey: undefined,\n\t// \t// \t\tisJson: false,\n\t// \t// \t\tisExtra,\n\t// \t// \t\tselection: [],\n\t// \t// \t});\n\t// \t// }\n\n\t// \tlet orderByOrig = typeof config.orderBy === 'function'\n\t// \t\t? config.orderBy(aliasedFields, orderByOperators)\n\t// \t\t: config.orderBy ?? [];\n\t// \tif (!Array.isArray(orderByOrig)) {\n\t// \t\torderByOrig = [orderByOrig];\n\t// \t}\n\t// \tconst orderBy = orderByOrig.map((orderByValue) => {\n\t// \t\tif (is(orderByValue, Column)) {\n\t// \t\t\treturn aliasedTableColumn(orderByValue, tableAlias) as PgColumn;\n\t// \t\t}\n\t// \t\treturn mapColumnsInSQLToAlias(orderByValue, tableAlias);\n\t// \t});\n\n\t// \tconst limit = isInnermostQuery ? config.limit : undefined;\n\t// \tconst offset = isInnermostQuery ? config.offset : undefined;\n\n\t// \t// For non-root queries without additional config except columns, return a table with selection\n\t// \tif (\n\t// \t\t!isRoot\n\t// \t\t&& initiallySelectedRelations.length === 0\n\t// \t\t&& selectedExtras.length === 0\n\t// \t\t&& !where\n\t// \t\t&& orderBy.length === 0\n\t// \t\t&& limit === undefined\n\t// \t\t&& offset === undefined\n\t// \t) {\n\t// \t\treturn {\n\t// \t\t\ttableTsKey: tableConfig.tsName,\n\t// \t\t\tsql: table,\n\t// \t\t\tselection: selectedColumns.map((key) => ({\n\t// \t\t\t\tdbKey: tableConfig.columns[key]!.name,\n\t// \t\t\t\ttsKey: key,\n\t// \t\t\t\tfield: tableConfig.columns[key] as PgColumn,\n\t// \t\t\t\trelationTableTsKey: undefined,\n\t// \t\t\t\tisJson: false,\n\t// \t\t\t\tselection: [],\n\t// \t\t\t})),\n\t// \t\t};\n\t// \t}\n\n\t// \tconst selectedRelationsWithoutPK:\n\n\t// \t// Process all relations without primary keys, because they need to be joined differently and will all be on the same query level\n\t// \tfor (\n\t// \t\tconst {\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tqueryConfig: selectedRelationConfigValue,\n\t// \t\t\trelation,\n\t// \t\t} of initiallySelectedRelations\n\t// \t) {\n\t// \t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t// \t\tconst relationTableName = relation.referencedTable[Table.Symbol.Name];\n\t// \t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t// \t\tconst relationTable = schema[relationTableTsName]!;\n\n\t// \t\tif (relationTable.primaryKey.length > 0) {\n\t// \t\t\tcontinue;\n\t// \t\t}\n\n\t// \t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t// \t\tconst joinOn = and(\n\t// \t\t\t...normalizedRelation.fields.map((field, i) =>\n\t// \t\t\t\teq(\n\t// \t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t// \t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t// \t\t\t\t)\n\t// \t\t\t),\n\t// \t\t);\n\t// \t\tconst builtRelation = this.buildRelationalQueryWithoutPK({\n\t// \t\t\tfullSchema,\n\t// \t\t\tschema,\n\t// \t\t\ttableNamesMap,\n\t// \t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n\t// \t\t\ttableConfig: schema[relationTableTsName]!,\n\t// \t\t\tqueryConfig: selectedRelationConfigValue,\n\t// \t\t\ttableAlias: relationTableAlias,\n\t// \t\t\tjoinOn,\n\t// \t\t\tnestedQueryRelation: relation,\n\t// \t\t});\n\t// \t\tconst field = sql`${sql.identifier(relationTableAlias)}.${sql.identifier('data')}`.as(selectedRelationTsKey);\n\t// \t\tjoins.push({\n\t// \t\t\ton: sql`true`,\n\t// \t\t\ttable: new Subquery(builtRelation.sql as SQL, {}, relationTableAlias),\n\t// \t\t\talias: relationTableAlias,\n\t// \t\t\tjoinType: 'left',\n\t// \t\t\tlateral: true,\n\t// \t\t});\n\t// \t\tselectedRelations.push({\n\t// \t\t\tdbKey: selectedRelationTsKey,\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tfield,\n\t// \t\t\trelationTableTsKey: relationTableTsName,\n\t// \t\t\tisJson: true,\n\t// \t\t\tselection: builtRelation.selection,\n\t// \t\t});\n\t// \t}\n\n\t// \tconst oneRelations = initiallySelectedRelations.filter((r): r is typeof r & { relation: One } =>\n\t// \t\tis(r.relation, One)\n\t// \t);\n\n\t// \t// Process all One relations with PKs, because they can all be joined on the same level\n\t// \tfor (\n\t// \t\tconst {\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tqueryConfig: selectedRelationConfigValue,\n\t// \t\t\trelation,\n\t// \t\t} of oneRelations\n\t// \t) {\n\t// \t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t// \t\tconst relationTableName = relation.referencedTable[Table.Symbol.Name];\n\t// \t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t// \t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t// \t\tconst relationTable = schema[relationTableTsName]!;\n\n\t// \t\tif (relationTable.primaryKey.length === 0) {\n\t// \t\t\tcontinue;\n\t// \t\t}\n\n\t// \t\tconst joinOn = and(\n\t// \t\t\t...normalizedRelation.fields.map((field, i) =>\n\t// \t\t\t\teq(\n\t// \t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t// \t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t// \t\t\t\t)\n\t// \t\t\t),\n\t// \t\t);\n\t// \t\tconst builtRelation = this.buildRelationalQueryWithPK({\n\t// \t\t\tfullSchema,\n\t// \t\t\tschema,\n\t// \t\t\ttableNamesMap,\n\t// \t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n\t// \t\t\ttableConfig: schema[relationTableTsName]!,\n\t// \t\t\tqueryConfig: selectedRelationConfigValue,\n\t// \t\t\ttableAlias: relationTableAlias,\n\t// \t\t\tjoinOn,\n\t// \t\t});\n\t// \t\tconst field = sql`case when ${sql.identifier(relationTableAlias)} is null then null else json_build_array(${\n\t// \t\t\tsql.join(\n\t// \t\t\t\tbuiltRelation.selection.map(({ field }) =>\n\t// \t\t\t\t\tis(field, SQL.Aliased)\n\t// \t\t\t\t\t\t? sql`${sql.identifier(relationTableAlias)}.${sql.identifier(field.fieldAlias)}`\n\t// \t\t\t\t\t\t: is(field, Column)\n\t// \t\t\t\t\t\t? aliasedTableColumn(field, relationTableAlias)\n\t// \t\t\t\t\t\t: field\n\t// \t\t\t\t),\n\t// \t\t\t\tsql`, `,\n\t// \t\t\t)\n\t// \t\t}) end`.as(selectedRelationTsKey);\n\t// \t\tconst isLateralJoin = is(builtRelation.sql, SQL);\n\t// \t\tjoins.push({\n\t// \t\t\ton: isLateralJoin ? sql`true` : joinOn,\n\t// \t\t\ttable: is(builtRelation.sql, SQL)\n\t// \t\t\t\t? new Subquery(builtRelation.sql, {}, relationTableAlias)\n\t// \t\t\t\t: aliasedTable(builtRelation.sql, relationTableAlias),\n\t// \t\t\talias: relationTableAlias,\n\t// \t\t\tjoinType: 'left',\n\t// \t\t\tlateral: is(builtRelation.sql, SQL),\n\t// \t\t});\n\t// \t\tselectedRelations.push({\n\t// \t\t\tdbKey: selectedRelationTsKey,\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tfield,\n\t// \t\t\trelationTableTsKey: relationTableTsName,\n\t// \t\t\tisJson: true,\n\t// \t\t\tselection: builtRelation.selection,\n\t// \t\t});\n\t// \t}\n\n\t// \tlet distinct: PgSelectConfig['distinct'];\n\t// \tlet tableFrom: PgTable | Subquery = table;\n\n\t// \t// Process first Many relation - each one requires a nested subquery\n\t// \tconst manyRelation = manyRelations[0];\n\t// \tif (manyRelation) {\n\t// \t\tconst {\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tqueryConfig: selectedRelationQueryConfig,\n\t// \t\t\trelation,\n\t// \t\t} = manyRelation;\n\n\t// \t\tdistinct = {\n\t// \t\t\ton: tableConfig.primaryKey.map((c) => aliasedTableColumn(c as PgColumn, tableAlias)),\n\t// \t\t};\n\n\t// \t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t// \t\tconst relationTableName = relation.referencedTable[Table.Symbol.Name];\n\t// \t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t// \t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t// \t\tconst joinOn = and(\n\t// \t\t\t...normalizedRelation.fields.map((field, i) =>\n\t// \t\t\t\teq(\n\t// \t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t// \t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t// \t\t\t\t)\n\t// \t\t\t),\n\t// \t\t);\n\n\t// \t\tconst builtRelationJoin = this.buildRelationalQueryWithPK({\n\t// \t\t\tfullSchema,\n\t// \t\t\tschema,\n\t// \t\t\ttableNamesMap,\n\t// \t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n\t// \t\t\ttableConfig: schema[relationTableTsName]!,\n\t// \t\t\tqueryConfig: selectedRelationQueryConfig,\n\t// \t\t\ttableAlias: relationTableAlias,\n\t// \t\t\tjoinOn,\n\t// \t\t});\n\n\t// \t\tconst builtRelationSelectionField = sql`case when ${\n\t// \t\t\tsql.identifier(relationTableAlias)\n\t// \t\t} is null then '[]' else json_agg(json_build_array(${\n\t// \t\t\tsql.join(\n\t// \t\t\t\tbuiltRelationJoin.selection.map(({ field }) =>\n\t// \t\t\t\t\tis(field, SQL.Aliased)\n\t// \t\t\t\t\t\t? sql`${sql.identifier(relationTableAlias)}.${sql.identifier(field.fieldAlias)}`\n\t// \t\t\t\t\t\t: is(field, Column)\n\t// \t\t\t\t\t\t? aliasedTableColumn(field, relationTableAlias)\n\t// \t\t\t\t\t\t: field\n\t// \t\t\t\t),\n\t// \t\t\t\tsql`, `,\n\t// \t\t\t)\n\t// \t\t})) over (partition by ${sql.join(distinct.on, sql`, `)}) end`.as(selectedRelationTsKey);\n\t// \t\tconst isLateralJoin = is(builtRelationJoin.sql, SQL);\n\t// \t\tjoins.push({\n\t// \t\t\ton: isLateralJoin ? sql`true` : joinOn,\n\t// \t\t\ttable: isLateralJoin\n\t// \t\t\t\t? new Subquery(builtRelationJoin.sql as SQL, {}, relationTableAlias)\n\t// \t\t\t\t: aliasedTable(builtRelationJoin.sql as PgTable, relationTableAlias),\n\t// \t\t\talias: relationTableAlias,\n\t// \t\t\tjoinType: 'left',\n\t// \t\t\tlateral: isLateralJoin,\n\t// \t\t});\n\n\t// \t\t// Build the \"from\" subquery with the remaining Many relations\n\t// \t\tconst builtTableFrom = this.buildRelationalQueryWithPK({\n\t// \t\t\tfullSchema,\n\t// \t\t\tschema,\n\t// \t\t\ttableNamesMap,\n\t// \t\t\ttable,\n\t// \t\t\ttableConfig,\n\t// \t\t\tqueryConfig: {\n\t// \t\t\t\t...config,\n\t// \t\t\t\twhere: undefined,\n\t// \t\t\t\torderBy: undefined,\n\t// \t\t\t\tlimit: undefined,\n\t// \t\t\t\toffset: undefined,\n\t// \t\t\t\twith: manyRelations.slice(1).reduce<NonNullable<typeof config['with']>>(\n\t// \t\t\t\t\t(result, { tsKey, queryConfig: configValue }) => {\n\t// \t\t\t\t\t\tresult[tsKey] = configValue;\n\t// \t\t\t\t\t\treturn result;\n\t// \t\t\t\t\t},\n\t// \t\t\t\t\t{},\n\t// \t\t\t\t),\n\t// \t\t\t},\n\t// \t\t\ttableAlias,\n\t// \t\t});\n\n\t// \t\tselectedRelations.push({\n\t// \t\t\tdbKey: selectedRelationTsKey,\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tfield: builtRelationSelectionField,\n\t// \t\t\trelationTableTsKey: relationTableTsName,\n\t// \t\t\tisJson: true,\n\t// \t\t\tselection: builtRelationJoin.selection,\n\t// \t\t});\n\n\t// \t\t// selection = builtTableFrom.selection.map((item) =>\n\t// \t\t// \tis(item.field, SQL.Aliased)\n\t// \t\t// \t\t? { ...item, field: sql`${sql.identifier(tableAlias)}.${sql.identifier(item.field.fieldAlias)}` }\n\t// \t\t// \t\t: item\n\t// \t\t// );\n\t// \t\t// selectionForBuild = [{\n\t// \t\t// \tdbKey: '*',\n\t// \t\t// \ttsKey: '*',\n\t// \t\t// \tfield: sql`${sql.identifier(tableAlias)}.*`,\n\t// \t\t// \tselection: [],\n\t// \t\t// \tisJson: false,\n\t// \t\t// \trelationTableTsKey: undefined,\n\t// \t\t// }];\n\t// \t\t// const newSelectionItem: (typeof selection)[number] = {\n\t// \t\t// \tdbKey: selectedRelationTsKey,\n\t// \t\t// \ttsKey: selectedRelationTsKey,\n\t// \t\t// \tfield,\n\t// \t\t// \trelationTableTsKey: relationTableTsName,\n\t// \t\t// \tisJson: true,\n\t// \t\t// \tselection: builtRelationJoin.selection,\n\t// \t\t// };\n\t// \t\t// selection.push(newSelectionItem);\n\t// \t\t// selectionForBuild.push(newSelectionItem);\n\n\t// \t\ttableFrom = is(builtTableFrom.sql, PgTable)\n\t// \t\t\t? builtTableFrom.sql\n\t// \t\t\t: new Subquery(builtTableFrom.sql, {}, tableAlias);\n\t// \t}\n\n\t// \tif (selectedColumns.length === 0 && selectedRelations.length === 0 && selectedExtras.length === 0) {\n\t// \t\tthrow new DrizzleError(`No fields selected for table \"${tableConfig.tsName}\" (\"${tableAlias}\")`);\n\t// \t}\n\n\t// \tlet selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'];\n\n\t// \tfunction prepareSelectedColumns() {\n\t// \t\treturn selectedColumns.map((key) => ({\n\t// \t\t\tdbKey: tableConfig.columns[key]!.name,\n\t// \t\t\ttsKey: key,\n\t// \t\t\tfield: tableConfig.columns[key] as PgColumn,\n\t// \t\t\trelationTableTsKey: undefined,\n\t// \t\t\tisJson: false,\n\t// \t\t\tselection: [],\n\t// \t\t}));\n\t// \t}\n\n\t// \tfunction prepareSelectedExtras() {\n\t// \t\treturn selectedExtras.map((item) => ({\n\t// \t\t\tdbKey: item.value.fieldAlias,\n\t// \t\t\ttsKey: item.tsKey,\n\t// \t\t\tfield: item.value,\n\t// \t\t\trelationTableTsKey: undefined,\n\t// \t\t\tisJson: false,\n\t// \t\t\tselection: [],\n\t// \t\t}));\n\t// \t}\n\n\t// \tif (isRoot) {\n\t// \t\tselection = [\n\t// \t\t\t...prepareSelectedColumns(),\n\t// \t\t\t...prepareSelectedExtras(),\n\t// \t\t];\n\t// \t}\n\n\t// \tif (hasUserDefinedWhere || orderBy.length > 0) {\n\t// \t\ttableFrom = new Subquery(\n\t// \t\t\tthis.buildSelectQuery({\n\t// \t\t\t\ttable: is(tableFrom, PgTable) ? aliasedTable(tableFrom, tableAlias) : tableFrom,\n\t// \t\t\t\tfields: {},\n\t// \t\t\t\tfieldsFlat: selectionForBuild.map(({ field }) => ({\n\t// \t\t\t\t\tpath: [],\n\t// \t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t// \t\t\t\t})),\n\t// \t\t\t\tjoins,\n\t// \t\t\t\tdistinct,\n\t// \t\t\t}),\n\t// \t\t\t{},\n\t// \t\t\ttableAlias,\n\t// \t\t);\n\t// \t\tselectionForBuild = selection.map((item) =>\n\t// \t\t\tis(item.field, SQL.Aliased)\n\t// \t\t\t\t? { ...item, field: sql`${sql.identifier(tableAlias)}.${sql.identifier(item.field.fieldAlias)}` }\n\t// \t\t\t\t: item\n\t// \t\t);\n\t// \t\tjoins = [];\n\t// \t\tdistinct = undefined;\n\t// \t}\n\n\t// \tconst result = this.buildSelectQuery({\n\t// \t\ttable: is(tableFrom, PgTable) ? aliasedTable(tableFrom, tableAlias) : tableFrom,\n\t// \t\tfields: {},\n\t// \t\tfieldsFlat: selectionForBuild.map(({ field }) => ({\n\t// \t\t\tpath: [],\n\t// \t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t// \t\t})),\n\t// \t\twhere,\n\t// \t\tlimit,\n\t// \t\toffset,\n\t// \t\tjoins,\n\t// \t\torderBy,\n\t// \t\tdistinct,\n\t// \t});\n\n\t// \treturn {\n\t// \t\ttableTsKey: tableConfig.tsName,\n\t// \t\tsql: result,\n\t// \t\tselection,\n\t// \t};\n\t// }\n\n\tbuildRelationalQueryWithoutPK({\n\t\tfullSchema,\n\t\tschema,\n\t\ttableNamesMap,\n\t\ttable,\n\t\ttableConfig,\n\t\tqueryConfig: config,\n\t\ttableAlias,\n\t\tnestedQueryRelation,\n\t\tjoinOn,\n\t}: {\n\t\tfullSchema: Record<string, unknown>;\n\t\tschema: TablesRelationalConfig;\n\t\ttableNamesMap: Record<string, string>;\n\t\ttable: PgTable;\n\t\ttableConfig: TableRelationalConfig;\n\t\tqueryConfig: true | DBQueryConfig<'many', true>;\n\t\ttableAlias: string;\n\t\tnestedQueryRelation?: Relation;\n\t\tjoinOn?: SQL;\n\t}): BuildRelationalQueryResult<PgTable, PgColumn> {\n\t\tlet selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = [];\n\t\tlet limit, offset, orderBy: NonNullable<PgSelectConfig['orderBy']> = [], where;\n\t\tconst joins: PgSelectJoinConfig[] = [];\n\n\t\tif (config === true) {\n\t\t\tconst selectionEntries = Object.entries(tableConfig.columns);\n\t\t\tselection = selectionEntries.map((\n\t\t\t\t[key, value],\n\t\t\t) => ({\n\t\t\t\tdbKey: value.name,\n\t\t\t\ttsKey: key,\n\t\t\t\tfield: aliasedTableColumn(value as PgColumn, tableAlias),\n\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\tisJson: false,\n\t\t\t\tselection: [],\n\t\t\t}));\n\t\t} else {\n\t\t\tconst aliasedColumns = Object.fromEntries(\n\t\t\t\tObject.entries(tableConfig.columns).map((\n\t\t\t\t\t[key, value],\n\t\t\t\t) => [key, aliasedTableColumn(value, tableAlias)]),\n\t\t\t);\n\n\t\t\tif (config.where) {\n\t\t\t\tconst whereSql = typeof config.where === 'function'\n\t\t\t\t\t? config.where(aliasedColumns, getOperators())\n\t\t\t\t\t: config.where;\n\t\t\t\twhere = whereSql && mapColumnsInSQLToAlias(whereSql, tableAlias);\n\t\t\t}\n\n\t\t\tconst fieldsSelection: { tsKey: string; value: PgColumn | SQL.Aliased }[] = [];\n\t\t\tlet selectedColumns: string[] = [];\n\n\t\t\t// Figure out which columns to select\n\t\t\tif (config.columns) {\n\t\t\t\tlet isIncludeMode = false;\n\n\t\t\t\tfor (const [field, value] of Object.entries(config.columns)) {\n\t\t\t\t\tif (value === undefined) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (field in tableConfig.columns) {\n\t\t\t\t\t\tif (!isIncludeMode && value === true) {\n\t\t\t\t\t\t\tisIncludeMode = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tselectedColumns.push(field);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (selectedColumns.length > 0) {\n\t\t\t\t\tselectedColumns = isIncludeMode\n\t\t\t\t\t\t? selectedColumns.filter((c) => config.columns?.[c] === true)\n\t\t\t\t\t\t: Object.keys(tableConfig.columns).filter((key) => !selectedColumns.includes(key));\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Select all columns if selection is not specified\n\t\t\t\tselectedColumns = Object.keys(tableConfig.columns);\n\t\t\t}\n\n\t\t\tfor (const field of selectedColumns) {\n\t\t\t\tconst column = tableConfig.columns[field]! as PgColumn;\n\t\t\t\tfieldsSelection.push({ tsKey: field, value: column });\n\t\t\t}\n\n\t\t\tlet selectedRelations: {\n\t\t\t\ttsKey: string;\n\t\t\t\tqueryConfig: true | DBQueryConfig<'many', false>;\n\t\t\t\trelation: Relation;\n\t\t\t}[] = [];\n\n\t\t\t// Figure out which relations to select\n\t\t\tif (config.with) {\n\t\t\t\tselectedRelations = Object.entries(config.with)\n\t\t\t\t\t.filter((entry): entry is [typeof entry[0], NonNullable<typeof entry[1]>] => !!entry[1])\n\t\t\t\t\t.map(([tsKey, queryConfig]) => ({ tsKey, queryConfig, relation: tableConfig.relations[tsKey]! }));\n\t\t\t}\n\n\t\t\tlet extras;\n\n\t\t\t// Figure out which extras to select\n\t\t\tif (config.extras) {\n\t\t\t\textras = typeof config.extras === 'function'\n\t\t\t\t\t? config.extras(aliasedColumns, { sql })\n\t\t\t\t\t: config.extras;\n\t\t\t\tfor (const [tsKey, value] of Object.entries(extras)) {\n\t\t\t\t\tfieldsSelection.push({\n\t\t\t\t\t\ttsKey,\n\t\t\t\t\t\tvalue: mapColumnsInAliasedSQLToAlias(value, tableAlias),\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Transform `fieldsSelection` into `selection`\n\t\t\t// `fieldsSelection` shouldn't be used after this point\n\t\t\tfor (const { tsKey, value } of fieldsSelection) {\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: is(value, SQL.Aliased) ? value.fieldAlias : tableConfig.columns[tsKey]!.name,\n\t\t\t\t\ttsKey,\n\t\t\t\t\tfield: is(value, Column) ? aliasedTableColumn(value, tableAlias) : value,\n\t\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\t\tisJson: false,\n\t\t\t\t\tselection: [],\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tlet orderByOrig = typeof config.orderBy === 'function'\n\t\t\t\t? config.orderBy(aliasedColumns, getOrderByOperators())\n\t\t\t\t: config.orderBy ?? [];\n\t\t\tif (!Array.isArray(orderByOrig)) {\n\t\t\t\torderByOrig = [orderByOrig];\n\t\t\t}\n\t\t\torderBy = orderByOrig.map((orderByValue) => {\n\t\t\t\tif (is(orderByValue, Column)) {\n\t\t\t\t\treturn aliasedTableColumn(orderByValue, tableAlias) as PgColumn;\n\t\t\t\t}\n\t\t\t\treturn mapColumnsInSQLToAlias(orderByValue, tableAlias);\n\t\t\t});\n\n\t\t\tlimit = config.limit;\n\t\t\toffset = config.offset;\n\n\t\t\t// Process all relations\n\t\t\tfor (\n\t\t\t\tconst {\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tqueryConfig: selectedRelationConfigValue,\n\t\t\t\t\trelation,\n\t\t\t\t} of selectedRelations\n\t\t\t) {\n\t\t\t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t\t\t\tconst relationTableName = getTableUniqueName(relation.referencedTable);\n\t\t\t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t\t\t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t\t\t\tconst joinOn = and(\n\t\t\t\t\t...normalizedRelation.fields.map((field, i) =>\n\t\t\t\t\t\teq(\n\t\t\t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t\t\t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t\t\t\t\t\t)\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t\tconst builtRelation = this.buildRelationalQueryWithoutPK({\n\t\t\t\t\tfullSchema,\n\t\t\t\t\tschema,\n\t\t\t\t\ttableNamesMap,\n\t\t\t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n\t\t\t\t\ttableConfig: schema[relationTableTsName]!,\n\t\t\t\t\tqueryConfig: is(relation, One)\n\t\t\t\t\t\t? (selectedRelationConfigValue === true\n\t\t\t\t\t\t\t? { limit: 1 }\n\t\t\t\t\t\t\t: { ...selectedRelationConfigValue, limit: 1 })\n\t\t\t\t\t\t: selectedRelationConfigValue,\n\t\t\t\t\ttableAlias: relationTableAlias,\n\t\t\t\t\tjoinOn,\n\t\t\t\t\tnestedQueryRelation: relation,\n\t\t\t\t});\n\t\t\t\tconst field = sql`${sql.identifier(relationTableAlias)}.${sql.identifier('data')}`.as(selectedRelationTsKey);\n\t\t\t\tjoins.push({\n\t\t\t\t\ton: sql`true`,\n\t\t\t\t\ttable: new Subquery(builtRelation.sql as SQL, {}, relationTableAlias),\n\t\t\t\t\talias: relationTableAlias,\n\t\t\t\t\tjoinType: 'left',\n\t\t\t\t\tlateral: true,\n\t\t\t\t});\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: selectedRelationTsKey,\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tfield,\n\t\t\t\t\trelationTableTsKey: relationTableTsName,\n\t\t\t\t\tisJson: true,\n\t\t\t\t\tselection: builtRelation.selection,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (selection.length === 0) {\n\t\t\tthrow new DrizzleError({ message: `No fields selected for table \"${tableConfig.tsName}\" (\"${tableAlias}\")` });\n\t\t}\n\n\t\tlet result;\n\n\t\twhere = and(joinOn, where);\n\n\t\tif (nestedQueryRelation) {\n\t\t\tlet field = sql`json_build_array(${\n\t\t\t\tsql.join(\n\t\t\t\t\tselection.map(({ field, tsKey, isJson }) =>\n\t\t\t\t\t\tisJson\n\t\t\t\t\t\t\t? sql`${sql.identifier(`${tableAlias}_${tsKey}`)}.${sql.identifier('data')}`\n\t\t\t\t\t\t\t: is(field, SQL.Aliased)\n\t\t\t\t\t\t\t? field.sql\n\t\t\t\t\t\t\t: field\n\t\t\t\t\t),\n\t\t\t\t\tsql`, `,\n\t\t\t\t)\n\t\t\t})`;\n\t\t\tif (is(nestedQueryRelation, Many)) {\n\t\t\t\tfield = sql`coalesce(json_agg(${field}${\n\t\t\t\t\torderBy.length > 0 ? sql` order by ${sql.join(orderBy, sql`, `)}` : undefined\n\t\t\t\t}), '[]'::json)`;\n\t\t\t\t// orderBy = [];\n\t\t\t}\n\t\t\tconst nestedSelection = [{\n\t\t\t\tdbKey: 'data',\n\t\t\t\ttsKey: 'data',\n\t\t\t\tfield: field.as('data'),\n\t\t\t\tisJson: true,\n\t\t\t\trelationTableTsKey: tableConfig.tsName,\n\t\t\t\tselection,\n\t\t\t}];\n\n\t\t\tconst needsSubquery = limit !== undefined || offset !== undefined || orderBy.length > 0;\n\n\t\t\tif (needsSubquery) {\n\t\t\t\tresult = this.buildSelectQuery({\n\t\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\t\tfields: {},\n\t\t\t\t\tfieldsFlat: [{\n\t\t\t\t\t\tpath: [],\n\t\t\t\t\t\tfield: sql.raw('*'),\n\t\t\t\t\t}],\n\t\t\t\t\twhere,\n\t\t\t\t\tlimit,\n\t\t\t\t\toffset,\n\t\t\t\t\torderBy,\n\t\t\t\t\tsetOperators: [],\n\t\t\t\t});\n\n\t\t\t\twhere = undefined;\n\t\t\t\tlimit = undefined;\n\t\t\t\toffset = undefined;\n\t\t\t\torderBy = [];\n\t\t\t} else {\n\t\t\t\tresult = aliasedTable(table, tableAlias);\n\t\t\t}\n\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: is(result, PgTable) ? result : new Subquery(result, {}, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: nestedSelection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\tjoins,\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t} else {\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: selection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\tjoins,\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t}\n\n\t\treturn {\n\t\t\ttableTsKey: tableConfig.tsName,\n\t\t\tsql: result,\n\t\t\tselection,\n\t\t};\n\t}\n}\n", "import type { Column } from '~/column.ts';\nimport { entityKind } from './entity.ts';\nimport { Table } from './table.ts';\nimport type { Casing } from './utils.ts';\n\nexport function toSnakeCase(input: string) {\n\tconst words = input\n\t\t.replace(/['\\u2019]/g, '')\n\t\t.match(/[\\da-z]+|[A-Z]+(?![a-z])|[A-Z][\\da-z]+/g) ?? [];\n\n\treturn words.map((word) => word.toLowerCase()).join('_');\n}\n\nexport function toCamelCase(input: string) {\n\tconst words = input\n\t\t.replace(/['\\u2019]/g, '')\n\t\t.match(/[\\da-z]+|[A-Z]+(?![a-z])|[A-Z][\\da-z]+/g) ?? [];\n\n\treturn words.reduce((acc, word, i) => {\n\t\tconst formattedWord = i === 0 ? word.toLowerCase() : `${word[0]!.toUpperCase()}${word.slice(1)}`;\n\t\treturn acc + formattedWord;\n\t}, '');\n}\n\nfunction noopCase(input: string) {\n\treturn input;\n}\n\nexport class CasingCache {\n\tstatic readonly [entityKind]: string = 'CasingCache';\n\n\t/** @internal */\n\tcache: Record<string, string> = {};\n\tprivate cachedTables: Record<string, true> = {};\n\tprivate convert: (input: string) => string;\n\n\tconstructor(casing?: Casing) {\n\t\tthis.convert = casing === 'snake_case'\n\t\t\t? toSnakeCase\n\t\t\t: casing === 'camelCase'\n\t\t\t? toCamelCase\n\t\t\t: noopCase;\n\t}\n\n\tgetColumnCasing(column: Column): string {\n\t\tif (!column.keyAsName) return column.name;\n\n\t\tconst schema = column.table[Table.Symbol.Schema] ?? 'public';\n\t\tconst tableName = column.table[Table.Symbol.OriginalName];\n\t\tconst key = `${schema}.${tableName}.${column.name}`;\n\n\t\tif (!this.cache[key]) {\n\t\t\tthis.cacheTable(column.table);\n\t\t}\n\t\treturn this.cache[key]!;\n\t}\n\n\tprivate cacheTable(table: Table) {\n\t\tconst schema = table[Table.Symbol.Schema] ?? 'public';\n\t\tconst tableName = table[Table.Symbol.OriginalName];\n\t\tconst tableKey = `${schema}.${tableName}`;\n\n\t\tif (!this.cachedTables[tableKey]) {\n\t\t\tfor (const column of Object.values(table[Table.Symbol.Columns])) {\n\t\t\t\tconst columnKey = `${tableKey}.${column.name}`;\n\t\t\t\tthis.cache[columnKey] = this.convert(column.name);\n\t\t\t}\n\t\t\tthis.cachedTables[tableKey] = true;\n\t\t}\n\t}\n\n\tclearCache() {\n\t\tthis.cache = {};\n\t\tthis.cachedTables = {};\n\t}\n}\n", "import { entityKind } from '~/entity.ts';\nimport { type ColumnsSelection, View } from '~/sql/sql.ts';\n\nexport abstract class PgViewBase<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> extends View<TName, TExisting, TSelectedFields> {\n\tstatic override readonly [entityKind]: string = 'PgViewBase';\n\n\tdeclare readonly _: View<TName, TExisting, TSelectedFields>['_'] & {\n\t\treadonly viewBrand: 'PgViewBase';\n\t};\n}\n", "import { ColumnAliasProxyHandler, TableAliasProxyHandler } from './alias.ts';\nimport { Column } from './column.ts';\nimport { entityKind, is } from './entity.ts';\nimport { SQL, View } from './sql/sql.ts';\nimport { Subquery } from './subquery.ts';\nimport { ViewBaseConfig } from './view-common.ts';\n\nexport class SelectionProxyHandler<T extends Subquery | Record<string, unknown> | View>\n\timplements ProxyHandler<Subquery | Record<string, unknown> | View>\n{\n\tstatic readonly [entityKind]: string = 'SelectionProxyHandler';\n\n\tprivate config: {\n\t\t/**\n\t\t * Table alias for the columns\n\t\t */\n\t\talias?: string;\n\t\t/**\n\t\t * What to do when a field is an instance of `SQL.Aliased` and it's not a selection field (from a subquery)\n\t\t *\n\t\t * `sql` - return the underlying SQL expression\n\t\t *\n\t\t * `alias` - return the field alias\n\t\t */\n\t\tsqlAliasedBehavior: 'sql' | 'alias';\n\t\t/**\n\t\t * What to do when a field is an instance of `SQL` and it doesn't have an alias declared\n\t\t *\n\t\t * `sql` - return the underlying SQL expression\n\t\t *\n\t\t * `error` - return a DrizzleTypeError on type level and throw an error on runtime\n\t\t */\n\t\tsqlBehavior: 'sql' | 'error';\n\n\t\t/**\n\t\t * Whether to replace the original name of the column with the alias\n\t\t * Should be set to `true` for views creation\n\t\t * @default false\n\t\t */\n\t\treplaceOriginalName?: boolean;\n\t};\n\n\tconstructor(config: SelectionProxyHandler<T>['config']) {\n\t\tthis.config = { ...config };\n\t}\n\n\tget(subquery: T, prop: string | symbol): any {\n\t\tif (prop === '_') {\n\t\t\treturn {\n\t\t\t\t...subquery['_' as keyof typeof subquery],\n\t\t\t\tselectedFields: new Proxy(\n\t\t\t\t\t(subquery as Subquery)._.selectedFields,\n\t\t\t\t\tthis as ProxyHandler<Record<string, unknown>>,\n\t\t\t\t),\n\t\t\t};\n\t\t}\n\n\t\tif (prop === ViewBaseConfig) {\n\t\t\treturn {\n\t\t\t\t...subquery[ViewBaseConfig as keyof typeof subquery],\n\t\t\t\tselectedFields: new Proxy(\n\t\t\t\t\t(subquery as View)[ViewBaseConfig].selectedFields,\n\t\t\t\t\tthis as ProxyHandler<Record<string, unknown>>,\n\t\t\t\t),\n\t\t\t};\n\t\t}\n\n\t\tif (typeof prop === 'symbol') {\n\t\t\treturn subquery[prop as keyof typeof subquery];\n\t\t}\n\n\t\tconst columns = is(subquery, Subquery)\n\t\t\t? subquery._.selectedFields\n\t\t\t: is(subquery, View)\n\t\t\t? subquery[ViewBaseConfig].selectedFields\n\t\t\t: subquery;\n\t\tconst value: unknown = columns[prop as keyof typeof columns];\n\n\t\tif (is(value, SQL.Aliased)) {\n\t\t\t// Never return the underlying SQL expression for a field previously selected in a subquery\n\t\t\tif (this.config.sqlAliasedBehavior === 'sql' && !value.isSelectionField) {\n\t\t\t\treturn value.sql;\n\t\t\t}\n\n\t\t\tconst newValue = value.clone();\n\t\t\tnewValue.isSelectionField = true;\n\t\t\treturn newValue;\n\t\t}\n\n\t\tif (is(value, SQL)) {\n\t\t\tif (this.config.sqlBehavior === 'sql') {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tthrow new Error(\n\t\t\t\t`You tried to reference \"${prop}\" field from a subquery, which is a raw SQL field, but it doesn't have an alias declared. Please add an alias to the field using \".as('alias')\" method.`,\n\t\t\t);\n\t\t}\n\n\t\tif (is(value, Column)) {\n\t\t\tif (this.config.alias) {\n\t\t\t\treturn new Proxy(\n\t\t\t\t\tvalue,\n\t\t\t\t\tnew ColumnAliasProxyHandler(\n\t\t\t\t\t\tnew Proxy(\n\t\t\t\t\t\t\tvalue.table,\n\t\t\t\t\t\t\tnew TableAliasProxyHandler(this.config.alias, this.config.replaceOriginalName ?? false),\n\t\t\t\t\t\t),\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t}\n\t\t\treturn value;\n\t\t}\n\n\t\tif (typeof value !== 'object' || value === null) {\n\t\t\treturn value;\n\t\t}\n\n\t\treturn new Proxy(value, new SelectionProxyHandler(this.config));\n\t}\n}\n", "import { entityKind, is } from '~/entity.ts';\nimport type { PgColumn } from '~/pg-core/columns/index.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type { PgSession, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport type { SubqueryWithSelection } from '~/pg-core/subquery.ts';\nimport type { PgTable } from '~/pg-core/table.ts';\nimport { PgViewBase } from '~/pg-core/view-base.ts';\nimport { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type {\n\tBuildSubquerySelection,\n\tGetSelectTableName,\n\tGetSelectTableSelection,\n\tJoinNullability,\n\tJoinType,\n\tSelectMode,\n\tSelectResult,\n\tSetOperator,\n} from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport { SQL, View } from '~/sql/sql.ts';\nimport type { ColumnsSelection, Placeholder, Query, SQLWrapper } from '~/sql/sql.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { tracer } from '~/tracing.ts';\nimport { applyMixins, getTableColumns, getTableLikeName, haveSameKeys, type ValueOrArray } from '~/utils.ts';\nimport { orderSelectedFields } from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type {\n\tAnyPgSelect,\n\tCreatePgSelectFromBuilderMode,\n\tGetPgSetOperators,\n\tLockConfig,\n\tLockStrength,\n\tPgCreateSetOperatorFn,\n\tPgSelectConfig,\n\tPgSelectDynamic,\n\tPgSelectHKT,\n\tPgSelectHKTBase,\n\tPgSelectJoinFn,\n\tPgSelectPrepare,\n\tPgSelectWithout,\n\tPgSetOperatorExcludedMethods,\n\tPgSetOperatorWithResult,\n\tSelectedFields,\n\tSetOperatorRightSelect,\n} from './select.types.ts';\n\nexport class PgSelectBuilder<\n\tTSelection extends SelectedFields | undefined,\n\tTBuilderMode extends 'db' | 'qb' = 'db',\n> {\n\tstatic readonly [entityKind]: string = 'PgSelectBuilder';\n\n\tprivate fields: TSelection;\n\tprivate session: PgSession | undefined;\n\tprivate dialect: PgDialect;\n\tprivate withList: Subquery[] = [];\n\tprivate distinct: boolean | {\n\t\ton: (PgColumn | SQLWrapper)[];\n\t} | undefined;\n\n\tconstructor(\n\t\tconfig: {\n\t\t\tfields: TSelection;\n\t\t\tsession: PgSession | undefined;\n\t\t\tdialect: PgDialect;\n\t\t\twithList?: Subquery[];\n\t\t\tdistinct?: boolean | {\n\t\t\t\ton: (PgColumn | SQLWrapper)[];\n\t\t\t};\n\t\t},\n\t) {\n\t\tthis.fields = config.fields;\n\t\tthis.session = config.session;\n\t\tthis.dialect = config.dialect;\n\t\tif (config.withList) {\n\t\t\tthis.withList = config.withList;\n\t\t}\n\t\tthis.distinct = config.distinct;\n\t}\n\n\tprivate authToken?: string;\n\t/** @internal */\n\tsetToken(token: string) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Specify the table, subquery, or other target that you're\n\t * building a select query against.\n\t *\n\t * {@link https://www.postgresql.org/docs/current/sql-select.html#SQL-FROM | Postgres from documentation}\n\t */\n\tfrom<TFrom extends PgTable | Subquery | PgViewBase | SQL>(\n\t\tsource: TFrom,\n\t): CreatePgSelectFromBuilderMode<\n\t\tTBuilderMode,\n\t\tGetSelectTableName<TFrom>,\n\t\tTSelection extends undefined ? GetSelectTableSelection<TFrom> : TSelection,\n\t\tTSelection extends undefined ? 'single' : 'partial'\n\t> {\n\t\tconst isPartialSelect = !!this.fields;\n\n\t\tlet fields: SelectedFields;\n\t\tif (this.fields) {\n\t\t\tfields = this.fields;\n\t\t} else if (is(source, Subquery)) {\n\t\t\t// This is required to use the proxy handler to get the correct field values from the subquery\n\t\t\tfields = Object.fromEntries(\n\t\t\t\tObject.keys(source._.selectedFields).map((\n\t\t\t\t\tkey,\n\t\t\t\t) => [key, source[key as unknown as keyof typeof source] as unknown as SelectedFields[string]]),\n\t\t\t);\n\t\t} else if (is(source, PgViewBase)) {\n\t\t\tfields = source[ViewBaseConfig].selectedFields as SelectedFields;\n\t\t} else if (is(source, SQL)) {\n\t\t\tfields = {};\n\t\t} else {\n\t\t\tfields = getTableColumns<PgTable>(source);\n\t\t}\n\n\t\treturn (this.authToken === undefined\n\t\t\t? new PgSelectBase({\n\t\t\t\ttable: source,\n\t\t\t\tfields,\n\t\t\t\tisPartialSelect,\n\t\t\t\tsession: this.session,\n\t\t\t\tdialect: this.dialect,\n\t\t\t\twithList: this.withList,\n\t\t\t\tdistinct: this.distinct,\n\t\t\t})\n\t\t\t: new PgSelectBase({\n\t\t\t\ttable: source,\n\t\t\t\tfields,\n\t\t\t\tisPartialSelect,\n\t\t\t\tsession: this.session,\n\t\t\t\tdialect: this.dialect,\n\t\t\t\twithList: this.withList,\n\t\t\t\tdistinct: this.distinct,\n\t\t\t}).setToken(this.authToken)) as any;\n\t}\n}\n\nexport abstract class PgSelectQueryBuilderBase<\n\tTHKT extends PgSelectHKTBase,\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends TypedQueryBuilder<TSelectedFields, TResult> {\n\tstatic override readonly [entityKind]: string = 'PgSelectQueryBuilder';\n\n\toverride readonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly hkt: THKT;\n\t\treadonly tableName: TTableName;\n\t\treadonly selection: TSelection;\n\t\treadonly selectMode: TSelectMode;\n\t\treadonly nullabilityMap: TNullabilityMap;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TResult;\n\t\treadonly selectedFields: TSelectedFields;\n\t};\n\n\tprotected config: PgSelectConfig;\n\tprotected joinsNotNullableMap: Record<string, boolean>;\n\tprivate tableName: string | undefined;\n\tprivate isPartialSelect: boolean;\n\tprotected session: PgSession | undefined;\n\tprotected dialect: PgDialect;\n\n\tconstructor(\n\t\t{ table, fields, isPartialSelect, session, dialect, withList, distinct }: {\n\t\t\ttable: PgSelectConfig['table'];\n\t\t\tfields: PgSelectConfig['fields'];\n\t\t\tisPartialSelect: boolean;\n\t\t\tsession: PgSession | undefined;\n\t\t\tdialect: PgDialect;\n\t\t\twithList: Subquery[];\n\t\t\tdistinct: boolean | {\n\t\t\t\ton: (PgColumn | SQLWrapper)[];\n\t\t\t} | undefined;\n\t\t},\n\t) {\n\t\tsuper();\n\t\tthis.config = {\n\t\t\twithList,\n\t\t\ttable,\n\t\t\tfields: { ...fields },\n\t\t\tdistinct,\n\t\t\tsetOperators: [],\n\t\t};\n\t\tthis.isPartialSelect = isPartialSelect;\n\t\tthis.session = session;\n\t\tthis.dialect = dialect;\n\t\tthis._ = {\n\t\t\tselectedFields: fields as TSelectedFields,\n\t\t} as this['_'];\n\t\tthis.tableName = getTableLikeName(table);\n\t\tthis.joinsNotNullableMap = typeof this.tableName === 'string' ? { [this.tableName]: true } : {};\n\t}\n\n\tprivate createJoin<TJoinType extends JoinType>(\n\t\tjoinType: TJoinType,\n\t): PgSelectJoinFn<this, TDynamic, TJoinType> {\n\t\treturn (\n\t\t\ttable: PgTable | Subquery | PgViewBase | SQL,\n\t\t\ton: ((aliases: TSelection) => SQL | undefined) | SQL | undefined,\n\t\t) => {\n\t\t\tconst baseTableName = this.tableName;\n\t\t\tconst tableName = getTableLikeName(table);\n\n\t\t\tif (typeof tableName === 'string' && this.config.joins?.some((join) => join.alias === tableName)) {\n\t\t\t\tthrow new Error(`Alias \"${tableName}\" is already used in this query`);\n\t\t\t}\n\n\t\t\tif (!this.isPartialSelect) {\n\t\t\t\t// If this is the first join and this is not a partial select and we're not selecting from raw SQL, \"move\" the fields from the main table to the nested object\n\t\t\t\tif (Object.keys(this.joinsNotNullableMap).length === 1 && typeof baseTableName === 'string') {\n\t\t\t\t\tthis.config.fields = {\n\t\t\t\t\t\t[baseTableName]: this.config.fields,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tif (typeof tableName === 'string' && !is(table, SQL)) {\n\t\t\t\t\tconst selection = is(table, Subquery)\n\t\t\t\t\t\t? table._.selectedFields\n\t\t\t\t\t\t: is(table, View)\n\t\t\t\t\t\t? table[ViewBaseConfig].selectedFields\n\t\t\t\t\t\t: table[Table.Symbol.Columns];\n\t\t\t\t\tthis.config.fields[tableName] = selection;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (typeof on === 'function') {\n\t\t\t\ton = on(\n\t\t\t\t\tnew Proxy(\n\t\t\t\t\t\tthis.config.fields,\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as TSelection,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif (!this.config.joins) {\n\t\t\t\tthis.config.joins = [];\n\t\t\t}\n\n\t\t\tthis.config.joins.push({ on, table, joinType, alias: tableName });\n\n\t\t\tif (typeof tableName === 'string') {\n\t\t\t\tswitch (joinType) {\n\t\t\t\t\tcase 'left': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'right': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'inner': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'full': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn this as any;\n\t\t};\n\t}\n\n\t/**\n\t * Executes a `left join` operation by adding another table to the current query.\n\t *\n\t * Calling this method associates each row of the table with the corresponding row from the joined table, if a match is found. If no matching row exists, it sets all columns of the joined table to null.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#left-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User; pets: Pet | null }[] = await db.select()\n\t *   .from(users)\n\t *   .leftJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number; petId: number | null }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .leftJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tleftJoin = this.createJoin('left');\n\n\t/**\n\t * Executes a `right join` operation by adding another table to the current query.\n\t *\n\t * Calling this method associates each row of the joined table with the corresponding row from the main table, if a match is found. If no matching row exists, it sets all columns of the main table to null.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#right-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User | null; pets: Pet }[] = await db.select()\n\t *   .from(users)\n\t *   .rightJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number | null; petId: number }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .rightJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\trightJoin = this.createJoin('right');\n\n\t/**\n\t * Executes an `inner join` operation, creating a new table by combining rows from two tables that have matching values.\n\t *\n\t * Calling this method retrieves rows that have corresponding entries in both joined tables. Rows without matching entries in either table are excluded, resulting in a table that includes only matching pairs.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#inner-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User; pets: Pet }[] = await db.select()\n\t *   .from(users)\n\t *   .innerJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number; petId: number }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .innerJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tinnerJoin = this.createJoin('inner');\n\n\t/**\n\t * Executes a `full join` operation by combining rows from two tables into a new table.\n\t *\n\t * Calling this method retrieves all rows from both main and joined tables, merging rows with matching values and filling in `null` for non-matching columns.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#full-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User | null; pets: Pet | null }[] = await db.select()\n\t *   .from(users)\n\t *   .fullJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number | null; petId: number | null }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .fullJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tfullJoin = this.createJoin('full');\n\n\tprivate createSetOperator(\n\t\ttype: SetOperator,\n\t\tisAll: boolean,\n\t): <TValue extends PgSetOperatorWithResult<TResult>>(\n\t\trightSelection:\n\t\t\t| ((setOperators: GetPgSetOperators) => SetOperatorRightSelect<TValue, TResult>)\n\t\t\t| SetOperatorRightSelect<TValue, TResult>,\n\t) => PgSelectWithout<\n\t\tthis,\n\t\tTDynamic,\n\t\tPgSetOperatorExcludedMethods,\n\t\ttrue\n\t> {\n\t\treturn (rightSelection) => {\n\t\t\tconst rightSelect = (typeof rightSelection === 'function'\n\t\t\t\t? rightSelection(getPgSetOperators())\n\t\t\t\t: rightSelection) as TypedQueryBuilder<\n\t\t\t\t\tany,\n\t\t\t\t\tTResult\n\t\t\t\t>;\n\n\t\t\tif (!haveSameKeys(this.getSelectedFields(), rightSelect.getSelectedFields())) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t'Set operator error (union / intersect / except): selected fields are not the same or are in a different order',\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthis.config.setOperators.push({ type, isAll, rightSelect });\n\t\t\treturn this as any;\n\t\t};\n\t}\n\n\t/**\n\t * Adds `union` set operator to the query.\n\t *\n\t * Calling this method will combine the result sets of the `select` statements and remove any duplicate rows that appear across them.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#union}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all unique names from customers and users tables\n\t * await db.select({ name: users.name })\n\t *   .from(users)\n\t *   .union(\n\t *     db.select({ name: customers.name }).from(customers)\n\t *   );\n\t * // or\n\t * import { union } from 'drizzle-orm/pg-core'\n\t *\n\t * await union(\n\t *   db.select({ name: users.name }).from(users),\n\t *   db.select({ name: customers.name }).from(customers)\n\t * );\n\t * ```\n\t */\n\tunion = this.createSetOperator('union', false);\n\n\t/**\n\t * Adds `union all` set operator to the query.\n\t *\n\t * Calling this method will combine the result-set of the `select` statements and keep all duplicate rows that appear across them.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#union-all}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all transaction ids from both online and in-store sales\n\t * await db.select({ transaction: onlineSales.transactionId })\n\t *   .from(onlineSales)\n\t *   .unionAll(\n\t *     db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n\t *   );\n\t * // or\n\t * import { unionAll } from 'drizzle-orm/pg-core'\n\t *\n\t * await unionAll(\n\t *   db.select({ transaction: onlineSales.transactionId }).from(onlineSales),\n\t *   db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n\t * );\n\t * ```\n\t */\n\tunionAll = this.createSetOperator('union', true);\n\n\t/**\n\t * Adds `intersect` set operator to the query.\n\t *\n\t * Calling this method will retain only the rows that are present in both result sets and eliminate duplicates.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select course names that are offered in both departments A and B\n\t * await db.select({ courseName: depA.courseName })\n\t *   .from(depA)\n\t *   .intersect(\n\t *     db.select({ courseName: depB.courseName }).from(depB)\n\t *   );\n\t * // or\n\t * import { intersect } from 'drizzle-orm/pg-core'\n\t *\n\t * await intersect(\n\t *   db.select({ courseName: depA.courseName }).from(depA),\n\t *   db.select({ courseName: depB.courseName }).from(depB)\n\t * );\n\t * ```\n\t */\n\tintersect = this.createSetOperator('intersect', false);\n\n\t/**\n\t * Adds `intersect all` set operator to the query.\n\t *\n\t * Calling this method will retain only the rows that are present in both result sets including all duplicates.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect-all}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all products and quantities that are ordered by both regular and VIP customers\n\t * await db.select({\n\t *   productId: regularCustomerOrders.productId,\n\t *   quantityOrdered: regularCustomerOrders.quantityOrdered\n\t * })\n\t * .from(regularCustomerOrders)\n\t * .intersectAll(\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * // or\n\t * import { intersectAll } from 'drizzle-orm/pg-core'\n\t *\n\t * await intersectAll(\n\t *   db.select({\n\t *     productId: regularCustomerOrders.productId,\n\t *     quantityOrdered: regularCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(regularCustomerOrders),\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * ```\n\t */\n\tintersectAll = this.createSetOperator('intersect', true);\n\n\t/**\n\t * Adds `except` set operator to the query.\n\t *\n\t * Calling this method will retrieve all unique rows from the left query, except for the rows that are present in the result set of the right query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#except}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all courses offered in department A but not in department B\n\t * await db.select({ courseName: depA.courseName })\n\t *   .from(depA)\n\t *   .except(\n\t *     db.select({ courseName: depB.courseName }).from(depB)\n\t *   );\n\t * // or\n\t * import { except } from 'drizzle-orm/pg-core'\n\t *\n\t * await except(\n\t *   db.select({ courseName: depA.courseName }).from(depA),\n\t *   db.select({ courseName: depB.courseName }).from(depB)\n\t * );\n\t * ```\n\t */\n\texcept = this.createSetOperator('except', false);\n\n\t/**\n\t * Adds `except all` set operator to the query.\n\t *\n\t * Calling this method will retrieve all rows from the left query, except for the rows that are present in the result set of the right query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#except-all}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all products that are ordered by regular customers but not by VIP customers\n\t * await db.select({\n\t *   productId: regularCustomerOrders.productId,\n\t *   quantityOrdered: regularCustomerOrders.quantityOrdered,\n\t * })\n\t * .from(regularCustomerOrders)\n\t * .exceptAll(\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered,\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * // or\n\t * import { exceptAll } from 'drizzle-orm/pg-core'\n\t *\n\t * await exceptAll(\n\t *   db.select({\n\t *     productId: regularCustomerOrders.productId,\n\t *     quantityOrdered: regularCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(regularCustomerOrders),\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * ```\n\t */\n\texceptAll = this.createSetOperator('except', true);\n\n\t/** @internal */\n\taddSetOperators(setOperators: PgSelectConfig['setOperators']): PgSelectWithout<\n\t\tthis,\n\t\tTDynamic,\n\t\tPgSetOperatorExcludedMethods,\n\t\ttrue\n\t> {\n\t\tthis.config.setOperators.push(...setOperators);\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `where` clause to the query.\n\t *\n\t * Calling this method will select only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#filtering}\n\t *\n\t * @param where the `where` clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be selected.\n\t *\n\t * ```ts\n\t * // Select all cars with green color\n\t * await db.select().from(cars).where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.select().from(cars).where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Select all BMW cars with a green color\n\t * await db.select().from(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Select all cars with the green or blue color\n\t * await db.select().from(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(\n\t\twhere: ((aliases: this['_']['selection']) => SQL | undefined) | SQL | undefined,\n\t): PgSelectWithout<this, TDynamic, 'where'> {\n\t\tif (typeof where === 'function') {\n\t\t\twhere = where(\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t}\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `having` clause to the query.\n\t *\n\t * Calling this method will select only those rows that fulfill a specified condition. It is typically used with aggregate functions to filter the aggregated data based on a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#aggregations}\n\t *\n\t * @param having the `having` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all brands with more than one car\n\t * await db.select({\n\t * \tbrand: cars.brand,\n\t * \tcount: sql<number>`cast(count(${cars.id}) as int)`,\n\t * })\n\t *   .from(cars)\n\t *   .groupBy(cars.brand)\n\t *   .having(({ count }) => gt(count, 1));\n\t * ```\n\t */\n\thaving(\n\t\thaving: ((aliases: this['_']['selection']) => SQL | undefined) | SQL | undefined,\n\t): PgSelectWithout<this, TDynamic, 'having'> {\n\t\tif (typeof having === 'function') {\n\t\t\thaving = having(\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t}\n\t\tthis.config.having = having;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `group by` clause to the query.\n\t *\n\t * Calling this method will group rows that have the same values into summary rows, often used for aggregation purposes.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#aggregations}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Group and count people by their last names\n\t * await db.select({\n\t *    lastName: people.lastName,\n\t *    count: sql<number>`cast(count(*) as int)`\n\t * })\n\t *   .from(people)\n\t *   .groupBy(people.lastName);\n\t * ```\n\t */\n\tgroupBy(\n\t\tbuilder: (aliases: this['_']['selection']) => ValueOrArray<PgColumn | SQL | SQL.Aliased>,\n\t): PgSelectWithout<this, TDynamic, 'groupBy'>;\n\tgroupBy(...columns: (PgColumn | SQL | SQL.Aliased)[]): PgSelectWithout<this, TDynamic, 'groupBy'>;\n\tgroupBy(\n\t\t...columns:\n\t\t\t| [(aliases: this['_']['selection']) => ValueOrArray<PgColumn | SQL | SQL.Aliased>]\n\t\t\t| (PgColumn | SQL | SQL.Aliased)[]\n\t): PgSelectWithout<this, TDynamic, 'groupBy'> {\n\t\tif (typeof columns[0] === 'function') {\n\t\t\tconst groupBy = columns[0](\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'alias', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t\tthis.config.groupBy = Array.isArray(groupBy) ? groupBy : [groupBy];\n\t\t} else {\n\t\t\tthis.config.groupBy = columns as (PgColumn | SQL | SQL.Aliased)[];\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `order by` clause to the query.\n\t *\n\t * Calling this method will sort the result-set in ascending or descending order. By default, the sort order is ascending.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#order-by}\n\t *\n\t * @example\n\t *\n\t * ```\n\t * // Select cars ordered by year\n\t * await db.select().from(cars).orderBy(cars.year);\n\t * ```\n\t *\n\t * You can specify whether results are in ascending or descending order with the `asc()` and `desc()` operators.\n\t *\n\t * ```ts\n\t * // Select cars ordered by year in descending order\n\t * await db.select().from(cars).orderBy(desc(cars.year));\n\t *\n\t * // Select cars ordered by year and price\n\t * await db.select().from(cars).orderBy(asc(cars.year), desc(cars.price));\n\t * ```\n\t */\n\torderBy(\n\t\tbuilder: (aliases: this['_']['selection']) => ValueOrArray<PgColumn | SQL | SQL.Aliased>,\n\t): PgSelectWithout<this, TDynamic, 'orderBy'>;\n\torderBy(...columns: (PgColumn | SQL | SQL.Aliased)[]): PgSelectWithout<this, TDynamic, 'orderBy'>;\n\torderBy(\n\t\t...columns:\n\t\t\t| [(aliases: this['_']['selection']) => ValueOrArray<PgColumn | SQL | SQL.Aliased>]\n\t\t\t| (PgColumn | SQL | SQL.Aliased)[]\n\t): PgSelectWithout<this, TDynamic, 'orderBy'> {\n\t\tif (typeof columns[0] === 'function') {\n\t\t\tconst orderBy = columns[0](\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'alias', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\n\t\t\tconst orderByArray = Array.isArray(orderBy) ? orderBy : [orderBy];\n\n\t\t\tif (this.config.setOperators.length > 0) {\n\t\t\t\tthis.config.setOperators.at(-1)!.orderBy = orderByArray;\n\t\t\t} else {\n\t\t\t\tthis.config.orderBy = orderByArray;\n\t\t\t}\n\t\t} else {\n\t\t\tconst orderByArray = columns as (PgColumn | SQL | SQL.Aliased)[];\n\n\t\t\tif (this.config.setOperators.length > 0) {\n\t\t\t\tthis.config.setOperators.at(-1)!.orderBy = orderByArray;\n\t\t\t} else {\n\t\t\t\tthis.config.orderBy = orderByArray;\n\t\t\t}\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `limit` clause to the query.\n\t *\n\t * Calling this method will set the maximum number of rows that will be returned by this query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#limit--offset}\n\t *\n\t * @param limit the `limit` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Get the first 10 people from this query.\n\t * await db.select().from(people).limit(10);\n\t * ```\n\t */\n\tlimit(limit: number | Placeholder): PgSelectWithout<this, TDynamic, 'limit'> {\n\t\tif (this.config.setOperators.length > 0) {\n\t\t\tthis.config.setOperators.at(-1)!.limit = limit;\n\t\t} else {\n\t\t\tthis.config.limit = limit;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `offset` clause to the query.\n\t *\n\t * Calling this method will skip a number of rows when returning results from this query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#limit--offset}\n\t *\n\t * @param offset the `offset` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Get the 10th-20th people from this query.\n\t * await db.select().from(people).offset(10).limit(10);\n\t * ```\n\t */\n\toffset(offset: number | Placeholder): PgSelectWithout<this, TDynamic, 'offset'> {\n\t\tif (this.config.setOperators.length > 0) {\n\t\t\tthis.config.setOperators.at(-1)!.offset = offset;\n\t\t} else {\n\t\t\tthis.config.offset = offset;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `for` clause to the query.\n\t *\n\t * Calling this method will specify a lock strength for this query that controls how strictly it acquires exclusive access to the rows being queried.\n\t *\n\t * See docs: {@link https://www.postgresql.org/docs/current/sql-select.html#SQL-FOR-UPDATE-SHARE}\n\t *\n\t * @param strength the lock strength.\n\t * @param config the lock configuration.\n\t */\n\tfor(strength: LockStrength, config: LockConfig = {}): PgSelectWithout<this, TDynamic, 'for'> {\n\t\tthis.config.lockingClause = { strength, config };\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildSelectQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\tas<TAlias extends string>(\n\t\talias: TAlias,\n\t): SubqueryWithSelection<this['_']['selectedFields'], TAlias> {\n\t\treturn new Proxy(\n\t\t\tnew Subquery(this.getSQL(), this.config.fields, alias),\n\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t) as SubqueryWithSelection<this['_']['selectedFields'], TAlias>;\n\t}\n\n\t/** @internal */\n\toverride getSelectedFields(): this['_']['selectedFields'] {\n\t\treturn new Proxy(\n\t\t\tthis.config.fields,\n\t\t\tnew SelectionProxyHandler({ alias: this.tableName, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t) as this['_']['selectedFields'];\n\t}\n\n\t$dynamic(): PgSelectDynamic<this> {\n\t\treturn this;\n\t}\n}\n\nexport interface PgSelectBase<\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends\n\tPgSelectQueryBuilderBase<\n\t\tPgSelectHKT,\n\t\tTTableName,\n\t\tTSelection,\n\t\tTSelectMode,\n\t\tTNullabilityMap,\n\t\tTDynamic,\n\t\tTExcludedMethods,\n\t\tTResult,\n\t\tTSelectedFields\n\t>,\n\tQueryPromise<TResult>,\n\tSQLWrapper\n{}\n\nexport class PgSelectBase<\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends PgSelectQueryBuilderBase<\n\tPgSelectHKT,\n\tTTableName,\n\tTSelection,\n\tTSelectMode,\n\tTNullabilityMap,\n\tTDynamic,\n\tTExcludedMethods,\n\tTResult,\n\tTSelectedFields\n> implements RunnableQuery<TResult, 'pg'>, SQLWrapper {\n\tstatic override readonly [entityKind]: string = 'PgSelect';\n\n\t/** @internal */\n\t_prepare(name?: string): PgSelectPrepare<this> {\n\t\tconst { session, config, dialect, joinsNotNullableMap, authToken } = this;\n\t\tif (!session) {\n\t\t\tthrow new Error('Cannot execute a query on a query builder. Please use a database instance instead.');\n\t\t}\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\tconst fieldsList = orderSelectedFields<PgColumn>(config.fields);\n\t\t\tconst query = session.prepareQuery<\n\t\t\t\tPreparedQueryConfig & { execute: TResult }\n\t\t\t>(dialect.sqlToQuery(this.getSQL()), fieldsList, name, true);\n\t\t\tquery.joinsNotNullableMap = joinsNotNullableMap;\n\n\t\t\treturn authToken === undefined ? query : query.setToken(authToken);\n\t\t});\n\t}\n\n\t/**\n\t * Create a prepared statement for this query. This allows\n\t * the database to remember this query for the given session\n\t * and call it by name, rather than specifying the full query.\n\t *\n\t * {@link https://www.postgresql.org/docs/current/sql-prepare.html | Postgres prepare documentation}\n\t */\n\tprepare(name: string): PgSelectPrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate authToken?: string;\n\t/** @internal */\n\tsetToken(token: string) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\texecute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues, this.authToken);\n\t\t});\n\t};\n}\n\napplyMixins(PgSelectBase, [QueryPromise]);\n\nfunction createSetOperator(type: SetOperator, isAll: boolean): PgCreateSetOperatorFn {\n\treturn (leftSelect, rightSelect, ...restSelects) => {\n\t\tconst setOperators = [rightSelect, ...restSelects].map((select) => ({\n\t\t\ttype,\n\t\t\tisAll,\n\t\t\trightSelect: select as AnyPgSelect,\n\t\t}));\n\n\t\tfor (const setOperator of setOperators) {\n\t\t\tif (!haveSameKeys((leftSelect as any).getSelectedFields(), setOperator.rightSelect.getSelectedFields())) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t'Set operator error (union / intersect / except): selected fields are not the same or are in a different order',\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\treturn (leftSelect as AnyPgSelect).addSetOperators(setOperators) as any;\n\t};\n}\n\nconst getPgSetOperators = () => ({\n\tunion,\n\tunionAll,\n\tintersect,\n\tintersectAll,\n\texcept,\n\texceptAll,\n});\n\n/**\n * Adds `union` set operator to the query.\n *\n * Calling this method will combine the result sets of the `select` statements and remove any duplicate rows that appear across them.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#union}\n *\n * @example\n *\n * ```ts\n * // Select all unique names from customers and users tables\n * import { union } from 'drizzle-orm/pg-core'\n *\n * await union(\n *   db.select({ name: users.name }).from(users),\n *   db.select({ name: customers.name }).from(customers)\n * );\n * // or\n * await db.select({ name: users.name })\n *   .from(users)\n *   .union(\n *     db.select({ name: customers.name }).from(customers)\n *   );\n * ```\n */\nexport const union = createSetOperator('union', false);\n\n/**\n * Adds `union all` set operator to the query.\n *\n * Calling this method will combine the result-set of the `select` statements and keep all duplicate rows that appear across them.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#union-all}\n *\n * @example\n *\n * ```ts\n * // Select all transaction ids from both online and in-store sales\n * import { unionAll } from 'drizzle-orm/pg-core'\n *\n * await unionAll(\n *   db.select({ transaction: onlineSales.transactionId }).from(onlineSales),\n *   db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n * );\n * // or\n * await db.select({ transaction: onlineSales.transactionId })\n *   .from(onlineSales)\n *   .unionAll(\n *     db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n *   );\n * ```\n */\nexport const unionAll = createSetOperator('union', true);\n\n/**\n * Adds `intersect` set operator to the query.\n *\n * Calling this method will retain only the rows that are present in both result sets and eliminate duplicates.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect}\n *\n * @example\n *\n * ```ts\n * // Select course names that are offered in both departments A and B\n * import { intersect } from 'drizzle-orm/pg-core'\n *\n * await intersect(\n *   db.select({ courseName: depA.courseName }).from(depA),\n *   db.select({ courseName: depB.courseName }).from(depB)\n * );\n * // or\n * await db.select({ courseName: depA.courseName })\n *   .from(depA)\n *   .intersect(\n *     db.select({ courseName: depB.courseName }).from(depB)\n *   );\n * ```\n */\nexport const intersect = createSetOperator('intersect', false);\n\n/**\n * Adds `intersect all` set operator to the query.\n *\n * Calling this method will retain only the rows that are present in both result sets including all duplicates.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect-all}\n *\n * @example\n *\n * ```ts\n * // Select all products and quantities that are ordered by both regular and VIP customers\n * import { intersectAll } from 'drizzle-orm/pg-core'\n *\n * await intersectAll(\n *   db.select({\n *     productId: regularCustomerOrders.productId,\n *     quantityOrdered: regularCustomerOrders.quantityOrdered\n *   })\n *   .from(regularCustomerOrders),\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered\n *   })\n *   .from(vipCustomerOrders)\n * );\n * // or\n * await db.select({\n *   productId: regularCustomerOrders.productId,\n *   quantityOrdered: regularCustomerOrders.quantityOrdered\n * })\n * .from(regularCustomerOrders)\n * .intersectAll(\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered\n *   })\n *   .from(vipCustomerOrders)\n * );\n * ```\n */\nexport const intersectAll = createSetOperator('intersect', true);\n\n/**\n * Adds `except` set operator to the query.\n *\n * Calling this method will retrieve all unique rows from the left query, except for the rows that are present in the result set of the right query.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#except}\n *\n * @example\n *\n * ```ts\n * // Select all courses offered in department A but not in department B\n * import { except } from 'drizzle-orm/pg-core'\n *\n * await except(\n *   db.select({ courseName: depA.courseName }).from(depA),\n *   db.select({ courseName: depB.courseName }).from(depB)\n * );\n * // or\n * await db.select({ courseName: depA.courseName })\n *   .from(depA)\n *   .except(\n *     db.select({ courseName: depB.courseName }).from(depB)\n *   );\n * ```\n */\nexport const except = createSetOperator('except', false);\n\n/**\n * Adds `except all` set operator to the query.\n *\n * Calling this method will retrieve all rows from the left query, except for the rows that are present in the result set of the right query.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#except-all}\n *\n * @example\n *\n * ```ts\n * // Select all products that are ordered by regular customers but not by VIP customers\n * import { exceptAll } from 'drizzle-orm/pg-core'\n *\n * await exceptAll(\n *   db.select({\n *     productId: regularCustomerOrders.productId,\n *     quantityOrdered: regularCustomerOrders.quantityOrdered\n *   })\n *   .from(regularCustomerOrders),\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered\n *   })\n *   .from(vipCustomerOrders)\n * );\n * // or\n * await db.select({\n *   productId: regularCustomerOrders.productId,\n *   quantityOrdered: regularCustomerOrders.quantityOrdered,\n * })\n * .from(regularCustomerOrders)\n * .exceptAll(\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered,\n *   })\n *   .from(vipCustomerOrders)\n * );\n * ```\n */\nexport const exceptAll = createSetOperator('except', true);\n", "import { entityKind } from '~/entity.ts';\nimport type { SQL, SQLWrapper } from '~/sql/index.ts';\n\nexport abstract class TypedQueryBuilder<TSelection, TResult = unknown> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'TypedQueryBuilder';\n\n\tdeclare _: {\n\t\tselectedFields: TSelection;\n\t\tresult: TResult;\n\t};\n\n\t/** @internal */\n\tgetSelectedFields(): TSelection {\n\t\treturn this._.selectedFields;\n\t}\n\n\tabstract getSQL(): SQL;\n}\n", "import { entityKind } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgMaterializedView } from '~/pg-core/view.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface PgRefreshMaterializedView<TQueryResult extends PgQueryResultHKT>\n\textends\n\t\tQueryPromise<PgQueryResultKind<TQueryResult, never>>,\n\t\tRunnableQuery<PgQueryResultKind<TQueryResult, never>, 'pg'>,\n\t\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly result: PgQueryResultKind<TQueryResult, never>;\n\t};\n}\n\nexport class PgRefreshMaterializedView<TQueryResult extends PgQueryResultHKT>\n\textends QueryPromise<PgQueryResultKind<TQueryResult, never>>\n\timplements RunnableQuery<PgQueryResultKind<TQueryResult, never>, 'pg'>, SQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'PgRefreshMaterializedView';\n\n\tprivate config: {\n\t\tview: PgMaterializedView;\n\t\tconcurrently?: boolean;\n\t\twithNoData?: boolean;\n\t};\n\n\tconstructor(\n\t\tview: PgMaterializedView,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t) {\n\t\tsuper();\n\t\tthis.config = { view };\n\t}\n\n\tconcurrently(): this {\n\t\tif (this.config.withNoData !== undefined) {\n\t\t\tthrow new Error('Cannot use concurrently and withNoData together');\n\t\t}\n\t\tthis.config.concurrently = true;\n\t\treturn this;\n\t}\n\n\twithNoData(): this {\n\t\tif (this.config.concurrently !== undefined) {\n\t\t\tthrow new Error('Cannot use concurrently and withNoData together');\n\t\t}\n\t\tthis.config.withNoData = true;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildRefreshMaterializedViewQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgPreparedQuery<\n\t\tPreparedQueryConfig & {\n\t\t\texecute: PgQueryResultKind<TQueryResult, never>;\n\t\t}\n\t> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()), undefined, name, true);\n\t\t});\n\t}\n\n\tprepare(name: string): PgPreparedQuery<\n\t\tPreparedQueryConfig & {\n\t\t\texecute: PgQueryResultKind<TQueryResult, never>;\n\t\t}\n\t> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate authToken?: string;\n\t/** @internal */\n\tsetToken(token: string) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\texecute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues, this.authToken);\n\t\t});\n\t};\n}\n", "import type { GetColumnData } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport { PgTable } from '~/pg-core/table.ts';\nimport type {\n\tAppendToNullabilityMap,\n\tAppendToResult,\n\tGetSelectTableName,\n\tGetSelectTableSelection,\n\tJoinNullability,\n\tJoinType,\n\tSelectMode,\n\tSelectResult,\n} from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport { type ColumnsSelection, type Query, SQL, type SQLWrapper } from '~/sql/sql.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { type Assume, getTableLikeName, mapUpdateSet, orderSelectedFields, type UpdateSet } from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type { PgColumn } from '../columns/common.ts';\nimport type { PgViewBase } from '../view-base.ts';\nimport type { PgSelectJoinConfig, SelectedFields, SelectedFieldsOrdered } from './select.types.ts';\n\nexport interface PgUpdateConfig {\n\twhere?: SQL | undefined;\n\tset: UpdateSet;\n\ttable: PgTable;\n\tfrom?: PgTable | Subquery | PgViewBase | SQL;\n\tjoins: PgSelectJoinConfig[];\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type PgUpdateSetSource<TTable extends PgTable> =\n\t& {\n\t\t[Key in keyof TTable['$inferInsert']]?:\n\t\t\t| GetColumnData<TTable['_']['columns'][Key]>\n\t\t\t| SQL\n\t\t\t| PgColumn;\n\t}\n\t& {};\n\nexport class PgUpdateBuilder<TTable extends PgTable, TQueryResult extends PgQueryResultHKT> {\n\tstatic readonly [entityKind]: string = 'PgUpdateBuilder';\n\n\tdeclare readonly _: {\n\t\treadonly table: TTable;\n\t};\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\tprivate withList?: Subquery[],\n\t) {}\n\n\tprivate authToken?: string;\n\tsetToken(token: string) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\tset(\n\t\tvalues: PgUpdateSetSource<TTable>,\n\t): PgUpdateWithout<PgUpdateBase<TTable, TQueryResult>, false, 'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'> {\n\t\treturn this.authToken === undefined\n\t\t\t? new PgUpdateBase<TTable, TQueryResult>(\n\t\t\t\tthis.table,\n\t\t\t\tmapUpdateSet(this.table, values),\n\t\t\t\tthis.session,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.withList,\n\t\t\t)\n\t\t\t: new PgUpdateBase<TTable, TQueryResult>(\n\t\t\t\tthis.table,\n\t\t\t\tmapUpdateSet(this.table, values),\n\t\t\t\tthis.session,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.withList,\n\t\t\t).setToken(this.authToken);\n\t}\n}\n\nexport type PgUpdateWithout<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T : Omit<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['from'],\n\t\tT['_']['returning'],\n\t\tT['_']['nullabilityMap'],\n\t\tT['_']['joins'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods'] | K\n\t>,\n\tT['_']['excludedMethods'] | K\n>;\n\nexport type PgUpdateWithJoins<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tTFrom extends PgTable | Subquery | PgViewBase | SQL,\n> = TDynamic extends true ? T : Omit<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tTFrom,\n\t\tT['_']['returning'],\n\t\tAppendToNullabilityMap<T['_']['nullabilityMap'], GetSelectTableName<TFrom>, 'inner'>,\n\t\t[...T['_']['joins'], {\n\t\t\tname: GetSelectTableName<TFrom>;\n\t\t\tjoinType: 'inner';\n\t\t\ttable: TFrom;\n\t\t}],\n\t\tTDynamic,\n\t\tExclude<T['_']['excludedMethods'] | 'from', 'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'>\n\t>,\n\tExclude<T['_']['excludedMethods'] | 'from', 'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'>\n>;\n\nexport type PgUpdateJoinFn<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tTJoinType extends JoinType,\n> = <\n\tTJoinedTable extends PgTable | Subquery | PgViewBase | SQL,\n>(\n\ttable: TJoinedTable,\n\ton:\n\t\t| (\n\t\t\t(\n\t\t\t\tupdateTable: T['_']['table']['_']['columns'],\n\t\t\t\tfrom: T['_']['from'] extends PgTable ? T['_']['from']['_']['columns']\n\t\t\t\t\t: T['_']['from'] extends Subquery | PgViewBase ? T['_']['from']['_']['selectedFields']\n\t\t\t\t\t: never,\n\t\t\t) => SQL | undefined\n\t\t)\n\t\t| SQL\n\t\t| undefined,\n) => PgUpdateJoin<T, TDynamic, TJoinType, TJoinedTable>;\n\nexport type PgUpdateJoin<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tTJoinType extends JoinType,\n\tTJoinedTable extends PgTable | Subquery | PgViewBase | SQL,\n> = TDynamic extends true ? T : PgUpdateBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['from'],\n\tT['_']['returning'],\n\tAppendToNullabilityMap<T['_']['nullabilityMap'], GetSelectTableName<TJoinedTable>, TJoinType>,\n\t[...T['_']['joins'], {\n\t\tname: GetSelectTableName<TJoinedTable>;\n\t\tjoinType: TJoinType;\n\t\ttable: TJoinedTable;\n\t}],\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\ntype Join = {\n\tname: string | undefined;\n\tjoinType: JoinType;\n\ttable: PgTable | Subquery | PgViewBase | SQL;\n};\n\ntype AccumulateToResult<\n\tT extends AnyPgUpdate,\n\tTSelectMode extends SelectMode,\n\tTJoins extends Join[],\n\tTSelectedFields extends ColumnsSelection,\n> = TJoins extends [infer TJoin extends Join, ...infer TRest extends Join[]] ? AccumulateToResult<\n\t\tT,\n\t\tTSelectMode extends 'partial' ? TSelectMode : 'multiple',\n\t\tTRest,\n\t\tAppendToResult<\n\t\t\tT['_']['table']['_']['name'],\n\t\t\tTSelectedFields,\n\t\t\tTJoin['name'],\n\t\t\tTJoin['table'] extends Table ? TJoin['table']['_']['columns']\n\t\t\t\t: TJoin['table'] extends Subquery ? Assume<TJoin['table']['_']['selectedFields'], SelectedFields>\n\t\t\t\t: never,\n\t\t\tTSelectMode extends 'partial' ? TSelectMode : 'multiple'\n\t\t>\n\t>\n\t: TSelectedFields;\n\nexport type PgUpdateReturningAll<T extends AnyPgUpdate, TDynamic extends boolean> = PgUpdateWithout<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['from'],\n\t\tSelectResult<\n\t\t\tAccumulateToResult<\n\t\t\t\tT,\n\t\t\t\t'single',\n\t\t\t\tT['_']['joins'],\n\t\t\t\tGetSelectTableSelection<T['_']['table']>\n\t\t\t>,\n\t\t\t'partial',\n\t\t\tT['_']['nullabilityMap']\n\t\t>,\n\t\tT['_']['nullabilityMap'],\n\t\tT['_']['joins'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgUpdateReturning<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFields,\n> = PgUpdateWithout<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['from'],\n\t\tSelectResult<\n\t\t\tAccumulateToResult<\n\t\t\t\tT,\n\t\t\t\t'partial',\n\t\t\t\tT['_']['joins'],\n\t\t\t\tTSelectedFields\n\t\t\t>,\n\t\t\t'partial',\n\t\t\tT['_']['nullabilityMap']\n\t\t>,\n\t\tT['_']['nullabilityMap'],\n\t\tT['_']['joins'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgUpdatePrepare<T extends AnyPgUpdate> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? PgQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type PgUpdateDynamic<T extends AnyPgUpdate> = PgUpdate<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['from'],\n\tT['_']['returning'],\n\tT['_']['nullabilityMap']\n>;\n\nexport type PgUpdate<\n\tTTable extends PgTable = PgTable,\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTFrom extends PgTable | Subquery | PgViewBase | SQL | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<TTable['_']['name'], 'not-null'>,\n\tTJoins extends Join[] = [],\n> = PgUpdateBase<TTable, TQueryResult, TFrom, TReturning, TNullabilityMap, TJoins, true, never>;\n\nexport type AnyPgUpdate = PgUpdateBase<any, any, any, any, any, any, any, any>;\n\nexport interface PgUpdateBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTFrom extends PgTable | Subquery | PgViewBase | SQL | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<TTable['_']['name'], 'not-null'>,\n\tTJoins extends Join[] = [],\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tQueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly table: TTable;\n\t\treadonly joins: TJoins;\n\t\treadonly nullabilityMap: TNullabilityMap;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly from: TFrom;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class PgUpdateBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTFrom extends PgTable | Subquery | PgViewBase | SQL | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<TTable['_']['name'], 'not-null'>,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTJoins extends Join[] = [],\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\t\tSQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'PgUpdate';\n\n\tprivate config: PgUpdateConfig;\n\tprivate tableName: string | undefined;\n\tprivate joinsNotNullableMap: Record<string, boolean>;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tset: UpdateSet,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { set, table, withList, joins: [] };\n\t\tthis.tableName = getTableLikeName(table);\n\t\tthis.joinsNotNullableMap = typeof this.tableName === 'string' ? { [this.tableName]: true } : {};\n\t}\n\n\tfrom<TFrom extends PgTable | Subquery | PgViewBase | SQL>(\n\t\tsource: TFrom,\n\t): PgUpdateWithJoins<this, TDynamic, TFrom> {\n\t\tconst tableName = getTableLikeName(source);\n\t\tif (typeof tableName === 'string') {\n\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t}\n\t\tthis.config.from = source;\n\t\treturn this as any;\n\t}\n\n\tprivate getTableLikeFields(table: PgTable | Subquery | PgViewBase): Record<string, unknown> {\n\t\tif (is(table, PgTable)) {\n\t\t\treturn table[Table.Symbol.Columns];\n\t\t} else if (is(table, Subquery)) {\n\t\t\treturn table._.selectedFields;\n\t\t}\n\t\treturn table[ViewBaseConfig].selectedFields;\n\t}\n\n\tprivate createJoin<TJoinType extends JoinType>(\n\t\tjoinType: TJoinType,\n\t): PgUpdateJoinFn<this, TDynamic, TJoinType> {\n\t\treturn ((\n\t\t\ttable: PgTable | Subquery | PgViewBase | SQL,\n\t\t\ton: ((updateTable: TTable, from: TFrom) => SQL | undefined) | SQL | undefined,\n\t\t) => {\n\t\t\tconst tableName = getTableLikeName(table);\n\n\t\t\tif (typeof tableName === 'string' && this.config.joins.some((join) => join.alias === tableName)) {\n\t\t\t\tthrow new Error(`Alias \"${tableName}\" is already used in this query`);\n\t\t\t}\n\n\t\t\tif (typeof on === 'function') {\n\t\t\t\tconst from = this.config.from && !is(this.config.from, SQL)\n\t\t\t\t\t? this.getTableLikeFields(this.config.from)\n\t\t\t\t\t: undefined;\n\t\t\t\ton = on(\n\t\t\t\t\tnew Proxy(\n\t\t\t\t\t\tthis.config.table[Table.Symbol.Columns],\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as any,\n\t\t\t\t\tfrom && new Proxy(\n\t\t\t\t\t\tfrom,\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as any,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthis.config.joins.push({ on, table, joinType, alias: tableName });\n\n\t\t\tif (typeof tableName === 'string') {\n\t\t\t\tswitch (joinType) {\n\t\t\t\t\tcase 'left': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'right': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'inner': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'full': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn this as any;\n\t\t}) as any;\n\t}\n\n\tleftJoin = this.createJoin('left');\n\n\trightJoin = this.createJoin('right');\n\n\tinnerJoin = this.createJoin('inner');\n\n\tfullJoin = this.createJoin('full');\n\n\t/**\n\t * Adds a 'where' clause to the query.\n\t *\n\t * Calling this method will update only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t *\n\t * @param where the 'where' clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be updated.\n\t *\n\t * ```ts\n\t * // Update all cars with green color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Update all BMW cars with a green color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Update all cars with the green or blue color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): PgUpdateWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the updated rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update#update-with-returning}\n\t *\n\t * @example\n\t * ```ts\n\t * // Update all cars with the green color and return all fields\n\t * const updatedCars: Car[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning();\n\t *\n\t * // Update all cars with the green color and return only their id and brand fields\n\t * const updatedCarsIdsAndBrands: { id: number, brand: string }[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning({ id: cars.id, brand: cars.brand });\n\t * ```\n\t */\n\treturning(): PgUpdateReturningAll<this, TDynamic>;\n\treturning<TSelectedFields extends SelectedFields>(\n\t\tfields: TSelectedFields,\n\t): PgUpdateReturning<this, TDynamic, TSelectedFields>;\n\treturning(\n\t\tfields?: SelectedFields,\n\t): PgUpdateWithout<AnyPgUpdate, TDynamic, 'returning'> {\n\t\tif (!fields) {\n\t\t\tfields = Object.assign({}, this.config.table[Table.Symbol.Columns]);\n\n\t\t\tif (this.config.from) {\n\t\t\t\tconst tableName = getTableLikeName(this.config.from);\n\n\t\t\t\tif (typeof tableName === 'string' && this.config.from && !is(this.config.from, SQL)) {\n\t\t\t\t\tconst fromFields = this.getTableLikeFields(this.config.from);\n\t\t\t\t\tfields[tableName] = fromFields as any;\n\t\t\t\t}\n\n\t\t\t\tfor (const join of this.config.joins) {\n\t\t\t\t\tconst tableName = getTableLikeName(join.table);\n\n\t\t\t\t\tif (typeof tableName === 'string' && !is(join.table, SQL)) {\n\t\t\t\t\t\tconst fromFields = this.getTableLikeFields(join.table);\n\t\t\t\t\t\tfields[tableName] = fromFields as any;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.config.returning = orderSelectedFields<PgColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildUpdateQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgUpdatePrepare<this> {\n\t\tconst query = this.session.prepareQuery<\n\t\t\tPreparedQueryConfig & { execute: TReturning[] }\n\t\t>(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true);\n\t\tquery.joinsNotNullableMap = this.joinsNotNullableMap;\n\t\treturn query;\n\t}\n\n\tprepare(name: string): PgUpdatePrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate authToken?: string;\n\t/** @internal */\n\tsetToken(token: string) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn this._prepare().execute(placeholderValues, this.authToken);\n\t};\n\n\t$dynamic(): PgUpdateDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n", "import { entityKind } from '~/entity.ts';\nimport { SQL, sql, type S<PERSON>Wrapper } from '~/sql/sql.ts';\nimport type { PgSession } from '../session.ts';\nimport type { PgTable } from '../table.ts';\n\nexport class PgCountBuilder<\n\tTSession extends PgSession<any, any, any>,\n> extends SQL<number> implements Promise<number>, SQLWrapper {\n\tprivate sql: SQL<number>;\n\tprivate token?: string;\n\n\tstatic override readonly [entityKind] = 'PgCountBuilder';\n\t[Symbol.toStringTag] = 'PgCountBuilder';\n\n\tprivate session: TSession;\n\n\tprivate static buildEmbeddedCount(\n\t\tsource: PgTable | SQL | SQLWrapper,\n\t\tfilters?: SQL<unknown>,\n\t): SQL<number> {\n\t\treturn sql<number>`(select count(*) from ${source}${sql.raw(' where ').if(filters)}${filters})`;\n\t}\n\n\tprivate static buildCount(\n\t\tsource: PgTable | SQL | SQLWrapper,\n\t\tfilters?: SQL<unknown>,\n\t): SQL<number> {\n\t\treturn sql<number>`select count(*) as count from ${source}${sql.raw(' where ').if(filters)}${filters};`;\n\t}\n\n\tconstructor(\n\t\treadonly params: {\n\t\t\tsource: PgTable | SQL | SQLWrapper;\n\t\t\tfilters?: SQL<unknown>;\n\t\t\tsession: TSession;\n\t\t},\n\t) {\n\t\tsuper(PgCountBuilder.buildEmbeddedCount(params.source, params.filters).queryChunks);\n\n\t\tthis.mapWith(Number);\n\n\t\tthis.session = params.session;\n\n\t\tthis.sql = PgCountBuilder.buildCount(\n\t\t\tparams.source,\n\t\t\tparams.filters,\n\t\t);\n\t}\n\n\t/** @intrnal */\n\tsetToken(token: string) {\n\t\tthis.token = token;\n\t}\n\n\tthen<TResult1 = number, TResult2 = never>(\n\t\tonfulfilled?: ((value: number) => TResult1 | PromiseLike<TResult1>) | null | undefined,\n\t\tonrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null | undefined,\n\t): Promise<TResult1 | TResult2> {\n\t\treturn Promise.resolve(this.session.count(this.sql, this.token))\n\t\t\t.then(\n\t\t\t\tonfulfilled,\n\t\t\t\tonrejected,\n\t\t\t);\n\t}\n\n\tcatch(\n\t\tonRejected?: ((reason: any) => any) | null | undefined,\n\t): Promise<number> {\n\t\treturn this.then(undefined, onRejected);\n\t}\n\n\tfinally(onFinally?: (() => void) | null | undefined): Promise<number> {\n\t\treturn this.then(\n\t\t\t(value) => {\n\t\t\t\tonFinally?.();\n\t\t\t\treturn value;\n\t\t\t},\n\t\t\t(reason) => {\n\t\t\t\tonFinally?.();\n\t\t\t\tthrow reason;\n\t\t\t},\n\t\t);\n\t}\n}\n", "import { entityKind } from '~/entity.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport {\n\ttype BuildQueryResult,\n\ttype BuildRelationalQueryResult,\n\ttype DBQueryConfig,\n\tmapRelationalRow,\n\ttype TableRelationalConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, QueryWithTypings, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\nimport type { KnownKeysOnly } from '~/utils.ts';\nimport type { PgDialect } from '../dialect.ts';\nimport type { PgPreparedQuery, PgSession, PreparedQueryConfig } from '../session.ts';\nimport type { PgTable } from '../table.ts';\n\nexport class RelationalQueryBuilder<TSchema extends TablesRelationalConfig, T<PERSON>ields extends TableRelationalConfig> {\n\tstatic readonly [entityKind]: string = 'PgRelationalQueryBuilder';\n\n\tconstructor(\n\t\tprivate fullSchema: Record<string, unknown>,\n\t\tprivate schema: TSchema,\n\t\tprivate tableNamesMap: Record<string, string>,\n\t\tprivate table: PgTable,\n\t\tprivate tableConfig: TableRelationalConfig,\n\t\tprivate dialect: PgDialect,\n\t\tprivate session: PgSession,\n\t) {}\n\n\tfindMany<TConfig extends DBQueryConfig<'many', true, TSchema, TFields>>(\n\t\tconfig?: KnownKeysOnly<TConfig, DBQueryConfig<'many', true, TSchema, TFields>>,\n\t): PgRelationalQuery<BuildQueryResult<TSchema, TFields, TConfig>[]> {\n\t\treturn new PgRelationalQuery(\n\t\t\tthis.fullSchema,\n\t\t\tthis.schema,\n\t\t\tthis.tableNamesMap,\n\t\t\tthis.table,\n\t\t\tthis.tableConfig,\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tconfig ? (config as DBQueryConfig<'many', true>) : {},\n\t\t\t'many',\n\t\t);\n\t}\n\n\tfindFirst<TSelection extends Omit<DBQueryConfig<'many', true, TSchema, TFields>, 'limit'>>(\n\t\tconfig?: KnownKeysOnly<TSelection, Omit<DBQueryConfig<'many', true, TSchema, TFields>, 'limit'>>,\n\t): PgRelationalQuery<BuildQueryResult<TSchema, TFields, TSelection> | undefined> {\n\t\treturn new PgRelationalQuery(\n\t\t\tthis.fullSchema,\n\t\t\tthis.schema,\n\t\t\tthis.tableNamesMap,\n\t\t\tthis.table,\n\t\t\tthis.tableConfig,\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tconfig ? { ...(config as DBQueryConfig<'many', true> | undefined), limit: 1 } : { limit: 1 },\n\t\t\t'first',\n\t\t);\n\t}\n}\n\nexport class PgRelationalQuery<TResult> extends QueryPromise<TResult>\n\timplements RunnableQuery<TResult, 'pg'>, SQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'PgRelationalQuery';\n\n\tdeclare readonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly result: TResult;\n\t};\n\n\tconstructor(\n\t\tprivate fullSchema: Record<string, unknown>,\n\t\tprivate schema: TablesRelationalConfig,\n\t\tprivate tableNamesMap: Record<string, string>,\n\t\tprivate table: PgTable,\n\t\tprivate tableConfig: TableRelationalConfig,\n\t\tprivate dialect: PgDialect,\n\t\tprivate session: PgSession,\n\t\tprivate config: DBQueryConfig<'many', true> | true,\n\t\tprivate mode: 'many' | 'first',\n\t) {\n\t\tsuper();\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgPreparedQuery<PreparedQueryConfig & { execute: TResult }> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\tconst { query, builtQuery } = this._toSQL();\n\n\t\t\treturn this.session.prepareQuery<PreparedQueryConfig & { execute: TResult }>(\n\t\t\t\tbuiltQuery,\n\t\t\t\tundefined,\n\t\t\t\tname,\n\t\t\t\ttrue,\n\t\t\t\t(rawRows, mapColumnValue) => {\n\t\t\t\t\tconst rows = rawRows.map((row) =>\n\t\t\t\t\t\tmapRelationalRow(this.schema, this.tableConfig, row, query.selection, mapColumnValue)\n\t\t\t\t\t);\n\t\t\t\t\tif (this.mode === 'first') {\n\t\t\t\t\t\treturn rows[0] as TResult;\n\t\t\t\t\t}\n\t\t\t\t\treturn rows as TResult;\n\t\t\t\t},\n\t\t\t);\n\t\t});\n\t}\n\n\tprepare(name: string): PgPreparedQuery<PreparedQueryConfig & { execute: TResult }> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate _getQuery() {\n\t\treturn this.dialect.buildRelationalQueryWithoutPK({\n\t\t\tfullSchema: this.fullSchema,\n\t\t\tschema: this.schema,\n\t\t\ttableNamesMap: this.tableNamesMap,\n\t\t\ttable: this.table,\n\t\t\ttableConfig: this.tableConfig,\n\t\t\tqueryConfig: this.config,\n\t\t\ttableAlias: this.tableConfig.tsName,\n\t\t});\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this._getQuery().sql as SQL;\n\t}\n\n\tprivate _toSQL(): { query: BuildRelationalQueryResult; builtQuery: QueryWithTypings } {\n\t\tconst query = this._getQuery();\n\n\t\tconst builtQuery = this.dialect.sqlToQuery(query.sql as SQL);\n\n\t\treturn { query, builtQuery };\n\t}\n\n\ttoSQL(): Query {\n\t\treturn this._toSQL().builtQuery;\n\t}\n\n\tprivate authToken?: string;\n\t/** @internal */\n\tsetToken(token: string) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\toverride execute(): Promise<TResult> {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(undefined, this.authToken);\n\t\t});\n\t}\n}\n", "import { entityKind } from '~/entity.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\n\nexport interface PgRaw<TResult> extends QueryPromise<TResult>, RunnableQuery<TResult, 'pg'>, SQLWrapper {}\n\nexport class PgRaw<TResult> extends QueryPromise<TResult>\n\timplements RunnableQuery<TResult, 'pg'>, SQLWrapper, PreparedQuery\n{\n\tstatic override readonly [entityKind]: string = 'PgRaw';\n\n\tdeclare readonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly result: TResult;\n\t};\n\n\tconstructor(\n\t\tpublic execute: () => Promise<TResult>,\n\t\tprivate sql: SQL,\n\t\tprivate query: Query,\n\t\tprivate mapBatchResult: (result: unknown) => unknown,\n\t) {\n\t\tsuper();\n\t}\n\n\t/** @internal */\n\tgetSQL() {\n\t\treturn this.sql;\n\t}\n\n\tgetQuery() {\n\t\treturn this.query;\n\t}\n\n\tmapResult(result: unknown, isFromBatch?: boolean) {\n\t\treturn isFromBatch ? this.mapBatchResult(result) : result;\n\t}\n\n\t_prepare(): PreparedQuery {\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode() {\n\t\treturn false;\n\t}\n}\n", "import type { Row, RowList, Sql, TransactionSql } from 'postgres';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport { PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\n\nexport class PostgresJsPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic override readonly [entityKind]: string = 'PostgresJsPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: Sql,\n\t\tprivate queryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper({ sql: queryString, params });\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\treturn tracer.startActiveSpan('drizzle.execute', async (span) => {\n\t\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\t\tspan?.setAttributes({\n\t\t\t\t'drizzle.query.text': this.queryString,\n\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t});\n\n\t\t\tthis.logger.logQuery(this.queryString, params);\n\n\t\t\tconst { fields, queryString: query, client, joinsNotNullableMap, customResultMapper } = this;\n\t\t\tif (!fields && !customResultMapper) {\n\t\t\t\treturn tracer.startActiveSpan('drizzle.driver.execute', () => {\n\t\t\t\t\treturn client.unsafe(query, params as any[]);\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tconst rows = await tracer.startActiveSpan('drizzle.driver.execute', () => {\n\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t'drizzle.query.text': query,\n\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t});\n\n\t\t\t\treturn client.unsafe(query, params as any[]).values();\n\t\t\t});\n\n\t\t\treturn tracer.startActiveSpan('drizzle.mapResponse', () => {\n\t\t\t\treturn customResultMapper\n\t\t\t\t\t? customResultMapper(rows)\n\t\t\t\t\t: rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t\t\t});\n\t\t});\n\t}\n\n\tall(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\treturn tracer.startActiveSpan('drizzle.execute', async (span) => {\n\t\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\t\tspan?.setAttributes({\n\t\t\t\t'drizzle.query.text': this.queryString,\n\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t});\n\t\t\tthis.logger.logQuery(this.queryString, params);\n\t\t\treturn tracer.startActiveSpan('drizzle.driver.execute', () => {\n\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t'drizzle.query.text': this.queryString,\n\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t});\n\t\t\t\treturn this.client.unsafe(this.queryString, params as any[]);\n\t\t\t});\n\t\t});\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface PostgresJsSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class PostgresJsSession<\n\tTSQL extends Sql,\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<PostgresJsQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'PostgresJsSession';\n\n\tlogger: Logger;\n\n\tconstructor(\n\t\tpublic client: TSQL,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\t/** @internal */\n\t\treadonly options: PostgresJsSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t): PgPreparedQuery<T> {\n\t\treturn new PostgresJsPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\tquery(query: string, params: unknown[]): Promise<RowList<Row[]>> {\n\t\tthis.logger.logQuery(query, params);\n\t\treturn this.client.unsafe(query, params as any[]).values();\n\t}\n\n\tqueryObjects<T extends Row>(\n\t\tquery: string,\n\t\tparams: unknown[],\n\t): Promise<RowList<T[]>> {\n\t\treturn this.client.unsafe(query, params as any[]);\n\t}\n\n\toverride transaction<T>(\n\t\ttransaction: (tx: PostgresJsTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig,\n\t): Promise<T> {\n\t\treturn this.client.begin(async (client) => {\n\t\t\tconst session = new PostgresJsSession<TransactionSql, TFullSchema, TSchema>(\n\t\t\t\tclient,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.schema,\n\t\t\t\tthis.options,\n\t\t\t);\n\t\t\tconst tx = new PostgresJsTransaction(this.dialect, session, this.schema);\n\t\t\tif (config) {\n\t\t\t\tawait tx.setTransaction(config);\n\t\t\t}\n\t\t\treturn transaction(tx);\n\t\t}) as Promise<T>;\n\t}\n}\n\nexport class PostgresJsTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<PostgresJsQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'PostgresJsTransaction';\n\n\tconstructor(\n\t\tdialect: PgDialect,\n\t\t/** @internal */\n\t\toverride readonly session: PostgresJsSession<TransactionSql, TFullSchema, TSchema>,\n\t\tschema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tnestedIndex = 0,\n\t) {\n\t\tsuper(dialect, session, schema, nestedIndex);\n\t}\n\n\toverride transaction<T>(\n\t\ttransaction: (tx: PostgresJsTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\treturn this.session.client.savepoint((client) => {\n\t\t\tconst session = new PostgresJsSession<TransactionSql, TFullSchema, TSchema>(\n\t\t\t\tclient,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.schema,\n\t\t\t\tthis.session.options,\n\t\t\t);\n\t\t\tconst tx = new PostgresJsTransaction<TFullSchema, TSchema>(this.dialect, session, this.schema);\n\t\t\treturn transaction(tx);\n\t\t}) as Promise<T>;\n\t}\n}\n\nexport interface PostgresJsQueryResultHKT extends PgQueryResultHKT {\n\ttype: RowList<Assume<this['row'], Row>[]>;\n}\n", "import { entityKind } from '~/entity.ts';\nimport { TransactionRollbackError } from '~/errors.ts';\nimport type { TablesRelationalConfig } from '~/relations.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport { type Query, type SQL, sql } from '~/sql/index.ts';\nimport { tracer } from '~/tracing.ts';\nimport { PgDatabase } from './db.ts';\nimport type { PgDialect } from './dialect.ts';\nimport type { SelectedFieldsOrdered } from './query-builders/select.types.ts';\n\nexport interface PreparedQueryConfig {\n\texecute: unknown;\n\tall: unknown;\n\tvalues: unknown;\n}\n\nexport abstract class PgPreparedQuery<T extends PreparedQueryConfig> implements PreparedQuery {\n\tconstructor(protected query: Query) {}\n\n\tprotected authToken?: string;\n\n\tgetQuery(): Query {\n\t\treturn this.query;\n\t}\n\n\tmapResult(response: unknown, _isFromBatch?: boolean): unknown {\n\t\treturn response;\n\t}\n\n\t/** @internal */\n\tsetToken(token?: string) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\tstatic readonly [entityKind]: string = 'PgPreparedQuery';\n\n\t/** @internal */\n\tjoinsNotNullableMap?: Record<string, boolean>;\n\n\tabstract execute(placeholderValues?: Record<string, unknown>): Promise<T['execute']>;\n\t/** @internal */\n\tabstract execute(placeholderValues?: Record<string, unknown>, token?: string): Promise<T['execute']>;\n\t/** @internal */\n\tabstract execute(placeholderValues?: Record<string, unknown>, token?: string): Promise<T['execute']>;\n\n\t/** @internal */\n\tabstract all(placeholderValues?: Record<string, unknown>): Promise<T['all']>;\n\n\t/** @internal */\n\tabstract isResponseInArrayMode(): boolean;\n}\n\nexport interface PgTransactionConfig {\n\tisolationLevel?: 'read uncommitted' | 'read committed' | 'repeatable read' | 'serializable';\n\taccessMode?: 'read only' | 'read write';\n\tdeferrable?: boolean;\n}\n\nexport abstract class PgSession<\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> {\n\tstatic readonly [entityKind]: string = 'PgSession';\n\n\tconstructor(protected dialect: PgDialect) {}\n\n\tabstract prepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][], mapColumnValue?: (value: unknown) => unknown) => T['execute'],\n\t): PgPreparedQuery<T>;\n\n\texecute<T>(query: SQL): Promise<T>;\n\t/** @internal */\n\texecute<T>(query: SQL, token?: string): Promise<T>;\n\t/** @internal */\n\texecute<T>(query: SQL, token?: string): Promise<T> {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\tconst prepared = tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\t\treturn this.prepareQuery<PreparedQueryConfig & { execute: T }>(\n\t\t\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\t\t\tundefined,\n\t\t\t\t\tundefined,\n\t\t\t\t\tfalse,\n\t\t\t\t);\n\t\t\t});\n\n\t\t\treturn prepared.setToken(token).execute(undefined, token);\n\t\t});\n\t}\n\n\tall<T = unknown>(query: SQL): Promise<T[]> {\n\t\treturn this.prepareQuery<PreparedQueryConfig & { all: T[] }>(\n\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tfalse,\n\t\t).all();\n\t}\n\n\tasync count(sql: SQL): Promise<number>;\n\t/** @internal */\n\tasync count(sql: SQL, token?: string): Promise<number>;\n\t/** @internal */\n\tasync count(sql: SQL, token?: string): Promise<number> {\n\t\tconst res = await this.execute<[{ count: string }]>(sql, token);\n\n\t\treturn Number(\n\t\t\tres[0]['count'],\n\t\t);\n\t}\n\n\tabstract transaction<T>(\n\t\ttransaction: (tx: PgTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig,\n\t): Promise<T>;\n}\n\nexport abstract class PgTransaction<\n\tTQueryResult extends PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> extends PgDatabase<TQueryResult, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'PgTransaction';\n\n\tconstructor(\n\t\tdialect: PgDialect,\n\t\tsession: PgSession<any, any, any>,\n\t\tprotected schema: {\n\t\t\tfullSchema: Record<string, unknown>;\n\t\t\tschema: TSchema;\n\t\t\ttableNamesMap: Record<string, string>;\n\t\t} | undefined,\n\t\tprotected readonly nestedIndex = 0,\n\t) {\n\t\tsuper(dialect, session, schema);\n\t}\n\n\trollback(): never {\n\t\tthrow new TransactionRollbackError();\n\t}\n\n\t/** @internal */\n\tgetTransactionConfigSQL(config: PgTransactionConfig): SQL {\n\t\tconst chunks: string[] = [];\n\t\tif (config.isolationLevel) {\n\t\t\tchunks.push(`isolation level ${config.isolationLevel}`);\n\t\t}\n\t\tif (config.accessMode) {\n\t\t\tchunks.push(config.accessMode);\n\t\t}\n\t\tif (typeof config.deferrable === 'boolean') {\n\t\t\tchunks.push(config.deferrable ? 'deferrable' : 'not deferrable');\n\t\t}\n\t\treturn sql.raw(chunks.join(' '));\n\t}\n\n\tsetTransaction(config: PgTransactionConfig): Promise<void> {\n\t\treturn this.session.execute(sql`set transaction ${this.getTransactionConfigSQL(config)}`);\n\t}\n\n\tabstract override transaction<T>(\n\t\ttransaction: (tx: PgTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T>;\n}\n\nexport interface PgQueryResultHKT {\n\treadonly $brand: 'PgQueryResultHKT';\n\treadonly row: unknown;\n\treadonly type: unknown;\n}\n\nexport type PgQueryResultKind<TKind extends PgQueryResultHKT, TRow> = (TKind & {\n\treadonly row: TRow;\n})['type'];\n", "// 环境变量加载工具\n\nlet envLoaded = false;\n\nexport function ensureEnvLoaded() {\n  if (envLoaded || typeof window !== 'undefined') {\n    return;\n  }\n\n  try {\n    // 尝试加载 .env 文件\n    require('dotenv').config({ path: '.env' });\n    envLoaded = true;\n    console.log('✅ 环境变量已加载');\n  } catch (error) {\n    // dotenv 可能不存在，尝试其他路径\n    try {\n      require('dotenv').config({ path: '.env.local' });\n      envLoaded = true;\n      console.log('✅ 环境变量已从 .env.local 加载');\n    } catch (error2) {\n      console.warn('⚠️ 无法加载 dotenv，使用系统环境变量');\n    }\n  }\n}\n\nexport function getRequiredEnv(key: string): string {\n  ensureEnvLoaded();\n  \n  const value = process.env[key];\n  if (!value) {\n    throw new Error(`环境变量 ${key} 未设置`);\n  }\n  return value;\n}\n\nexport function getOptionalEnv(key: string, defaultValue?: string): string | undefined {\n  ensureEnvLoaded();\n  \n  return process.env[key] || defaultValue;\n}\n\n// 验证所有必需的环境变量\nexport function validateRequiredEnvVars() {\n  ensureEnvLoaded();\n  \n  const required = [\n    'DATABASE_URL',\n    'OPENROUTER_API_KEY',\n    'NEXT_PUBLIC_SUPABASE_URL',\n    'NEXT_PUBLIC_SUPABASE_ANON_KEY'\n  ];\n\n  const missing = required.filter(key => !process.env[key]);\n  \n  if (missing.length > 0) {\n    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}`);\n  }\n  \n  console.log('✅ 所有必需的环境变量都已设置');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,MACE,MAAQ;AAAA,MACR,SAAW;AAAA,MACX,aAAe;AAAA,MACf,MAAQ;AAAA,MACR,OAAS;AAAA,MACT,SAAW;AAAA,QACT,KAAK;AAAA,UACH,OAAS;AAAA,UACT,SAAW;AAAA,UACX,SAAW;AAAA,QACb;AAAA,QACA,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,qBAAqB;AAAA,QACrB,wBAAwB;AAAA,QACxB,qBAAqB;AAAA,QACrB,wBAAwB;AAAA,QACxB,kBAAkB;AAAA,MACpB;AAAA,MACA,SAAW;AAAA,QACT,aAAa;AAAA,QACb,MAAQ;AAAA,QACR,SAAW;AAAA,QACX,MAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,YAAc;AAAA,QACd,SAAW;AAAA,MACb;AAAA,MACA,YAAc;AAAA,QACZ,MAAQ;AAAA,QACR,KAAO;AAAA,MACT;AAAA,MACA,UAAY;AAAA,MACZ,SAAW;AAAA,MACX,UAAY;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,gBAAkB;AAAA,MAClB,SAAW;AAAA,MACX,iBAAmB;AAAA,QACjB,eAAe;AAAA,QACf,SAAW;AAAA,QACX,OAAS;AAAA,QACT,UAAY;AAAA,QACZ,oBAAoB;AAAA,QACpB,KAAO;AAAA,QACP,YAAc;AAAA,MAChB;AAAA,MACA,SAAW;AAAA,QACT,MAAQ;AAAA,MACV;AAAA,MACA,SAAW;AAAA,QACT,IAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;;;AC7DA;AAAA;AAAA;AAAA,QAAMA,MAAK,UAAQ,IAAI;AACvB,QAAM,OAAO,UAAQ,MAAM;AAC3B,QAAMC,MAAK,UAAQ,IAAI;AACvB,QAAMC,UAAS,UAAQ,QAAQ;AAC/B,QAAM,cAAc;AAEpB,QAAM,UAAU,YAAY;AAE5B,QAAM,OAAO;AAGb,aAASC,OAAO,KAAK;AACnB,YAAM,MAAM,CAAC;AAGb,UAAI,QAAQ,IAAI,SAAS;AAGzB,cAAQ,MAAM,QAAQ,WAAW,IAAI;AAErC,UAAI;AACJ,cAAQ,QAAQ,KAAK,KAAK,KAAK,MAAM,MAAM;AACzC,cAAM,MAAM,MAAM,CAAC;AAGnB,YAAI,QAAS,MAAM,CAAC,KAAK;AAGzB,gBAAQ,MAAM,KAAK;AAGnB,cAAM,aAAa,MAAM,CAAC;AAG1B,gBAAQ,MAAM,QAAQ,0BAA0B,IAAI;AAGpD,YAAI,eAAe,KAAK;AACtB,kBAAQ,MAAM,QAAQ,QAAQ,IAAI;AAClC,kBAAQ,MAAM,QAAQ,QAAQ,IAAI;AAAA,QACpC;AAGA,YAAI,GAAG,IAAI;AAAA,MACb;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,YAAa,SAAS;AAC7B,YAAM,YAAY,WAAW,OAAO;AAGpC,YAAM,SAAS,aAAa,aAAa,EAAE,MAAM,UAAU,CAAC;AAC5D,UAAI,CAAC,OAAO,QAAQ;AAClB,cAAM,MAAM,IAAI,MAAM,8BAA8B,SAAS,wBAAwB;AACrF,YAAI,OAAO;AACX,cAAM;AAAA,MACR;AAIA,YAAM,OAAO,WAAW,OAAO,EAAE,MAAM,GAAG;AAC1C,YAAM,SAAS,KAAK;AAEpB,UAAI;AACJ,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAI;AAEF,gBAAM,MAAM,KAAK,CAAC,EAAE,KAAK;AAGzB,gBAAM,QAAQ,cAAc,QAAQ,GAAG;AAGvC,sBAAY,aAAa,QAAQ,MAAM,YAAY,MAAM,GAAG;AAE5D;AAAA,QACF,SAAS,OAAO;AAEd,cAAI,IAAI,KAAK,QAAQ;AACnB,kBAAM;AAAA,UACR;AAAA,QAEF;AAAA,MACF;AAGA,aAAO,aAAa,MAAM,SAAS;AAAA,IACrC;AAEA,aAAS,MAAO,SAAS;AACvB,cAAQ,IAAI,WAAW,OAAO,WAAW,OAAO,EAAE;AAAA,IACpD;AAEA,aAAS,OAAQ,SAAS;AACxB,cAAQ,IAAI,WAAW,OAAO,YAAY,OAAO,EAAE;AAAA,IACrD;AAEA,aAAS,WAAY,SAAS;AAE5B,UAAI,WAAW,QAAQ,cAAc,QAAQ,WAAW,SAAS,GAAG;AAClE,eAAO,QAAQ;AAAA,MACjB;AAGA,UAAI,QAAQ,IAAI,cAAc,QAAQ,IAAI,WAAW,SAAS,GAAG;AAC/D,eAAO,QAAQ,IAAI;AAAA,MACrB;AAGA,aAAO;AAAA,IACT;AAEA,aAAS,cAAe,QAAQ,WAAW;AAEzC,UAAI;AACJ,UAAI;AACF,cAAM,IAAI,IAAI,SAAS;AAAA,MACzB,SAAS,OAAO;AACd,YAAI,MAAM,SAAS,mBAAmB;AACpC,gBAAM,MAAM,IAAI,MAAM,4IAA4I;AAClK,cAAI,OAAO;AACX,gBAAM;AAAA,QACR;AAEA,cAAM;AAAA,MACR;AAGA,YAAM,MAAM,IAAI;AAChB,UAAI,CAAC,KAAK;AACR,cAAM,MAAM,IAAI,MAAM,sCAAsC;AAC5D,YAAI,OAAO;AACX,cAAM;AAAA,MACR;AAGA,YAAM,cAAc,IAAI,aAAa,IAAI,aAAa;AACtD,UAAI,CAAC,aAAa;AAChB,cAAM,MAAM,IAAI,MAAM,8CAA8C;AACpE,YAAI,OAAO;AACX,cAAM;AAAA,MACR;AAGA,YAAM,iBAAiB,gBAAgB,YAAY,YAAY,CAAC;AAChE,YAAM,aAAa,OAAO,OAAO,cAAc;AAC/C,UAAI,CAAC,YAAY;AACf,cAAM,MAAM,IAAI,MAAM,2DAA2D,cAAc,2BAA2B;AAC1H,YAAI,OAAO;AACX,cAAM;AAAA,MACR;AAEA,aAAO,EAAE,YAAY,IAAI;AAAA,IAC3B;AAEA,aAAS,WAAY,SAAS;AAC5B,UAAI,oBAAoB;AAExB,UAAI,WAAW,QAAQ,QAAQ,QAAQ,KAAK,SAAS,GAAG;AACtD,YAAI,MAAM,QAAQ,QAAQ,IAAI,GAAG;AAC/B,qBAAW,YAAY,QAAQ,MAAM;AACnC,gBAAIH,IAAG,WAAW,QAAQ,GAAG;AAC3B,kCAAoB,SAAS,SAAS,QAAQ,IAAI,WAAW,GAAG,QAAQ;AAAA,YAC1E;AAAA,UACF;AAAA,QACF,OAAO;AACL,8BAAoB,QAAQ,KAAK,SAAS,QAAQ,IAAI,QAAQ,OAAO,GAAG,QAAQ,IAAI;AAAA,QACtF;AAAA,MACF,OAAO;AACL,4BAAoB,KAAK,QAAQ,QAAQ,IAAI,GAAG,YAAY;AAAA,MAC9D;AAEA,UAAIA,IAAG,WAAW,iBAAiB,GAAG;AACpC,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,aAAc,SAAS;AAC9B,aAAO,QAAQ,CAAC,MAAM,MAAM,KAAK,KAAKC,IAAG,QAAQ,GAAG,QAAQ,MAAM,CAAC,CAAC,IAAI;AAAA,IAC1E;AAEA,aAAS,aAAc,SAAS;AAC9B,YAAM,QAAQ,QAAQ,WAAW,QAAQ,KAAK;AAC9C,UAAI,OAAO;AACT,eAAO,uCAAuC;AAAA,MAChD;AAEA,YAAM,SAAS,aAAa,YAAY,OAAO;AAE/C,UAAI,aAAa,QAAQ;AACzB,UAAI,WAAW,QAAQ,cAAc,MAAM;AACzC,qBAAa,QAAQ;AAAA,MACvB;AAEA,mBAAa,SAAS,YAAY,QAAQ,OAAO;AAEjD,aAAO,EAAE,OAAO;AAAA,IAClB;AAEA,aAAS,aAAc,SAAS;AAC9B,YAAM,aAAa,KAAK,QAAQ,QAAQ,IAAI,GAAG,MAAM;AACrD,UAAI,WAAW;AACf,YAAM,QAAQ,QAAQ,WAAW,QAAQ,KAAK;AAE9C,UAAI,WAAW,QAAQ,UAAU;AAC/B,mBAAW,QAAQ;AAAA,MACrB,OAAO;AACL,YAAI,OAAO;AACT,iBAAO,oDAAoD;AAAA,QAC7D;AAAA,MACF;AAEA,UAAI,cAAc,CAAC,UAAU;AAC7B,UAAI,WAAW,QAAQ,MAAM;AAC3B,YAAI,CAAC,MAAM,QAAQ,QAAQ,IAAI,GAAG;AAChC,wBAAc,CAAC,aAAa,QAAQ,IAAI,CAAC;AAAA,QAC3C,OAAO;AACL,wBAAc,CAAC;AACf,qBAAW,YAAY,QAAQ,MAAM;AACnC,wBAAY,KAAK,aAAa,QAAQ,CAAC;AAAA,UACzC;AAAA,QACF;AAAA,MACF;AAIA,UAAI;AACJ,YAAM,YAAY,CAAC;AACnB,iBAAWG,SAAQ,aAAa;AAC9B,YAAI;AAEF,gBAAM,SAAS,aAAa,MAAMJ,IAAG,aAAaI,OAAM,EAAE,SAAS,CAAC,CAAC;AAErE,uBAAa,SAAS,WAAW,QAAQ,OAAO;AAAA,QAClD,SAAS,GAAG;AACV,cAAI,OAAO;AACT,mBAAO,kBAAkBA,KAAI,IAAI,EAAE,OAAO,EAAE;AAAA,UAC9C;AACA,sBAAY;AAAA,QACd;AAAA,MACF;AAEA,UAAI,aAAa,QAAQ;AACzB,UAAI,WAAW,QAAQ,cAAc,MAAM;AACzC,qBAAa,QAAQ;AAAA,MACvB;AAEA,mBAAa,SAAS,YAAY,WAAW,OAAO;AAEpD,UAAI,WAAW;AACb,eAAO,EAAE,QAAQ,WAAW,OAAO,UAAU;AAAA,MAC/C,OAAO;AACL,eAAO,EAAE,QAAQ,UAAU;AAAA,MAC7B;AAAA,IACF;AAGA,aAAS,OAAQ,SAAS;AAExB,UAAI,WAAW,OAAO,EAAE,WAAW,GAAG;AACpC,eAAO,aAAa,aAAa,OAAO;AAAA,MAC1C;AAEA,YAAM,YAAY,WAAW,OAAO;AAGpC,UAAI,CAAC,WAAW;AACd,cAAM,+DAA+D,SAAS,+BAA+B;AAE7G,eAAO,aAAa,aAAa,OAAO;AAAA,MAC1C;AAEA,aAAO,aAAa,aAAa,OAAO;AAAA,IAC1C;AAEA,aAAS,QAAS,WAAW,QAAQ;AACnC,YAAM,MAAM,OAAO,KAAK,OAAO,MAAM,GAAG,GAAG,KAAK;AAChD,UAAI,aAAa,OAAO,KAAK,WAAW,QAAQ;AAEhD,YAAM,QAAQ,WAAW,SAAS,GAAG,EAAE;AACvC,YAAM,UAAU,WAAW,SAAS,GAAG;AACvC,mBAAa,WAAW,SAAS,IAAI,GAAG;AAExC,UAAI;AACF,cAAM,SAASF,QAAO,iBAAiB,eAAe,KAAK,KAAK;AAChE,eAAO,WAAW,OAAO;AACzB,eAAO,GAAG,OAAO,OAAO,UAAU,CAAC,GAAG,OAAO,MAAM,CAAC;AAAA,MACtD,SAAS,OAAO;AACd,cAAM,UAAU,iBAAiB;AACjC,cAAM,mBAAmB,MAAM,YAAY;AAC3C,cAAM,mBAAmB,MAAM,YAAY;AAE3C,YAAI,WAAW,kBAAkB;AAC/B,gBAAM,MAAM,IAAI,MAAM,6DAA6D;AACnF,cAAI,OAAO;AACX,gBAAM;AAAA,QACR,WAAW,kBAAkB;AAC3B,gBAAM,MAAM,IAAI,MAAM,iDAAiD;AACvE,cAAI,OAAO;AACX,gBAAM;AAAA,QACR,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAGA,aAAS,SAAU,YAAY,QAAQ,UAAU,CAAC,GAAG;AACnD,YAAM,QAAQ,QAAQ,WAAW,QAAQ,KAAK;AAC9C,YAAM,WAAW,QAAQ,WAAW,QAAQ,QAAQ;AAEpD,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM,MAAM,IAAI,MAAM,gFAAgF;AACtG,YAAI,OAAO;AACX,cAAM;AAAA,MACR;AAGA,iBAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACrC,YAAI,OAAO,UAAU,eAAe,KAAK,YAAY,GAAG,GAAG;AACzD,cAAI,aAAa,MAAM;AACrB,uBAAW,GAAG,IAAI,OAAO,GAAG;AAAA,UAC9B;AAEA,cAAI,OAAO;AACT,gBAAI,aAAa,MAAM;AACrB,qBAAO,IAAI,GAAG,0CAA0C;AAAA,YAC1D,OAAO;AACL,qBAAO,IAAI,GAAG,8CAA8C;AAAA,YAC9D;AAAA,UACF;AAAA,QACF,OAAO;AACL,qBAAW,GAAG,IAAI,OAAO,GAAG;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAEA,QAAM,eAAe;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAAC;AAAA,MACA;AAAA,IACF;AAEA,WAAO,QAAQ,eAAe,aAAa;AAC3C,WAAO,QAAQ,eAAe,aAAa;AAC3C,WAAO,QAAQ,cAAc,aAAa;AAC1C,WAAO,QAAQ,SAAS,aAAa;AACrC,WAAO,QAAQ,UAAU,aAAa;AACtC,WAAO,QAAQ,QAAQ,aAAa;AACpC,WAAO,QAAQ,WAAW,aAAa;AAEvC,WAAO,UAAU;AAAA;AAAA;;;ACvWjB;;;ACAA;;;ACAA;AAAA,OAAO,QAAQ;AACf,OAAO,QAAQ;;;ACDf;;;ACAA;AAAA,IAAM,cAAc,oBAAI,IAAI;AAA5B,IACM,mBAAmB,oBAAI,IAAI;AADjC,IAEM,cAAc,OAAO,aAAa;AAEjC,IAAM,QAAQ,CAAC;AACf,IAAM,QAAN,cAAoB,QAAQ;AAAA,EACjC,YAAY,SAAS,MAAM,SAAS,WAAW,UAAU,CAAC,GAAG;AAC3D,QAAI,SACA;AAEJ,UAAM,CAAC,GAAGE,OAAM;AACd,gBAAU;AACV,eAASA;AAAA,IACX,CAAC;AAED,SAAK,SAAS,MAAM,QAAQ,QAAQ,GAAG;AACvC,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,UAAU;AAEf,SAAK,QAAQ;AACb,SAAK,YAAY;AAEjB,SAAK,UAAU,QAAM,KAAK,SAAS,OAAO,QAAQ,CAAC;AACnD,SAAK,SAAS,QAAM,KAAK,SAAS,OAAO,OAAO,CAAC;AAEjD,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,YAAY;AAEjB,SAAK,WAAW,IAAI,KAAK,QAAQ,QAC7B,IAAI,MAAM,IACV,KAAK,UAAU,YAAY,KAAK,OAAO;AAAA,EAC7C;AAAA,EAEA,IAAI,SAAS;AACX,YAAQ,KAAK,QAAQ,QACjB,KAAK,WAAW,EAAE,QAClB,KAAK,UAAU,iBAAiB,IAAI,KAAK,OAAO,IAC9C,iBAAiB,IAAI,KAAK,OAAO,IACjC,iBAAiB,IAAI,KAAK,SAAS,KAAK,WAAW,EAAE,KAAK,EAAE,IAAI,KAAK,OAAO,MAC7E;AAAA,EACP;AAAA,EAEA,YAAY,OAAO,OAAO,IAAI;AAC5B,WAAO;AAAA,EACT;AAAA,EAEA,SAAS;AACP,WAAO,KAAK,cAAc,KAAK,UAAU,IAAI,GAAG,KAAK,YAAY;AAAA,EACnE;AAAA,EAEA,SAAS;AACP,SAAK,QAAQ,SAAS;AACtB,SAAK,QAAQ,UAAU;AACvB,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,WAAW;AACf,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,WAAW;AACf,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAO,GAAG,IAAI;AACnB,SAAK,QAAQ,SAAS;AACtB,QAAI,OAAO,SAAS,YAAY;AAC9B,WAAK;AACL,aAAO;AAAA,IACT;AAEA,SAAK,aAAa;AAElB,QAAI,OAAO,OAAO;AAChB,aAAQ,KAAK,WAAW,IAAI;AAE9B,QAAI;AACJ,WAAO;AAAA,MACL,CAAC,OAAO,aAAa,GAAG,OAAO;AAAA,QAC7B,MAAM,MAAM;AACV,cAAI,KAAK,YAAY,CAAC,KAAK;AACzB,mBAAO,EAAE,MAAM,KAAK;AAEtB,kBAAQ,KAAK;AACb,gBAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/C,iBAAK,WAAW,WAAS;AACvB,sBAAQ,EAAE,OAAO,MAAM,MAAM,CAAC;AAC9B,qBAAO,IAAI,QAAQ,OAAK,OAAO,CAAC;AAAA,YAClC;AACA,iBAAK,UAAU,OAAO,KAAK,SAAS,OAAO,QAAQ,EAAE,MAAM,KAAK,CAAC;AACjE,iBAAK,SAAS,QAAM,KAAK,SAAS,OAAO,OAAO,CAAC;AAAA,UACnD,CAAC;AACD,eAAK,QAAQ;AACb,iBAAO;AAAA,QACT;AAAA,QACA,SAAS;AACP,kBAAQ,KAAK,KAAK;AAClB,iBAAO,EAAE,MAAM,KAAK;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW;AACT,SAAK,QAAQ,SAAS;AACtB,SAAK,eAAe,KAAK,QAAQ,UAAU;AAC3C,WAAO;AAAA,EACT;AAAA,EAEA,SAAS;AACP,UAAM,IAAI,MAAM,sCAAsC;AAAA,EACxD;AAAA,EAEA,QAAQ,IAAI;AACV,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,WAAO;AAAA,EACT;AAAA,EAEA,MAAM;AACJ,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA,EAEA,SAAS;AACP,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,SAAS;AACb,KAAC,KAAK,aAAa,KAAK,WAAW,SAAS,MAAM,KAAK,KAAK,QAAQ,IAAI;AAAA,EAC1E;AAAA,EAEA,UAAU;AACR,SAAK,OAAO;AACZ,WAAO;AAAA,EACT;AAAA,EAEA,OAAO;AACL,SAAK,OAAO;AACZ,WAAO,MAAM,KAAK,MAAM,MAAM,SAAS;AAAA,EACzC;AAAA,EAEA,QAAQ;AACN,SAAK,OAAO;AACZ,WAAO,MAAM,MAAM,MAAM,MAAM,SAAS;AAAA,EAC1C;AAAA,EAEA,UAAU;AACR,SAAK,OAAO;AACZ,WAAO,MAAM,QAAQ,MAAM,MAAM,SAAS;AAAA,EAC5C;AACF;AAEA,SAAS,YAAY,IAAI;AACvB,MAAI,YAAY,IAAI,EAAE;AACpB,WAAO,YAAY,IAAI,EAAE;AAE3B,QAAM,IAAI,MAAM;AAChB,QAAM,kBAAkB;AACxB,cAAY,IAAI,IAAI,IAAI,MAAM,CAAC;AAC/B,QAAM,kBAAkB;AACxB,SAAO,YAAY,IAAI,EAAE;AAC3B;;;AC5KA;AAAO,IAAM,gBAAN,cAA4B,MAAM;AAAA,EACvC,YAAY,GAAG;AACb,UAAM,EAAE,OAAO;AACf,SAAK,OAAO,KAAK,YAAY;AAC7B,WAAO,OAAO,MAAM,CAAC;AAAA,EACvB;AACF;AAEO,IAAM,SAAS;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,WAAW,GAAG,SAAS,QAAQ;AACtC,QAAM,EAAE,MAAM,KAAK,IAAI,UAAU;AACjC,QAAM,QAAQ,OAAO;AAAA,IACnB,IAAI,MAAO,WAAW,IAAI,OAAO,QAAQ,QAAS,OAAO,MAAM,KAAO;AAAA,IACtE;AAAA,MACE,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS,QAAQ,QAAQ;AAAA,IAC3B;AAAA,IAAG,QAAQ,OAAO,CAAC,IAAI,EAAE,KAAW;AAAA,EACtC;AACA,QAAM,kBAAkB,OAAO,UAAU;AACzC,SAAO;AACT;AAEA,SAAS,SAAS,GAAG;AACnB,QAAM,QAAQ,IAAI,cAAc,CAAC;AACjC,QAAM,kBAAkB,OAAO,QAAQ;AACvC,SAAO;AACT;AAEA,SAAS,QAAQ,MAAM,SAAS;AAC9B,QAAM,QAAQ,OAAO,OAAO,IAAI,MAAM,OAAO,OAAO,OAAO,GAAG,EAAE,KAAK,CAAC;AACtE,QAAM,kBAAkB,OAAO,OAAO;AACtC,SAAO;AACT;AAGA,SAAS,aAAa,GAAG;AACvB,QAAM,QAAQ,OAAO;AAAA,IACnB,IAAI,MAAM,IAAI,uBAAuB;AAAA,IACrC;AAAA,MACE,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,EACF;AACA,QAAM,kBAAkB,OAAO,YAAY;AAC3C,SAAO;AACT;;;AFjDO,IAAM,QAAQ;AAAA,EACnB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,MAAM;AAAA;AAAA,IACN,WAAW,OAAK,KAAK;AAAA,EACvB;AAAA,EACA,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,MAAM,CAAC,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,IAC3B,WAAW,OAAK,KAAK;AAAA,IACrB,OAAO,OAAK,CAAC;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,MAAM,CAAC,KAAK,IAAI;AAAA,IAChB,WAAW,OAAK,KAAK,UAAU,CAAC;AAAA,IAChC,OAAO,OAAK,KAAK,MAAM,CAAC;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,WAAW,OAAK,MAAM,OAAO,MAAM;AAAA,IACnC,OAAO,OAAK,MAAM;AAAA,EACpB;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,IACvB,WAAW,QAAM,aAAa,OAAO,IAAI,IAAI,KAAK,CAAC,GAAG,YAAY;AAAA,IAClE,OAAO,OAAK,IAAI,KAAK,CAAC;AAAA,EACxB;AAAA,EACA,OAAO;AAAA,IACL,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,WAAW,OAAK,QAAQ,OAAO,KAAK,CAAC,EAAE,SAAS,KAAK;AAAA,IACrD,OAAO,OAAK,OAAO,KAAK,EAAE,MAAM,CAAC,GAAG,KAAK;AAAA,EAC3C;AACF;AAEA,IAAM,YAAN,MAAgB;AAAA,EAAE,OAAO;AAAE,cAAU;AAAA,EAAE;AAAA,EAAE,QAAQ;AAAE,cAAU;AAAA,EAAE;AAAA,EAAE,UAAU;AAAE,cAAU;AAAA,EAAE;AAAC;AAEnF,IAAM,aAAN,cAAyB,UAAU;AAAA,EACxC,YAAY,OAAO;AACjB,UAAM;AACN,SAAK,QAAQ,iBAAiB,KAAK;AAAA,EACrC;AACF;AAEO,IAAM,YAAN,cAAwB,UAAU;AAAA,EACvC,YAAY,OAAO,MAAM,OAAO;AAC9B,UAAM;AACN,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACf;AACF;AAEO,IAAM,UAAN,cAAsB,UAAU;AAAA,EACrC,YAAY,OAAO,MAAM;AACvB,UAAM;AACN,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,MAAM,QAAQ,YAAYC,QAAO,SAAS;AACxC,UAAM,UAAU,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,GAAG,OAAO,OAAO,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,GAAGC,OAAM,EAAE,IAAIA,GAAE,CAAC,EAAE,IAAI;AACvG,WAAO,QAAQ,MAAM,KACjB,kBAAkB,KAAK,OAAO,OAAO,IACrC,QAAQ,GAAG,KAAK,OAAO,KAAK,MAAM,YAAYD,QAAO,OAAO;AAAA,EAClE;AACF;AAEO,SAAS,YAAY,GAAG,YAAYA,QAAO,SAAS;AACzD,MAAI,QAAQ,aAAa,YAAY,EAAE,QAAQ;AAC/C,MAAI,UAAU,QAAW;AACvB,iBAAa,YACT,EAAE,QAAQ,QAAQ,UAAU,YAC5B,QAAQ,IAAI,QAAQ,UAAU;AAElC,QAAI,UAAU;AACZ,YAAM,OAAO,QAAQ,mBAAmB,kCAAkC;AAAA,EAC9E;AAEA,SAAO,MAAOA,OAAM;AAAA,IAClB,aAAa,aACR,WAAW,KAAK,EAAE,KAAK,GAAG,EAAE,QAC3B,EAAE,MAAM,EAAE,QAAQ,UAAU,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,cAAc,EAAE,KAAK,IACxE,EAAE,SAEH,WAAW,KAAK,CAAC,GAAG,UAAU,CAAC;AAAA,EACtC;AACF;AAEA,IAAM,kBAAkB,aAAa,KAAK;AAEnC,SAAS,UAAU,GAAG,QAAQ,OAAO,YAAYA,QAAO,SAAS;AACtE,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,QAAQ,KAAK;AACzC,cAAW,eAAe,QAAQ,OAAO,YAAYA,QAAO,OAAO,IAAK,EAAE,QAAQ,CAAC;AACnF,YAAQ,EAAE,KAAK,CAAC;AAAA,EAClB;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ,OAAO,YAAYA,QAAO,GAAG;AAC3D,SACE,iBAAiB,UAAU,MAAM,MAAM,QAAQ,YAAYA,QAAO,CAAC,IACnE,iBAAiB,QAAQ,SAAS,OAAO,YAAYA,QAAO,CAAC,IAC7D,iBAAiB,aAAa,MAAM,QACpC,SAAS,MAAM,CAAC,aAAa,QAAQ,MAAM,OAAO,CAAC,KAAK,MAAM,MAAM,MAAM,SAAS,GAAG,YAAYA,QAAO,CAAC,GAAG,EAAE,IAC/G,YAAY,OAAO,YAAYA,QAAO,CAAC;AAE3C;AAEA,SAAS,SAAS,GAAG,YAAYA,QAAO,SAAS;AAC/C,IAAE,WAAW;AACb,SAAO,UAAU,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,YAAYA,QAAO,OAAO;AACzE;AAEA,SAAS,cAAc,OAAO,YAAYA,QAAO,SAAS,SAAS;AACjE,SAAO,MAAM;AAAA,IAAI,SACf,MAAM,QAAQ;AAAA,MAAI,YAChB,eAAe,UAAU,IAAI,MAAM,GAAG,YAAYA,QAAO,OAAO;AAAA,IAClE,EAAE,KAAK,GAAG,IAAI;AAAA,EAChB,EAAE,KAAK,GAAG;AACZ;AAEA,SAAS,OAAO,OAAO,MAAM,YAAYA,QAAO,SAAS;AACvD,QAAM,QAAQ,MAAM,QAAQ,MAAM,CAAC,CAAC;AACpC,QAAM,UAAU,KAAK,SAAS,KAAK,KAAK,IAAI,OAAO,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK;AAChF,SAAO,cAAc,QAAQ,QAAQ,CAAC,KAAK,GAAG,YAAYA,QAAO,SAAS,OAAO;AACnF;AAEA,SAAS,OAAO,OAAO,MAAM,YAAYA,QAAO,SAAS;AACvD,SAAO,UAAU,aAAa,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI;AACzD,MAAI,MAAM,QAAQ,KAAK;AACrB,WAAO,kBAAkB,OAAO,OAAO;AAEzC,MAAI;AACJ,QAAM,UAAU,KAAK,SAAS,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK;AAC7D,SAAO,QAAQ,IAAI,OAAK;AACtB,YAAQ,MAAM,CAAC;AACf,YACE,iBAAiB,QAAQ,SAAS,OAAO,YAAYA,QAAO,OAAO,IACnE,iBAAiB,aAAa,MAAM,QACpC,YAAY,OAAO,YAAYA,QAAO,OAAO,KAC3C,SAAS,iBAAiB,QAAQ,UAAU,OAAO,KAAK,QAAQ,UAAU,OAAO,GAAG,CAAC,IAAI,CAAC;AAAA,EAChG,CAAC,EAAE,KAAK,GAAG;AACb;AAEA,IAAM,WAAW,OAAO,QAAQ;AAAA,EAC9B;AAAA,EACA,IAAI,IAAI,OAAO;AACb,UAAM,IAAI,OAAO,GAAG,EAAE;AACtB,WAAO,MAAM,OAAO,WAAW;AAAA,EACjC;AAAA,EACA;AAAA,EACA,IAAI;AAAA,EACJ,WAAW;AAAA,EACX,OAAO;AAAA,EAEP,OAAO,OAAO,MAAM,YAAYA,QAAO,SAAS;AAC9C,YAAQ,KAAK,SAAS,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,GAAG;AAAA,MAAI,OAC1D,iBAAiB,QAAQ,UAAU,OAAO,KAAK,QAAQ,UAAU,OAAO,GAAG,CAAC,IAAI,CAAC,IACjF,MAAM,eAAe,UAAU,MAAM,CAAC,GAAG,YAAYA,QAAO,OAAO;AAAA,IACrE;AAAA,EACF;AAAA,EAEA,OAAO,OAAO,MAAM,YAAYA,QAAO,SAAS;AAC9C,UAAM,UAAU,KAAK,SAAS,KAAK,KAAK,IAAI,OAAO,KAAK,MAAM,QAAQ,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK;AAC/F,WAAO,MAAM,kBAAkB,SAAS,OAAO,IAAI,YACnD,cAAc,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,GAAG,YAAYA,QAAO,SAAS,OAAO;AAAA,EAC3F;AACF,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,MAAO,CAAC,IAAI,OAAO,kBAAkB,IAAI,iCAAiC,GAAG,GAAG,EAAE,CAAE;AAElG,SAAS,YAAY;AACnB,QAAM,OAAO,QAAQ,mBAAmB,+CAA+C;AACzF;AAEO,IAAM,cAAc,gBAAgB;AACpC,IAAM,UAAU,gBAAgB;AAIvC,SAAS,cAAc,GAAG;AACxB,MAAI,MAAM,QAAQ,CAAC;AACjB,WAAO,cAAc,EAAE,CAAC,CAAC;AAC3B,SAAO,OAAO,MAAM,WAAW,OAAO;AACxC;AAEO,IAAM,iBAAiB,SAASE,QAAO;AAC5C,QAAM,OAAO,aAAaA,UAAS,CAAC,CAAC;AACrC,SAAO;AAAA,IACL,aAAa,OAAO,OAAO,CAAC,GAAG,aAAa,KAAK,WAAW;AAAA,IAC5D,SAAS,OAAO,OAAO,CAAC,GAAG,SAAS,KAAK,OAAO;AAAA,EAClD;AACF;AAEA,SAAS,aAAaA,QAAO;AAC3B,SAAO,OAAO,KAAKA,MAAK,EAAE,OAAO,CAAC,KAAK,MAAM;AAC3C,IAAAA,OAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,OAAOA,OAAM,CAAC,EAAE,IAAI,EAAE,QAAQ,OAAK,IAAI,QAAQ,CAAC,IAAIA,OAAM,CAAC,EAAE,KAAK;AACtF,QAAIA,OAAM,CAAC,EAAE,WAAW;AACtB,UAAI,YAAYA,OAAM,CAAC,EAAE,EAAE,IAAIA,OAAM,CAAC,EAAE;AACxC,MAAAA,OAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,OAAOA,OAAM,CAAC,EAAE,IAAI,EAAE,QAAQ,OAAK,IAAI,YAAY,CAAC,IAAIA,OAAM,CAAC,EAAE,SAAS;AAAA,IAChG;AACA,WAAO;AAAA,EACT,GAAG,EAAE,SAAS,CAAC,GAAG,aAAa,CAAC,EAAE,CAAC;AACrC;AAEA,SAAS,kBAAkB,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG;AACxD,SAAO,GAAG,IAAI,OAAK,iBAAiB,OAAO,KAAK,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG;AAC7E;AAEO,IAAM,mBAAmB,SAAS,OAAO,KAAK;AACnD,SAAO,MAAM,IAAI,QAAQ,MAAM,IAAI,EAAE,QAAQ,OAAO,KAAK,IAAI;AAC/D;AAEO,IAAM,YAAY,SAASC,WAAU,GAAG;AAC7C,SACE,aAAa,YAAY,EAAE,OAC3B,aAAa,OAAO,OACpB,aAAa,aAAa,KACzB,MAAM,QAAQ,MAAM,QAAS,KAC9B,OAAO,MAAM,WAAW,KACxB,MAAM,QAAQ,CAAC,IAAIA,WAAU,EAAE,CAAC,CAAC,IACjC;AAEJ;AAEA,IAAM,kBAAkB;AACxB,IAAM,cAAc;AAEpB,SAAS,YAAY,GAAG;AACtB,SAAO,EACJ,QAAQ,iBAAiB,MAAM,EAC/B,QAAQ,aAAa,KAAK;AAC/B;AAEO,IAAM,kBAAkB,SAASC,iBAAgB,IAAI,YAAY,SAAS,UAAU;AACzF,MAAI,MAAM,QAAQ,EAAE,MAAM;AACxB,WAAO;AAET,MAAI,CAAC,GAAG;AACN,WAAO;AAET,QAAM,QAAQ,GAAG,CAAC;AAElB,QAAM,YAAY,aAAa,OAAO,MAAM;AAE5C,MAAI,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM;AACjC,WAAO,MAAM,GAAG,IAAI,OAAKA,iBAAgB,GAAG,YAAY,SAAS,QAAQ,CAAC,EAAE,KAAK,SAAS,IAAI;AAEhG,SAAO,MAAM,GAAG,IAAI,OAAK;AACvB,QAAI,MAAM,QAAW;AACnB,UAAI,QAAQ,UAAU;AACtB,UAAI,MAAM;AACR,cAAM,OAAO,QAAQ,mBAAmB,kCAAkC;AAAA,IAC9E;AAEA,WAAO,MAAM,OACT,SACA,MAAM,YAAY,aAAa,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC,IAAI;AAAA,EAClF,CAAC,EAAE,KAAK,SAAS,IAAI;AACvB;AAEA,IAAM,mBAAmB;AAAA,EACvB,GAAG;AAAA,EACH,MAAM;AAAA,EACN,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AACR;AAEO,IAAM,cAAc,SAASC,aAAY,GAAG,QAAQ,UAAU;AACnE,mBAAiB,IAAI,iBAAiB,OAAO;AAC7C,SAAO,gBAAgB,kBAAkB,GAAG,QAAQ,QAAQ;AAC9D;AAEA,SAAS,gBAAgB,GAAG,GAAG,QAAQ,UAAU;AAC/C,QAAM,KAAK,CAAC;AAEZ,QAAM,YAAY,aAAa,OAAO,MAAM;AAC5C,SAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK;AAC5B,MAAE,OAAO,EAAE,EAAE,CAAC;AACd,QAAI,EAAE,QAAQ;AACZ,UAAI,EAAE,SAAS,MAAM;AACnB,UAAE,OAAO,EAAE,EAAE,EAAE,CAAC;AAAA,MAClB,WAAW,EAAE,SAAS,KAAK;AACzB,WAAG,KAAK,SAAS,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG;AACtC,UAAE,MAAM;AACR,UAAE,SAAS,EAAE,EAAE,IAAI,CAAC,MAAM;AAC1B,UAAE,OAAO,EAAE,IAAI;AAAA,MACjB,OAAO;AACL,UAAE,OAAO,EAAE;AAAA,MACb;AAAA,IACF,WAAW,EAAE,SAAS,KAAK;AACzB,QAAE,SAAS;AAAA,IACb,WAAW,EAAE,SAAS,KAAK;AACzB,QAAE,OAAO,EAAE,EAAE;AACb,SAAG,KAAK,gBAAgB,GAAG,GAAG,QAAQ,QAAQ,CAAC;AAAA,IACjD,WAAW,EAAE,SAAS,KAAK;AACzB,QAAE,SAAS;AACX,QAAE,OAAO,EAAE,KAAK,GAAG,KAAK,SAAS,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;AACpF,QAAE,OAAO,EAAE,IAAI;AACf;AAAA,IACF,WAAW,EAAE,SAAS,aAAa,EAAE,MAAM,OAAO,EAAE,MAAM,KAAK;AAC7D,SAAG,KAAK,SAAS,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;AACpE,QAAE,OAAO,EAAE,IAAI;AAAA,IACjB;AACA,MAAE,IAAI,EAAE;AAAA,EACV;AACA,IAAE,OAAO,EAAE,KAAK,GAAG,KAAK,SAAS,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAC5F,SAAO;AACT;AAEO,IAAM,UAAU,OAAK;AAC1B,MAAI,MAAM,EAAE,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC5B,WAAO,EAAE,CAAC,MAAM,MAAM,EAAE,EAAE,CAAC,EAAE,YAAY,IAAI,EAAE,CAAC;AAClD,SAAO;AACT;AAEO,IAAM,WAAW,OAAK;AAC3B,MAAI,MAAM,EAAE,CAAC,EAAE,YAAY;AAC3B,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC5B,WAAO,EAAE,CAAC,MAAM,MAAM,EAAE,EAAE,CAAC,EAAE,YAAY,IAAI,EAAE,CAAC;AAClD,SAAO;AACT;AAEO,IAAM,UAAU,OAAK,EAAE,QAAQ,MAAM,GAAG;AAExC,IAAM,YAAY,OAAK,EAAE,QAAQ,YAAY,KAAK,EAAE,YAAY;AAChE,IAAM,aAAa,QAAM,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,QAAQ,YAAY,KAAK,GAAG,YAAY;AAC5F,IAAM,YAAY,OAAK,EAAE,QAAQ,MAAM,GAAG;AAEjD,SAAS,oBAAoB,IAAI;AAC/B,SAAO,SAAS,cAAc,GAAG,QAAQ;AACvC,WAAO,OAAO,MAAM,YAAY,MAAM,SAAS,OAAO,SAAS,OAAO,OAAO,SAAS,QAClF,MAAM,QAAQ,CAAC,IACb,EAAE,IAAI,CAAAC,OAAK,cAAcA,IAAG,MAAM,CAAC,IACnC,OAAO,QAAQ,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,OAAO,OAAO,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,IACzG;AAAA,EACN;AACF;AAEA,QAAQ,SAAS,EAAE,MAAM,QAAQ;AACjC,QAAQ,QAAQ,EAAE,MAAM,oBAAoB,OAAO,EAAE;AACrD,UAAU,SAAS,EAAE,IAAI,UAAU;AAE5B,IAAM,QAAQ,EAAE,GAAG,QAAQ;AAClC,MAAM,OAAO,KAAK;AAElB,SAAS,SAAS,EAAE,MAAM,SAAS;AACnC,SAAS,QAAQ,EAAE,MAAM,oBAAoB,QAAQ,EAAE;AACvD,WAAW,SAAS,EAAE,IAAI,WAAW;AAE9B,IAAM,SAAS,EAAE,GAAG,SAAS;AACpC,OAAO,OAAO,KAAK;AAEnB,QAAQ,SAAS,EAAE,MAAM,QAAQ;AACjC,QAAQ,QAAQ,EAAE,MAAM,oBAAoB,OAAO,EAAE;AACrD,UAAU,SAAS,EAAE,IAAI,UAAU;AAE5B,IAAM,QAAQ,EAAE,GAAG,QAAQ;AAClC,MAAM,OAAO,KAAK;;;AG9WlB;AAAA,OAAO,SAAS;AAChB,OAAO,SAAS;AAChB,OAAO,YAAY;AACnB,OAAO,YAAY;AACnB,SAAS,mBAAmB;;;ACJ5B;AAAA,IAAqB,SAArB,cAAoC,MAAM;AAAA,EACxC,cAAc;AACZ,UAAM;AACN,WAAO,iBAAiB,MAAM;AAAA,MAC5B,OAAO,EAAE,OAAO,MAAM,UAAU,KAAK;AAAA,MACrC,OAAO,EAAE,OAAO,MAAM,UAAU,KAAK;AAAA,MACrC,SAAS,EAAE,OAAO,MAAM,UAAU,KAAK;AAAA,MACvC,SAAS,EAAE,OAAO,MAAM,UAAU,KAAK;AAAA,MACvC,WAAW,EAAE,OAAO,MAAM,UAAU,KAAK;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EAEA,YAAY,OAAO,OAAO,IAAI;AAC5B,WAAO;AAAA,EACT;AACF;;;ACfA;AAAA,IAAO,gBAAQ;AAEf,SAAS,MAAM,UAAU,CAAC,GAAG;AAC3B,MAAI,KAAK,QAAQ,MAAM;AACvB,MAAI,QAAQ;AAEZ,SAAO;AAAA,IACL,IAAI,SAAS;AACX,aAAO,GAAG,SAAS;AAAA,IACrB;AAAA,IACA,QAAQ,CAAC,MAAM;AACb,YAAMC,SAAQ,GAAG,QAAQ,CAAC;AAC1B,aAAOA,WAAU,KACb,QACC,GAAG,OAAOA,QAAO,CAAC,GAAG;AAAA,IAC5B;AAAA,IACA,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG;AAAA,IAC1B,OAAO,MAAM;AACX,YAAM,MAAM,GAAG,OAAO;AAEtB,UAAI,UAAU,GAAG,QAAQ;AACvB,gBAAQ;AACR,aAAK,CAAC;AAAA,MACR,OAAO;AACL,WAAG,QAAQ,CAAC,IAAI;AAAA,MAClB;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;AC9BA;AAAA,IAAM,OAAO;AACb,IAAI,SAAS,OAAO,YAAY,IAAI;AAEpC,IAAM,WAAW,iBAAiB,MAAM,EAAE,EAAE,OAAO,CAAC,KAAK,MAAM;AAC7D,QAAM,IAAI,EAAE,WAAW,CAAC;AACxB,MAAI,CAAC,IAAI,MAAM;AACb,WAAO,CAAC,IAAI;AACZ,MAAE,IAAI;AACN,WAAO;AAAA,EACT;AACA,SAAO;AACT,GAAG,CAAC,CAAC;AAEL,IAAM,IAAI,OAAO,OAAO,OAAO,UAAU;AAAA,EACvC,GAAG,OAAO,aAAa,CAAC;AAAA,EACxB,GAAG;AAAA,EACH,IAAI,GAAG;AACL,MAAE,KAAK;AACP,WAAO;AAAA,EACT;AAAA,EACA,IAAI,GAAG;AACL,UAAM,SAAS,OAAO,WAAW,CAAC;AAClC,QAAI,MAAM;AACV,MAAE,KAAK,OAAO,MAAM,GAAG,EAAE,GAAG,QAAQ,MAAM;AAC1C,WAAO;AAAA,EACT;AAAA,EACA,IAAI,GAAG;AACL,QAAI,CAAC;AACL,WAAO,cAAc,GAAG,EAAE,CAAC;AAC3B,MAAE,KAAK;AACP,WAAO;AAAA,EACT;AAAA,EACA,IAAI,GAAG,GAAG;AACR,QAAI,KAAK,MAAM,GAAG;AAChB,aAAO,cAAc,GAAG,CAAC;AACzB,aAAO;AAAA,IACT;AACA,QAAI,CAAC;AACL,WAAO,cAAc,GAAG,EAAE,CAAC;AAC3B,MAAE,KAAK;AACP,WAAO;AAAA,EACT;AAAA,EACA,EAAE,GAAG;AACH,QAAI,CAAC;AACL,WAAO,KAAK,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;AAC3B,MAAE,KAAK;AACP,WAAO;AAAA,EACT;AAAA,EACA,IAAI,GAAG;AACL,aAAS,OAAO,OAAO,CAAC,OAAO,SAAS,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;AACnD,MAAE,IAAI,OAAO;AACb,WAAO;AAAA,EACT;AAAA,EACA,IAAI,KAAK,GAAG;AACV,WAAO,cAAc,EAAE,IAAI,IAAI,EAAE;AACjC,UAAM,MAAM,OAAO,SAAS,GAAG,EAAE,CAAC;AAClC,MAAE,IAAI;AACN,aAAS,OAAO,YAAY,IAAI;AAChC,WAAO;AAAA,EACT;AACF,CAAC;AAED,IAAO,gBAAQ;AAEf,SAAS,IAAI,GAAG;AACd,MAAI,OAAO,SAAS,EAAE,IAAI,GAAG;AAC3B,UAAM,OAAO,QACP,SAAS,KAAK;AAEpB,aAAS,OAAO,YAAY,UAAU,UAAU,KAAK,CAAC;AACtD,SAAK,KAAK,MAAM;AAAA,EAClB;AACF;AAEA,SAAS,QAAQ;AACf,IAAE,IAAI;AACN,SAAO;AACT;;;AHhEA,IAAO,qBAAQ;AAEf,IAAI,MAAM;AAEV,IAAM,OAAO,cAAE,EAAE,EAAE,EAAE,IAAI;AAAzB,IACM,QAAQ,cAAE,EAAE,EAAE,EAAE,IAAI;AAD1B,IAEM,aAAa,cAAE,EAAE,IAAI,CAAC,EAAE,IAAI,QAAQ,EAAE,IAAI,CAAC;AAFjD,IAGM,iBAAiB,OAAO,OAAO,CAAC,cAAE,EAAE,EAAE,EAAE,IAAI,cAAE,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC;AAH1E,IAIM,kBAAkB,cAAE,EAAE,EAAE,EAAE,IAAI,GAAG,EAAE,IAAI,cAAE,CAAC,EAAE,IAAI;AAJtD,IAKM,OAAO,MAAM;AAAa;AAEhC,IAAM,gBAAgB,oBAAI,IAAI;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,IAAM,cAAc;AAAA,EAClB,IAAM;AAAA;AAAA,EACN,IAAM;AAAA;AAAA,EACN,IAAM;AAAA;AAAA,EACN,IAAM;AAAA;AAAA,EACN,IAAM;AAAA;AAAA,EACN,IAAM;AAAA;AAAA,EACN,IAAM;AAAA;AAAA,EACN,KAAM;AAAA;AAAA,EACN,KAAM;AAAA;AAAA,EACN,IAAM;AAAA;AAAA,EACN,KAAM;AAAA;AAAA,EACN,KAAM;AAAA;AAAA,EACN,IAAM;AAAA;AAAA,EACN,KAAM;AAAA;AAAA,EACN,KAAM;AAAA;AAAA,EACN,IAAM;AAAA;AAAA,EACN,IAAM;AAAA;AAAA,EACN,IAAM;AAAA;AACR;AAEA,SAAS,WAAW,SAAS,SAAS,CAAC,GAAG,EAAE,SAAS,MAAM,QAAQ,MAAM,UAAU,KAAK,IAAI,CAAC,GAAG;AAC9F,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAAC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAAC;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,OAAO,cAAM,GACb,KAAK,OACL,UAAU,EAAE,KAAK,MAAM,QAAQ,KAAK,GACpC,YAAY,MAAM,KAAK,QAAQ,YAAY,GAC3C,YAAY,MAAM,KAAK,QAAQ,YAAY,GAC3C,eAAe,MAAM,iBAAiB,QAAQ,eAAe;AAEnE,MAAI,SAAS,MACT,eACA,SAAS,IAAI,OAAO,GACpB,WAAW,OAAO,MAAM,CAAC,GACzB,aAAa,QAAQ,aACrB,oBAAoB,CAAC,GACrB,aAAa,CAAC,GACd,cAAc,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,GAChD,iBAAiB,GACjB,aAAa,GACb,YAAY,GACZ,YAAY,GACZ,UAAU,GACV,SAAS,GACT,QAAQ,GACR,OAAO,GACP,kBAAkB,MAClB,iBAAiB,MACjB,aAAa,OACb,YAAY,MACZ,UAAU,MACV,UAAU,MACV,SAAS,MACT,SAAS,MACT,QAAQ,MACR,QAAQ,MACR,QAAQ,MACR,QAAQ,MACR,QAAQ;AAEZ,QAAMC,cAAa;AAAA,IACjB,OAAO,OAAO;AAAA,IACd;AAAA,IACA,QAAQC,QAAO;AACb,gBAAUA;AACV,gBAAU;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP;AAAA,EACF;AAEA,SAAO,UAAU,OAAO,OAAO,KAAKD,WAAU;AAE9C,SAAOA;AAEP,iBAAe,eAAe;AAC5B,QAAI;AACJ,QAAI;AACF,UAAI,QAAQ,SACP,MAAM,QAAQ,QAAQ,QAAQ,OAAO,OAAO,CAAC,IAC9C,IAAI,IAAI,OAAO;AAAA,IACrB,SAAS,GAAG;AACV,YAAM,CAAC;AACP;AAAA,IACF;AACA,MAAE,GAAG,SAAS,KAAK;AACnB,MAAE,GAAG,SAAS,MAAM;AACpB,MAAE,GAAG,SAAS,KAAK;AACnB,WAAO;AAAA,EACT;AAEA,iBAAe,OAAO,EAAE,KAAK,OAAO,GAAG,SAAS,QAAQ;AACtD,QAAI;AACF,sBAAgB,cAAE,EAAE,IAAI,EAAE,EAAE,IAAI,QAAQ,EAAE,IAAI,GAAG,EAAE,IAAI,MAAM,EAAE,IAAI,EAAE;AACrE,YAAM,QAAQ;AACd,aAAO,KAAK,SAAS,MAAM;AAC3B,aAAO,KAAK,SAAS,OAAO;AAAA,IAC9B,SAASE,QAAO;AACd,aAAOA,MAAK;AAAA,IACd;AAAA,EACF;AAEA,WAAS,QAAQ,GAAG;AAClB,QAAI;AACF,aAAO,WAAW,GAAG,OAAO,WAAW,wBAAwB,OAAO,CAAC;AAEzE,QAAI,EAAE;AACJ;AAEF,QAAI;AACF,QAAE,QAAQ;AACV,cACI,KAAK,KAAK,CAAC,KACV,QAAQ,GAAG,MAAM,SAAS;AAE/B,YAAM,CAAC;AACP,aAAO,MAAM,SAAS,CAAC,CAAC,KACnB,CAAC,EAAE,iBACH,CAAC,EAAE,YACH,KAAK,SAAS,iBACb,CAAC,EAAE,QAAQ,aAAa,EAAE,QAAQ,UAAUF,WAAU;AAAA,IAC9D,SAASE,QAAO;AACd,WAAK,WAAW,KAAK,MAAM,IAAI;AAC/B,cAAQA,MAAK;AACb,aAAO;AAAA,IACT;AAAA,EACF;AAEA,WAAS,SAAS,GAAG;AACnB,QAAI,EAAE,WAAW,UAAU;AACzB,YAAM,OAAO,QAAQ,2BAA2B,2CAA2C;AAE7F,WAAO,EAAE,QAAQ,SACb,cAAE,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,SAAS,cAAE,CAAC,EAAE,IAAI,IAC1C,EAAE,gBACA,OAAO,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,IAClC,EAAE,UACA,EAAE,WACA,SAAS,CAAC,IACV,OAAO,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,IAC1C,QAAQ,CAAC;AAAA,EACnB;AAEA,WAAS,SAAS,GAAG;AACnB,WAAO,OAAO,OAAO;AAAA,MACnB,MAAM,EAAE,UAAU,QAAQ,EAAE,YAAY,EAAE,UAAU,OAAO,EAAE,UAAU,IAAI;AAAA,MAC3E,SAAS,KAAK,EAAE,UAAU,IAAI;AAAA,IAChC,CAAC;AAAA,EACH;AAEA,WAAS,SAAS,GAAG;AACnB,WAAO,OAAO,OAAO;AAAA,MACnB,KAAK,EAAE,YAAY,EAAE,UAAU,OAAO,EAAE,UAAU,MAAM,EAAE,UAAU;AAAA,MACpE,EAAE,WACE,QAAQ,IAAI,EAAE,UAAU,IACxB;AAAA,IACN,CAAC;AAAA,EACH;AAEA,WAAS,QAAQ,GAAG;AAClB,WAAO,OAAO,OAAO;AAAA,MACnB,MAAM,EAAE,UAAU,QAAQ,EAAE,YAAY,EAAE,UAAU,KAAK;AAAA,MACzD;AAAA,MACA,SAAS,CAAC;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,WAAS,MAAM,GAAG;AAChB,UAAM,aAAa,CAAC,GACdC,SAAQ,CAAC;AAEf,UAAM,SAAS,UAAU,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,YAAYA,QAAO,OAAO;AAE/E,KAAC,EAAE,UAAU,EAAE,KAAK,QAAQ,OAAK,YAAY,GAAG,YAAYA,QAAO,OAAO,CAAC;AAE3E,MAAE,UAAU,QAAQ,YAAY,aAAa,EAAE,UAAU,EAAE,QAAQ,UAAU;AAC7E,MAAE,SAAS;AACX,MAAE,YAAY,EAAE,WAAWA,SAAQ;AACnC,MAAE,gBAAiB,OAAO,WAAW,EAAE,SAAS;AAChD,MAAE,aAAa,EAAE,cAAc;AAC/B,MAAE,WAAW,EAAE,WAAW,EAAE,aAAa;AACzC,MAAE,gBAAgB,EAAE,gBAAiB,WAAW,UAAU,CAAC,EAAE;AAC7D,MAAE,YAAY,EAAE,WACZ,WAAW,EAAE,SAAS,IACtB,EAAE,QAAQ,OAAAA,QAAO,MAAM,EAAE,UAAU,cAAc,mBAAmB,GAAG;AAE3E,WAAO,QAAQ,UAAU,cAAc,QAAQ,MAAM,IAAI,QAAQ,YAAYA,MAAK;AAAA,EACpF;AAEA,WAAS,MAAM,GAAG,IAAI;AACpB,YAAQ,QAAQ,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,OAAO,KAAK,CAAC;AACzD,QAAI,MAAM,MAAM,UAAU;AACxB,aAAO,UAAU,EAAE;AACrB,uBAAmB,SAAS,iBAAiB,aAAa,SAAS;AACnE,WAAO;AAAA,EACT;AAEA,WAAS,UAAU,IAAI;AACrB,UAAM,IAAI,OAAO,MAAM,OAAO,EAAE;AAChC,uBAAmB,QAAQ,eAAe,cAAc;AACxD,YAAQ,iBAAiB;AACzB,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkB;AACzB,YAAQ,OAAO,WAAW,mBAAmB,SAAS,MAAM,CAAC;AAC7D,WAAO,QAAQ;AAAA,EACjB;AAEA,iBAAe,SAAS;AACtB,UAAM,UAAU;AAChB,UAAM,SAAS,MAAM,IAAI,QAAQ,OAAK,OAAO,KAAK,QAAQ,OAAK,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;AAE9E,QAAI,CAAC,UAAU,QAAQ;AACrB,aAAO,UAAU;AAEnB,WAAO,mBAAmB;AAC1B,aAAS,IAAI,QAAQ;AAAA,MACnB;AAAA,MACA,YAAY,IAAI,KAAK,OAAO,IAAI,IAAI,SAAY,OAAO;AAAA,MACvD,GAAI,QAAQ,aAAa,QAAQ,WAAW,QAAQ,WAChD,EAAE,oBAAoB,MAAM,IAC5B,QAAQ,gBACN,CAAC,IACD,OAAO,QAAQ,WACb,MACA,CAAC;AAAA,IAEX,CAAC;AACD,WAAO,GAAG,iBAAiB,SAAS;AACpC,WAAO,GAAG,SAAS,KAAK;AACxB,WAAO,GAAG,SAAS,MAAM;AACzB,WAAO,GAAG,SAAS,KAAK;AAAA,EAC1B;AAGA,WAAS,QAAQ;AACf,KAAC,SAAS,OAAOH,WAAU;AAAA,EAC7B;AAEA,WAAS,KAAK,GAAG;AACf,QAAI,WAAW;AACb,gBAAU,KAAK,CAAC;AAChB,mBAAa,EAAE;AACf,UAAI,YAAY;AACd;AAAA,IACJ;AAEA,eAAW,YACP,OAAO,OAAO,WAAW,SAAS,SAAS,IAC3C,SAAS,WAAW,IAClB,IACA,OAAO,OAAO,CAAC,UAAU,CAAC,GAAG,SAAS,SAAS,EAAE,MAAM;AAE7D,WAAO,SAAS,SAAS,GAAG;AAC1B,eAAS,SAAS,aAAa,CAAC;AAChC,UAAI,UAAU,SAAS,QAAQ;AAC7B,oBAAY,SAAS,SAAS;AAC9B,oBAAY,CAAC,QAAQ;AACrB;AAAA,MACF;AAEA,UAAI;AACF,eAAO,SAAS,SAAS,GAAG,SAAS,CAAC,CAAC;AAAA,MACzC,SAAS,GAAG;AACV,kBAAU,MAAM,YAAY,MAAM,kBAAkB,MAAM,IAAI;AAC9D,gBAAQ,CAAC;AAAA,MACX;AACA,iBAAW,SAAS,SAAS,SAAS,CAAC;AACvC,kBAAY;AACZ,kBAAY;AAAA,IACd;AAAA,EACF;AAEA,iBAAe,UAAU;AACvB,iBAAa;AACb,wBAAoB,CAAC;AACrB,eAAW,SAAS,MAAM,aAAa;AAEvC,QAAI,CAAC;AACH;AAEF,iBAAa,MAAM;AAEnB,QAAI,QAAQ;AACV,aAAO,MAAM,OAAO,IAAI,UAAU;AAEpC,WAAO,GAAG,WAAW,MAAM,SAAS,SAAS;AAE7C,QAAI,QAAQ;AACV,aAAO,OAAO,QAAQ,QAAQ,IAAI;AAEpC,WAAO,MAAM;AACb,WAAO,QAAQ,KAAK,SAAS,GAAG,KAAK,SAAS,CAAC;AAC/C,WAAO,OAAO,KAAK,SAAS;AAC5B,WAAO,OAAO,KAAK,SAAS;AAE5B,iBAAa,YAAY,KAAK,KAAK;AAAA,EACrC;AAEA,WAAS,YAAY;AACnB,eAAW,SAAS,aAAa,aAAa,QAAQ,YAAY,IAAI,IAAI,CAAC;AAAA,EAC7E;AAEA,WAAS,YAAY;AACnB,QAAI;AACF,mBAAa,CAAC;AACd,mBAAa,QAAQ;AACrB,oBAAc,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC;AAChD,uBAAiB;AACjB,gBAAU,MAAM;AAChB,aAAO,GAAG,QAAQ,IAAI;AACtB,oBAAc,OAAO,gBAAgB,OAAO,aAAa,MAAM,MAAO,UAAU;AAChF,YAAM,IAAI,eAAe;AACzB,YAAM,CAAC;AAAA,IACT,SAAS,KAAK;AACZ,YAAM,GAAG;AAAA,IACX;AAAA,EACF;AAEA,WAAS,MAAM,KAAK;AAClB,QAAIA,YAAW,UAAU,OAAO,cAAc,QAAQ,KAAK,UAAU,CAAC;AACpE;AAEF,YAAQ,GAAG;AACX,WAAO,KAAK;AACV,iBAAW,KAAK,MAAM,GAAG,GAAG;AAAA,EAChC;AAEA,WAAS,QAAQ,KAAK;AACpB,eAAW,OAAO,QAAQ,GAAG,GAAG,SAAS;AACzC,aAAS,WAAW,OAAO,GAAG;AAC9B,gBAAY,WAAW,SAAS,GAAG,GAAG,UAAU;AAAA,EAClD;AAEA,WAAS,WAAWC,QAAO,KAAK;AAC9B,QAAIA,OAAM;AACR,aAAOA,OAAM,OAAO,GAAG;AAEzB,QAAI,CAAC,OAAO,OAAO,QAAQ;AACzB,YAAM,IAAI,MAAM,GAAG;AAErB,eAAW,OAAO,gBAAgB,OAAO,OAAO,iBAAiB,KAAK;AAAA,MACpE,OAAO,EAAE,OAAO,IAAI,QAAQA,OAAM,OAAO,QAAQ,QAAQ,IAAI,GAAG,YAAY,QAAQ,MAAM;AAAA,MAC1F,OAAO,EAAE,OAAOA,OAAM,QAAQ,YAAY,QAAQ,MAAM;AAAA,MACxD,YAAY,EAAE,OAAOA,OAAM,YAAY,YAAY,QAAQ,MAAM;AAAA,MACjE,MAAM,EAAE,OAAOA,OAAM,MAAM,YAAY,QAAQ,MAAM;AAAA,MACrD,OAAO,EAAE,OAAOA,OAAM,aAAaA,OAAM,UAAU,OAAO,YAAY,QAAQ,MAAM;AAAA,IACtF,CAAC;AACD,IAAAA,OAAM,OAAO,GAAG;AAAA,EAClB;AAEA,WAAS,MAAM;AACb,WAAO,WACL,CAACD,YAAW,YAAY,MAAMA,WAAU,GACxC,CAACA,YAAW,YAAY,CAAC,WAAW,CAAC,SAAS,KAAK,WAAW,KACzD,UAAU,GAAG,IAAI,QAAQ,OAAK,UAAU,OAAO,eAAe,WAAW,OAAO,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC,KACvG,SAAS,IAAI,QAAQ,OAAK,QAAQ,CAAC;AAAA,EAE3C;AAEA,WAAS,YAAY;AACnB,iBAAa;AACb,QAAI,UAAU,SAAS,WAAW,KAAK;AACrC,YAAM,OAAO,WAAW,wBAAwB,OAAO,CAAC;AAE1D,mBAAe,cAAc;AAC7B,QAAI,QAAQ;AACV,aAAO,eAAe,QAAQ,IAAI;AAClC,aAAO,eAAe,WAAW,SAAS;AAC1C,aAAO,eAAe,UAAU,OAAO,IAAI,cAAE,EAAE,EAAE,EAAE,IAAI,CAAC;AAAA,IAC1D;AACA,cAAU,MAAM,GAAG,SAAS,QAAQ;AAAA,EACtC;AAEA,iBAAe,OAAO,UAAU;AAC9B,eAAW,OAAO,MAAM,CAAC;AACzB,gBAAY;AACZ,gBAAY;AACZ,mBAAe,cAAc;AAC7B,WAAO,eAAe,QAAQ,IAAI;AAClC,WAAO,eAAe,WAAW,SAAS;AAC1C,cAAU,OAAO;AACjB,cAAU,OAAO;AACjB,iBAAa,OAAO;AAEpB,WAAO,mBAAmB;AAC1B,aAAS;AAET,QAAI;AACF,aAAO,UAAU;AAEnB,KAAC,aAAa,SAAS,KAAK,WAAW,MAAM,OAAO,WAAW,qBAAqB,SAAS,MAAM,CAAC;AACpG,iBAAa,YAAY,IAAI;AAC7B,gBAAY,QAAQ,OAAO;AAC3B,aAAS,OAAOD,aAAY,aAAaA,SAAQ,QAAQ,OAAO,OAAO,IAAIA,YAAW;AACtF,YAAQC,aAAY,OAAO,WAAW,qBAAqB,SAAS,MAAM,CAAC;AAAA,EAC7E;AAGA,WAAS,OAAO,IAAI,IAAI,GAAG,CAAC,GAAG;AAC7B,KACE,MAAM,KAAK;AAAA;AAAA,MACX,MAAM,MAAM;AAAA;AAAA,QACZ,MAAM,KAAK;AAAA;AAAA,UACX,MAAM,KAAK;AAAA;AAAA,YACX,MAAM,KAAK;AAAA;AAAA,cACX,MAAM,KAAK;AAAA;AAAA,gBACX,MAAM,KAAK;AAAA;AAAA,kBACX,MAAM,KAAK;AAAA;AAAA,oBACX,MAAM,MAAM;AAAA;AAAA,sBACZ,MAAM,KAAK;AAAA;AAAA,wBACX,MAAM,KAAK;AAAA;AAAA,0BACX,MAAM,MAAM;AAAA;AAAA,4BACZ,MAAM,KAAK;AAAA;AAAA,8BACX,MAAM,KAAK;AAAA;AAAA,gCACX,MAAM,MAAM;AAAA;AAAA,kCACZ,MAAM,KAAK;AAAA;AAAA,oCACX,MAAM,KAAK;AAAA;AAAA,sCACX,MAAM,KAAK;AAAA;AAAA,wCACX,MAAM,KAAK;AAAA;AAAA,0CACX,MAAM,KAAK;AAAA;AAAA,4CACX,MAAM,KAAK;AAAA;AAAA,8CACX,MAAM,KAAK;AAAA;AAAA,gDACX,MAAM,MAAM;AAAA;AAAA,kDACZ,MAAM,KAAK;AAAA;AAAA;AAAA,oDAEX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OACA,EAAE;AAAA,EACN;AAEA,WAAS,QAAQ,GAAG;AAClB,QAAI,QAAQ;AACZ,QAAII;AACJ,QAAI;AACJ,QAAI;AAEJ,UAAM,MAAM,MAAM,QAAQ,IAAI,MAAM,MAAM,UAAU,QAAQ,MAAM,IAAI,CAAC;AACvE,aAAS,IAAI,GAAG,IAAI,MAAM,UAAU,QAAQ,QAAQ,KAAK;AACvD,eAAS,MAAM,UAAU,QAAQ,CAAC;AAClC,MAAAA,UAAS,EAAE,YAAY,KAAK;AAC5B,eAAS;AAET,cAAQA,YAAW,KACf,OACA,MAAM,UAAU,OACd,EAAE,SAAS,OAAO,SAASA,OAAM,IACjC,OAAO,WAAW,SAChB,EAAE,SAAS,QAAQ,OAAO,SAASA,OAAM,IACzC,OAAO,OAAO,UAAU,OACtB,OAAO,OAAO,EAAE,SAAS,QAAQ,QAAQ,GAAG,SAASA,OAAM,CAAC,IAC5D,OAAO,OAAO,EAAE,SAAS,QAAQ,OAAO,SAASA,OAAM,CAAC;AAElE,YAAM,QACD,IAAI,CAAC,IAAI,MAAM,UAAU,OACxB,QACA,UAAU,MAAM,OAAO,UAAU,MAAM,KAAK,OAAO,MAAM,IAAI,QAC9D,IAAI,OAAO,IAAI,IAAI,UAAU,MAAM,OAAO,UAAU,MAAM,KAAK,OAAO,MAAM,IAAI;AAAA,IACvF;AAEA,UAAM,YACF,MAAM,UAAU,UAAU,IAAI,OAAO,UAAU,IAAI,KAAK,GAAG,IAAI,KAAK,MAAM,IACzE,OAAO,MAAM,IAAI,UAAU,IAAI,OAAO,UAAU,IAAI,KAAK,GAAG,IAAI;AAAA,EACvE;AAEA,WAAS,gBAAgB,GAAG;AAC1B,UAAM,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,QAAQ,GAAG,EAAE,SAAS,CAAC,EAAE,MAAM,cAAE,CAAC;AAC5D,sBAAkB,CAAC,IAAI;AACvB,QAAI,QAAQ,WAAW,CAAC,MAAM,GAAG;AAC/B,cAAQ,WAAW,CAAC,IAAI;AACxB,qBAAe,YAAY,GAAG,CAAC;AAAA,IACjC;AAAA,EACF;AAEA,WAAS,cAAc,GAAG;AACxB,aAAS,MAAM,QAAQ,UAAU,MAAM,QAAQ,WAAW,MAAM;AAChE,YAAQ,UAAU;AAClB,aAAS,IAAI,OAAO;AACpB,iBAAa,OAAO;AAEpB,QAAI,SAAS;AACX,UAAI,sBAAsB;AACxB,YAAI,CAAC,kBAAkB,kBAAkB,CAAC,kBAAkB;AAC1D,iBAAO,WAAW;AAAA,iBACX,QAAQ,sBAAsB,iBAAiB;AACtD,iBAAO,UAAU;AAAA,MACrB;AAEA,UAAI,YAAY;AACd,gBAAQ,YAAY,UAAU;AAC9B,eAAO,gBAAgB;AAAA,MACzB;AAEA,iBAAW,CAAC,QAAQ,WAAW,QAAQ,OAAO;AAC9C,cAAQ,OAAO,UAAU,UAAU;AACnC,gBAAU;AACV;AAAA,IACF;AAEA,WAAO,KAAK,WAAW,QAAQ,KAAK,MAAM,OAAO,MAAM,SAAS,MAAM,MAAM;AAC1E,iBAAW,OAAO,EAAE,OAAO,MAAM,OAAO,MAAM,UAAU,SAAS,MAAM,UAAU,MAAM;AAEzF,QAAI;AACF;AAEF,IAAAJ,YAAW,WACP,CAACA,YAAW,SAAS,WAAW,EAAE,CAAC,MAAM,KACvC,SACE,UAAU,KACTA,YAAW,WAAW,MAAM,OAAOA,WAAU,KAChDA,YAAW,SAAS,IACtB,SACE,UAAU,IACV,OAAOA,WAAU;AAAA,EACzB;AAEA,WAAS,gBAAgB,GAAG;AAC1B,WAAO;AAEP,aAAS,IAAI,EAAE,SAAS,GAAG,IAAI,GAAG,KAAK;AACrC,UAAI,EAAE,CAAC,MAAM,MAAM,EAAE,IAAI,CAAC,IAAI,MAAM,OAAO,UAAU;AACnD,eAAO,QAAQ,CAAC,EAAE,SAAS,QAAQ,IAAI,GAAG,EAAE,SAAS,CAAC;AACxD,UAAI,EAAE,IAAI,CAAC,KAAK,IAAI;AAClB,eAAO,UAAU,EAAE,SAAS,QAAQ,GAAG,CAAC;AACxC,eAAO,QAAQ;AACf;AAAA,MACF;AAAA,IACF;AAEA,cAAU,MAAM,GAAG,QAAQ;AAE3B,QAAI,OAAO,YAAY,WAAW,QAAQ,KAAK,CAACA,YAAW;AACzD,aAAO,QAAQ,OAAO,QAAQ,sBAAsB,4CAA4C,CAAC;AAEnG,QAAI,MAAM,QAAQ;AAChB,aAAO,aAAa;AAEtB,QAAI,MAAM,UAAU;AAClB,aAAO,SAAS,MAAM,SAAS,MAAM;AACrC,YAAM,IAAI;AAAA,IACZ;AAEA,UAAM,QAAQ,MAAM;AAAA,EACtB;AAEA,WAAS,gBAAgB;AACvB,UAAM,UAAU;AAAA,EAClB;AAEA,WAAS,eAAe;AACtB,KAAC,OAAO,cAAc,OAAO,YAAY,MAAM;AAC/C,WAAO,UAAU,MAAM,UAAU;AAAA,EACnC;AAEA,WAAS,qBAAqB,GAAG;AAC/B,UAAMI,UAAS,EAAE,aAAa,CAAC;AAE/B,aAAS,IAAI,GAAG,IAAIA,SAAQ,EAAE;AAC5B,OAAC,MAAM,UAAU,MAAM,CAAC,MAAM,MAAM,UAAU,MAAM,CAAC,IAAI,EAAE,aAAa,IAAI,IAAI,CAAC;AAEnF,UAAM,YAAY,WAAW,MAAM,SAAS,IAAI,MAAM;AACtD,UAAM,iBAAiB,CAAC,MAAM,iBAAiB,MAAM,SAAS,KAAK,CAAC,GAAG,MAAM,gBAAgB;AAAA,EAC/F;AAEA,WAAS,eAAe,GAAG;AACzB,QAAI,OAAO,SAAS;AAClB,gBAAU,WAAW,CAAC,MAAM;AAC5B,cAAQ,KAAK,SAAS,IAAI,OAAO,CAAC;AAClC,aAAO,QAAQ;AACf,YAAM,UAAU,UAAU;AAAA,IAC5B;AAEA,UAAMA,UAAS,EAAE,aAAa,CAAC;AAC/B,QAAI,QAAQ;AACZ,QAAI;AAEJ,UAAM,UAAU,UAAU,MAAMA,OAAM;AAEtC,aAAS,IAAI,GAAG,IAAIA,SAAQ,EAAE,GAAG;AAC/B,cAAQ;AACR,aAAO,EAAE,OAAO,MAAM,EAAE;AACxB,YAAM,QAAQ,EAAE,aAAa,KAAK;AAClC,YAAM,SAAS,EAAE,aAAa,QAAQ,CAAC;AACvC,YAAM,OAAO,EAAE,aAAa,QAAQ,CAAC;AACrC,YAAM,UAAU,QAAQ,CAAC,IAAI;AAAA,QAC3B,MAAM,UAAU,OAAO,OACnB,UAAU,OAAO,KAAK,EAAE,SAAS,QAAQ,OAAO,QAAQ,CAAC,CAAC,IAC1D,EAAE,SAAS,QAAQ,OAAO,QAAQ,CAAC;AAAA,QACvC,QAAQN,SAAQ,IAAI;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,eAAS;AAAA,IACX;AAEA,WAAO,YAAY,MAAM;AACzB,QAAI,MAAM;AACR,aAAQ,MAAM,QAAQ,MAAM,SAAS,GAAG,MAAM,IAAI;AAAA,EACtD;AAEA,iBAAe,eAAe,GAAG,OAAO,EAAE,aAAa,CAAC,GAAG;AACzD,KACE,SAAS,IAAI,kCACb,SAAS,IAAI,4BACb,SAAS,KAAK,OACd,SAAS,KAAK,eACd,SAAS,KAAK,YACd,SAAS,IAAI,cACb,MACA,GAAG,IAAI;AAAA,EACX;AAGA,iBAAe,kCAAkC;AAC/C,UAAM,UAAU,MAAM,KAAK;AAC3B;AAAA,MACE,cAAE,EAAE,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,CAAC,EAAE,IAAI;AAAA,IAChC;AAAA,EACF;AAEA,iBAAe,0BAA0B,GAAG;AAC1C,UAAM,UAAU,QACd,MAAM;AAAA,MACJ,OAAO,OAAO;AAAA,QACZ,OAAO,KAAK,MAAM,IAAK,MAAM,KAAK,IAAK,IAAI,CAAC;AAAA,QAC5C,EAAE,SAAS,CAAC;AAAA,MACd,CAAC;AAAA,IACH;AAEF;AAAA,MACE,cAAE,EAAE,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,CAAC,EAAE,IAAI;AAAA,IAChC;AAAA,EACF;AAEA,iBAAe,OAAO;AACpB,aAAS,MAAM,OAAO,YAAY,EAAE,GAAG,SAAS,QAAQ;AACxD,kBAAE,EAAE,EAAE,EAAE,IAAI,kBAAkB,cAAE,CAAC;AACjC,UAAM,IAAI,cAAE;AACZ,UAAM,cAAE,IAAI,CAAC,EAAE,IAAI,cAAc,KAAK,EAAE,IAAI,cAAE,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC;AAAA,EACnE;AAEA,iBAAe,aAAa,GAAG;AAC7B,UAAM,MAAM,EAAE,SAAS,QAAQ,CAAC,EAAE,MAAM,GAAG,EAAE,OAAO,CAAC,KAAKO,QAAO,IAAIA,GAAE,CAAC,CAAC,IAAIA,GAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAEjG,UAAM,iBAAiB,MAAM,OAAO;AAAA,MAClC,MAAM,KAAK;AAAA,MACX,OAAO,KAAK,IAAI,GAAG,QAAQ;AAAA,MAC3B,SAAS,IAAI,CAAC;AAAA,MAAG;AAAA,MACjB;AAAA,IACF;AAEA,UAAM,YAAY,MAAM,KAAK,gBAAgB,YAAY;AAEzD,UAAM,OAAO,WAAW,QAAQ,QACZ,IAAI,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,IAC3C,eAAe,IAAI;AAEhC,uBAAmB,MAAM,KAAK,MAAM,KAAK,gBAAgB,YAAY,GAAG,IAAI,GAAG,SAAS,QAAQ;AAEhG,UAAM,UAAU,cAAc,IAAI,IAAI,QAAQ;AAAA,MAC5C;AAAA,MAAW,OAAO,KAAK,MAAM,KAAK,MAAM,OAAO,SAAS,GAAG,IAAI,CAAC;AAAA,IAClE,EAAE,SAAS,QAAQ;AAEnB;AAAA,MACE,cAAE,EAAE,EAAE,EAAE,IAAI,OAAO,EAAE,IAAI;AAAA,IAC3B;AAAA,EACF;AAEA,WAAS,UAAU,GAAG;AACpB,QAAI,EAAE,SAAS,QAAQ,CAAC,EAAE,MAAM,cAAE,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM;AACtD;AAEF,YAAQ,OAAO,QAAQ,2BAA2B,iDAAiD,CAAC;AACpG,WAAO,QAAQ;AAAA,EACjB;AAEA,WAAS,OAAO;AACd,WAAO,QAAQ;AAAA,MAAQ,OAAO,QAAQ,SAAS,aAC3C,QAAQ,KAAK,IACb,QAAQ;AAAA,IACZ;AAAA,EACF;AAEA,WAAS,SAAS;AAChB,WAAO,YAAY,MAAM;AACzB,WAAO,UAAU,UAAU,CAAC;AAC5B,QAAI,MAAM;AACR,aAAQ,MAAM,QAAQ,MAAM,SAAS,GAAG,MAAM,IAAI;AAAA,EACtD;AAEA,WAAS,eAAe,GAAG;AACzB,YAAQ,MAAM,EAAE,aAAa,CAAC;AAC9B,YAAQ,SAAS,EAAE,aAAa,CAAC;AAAA,EACnC;AAEA,iBAAe,kBAAkB;AAC/B,iBAAa;AACb,UAAMF,SAAQ,MAAM,IAAI,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAO9B,GAAG,CAAC,GAAG,OAAO;AACf,IAAAA,OAAM,QAAQ,CAAC,EAAE,KAAK,SAAS,MAAM,aAAa,KAAK,QAAQ,CAAC;AAAA,EAClE;AAEA,WAAS,aAAa,KAAK,UAAU;AACnC,QAAI,CAAC,CAAC,QAAQ,QAAQ,QAAQ,KAAK,CAAC,CAAC,QAAQ,YAAY,QAAQ,EAAG;AACpE,UAAM,SAAS,QAAQ,QAAQ,GAAG;AAClC,YAAQ,OAAO,aAAa,GAAG,IAAI;AACnC,YAAQ,QAAQ,QAAQ,IAAI,CAAC,OAAO,YAAY,IAAI,QAAQ,QAAQ;AACpE,YAAQ,QAAQ,QAAQ,EAAE,QAAQ;AAClC,YAAQ,YAAY,QAAQ,IAAI,CAAC,OAAO,gBAAgB,IAAI,QAAQ,YAAY,GAAG,GAAG,SAAS,QAAQ;AAAA,EACzG;AAEA,WAAS,QAAQ,GAAG,IAAI;AACtB,WACG,MAAM,gBAAgB,GAAG,kCAAkC,QAC3D,MAAM,eAAe,GAAG,kCAAkC,SAC1D,MAAM,aAAa,GAAG,mBAAmB,QACzC,MAAM,aAAa,GAAG,mBAAmB,SACzC,MAAM,oBAAoB,GAAG,mBAAmB,SAAS,QAAQ,KAAK,OAAO;AAAA,EAElF;AAEA,WAAS,aAAa;AACpB,UAAMF,SAAQ,IAAI,MAAM,CAAC;AAAA;AAAA;AAAA,KAGxB,GAAG,CAAC,GAAG,SAAS,MAAM,EAAE,QAAQ,KAAK,CAAC;AACvC,IAAAA,OAAM,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAACK,EAAC,CAAC,MAAM;AAC9B,wBAAkB,gCAAgC,EAAE;AACpD,wBAAkB,iBAAiBA,GAAE,oBAAoB,OAAO;AAAA,IAClE;AACA,IAAAL,OAAM,QAAQ;AAAA,EAChB;AAEA,WAAS,cAAc,GAAG;AACxB,cAAU,MAAM,YAAY,MAAM,kBAAkB,MAAM,IAAI;AAC9D,UAAMC,SAAQ,OAAO,SAAS,WAAW,CAAC,CAAC;AAC3C,aAAS,MAAM,UACX,QAAQ,MAAM,OAAO,IACrB,SAAS,MAAM,YAAY,cAAc,IAAIA,OAAM,OAAO,IACxD,MAAM,OAAOA,MAAK,IAClB,QAAQA,MAAK;AAAA,EACrB;AAEA,WAAS,MAAM,GAAGA,QAAO;AACvB,WAAO,WAAW,EAAE,SAAS;AAC7B,MAAE,UAAUA;AACZ,YAAQ,CAAC;AAAA,EACX;AAEA,WAAS,qBAAqB,GAAG;AAC/B,QAAI,CAAC;AACH;AAEF,QAAI,QAAQ;AACZ,WAAO,EAAE,OAAO,MAAM,EAAE;AACxB;AAAA,MACE,EAAE,SAAS,QAAQ,GAAG,QAAQ,CAAC;AAAA,MAC/B,EAAE,SAAS,QAAQ,OAAO,EAAE,SAAS,CAAC;AAAA,IACxC;AAAA,EACF;AAEA,iBAAe,kBAAkB;AAC/B,QAAI;AACF,YAAM,IAAI,MAAM,QAAQ,QAAQ,MAAM,SAAS,MAAM,CAAC;AACtD,aAAO;AACP,YAAM,QACF,MAAM,MAAM,MAAM,MAAM,CAAC,KACxB,SAAS,IAAI,OAAO,GAAG,MAAM,QAAQ,IAAI,MAAM,UAAU,CAAC;AAAA,IACjE,SAAS,KAAK;AACZ,YAAM,IAAI;AACV,YAAM,OAAO,GAAG;AAAA,IAClB;AAAA,EACF;AAEA,WAAS,gBAAgB;AACvB,WAAO,SAAS,MAAM,SAAS,MAAM;AACrC,UAAM,QAAQ,MAAM;AAAA,EACtB;AAEA,WAAS,iBAAiB;AACxB,aAAS,IAAI,OAAO,SAAS;AAAA,MAC3B,aAAa;AAAA,MACb,MAAMK,QAAO,UAAU,UAAU;AAC/B,eAAO,MAAM,cAAE,EAAE,EAAE,EAAE,IAAIA,MAAK,EAAE,IAAI,GAAG,QAAQ;AAAA,MACjD;AAAA,MACA,QAAQL,QAAO,UAAU;AACvB,iBAASA,MAAK;AACd,eAAO,MAAM,cAAE,EAAE,EAAE,EAAE,IAAIA,SAAQ,cAAE,CAAC,EAAE,IAAI,CAAC;AAC3C,iBAAS;AAAA,MACX;AAAA,MACA,MAAM,UAAU;AACd,eAAO,MAAM,cAAE,EAAE,EAAE,EAAE,IAAI,CAAC;AAC1B,gBAAQ;AAAA,MACV;AAAA,IACF,CAAC;AACD,UAAM,QAAQ,MAAM;AAAA,EACtB;AAEA,WAAS,kBAAkB;AACzB,aAAS,IAAI,OAAO,SAAS;AAAA,MAC3B,OAAO;AAAE,eAAO,OAAO;AAAA,MAAE;AAAA,IAC3B,CAAC;AACD,UAAM,QAAQ,MAAM;AAAA,EACtB;AAGA,WAAS,mBAAmB;AAC1B,aAAS,IAAI,OAAO,OAAO;AAAA,MACzB,aAAa;AAAA,MACb,OAAO;AAAE,eAAO,OAAO;AAAA,MAAE;AAAA;AAAA,MAEzB,MAAMK,QAAO,UAAU,UAAU;AAC/B,eAAO,MAAM,cAAE,EAAE,EAAE,EAAE,IAAIA,MAAK,EAAE,IAAI,GAAG,QAAQ;AAAA,MACjD;AAAA,MACA,QAAQL,QAAO,UAAU;AACvB,iBAASA,MAAK;AACd,eAAO,MAAM,cAAE,EAAE,EAAE,EAAE,IAAIA,SAAQ,cAAE,CAAC,EAAE,IAAI,CAAC;AAC3C,iBAAS;AAAA,MACX;AAAA,MACA,MAAM,UAAU;AACd,eAAO,MAAM,cAAE,EAAE,EAAE,EAAE,IAAI,CAAC;AAC1B,gBAAQ;AAAA,MACV;AAAA,IACF,CAAC;AACD,UAAM,QAAQ,MAAM;AAAA,EACtB;AAEA,WAAS,SAAS,GAAG;AACnB,eAAW,OAAO,KAAK,EAAE,SAAS,CAAC,CAAC,KAAK,OAAO,MAAM;AAAA,EACxD;AAEA,WAAS,WAAW;AAClB,cAAU,OAAO,KAAK,IAAI;AAC1B,aAAS;AAAA,EACX;AAEA,WAAS,eAAe,GAAG;AACzB,eACI,SAAS,WAAW,CAAC,CAAC,IACtB,QAAQ,IAAI,WAAW,CAAC,CAAC;AAAA,EAE/B;AAGA,WAAS,qBAAqB;AAAA,EAE9B;AAGA,WAAS,uBAAuB;AAC9B,YAAQ,OAAO,aAAa,sBAAsB,CAAC;AAAA,EACrD;AAGA,WAAS,2BAA2B;AAClC,YAAQ,OAAO,aAAa,0BAA0B,CAAC;AAAA,EACzD;AAGA,WAAS,eAAe,GAAG;AACzB,YAAQ,MAAM,kCAAkC,EAAE,CAAC,CAAC;AAAA,EACtD;AAGA,WAAS,YAAY,GAAG,MAAM;AAC5B,YAAQ,MAAM,+BAA+B,IAAI;AAAA,EACnD;AAGA,WAAS,KAAK,YAAYC,QAAO,YAAY,IAAI,SAAS,IAAI;AAC5D,QAAI,MACA;AAEJ,kBAAE,EAAE,EAAE,EAAE,IAAI,SAAS,cAAE,CAAC,EAAE,IAAI,YAAY,cAAE,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,WAAW,MAAM;AAE3E,eAAW,QAAQ,CAAC,GAAG,MAAM;AAC3B,UAAI,MAAM;AACR,eAAO,cAAE,IAAI,UAAU;AAEzB,aAAOA,OAAM,CAAC;AACd,iBAAW,CAAC,IAAI,IAAI,QAAQ,QAAQ,cAChC,QAAQ,YAAY,IAAI,EAAE,CAAC,IAC3B,KAAK;AAET,aAAO,cAAE;AACT,oBAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,cAAE,IAAI,OAAO,GAAG,IAAI;AAAA,IAC1C,CAAC;AAED,kBAAE,IAAI,CAAC;AAEP,WAAO,cAAE,IAAI;AAAA,EACf;AAEA,WAAS,MAAM,KAAK,YAAYA,QAAO,OAAO,IAAI;AAChD,kBAAE,EAAE,EAAE,EAAE,IAAI,OAAO,cAAE,CAAC,EAAE,IAAI,MAAM,cAAE,CAAC,EAAE,IAAI,WAAW,MAAM;AAC5D,eAAW,QAAQ,CAAC,GAAG,MAAM,cAAE,IAAIA,OAAM,CAAC,KAAK,CAAC,CAAC;AACjD,WAAO,cAAE,IAAI;AAAA,EACf;AAEA,WAAS,SAAS,GAAG,OAAO,IAAI;AAC9B,WAAO,cAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,IAAI,OAAO,cAAE,CAAC,EAAE,IAAI;AAAA,EAC5C;AAEA,WAAS,QAAQ,SAAS,IAAIK,QAAO,GAAG;AACtC,WAAO,OAAO,OAAO;AAAA,MACnB,cAAE,EAAE,EAAE,EAAE,IAAI,SAAS,cAAE,CAAC,EAAE,IAAIA,KAAI,EAAE,IAAI;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,MAAM,SAAS,IAAI;AAC1B,WAAO,OAAO,OAAO;AAAA,MACnB,cAAE,EAAE,EAAE,EAAE,IAAI,GAAG,EAAE,IAAI,SAAS,cAAE,CAAC,EAAE,IAAI;AAAA,MACvC,cAAE,EAAE,EAAE,EAAE,IAAI;AAAA,IACd,CAAC;AAAA,EACH;AAEA,WAAS,iBAAiB;AACxB,WAAO,iBAAiB,cAAE,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AAAA,MAC7C,OAAO,QAAQ,OAAO;AAAA,QAAO;AAAA,UAC3B;AAAA,UACA;AAAA,UACA,iBAAiB;AAAA,QACnB;AAAA,QACE,QAAQ;AAAA,MACV,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,IAAI,cAAE,IAAI,CAAC,EAAE,KAAK,cAAE,CAAC;AAAA,IAC/D,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC;AAAA,EACd;AAEF;AAEA,SAAS,WAAW,GAAG;AACrB,QAAM,QAAQ,CAAC;AACf,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,EAAE,SAAS,GAAG,KAAK;AACrC,QAAI,EAAE,CAAC,MAAM,GAAG;AACd,YAAM,YAAY,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,SAAS,QAAQ,QAAQ,GAAG,CAAC;AAC9D,cAAQ,IAAI;AAAA,IACd;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,IAAI,GAAG;AACd,SAAO,OAAO,WAAW,KAAK,EAAE,OAAO,CAAC,EAAE,OAAO,KAAK;AACxD;AAEA,SAAS,KAAK,KAAK,GAAG;AACpB,SAAO,OAAO,WAAW,UAAU,GAAG,EAAE,OAAO,CAAC,EAAE,OAAO;AAC3D;AAEA,SAAS,OAAO,GAAG;AACjB,SAAO,OAAO,WAAW,QAAQ,EAAE,OAAO,CAAC,EAAE,OAAO;AACtD;AAEA,SAAS,IAAI,GAAGF,IAAG;AACjB,QAAM,SAAS,KAAK,IAAI,EAAE,QAAQA,GAAE,MAAM;AAC1C,QAAMG,UAAS,OAAO,YAAY,MAAM;AACxC,WAAS,IAAI,GAAG,IAAI,QAAQ;AAC1B,IAAAA,QAAO,CAAC,IAAI,EAAE,CAAC,IAAIH,GAAE,CAAC;AACxB,SAAOG;AACT;AAEA,SAAS,MAAM,IAAI,SAAS;AAC1B,YAAU,OAAO,YAAY,aAAa,QAAQ,IAAI;AACtD,MAAI,CAAC;AACH,WAAO,EAAE,QAAQ,MAAM,OAAO,KAAK;AAErC,MAAIC;AACJ,SAAO;AAAA,IACL,SAAS;AACP,MAAAA,WAAU,aAAaA,MAAK,GAAGA,SAAQ;AAAA,IACzC;AAAA,IACA,QAAQ;AACN,MAAAA,UAAS,aAAaA,MAAK;AAC3B,MAAAA,SAAQ,WAAW,MAAM,UAAU,KAAM,SAAS;AAAA,IACpD;AAAA,EACF;AAEA,WAAS,KAAK,MAAM;AAClB,OAAG,MAAM,MAAM,IAAI;AACnB,IAAAA,SAAQ;AAAA,EACV;AACF;;;AIjhCA;AAAA,IAAMC,QAAO,MAAM;AAAa;AAEjB,SAAR,UAA2BC,WAAU,SAAS;AACnD,QAAM,cAAc,oBAAI,IAAI,GACtB,OAAO,gBAAgB,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,GACzD,QAAQ,CAAC;AAEf,MAAIC,aACA,QACA,QAAQ;AAEZ,QAAMC,OAAM,UAAU,MAAMF,UAAS;AAAA,IACnC,GAAG;AAAA,IACH,WAAW,EAAE,QAAQ,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC,EAAE;AAAA,IAC5C,KAAK;AAAA,IACL,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,YAAY;AAAA,MACV,GAAG,QAAQ;AAAA,MACX,aAAa;AAAA,IACf;AAAA,IACA,SAAS,iBAAiB;AACxB,UAAI;AACF;AACF,eAAS;AACT,YAAM,MAAM,MAAM,SAAS;AAC3B,gBAAU,MAAM,KAAKE,MAAK,MAAM,QAAQ,YAAY,CAAC;AACrD,kBAAY,QAAQ,WAAS,MAAM,QAAQ,CAAC,EAAE,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,IAChF;AAAA,IACA,cAAc;AAAA,EAChB,CAAC;AAED,QAAM,MAAMA,KAAI,KACV,QAAQA,KAAI;AAElB,EAAAA,KAAI,MAAM,YAAW;AACnB,YAAQ;AACR,cAAW,MAAM,IAAI,QAAQ,QAAM,OAAO,KAAK,SAAS,CAAC,GAAG,OAAO,IAAI,EAAE;AACzE,WAAO,IAAI;AAAA,EACb;AAEA,EAAAA,KAAI,QAAQ,YAAW;AACrB,cAAW,MAAM,IAAI,QAAQ,QAAM,OAAO,KAAK,SAAS,CAAC,GAAG,OAAO,IAAI,EAAE;AACzE,WAAO,MAAM;AAAA,EACf;AAEA,SAAO;AAEP,iBAAe,UAAU,OAAO,IAAI,cAAcH,OAAM,UAAUA,OAAM;AACtE,YAAQ,WAAW,KAAK;AAExB,QAAI,CAACE;AACH,MAAAA,cAAa,KAAKC,MAAK,MAAM,QAAQ,YAAY;AAEnD,UAAM,aAAa,EAAE,IAAI,YAAY;AACrC,UAAM,MAAM,YAAY,IAAI,KAAK,IAC7B,YAAY,IAAI,KAAK,EAAE,IAAI,UAAU,IACrC,YAAY,IAAI,OAAO,oBAAI,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,KAAK;AAE3D,UAAM,cAAc,MAAM;AACxB,UAAI,OAAO,UAAU;AACrB,UAAI,SAAS,KAAK,YAAY,OAAO,KAAK;AAAA,IAC5C;AAEA,WAAOD,YAAW,KAAK,OAAK;AAC1B,gBAAU,CAAC;AACX,kBAAY;AACZ,gBAAU,OAAO,GAAG,SAAS,OAAO;AACpC,aAAO,EAAE,aAAa,OAAO,KAAAC,KAAI;AAAA,IACnC,CAAC;AAAA,EACH;AAEA,WAAS,UAAU,GAAG;AACpB,aAAS,EAAE;AACX,UAAM,MAAM,EAAE,MAAM;AACpB,UAAM,SAAS,EAAE,MAAM;AAAA,EACzB;AAEA,iBAAe,KAAKA,MAAKC,OAAM,cAAc;AAC3C,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,2BAA2B;AAE7C,UAAM,KAAK,MAAMD,KAAI;AAAA,MACnB,2BAA4BC,KAAK;AAAA,IACnC;AAEA,UAAM,CAAC,CAAC,IAAI;AAEZ,UAAMC,UAAS,MAAMF,KAAI;AAAA,MACvB,0BAA2BC,KAAK,YAC9B,EAAE,gBACJ,2CAA4C,YAAa;AAAA,IAC3D,EAAE,SAAS;AAEX,UAAME,SAAQ;AAAA,MACZ,KAAK,OAAO,OAAO,EAAE,iBAAiB,MAAM,GAAG,EAAE,IAAI,CAAAC,OAAK,OAAO,MAAM,aAAaA,IAAG,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC;AAAA,IAC3G;AAEA,IAAAF,QAAO,GAAG,QAAQ,IAAI;AACtB,IAAAA,QAAO,GAAG,SAAS,KAAK;AACxB,IAAAA,QAAO,GAAG,SAASF,KAAI,KAAK;AAE5B,WAAO,EAAE,QAAAE,SAAQ,OAAO,GAAG,MAAM;AAEjC,aAAS,MAAM,GAAG;AAChB,cAAQ,MAAM,4DAA4D,CAAC;AAAA,IAC7E;AAEA,aAAS,KAAKE,IAAG;AACf,UAAIA,GAAE,CAAC,MAAM,KAAM;AACjB,cAAMA,GAAE,SAAS,EAAE,GAAGD,QAAOH,KAAI,QAAQ,SAAS,QAAQ,QAAQ,SAAS;AAAA,MAC7E,WAAWI,GAAE,CAAC,MAAM,OAAQA,GAAE,EAAE,GAAG;AACjC,QAAAD,OAAM,MAAMC,GAAE,SAAS,GAAG,CAAC;AAC3B,aAAK;AAAA,MACP;AAAA,IACF;AAEA,aAAS,OAAO,GAAGC,IAAG;AACpB,YAAM,OAAOA,GAAE,SAAS,SAAS,MAAMA,GAAE,SAAS;AAClD,WAAK,KAAK,GAAGA,EAAC;AACd,WAAK,OAAO,MAAM,GAAGA,EAAC;AACtB,MAAAA,GAAE,SAAS,KAAK,UAAU,KAAK,OAAO,OAAO,MAAMA,GAAE,SAAS,KAAK,IAAI,CAAAD,OAAK,EAAEA,GAAE,IAAI,CAAC,GAAG,GAAGC,EAAC;AAC5F,WAAKA,GAAE,SAAS,GAAGA,EAAC;AACpB,WAAKA,GAAE,UAAU,MAAM,MAAM,GAAGA,EAAC;AACjC,MAAAA,GAAE,SAAS,KAAK,UAAU,KAAKA,GAAE,UAAU,MAAM,OAAO,MAAMA,GAAE,SAAS,KAAK,IAAI,CAAAD,OAAK,EAAEA,GAAE,IAAI,CAAC,GAAG,GAAGC,EAAC;AAAA,IACzG;AAEA,aAAS,OAAO;AACd,YAAMD,KAAI,OAAO,MAAM,EAAE;AACzB,MAAAA,GAAE,CAAC,IAAI,IAAI,WAAW,CAAC;AACvB,MAAAA,GAAE,KAAKD,OAAM,KAAK,CAAC;AACnB,MAAAC,GAAE,gBAAgB,OAAO,KAAK,IAAI,IAAI,KAAK,IAAI,KAAM,GAAG,CAAC,CAAC,IAAI,OAAO,GAAI,GAAG,EAAE;AAC9E,MAAAF,QAAO,MAAME,EAAC;AAAA,IAChB;AAAA,EACF;AAEA,WAAS,KAAK,GAAG,GAAGC,IAAG;AACrB,gBAAY,IAAI,CAAC,KAAK,YAAY,IAAI,CAAC,EAAE,QAAQ,CAAC,EAAE,GAAG,MAAM,GAAG,GAAGA,IAAG,CAAC,CAAC;AAAA,EAC1E;AACF;AAEA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,KAAK,KAAK,IAAI,KAAM,GAAG,CAAC,IAAI,OAAO,IAAI,OAAO,GAAI,CAAC,CAAC;AACjE;AAEA,SAAS,MAAM,GAAG,OAAOC,UAAS,QAAQ,WAAW;AACnD,QAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE,WAAW,CAAC,CAAC,IAAI,GAAG;AAEzD,SAAO,QAAQ;AAAA,IACb,GAAG,CAAAF,OAAK;AACN,UAAI,IAAI;AACR,YAAM,IAAI,MAAMA,GAAE,aAAa,CAAC,CAAC,IAAI;AAAA,QACnC,QAAQA,GAAE,SAAS,QAAQ,KAAK,GAAG,IAAIA,GAAE,QAAQ,GAAG,CAAC,CAAC,KAAK;AAAA,QAC3D,OAAOA,GAAE,SAAS,QAAQ,IAAI,GAAG,IAAIA,GAAE,QAAQ,GAAG,IAAI,CAAC,CAAC;AAAA,QACxD,SAAS,MAAMA,GAAE,aAAa,KAAK,CAAC,CAAC;AAAA,QACrC,MAAM,CAAC;AAAA,MACT;AACA,WAAK;AAEL,UAAI,cAAc,GACd;AAEJ,aAAO,IAAIA,GAAE,QAAQ;AACnB,iBAAS,EAAE,QAAQ,aAAa,IAAI;AAAA,UAClC,KAAKA,GAAE,GAAG;AAAA,UACV,MAAM,UAAU,OAAO,OACnB,UAAU,OAAO,KAAKA,GAAE,SAAS,QAAQ,GAAG,IAAIA,GAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,IAChEA,GAAE,SAAS,QAAQ,GAAG,IAAIA,GAAE,QAAQ,GAAG,CAAC,CAAC;AAAA,UAC7C,MAAMA,GAAE,aAAa,KAAK,CAAC;AAAA,UAC3B,QAAQE,SAAQF,GAAE,aAAa,CAAC,CAAC;AAAA,UACjC,WAAWA,GAAE,aAAa,KAAK,CAAC;AAAA,QAClC;AAEA,eAAO,OAAO,EAAE,KAAK,KAAK,MAAM;AAChC,aAAK;AAAA,MACP;AAAA,IACF;AAAA,IACA,GAAG,MAAM;AAAA,IAAa;AAAA;AAAA,IACtB,GAAG,MAAM;AAAA,IAAa;AAAA;AAAA,IACtB,GAAG,CAAAA,OAAK;AACN,YAAM,OAAO,KAAKA,GAAE,eAAe,CAAC,CAAC;AACrC,YAAM,MAAMA,GAAE,SAAS,GAAG,CAAC;AAAA,IAC7B;AAAA,IACA,GAAG,CAAAA,OAAK;AACN,UAAI,IAAI;AACR,YAAM,WAAW,MAAMA,GAAE,aAAa,CAAC,CAAC;AACxC,YAAM,EAAE,IAAI,IAAI,OAAOA,IAAG,SAAS,SAAS,KAAK,GAAG,SAAS;AAE7D,aAAO,KAAK;AAAA,QACV,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,GAAG,CAAAA,OAAK;AACN,UAAI,IAAI;AACR,YAAM,WAAW,MAAMA,GAAE,aAAa,CAAC,CAAC;AACxC,WAAK;AACL,YAAM,MAAMA,GAAE,CAAC,MAAM;AACrB;AAAA,QAAO,OAAOA,GAAE,CAAC,MAAM,KACnB,OAAOA,IAAG,SAAS,SAAS,KAAK,GAAG,SAAS,EAAE,MAC/C;AAAA,QACF;AAAA,UACA,SAAS;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,MAAC;AAAA,IACH;AAAA,IACA,GAAG,CAAAA,OAAK;AACN,UAAI,IAAI;AACR,YAAM,WAAW,MAAMA,GAAE,aAAa,CAAC,CAAC;AACxC,WAAK;AACL,YAAM,MAAMA,GAAE,CAAC,MAAM;AACrB,YAAM,KAAK,OAAOA,GAAE,CAAC,MAAM,KACvB,OAAOA,IAAG,SAAS,SAAS,KAAK,GAAG,SAAS,IAC7C;AAEJ,aAAO,IAAI,GAAG;AAEd,YAAM,EAAE,IAAI,IAAI,OAAOA,IAAG,SAAS,SAAS,IAAI,GAAG,SAAS;AAE5D,aAAO,KAAK;AAAA,QACV,SAAS;AAAA,QACT;AAAA,QACA;AAAA,QACA,KAAK,MAAM,GAAG;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,IACA,GAAG,MAAM;AAAA,IAAa;AAAA;AAAA,IACtB,GAAG,MAAM;AAAA,IAAa;AAAA;AAAA,EACxB,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;AAC7B;AAEA,SAAS,OAAO,GAAG,SAAS,IAAI,WAAW;AACzC,MAAI,MACA,QACA;AAEJ,QAAM,MAAM,UAAU,MAAM,IAAI,MAAM,QAAQ,MAAM,IAAI,CAAC;AACzD,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,WAAO,EAAE,IAAI;AACb,aAAS,QAAQ,CAAC;AAClB,YAAQ,SAAS,MACb,OACA,SAAS,MACP,SACA,OAAO,WAAW,SAChB,EAAE,SAAS,QAAQ,KAAK,GAAG,MAAM,IAAI,EAAE,aAAa,EAAE,CAAC,IACvD,OAAO,OAAO,UAAU,OACtB,OAAO,OAAO,EAAE,SAAS,QAAQ,KAAK,GAAG,MAAM,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC,IACtE,OAAO,OAAO,EAAE,SAAS,QAAQ,KAAK,GAAG,MAAM,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;AAEhF,cAAU,MACL,IAAI,CAAC,IAAI,UAAU,QAAQ,OAC1B,QACA,UAAU,MAAM,OAAO,UAAU,MAAM,KAAK,OAAO,MAAM,IAAI,QAC9D,IAAI,OAAO,IAAI,IAAI,UAAU,MAAM,OAClC,UAAU,MAAM,KAAK,OAAO,MAAM,IAClC;AAAA,EAER;AAEA,SAAO,EAAE,GAAG,IAAI,KAAK,UAAU,IAAI,OAAO,UAAU,IAAI,KAAK,GAAG,IAAI,IAAI;AAC1E;AAEA,SAAS,WAAW,GAAG;AACrB,QAAM,KAAK,EAAE,MAAM,wDAAwD,KAAK,CAAC;AAEjF,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,kCAAkC,CAAC;AAErD,QAAM,CAAC,EAAE,SAAS,MAAM,GAAG,IAAI;AAE/B,UAAQ,WAAW,QACX,OAAO,OAAO,KAAK,QAAQ,GAAG,MAAM,KAAK,YAAY,OAAO,QAAQ,OACpE,MAAM,MAAM,MAAM;AAC5B;;;ACpRA;AAAA,OAAOG,aAAY;AAEJ,SAAR,YAA6BC,MAAK,KAAK,OAAO,SAAa,QAAY;AAC5E,SAAO,IAAI,QAAQ,OAAM,SAAS,WAAW;AAC3C,UAAMA,KAAI,MAAM,OAAMA,SAAO;AAC3B,UAAI;AACJ,OAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,IAAI,MAAMA;AAC3B,YAAM,CAAC,EAAE,GAAG,CAAC,IAAI,MAAMA,sBAAsB,GAAI,KAAM,IAAK;AAE5D,YAAM,KAAK;AAAA,QACT;AAAA,QACA;AAAA,QACA,OAAY,MAAMA,uBAAuB,EAAG,IAAI,KAAK,MAAM;AAAA,QAC3D,MAAY,MAAMA,wBAAwB,EAAG;AAAA,QAC7C,MAAY,CAAC,MAAMA,qBAAqB,EAAG,KAAM,CAAE;AAAA,QACnD,OAAY,CAAC,MAAMA,sBAAsB,EAAG,KAAM,CAAE;AAAA,QACpD,UAAY,CAAC,MAAMA,4BAA4B,EAAG,KAAM,CAAE;AAAA,QAC1D,MAAY,CAAC,GAAG,SAAS,MAAMA,yBAAyB,EAAG,KAAM,CAAE,KAAM,MAAO;AAAA,QAChF,MAAY,MAAMA;AAAA;AAAA,yBAEA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASvB;AAEA,cAAQ,EAAE;AAEV,aAAO,IAAI,QAAQ,OAAM,MAAK,SAAS,CAAC;AAExC,qBAAe,SAAS;AAAA,QACtB,gBAAgB,OAAO;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,IAAI,CAAC,GAAG;AACN,YAAI,MAAM,MAAM;AAChB,iBAAS,MAAM,GAAG,KAAK,KAAK;AAC5B,eAAO,IAAID,QAAO,SAAS;AAAA,UACzB;AAAA,UACA,MAAM,KAAKE,OAAM;AACf,kBAAM,IAAIA,QAAO,MAAMA,QAAO,MAAMA;AACpC,mBAAOA;AACP,kBAAM,CAAC,EAAE,KAAK,CAAC,IAAI,MAAM,GAAG,KAAK,CAAC;AAClC,iBAAK,KAAK,IAAI;AACd,gBAAI,KAAK,SAASA;AAChB,mBAAK,KAAK,IAAI;AAAA,UAClB;AAAA,QACF,CAAC;AAAA,MACH;AAEA,qBAAe,SAAS;AAAA,QACtB,gBAAgB,OAAO;AAAA,QACvB,QAAQ;AAAA,MACV,IAAI,CAAC,GAAG;AACN,iBAAS,MAAM,GAAG,KAAK,KAAK;AAC5B,eAAO,IAAIF,QAAO,SAAS;AAAA,UACzB;AAAA,UACA,MAAM,OAAO,UAAU,UAAU;AAC/B,eAAG,MAAM,KAAK,EAAE,KAAK,MAAM,SAAS,GAAG,QAAQ;AAAA,UACjD;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC,EAAE,MAAM,MAAM;AAAA,EACjB,CAAC;AACH;;;AT1CA,OAAO,OAAO,UAAU;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,MAAM,CAAC,EAAE;AAAA,IACT,OAAO,OAAK,OAAO,CAAC;AAAA;AAAA,IACpB,WAAW,OAAK,EAAE,SAAS;AAAA,EAC7B;AACF,CAAC;AAED,IAAO,cAAQ;AAEf,SAAS,SAAS,GAAGG,IAAG;AACtB,QAAM,UAAU,aAAa,GAAGA,EAAC,GAC3B,YAAY,QAAQ,gBAAgB,UAAU,UAAU,EAAE,GAAG,QAAQ,CAAC;AAE5E,MAAI,SAAS;AAEb,QAAM,UAAU,cAAM,GAChB,aAAa,cAAM,GACnB,WAAW,cAAM,GACjB,SAAS,cAAM,GACf,QAAQ,cAAM,GACd,OAAO,cAAM,GACb,OAAO,cAAM,GACb,OAAO,cAAM,GACb,SAAS,EAAE,YAAY,UAAU,QAAQ,OAAO,MAAM,MAAM,KAAK;AAEvE,QAAM,cAAc,CAAC,GAAG,MAAM,QAAQ,GAAG,CAAC,EAAE,IAAI,MAAM,mBAAW,SAAS,QAAQ,EAAE,QAAQ,OAAO,QAAQ,CAAC,CAAC;AAE7G,QAAMC,OAAM,IAAI,OAAO;AAEvB,SAAO,OAAOA,MAAK;AAAA,IACjB,IAAI,aAAa;AAAE,aAAO,QAAQ;AAAA,IAAW;AAAA,IAC7C,aAAa,YAAY,KAAK,MAAMA,IAAG;AAAA,IACvC;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,SAAOA;AAEP,WAAS,IAAIC,UAAS;AACpB,IAAAA,SAAQ,QAAQ,QAAQ;AAExB,WAAO,QAAQ,QAAQ,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,IAAI,MAAM;AAC1D,UAAI,IAAI,IAAI,CAAC,MAAM,IAAI,UAAU,GAAG,KAAK,EAAE;AAC3C,aAAO;AAAA,IACT,GAAG,KAAK;AAER,WAAO,OAAOD,MAAK;AAAA,MACjB,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,WAAOA;AAEP,aAAS,MAAM,OAAO,MAAM;AAC1B,aAAO,IAAI,UAAU,OAAO,IAAI;AAAA,IAClC;AAEA,aAASA,KAAI,YAAY,MAAM;AAC7B,YAAM,QAAQ,WAAW,MAAM,QAAQ,QAAQ,GAAG,IAC9C,IAAI,MAAM,SAAS,MAAMC,UAAS,MAAM,IACxC,OAAO,YAAY,YAAY,CAAC,KAAK,SACnC,IAAI,WAAW,QAAQ,UAAU,OAAO,KAAK,QAAQ,UAAU,OAAO,GAAG,OAAO,IAAI,OAAO,IAC3F,IAAI,QAAQ,SAAS,IAAI;AAC/B,aAAO;AAAA,IACT;AAEA,aAAS,OAAO,QAAQ,OAAO,CAAC,GAAGC,WAAU,CAAC,GAAG;AAC/C,gBAAU,WAAW,KAAK,CAAC,MAAM,QAAQ,IAAI,MAAMA,WAAU,MAAM,OAAO,CAAC;AAC3E,YAAM,QAAQ,IAAI,MAAM,CAAC,MAAM,GAAG,MAAMD,UAAS,QAAQ;AAAA,QACvD,SAAS;AAAA,QACT,GAAGC;AAAA,QACH,QAAQ,YAAYA,WAAUA,SAAQ,SAAS,KAAK,WAAW;AAAA,MACjE,CAAC;AACD,aAAO;AAAA,IACT;AAEA,aAAS,KAAK,MAAM,OAAO,CAAC,GAAGA,WAAU,CAAC,GAAG;AAC3C,gBAAU,WAAW,KAAK,CAAC,MAAM,QAAQ,IAAI,MAAMA,WAAU,MAAM,OAAO,CAAC;AAC3E,YAAM,QAAQ,IAAI,MAAM,CAAC,GAAG,MAAM,CAACC,WAAU;AAC3C,WAAG,SAAS,MAAM,QAAQ,CAAC,KAAK,WAAW;AACzC,cAAI;AACF,mBAAOA,OAAM,OAAO,GAAG;AAEzB,UAAAA,OAAM,UAAU,CAAC,MAAM;AACvB,UAAAF,SAAQE,MAAK;AAAA,QACf,CAAC;AAAA,MACH,GAAG,QAAQ;AAAA,QACT,GAAGD;AAAA,QACH,QAAQ,YAAYA,WAAUA,SAAQ,SAAS,KAAK,WAAW;AAAA,MACjE,CAAC;AACD,aAAO;AAAA,IACT;AAAA,EACF;AAEA,iBAAe,OAAO,MAAM,IAAI,UAAU;AACxC,UAAM,WAAW,EAAE,IAAI,SAAS;AAEhC,UAAMF,OAAM,OAAO,QAAQ,OAAO,MAAM,SAAS;AAAA,MAC/C,GAAG;AAAA,MACH,KAAK;AAAA,MACL,cAAc;AAAA,MACd,cAAc;AAAA,MACd,aAAa;AAAA,MACb,UAAU;AACR,eAAO,QAAQ,OAAO,QAAQ,EAAE,QAAQ,CAAC,CAACI,OAAM,EAAE,UAAU,CAAC,MAAM;AACjE,iBAAO,OAAO,SAASA,KAAI;AAC3B,kBAAQ,IAAI,UAAU,IAAI,OAAK,OAAOA,OAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,MAAM;AAAA,UAAa,CAAC,CAAC,CAAC;AAAA,QAC5F,CAAC;AAAA,MACH;AAAA,MACA,SAAS,GAAG,GAAG;AACb,aAAK,OAAO,YAAY,OAAO,SAAS,CAAC,EAAE,UAAU,QAAQ,OAAK,EAAE,GAAG,CAAC,CAAC;AAAA,MAC3E;AAAA,IACF,CAAC;AAED,UAAM,WAAW,OAAO,aAAa,OAAO,WAAW,CAAC,IAClD,SAAS,QAAQ;AAEvB,QAAI,QAAQ;AACV,eAAS,IAAI,EAAE,UAAU,KAAK,QAAQ;AACtC,YAAMC,UAAS,MAAM,SAAS,IAAI,EAAE;AACpC,eAAS,YAAY,SAAS,SAAS;AACvC,aAAO,EAAE,OAAOA,QAAO,OAAO,SAAS;AAAA,IACzC;AAEA,aAAS,IAAI,IAAI,EAAE,QAAQL,cACzBA,KAAI,OAAO,MAAM,KAAK,QAAQ,MAAM,IAAI,IAAI,GAAG,CACjD,IAAI,WAAW,CAAC,QAAQ,EAAE;AAC1B,UAAM,SAAS,MAAM,SAAS,IAAI,EAAE;AACpC,aAAS,YAAY,SAAS,SAAS;AACvC,WAAO,EAAE,OAAO,OAAO,OAAO,SAAS;AAEvC,mBAAe,WAAW;AACxB,UAAI,QAAQ,aAAa;AACvB;AAEF,eAAS,IAAI,EAAE,YAAY,SAAS,IAAI,EAAE,UAAU,OAAO,OAAK,MAAM,QAAQ;AAC9E,UAAI,SAAS,IAAI,EAAE,UAAU;AAC3B;AAEF,aAAO,SAAS,IAAI;AACpB,aAAOA,gBACLA,KAAI,OAAO,MAAM,KAAK,QAAQ,MAAM,IAAI,IAAI,GAAG,CACjD;AAAA,IACF;AAAA,EACF;AAEA,iBAAe,OAAO,SAAS,SAAS;AACtC,WAAO,MAAMA,wBAAwB,OAAQ,KAAM,KAAK,OAAQ;AAAA,EAClE;AAEA,iBAAe,UAAU;AACvB,UAAM,QAAQ,cAAM;AACpB,UAAM,IAAI,KAAK,SACX,KAAK,MAAM,IACX,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,YAAM,QAAQ,EAAE,SAAS,SAAS,OAAO;AACzC,cAAQ,KAAK,KAAK;AAClB,aAAO,UAAU,QAAQ,OAAO,MAAM,GAAG,KAAK;AAAA,IAChD,CAAC;AAEH,SAAK,GAAG,QAAQ;AAChB,MAAE,WAAW,MAAM,MAAM,SACrB,EAAE,QAAQ,MAAM,MAAM,CAAC,IACvB,KAAK,GAAG,QAAQ;AACpB,MAAE,SAAS,UAAU;AAErB,UAAMA,OAAM,IAAIC,QAAO;AACvB,IAAAD,KAAI,UAAU,MAAM;AAClB,QAAE,WAAW;AACb,aAAO,CAAC;AAAA,IACV;AAEA,WAAOA;AAEP,aAASC,SAAQ,GAAG;AAClB,QAAE,UAAU,OACR,MAAM,KAAK,CAAC,IACZ,EAAE,QAAQ,CAAC,KAAK,KAAK,GAAG,IAAI;AAAA,IAClC;AAAA,EACF;AAEA,iBAAe,MAAMC,UAAS,IAAI;AAChC,KAAC,OAAO,KAAKA,UAASA,WAAU;AAChC,UAAMI,WAAU,cAAM;AACtB,QAAI,aAAa,GACbC,aACA,UAAU;AAEd,QAAI;AACF,YAAMP,KAAI,OAAO,WAAWE,SAAQ,QAAQ,aAAa,EAAE,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,EAAE,QAAQ;AACzF,aAAO,MAAM,QAAQ,KAAK;AAAA,QACxB,MAAMK,aAAY,EAAE;AAAA,QACpB,IAAI,QAAQ,CAAC,GAAG,WAAWA,YAAW,UAAU,MAAM;AAAA,MACxD,CAAC;AAAA,IACH,SAAS,OAAO;AACd,YAAM;AAAA,IACR;AAEA,mBAAe,MAAM,GAAGC,KAAI,MAAM;AAChC,YAAMR,OAAM,IAAIC,QAAO;AACvB,MAAAD,KAAI,YAAY;AAChB,MAAAA,KAAI,UAAU,OAAK,UAAU,EAAE,QAAQ,kBAAkB;AACzD,UAAI,eACA;AAEJ,cAAQ,MAAMA,iBAAiBA,KAAI,IAAI,CAAE;AACzC,UAAI;AACF,iBAAS,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC9C,gBAAM,IAAIQ,IAAGR,IAAG;AAChB,kBAAQ,QAAQ,MAAM,QAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,SAAS,MAAM;AAAA,QAC7E,CAAC;AAED,YAAI;AACF,gBAAM;AAAA,MACV,SAAS,GAAG;AACV,eAAO,OACHA,mBAAmBA,KAAI,IAAI,CAAE,KAC7BA;AAEJ,cAAM,aAAa,iBAAiB,EAAE,SAAS,WAAW,iBAAiB;AAAA,MAC7E;AAEA,UAAI,CAAC,MAAM;AACT,kBACI,MAAMA,4BAA4BA,KAAI,OAAO,OAAO,CAAE,MACtD,MAAMA;AAAA,MACZ;AAEA,aAAO;AAEP,eAAS,UAAUI,OAAMI,KAAI;AAC3B,YAAIJ,SAAQ,MAAM,QAAQA,MAAK,GAAG;AAChC,iBAAO,UAAU,CAAAJ,SAAOA,KAAI,MAAMA,MAAK,SAAS,CAAC;AAEnD,kBAAU,WAAW,MAAMQ,MAAKJ,OAAMA,QAAO;AAC7C,eAAO,MAAM,GAAGI,KAAI,MAAM,gBAAgBJ,QAAO,MAAMA,QAAO,GAAG;AAAA,MACnE;AAEA,eAASH,SAAQ,GAAG;AAClB,UAAE,MAAM,OAAK,kBAAkB,gBAAgB,EAAE;AACjD,UAAE,UAAU,OACRK,SAAQ,KAAK,CAAC,IACd,EAAE,QAAQ,CAAC,KAAK,KAAK,GAAG,IAAI;AAAA,MAClC;AAAA,IACF;AAEA,aAAS,UAAU,GAAG;AACpB,MAAAC,cAAa;AACb,WAAK,GAAG,QAAQ;AAChB,QAAE,WAAW,MAAMD,SAAQ,SACvB,EAAE,QAAQA,SAAQ,MAAM,CAAC,IACzB,KAAK,GAAG,QAAQ;AAAA,IACtB;AAAA,EACF;AAEA,WAAS,KAAK,GAAG,OAAO;AACtB,MAAE,MAAM,OAAO,CAAC;AAChB,UAAM,KAAK,CAAC;AACZ,MAAE,QAAQ;AACV,cAAU,OACN,EAAE,UAAU,MAAM,IAClB,EAAE,UAAU,OAAO;AACvB,WAAO;AAAA,EACT;AAEA,WAAS,KAAK,GAAG;AACf,WAAO,IAAI,UAAU,GAAG,IAAI;AAAA,EAC9B;AAEA,WAAS,MAAM,GAAG,MAAM;AACtB,QAAI,CAAC,MAAM,QAAQ,CAAC;AAClB,aAAO,MAAM,MAAM,KAAK,SAAS,CAAC;AAEpC,WAAO,IAAI,UAAU,GAAG,SAAS,EAAE,SAAS,UAAU,CAAC,KAAK,KAAK,IAAI,QAAQ,OAAO,YAAY;AAAA,EAClG;AAEA,WAAS,QAAQ,OAAO;AACtB,QAAI;AACF,aAAO,MAAM,OAAO,OAAO,WAAW,oBAAoB,SAAS,OAAO,CAAC;AAE7E,QAAI,KAAK;AACP,aAAO,GAAG,KAAK,MAAM,GAAG,KAAK;AAE/B,QAAI,OAAO;AACT,aAAO,QAAQ,OAAO,MAAM,GAAG,KAAK;AAEtC,SAAK,SACD,GAAG,KAAK,MAAM,GAAG,KAAK,IACtB,QAAQ,KAAK,KAAK;AAAA,EACxB;AAEA,WAAS,GAAG,GAAG,OAAO;AACpB,WAAO,EAAE,QAAQ,KAAK,IAClB,KAAK,GAAG,IAAI,IACZ,KAAK,GAAG,IAAI;AAAA,EAClB;AAEA,WAAS,OAAO,OAAO;AACrB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,QACF,MAAM,SACJ,mBAAW,OAAO,EAAE,OAAO,MAAM,OAAO,SAAS,MAAM,IACvD,MAAM,YAAY,EAAE,SAAS,OAAO,KAEtC,QAAQ,OAAO,KAAK,GACpB,MAAM,YAAY,MAClB,MAAM,OAAO,OAAO,QAAQ,SAAS,yCAAyC,CAAC,GAC/E,QAAQ;AAAA,IAEd,CAAC;AAAA,EACH;AAEA,iBAAe,IAAI,EAAE,UAAU,KAAK,IAAI,CAAC,GAAG;AAC1C,QAAI;AACF,aAAO;AAET,UAAM;AACN,QAAIG;AACJ,WAAO,SAAS,QAAQ,KAAK;AAAA,MAC3B,IAAI,QAAQ,OAAK,YAAY,SAASA,SAAQ,WAAW,SAAS,UAAU,KAAM,CAAC,EAAE;AAAA,MACrF,QAAQ,IAAI,YAAY,IAAI,OAAK,EAAE,IAAI,CAAC,EAAE;AAAA,QACxC,OAAO,MAAM,OAAO,IAAI,IAAI,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC;AAAA,QAC/C,UAAU,MAAM,UAAU,IAAI,IAAI,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC;AAAA,MACvD,CAAC;AAAA,IACH,CAAC,EAAE,KAAK,MAAM,aAAaA,MAAK,CAAC;AAAA,EACnC;AAEA,iBAAe,QAAQ;AACrB,UAAM,QAAQ,IAAI,YAAY,IAAI,OAAK,EAAE,IAAI,CAAC,CAAC;AAAA,EACjD;AAEA,iBAAe,QAAQ,SAAS;AAC9B,UAAM,QAAQ,IAAI,YAAY,IAAI,OAAK,EAAE,UAAU,CAAC,CAAC;AACrD,WAAO,QAAQ;AACb,cAAQ,MAAM,EAAE,OAAO,OAAO,WAAW,wBAAwB,OAAO,CAAC;AAC3E,YAAQ;AAAA,EACV;AAEA,WAAS,QAAQ,GAAG,OAAO;AACzB,SAAK,GAAG,UAAU;AAClB,MAAE,QAAQ,KAAK;AACf,WAAO;AAAA,EACT;AAEA,WAAS,MAAM,GAAG;AAChB,SAAK,GAAG,KAAK;AAAA,EACf;AAEA,WAAS,OAAO,GAAG;AACjB,QAAI,QAAQ,WAAW;AACrB,aAAO,KAAK,GAAG,IAAI;AAErB,QAAI,MAAM,KAAK,KAAK,QAAQ,UAAU,WAAW,SAAS,EAAE,GACxD,QAAQ;AAEZ,WAAO,SAAS,QAAQ,UAAU,QAAQ,GAAG;AAC3C,YAAM,QAAQ,QAAQ,MAAM;AAC5B,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,CAAC;AAExB,cAAQ,EAAE,QAAQ,KAAK;AAAA,IACzB;AAEA,YACI,KAAK,GAAG,IAAI,IACZ,KAAK,GAAG,IAAI;AAAA,EAClB;AAEA,WAAS,QAAQ,GAAG,GAAG;AACrB,SAAK,GAAG,MAAM;AACd,MAAE,WAAW;AACb,MAAE,YAAY,EAAE,QAAQ,CAAC,GAAG,EAAE,UAAU;AACxC,YAAQ,WAAW,QAAQ,QAAQ,EAAE,EAAE;AACvC,YAAQ,UAAU,QAAQ,GAAG,QAAQ,MAAM,CAAC;AAAA,EAC9C;AACF;AAEA,SAAS,aAAa,GAAGV,IAAG;AAC1B,MAAI,KAAK,EAAE;AACT,WAAO;AAET,QAAM,MAAM,QAAQ,KACd,KAAK,CAAC,KAAK,OAAO,MAAM,WAAWA,KAAI,MAAM,CAAC,GAC9C,EAAE,KAAK,UAAU,IAAI,SAAS,CAAC,GAC/B,QAAQ,CAAC,GAAG,IAAI,YAAY,EAAE,OAAO,CAACW,IAAG,CAACX,IAAG,CAAC,OAAOW,GAAEX,EAAC,IAAI,GAAGW,KAAI,CAAC,CAAC,GACrE,OAAO,EAAE,YAAY,EAAE,QAAQ,aAAa,IAAI,YAAY,IAAI,UAAU,aAC1E,OAAO,EAAE,QAAQ,IAAI,QAAQ,IAAI,UAAU,MAC3C,OAAO,EAAE,QAAQ,EAAE,YAAY,IAAI,YAAY,IAAI,cAAc,IAAI,UAAU,WAAW;AAEhG,IAAE,eAAe,EAAE,UAAU;AAC7B,QAAM,YAAY,MAAM,MAAM,MAAM,SAAS,OAAO,MAAM;AAC1D,eAAa,MAAM,QAAQ,IAAI,4DAA4D,GAAG,EAAE,eAAe,EAAE;AACjH,QAAM,gBAAgB,aAAa,MAAM,MAAM;AAE/C,QAAM,OAAO,CAAC,gBAAgB,mBAAmB,gBAAgB,gBAAgB,WAAW,YAAY;AACxG,QAAM,WAAW;AAAA,IACf,KAAkB;AAAA,IAClB,KAAkB;AAAA,IAClB,cAAkB;AAAA,IAClB,iBAAkB;AAAA,IAClB;AAAA,IACA,cAAkB;AAAA,IAClB;AAAA,IACA,YAAkB;AAAA,IAClB,SAAkB;AAAA,IAClB,OAAkB;AAAA,IAClB,aAAkB;AAAA,IAClB,cAAkB;AAAA,IAClB,sBAAsB;AAAA,EACxB;AAEA,SAAO;AAAA,IACL,MAAkB,MAAM,QAAQ,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG,EAAE,IAAI,OAAK,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;AAAA,IACvF,MAAkB,MAAM,QAAQ,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG,EAAE,IAAI,OAAK,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,KAAK,IAAI,CAAC;AAAA,IACzG,MAAkB,EAAE,QAAQ,KAAK,QAAQ,GAAG,IAAI,MAAM,OAAO,eAAe;AAAA,IAC5E,UAAkB,EAAE,YAAY,EAAE,OAAO,IAAI,YAAY,IAAI,MAAM,CAAC,KAAK,IAAI,cAAc;AAAA,IAC3F;AAAA,IACA,MAAkB,EAAE,QAAQ,EAAE,YAAY,IAAI,YAAY,IAAI,cAAc;AAAA,IAC5E,GAAG,OAAO,QAAQ,QAAQ,EAAE;AAAA,MAC1B,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM;AACf,cAAM,QAAQ,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,QAC9B,MAAM,CAAC,MAAM,aAAa,MAAM,CAAC,MAAM,UAAU,QAAQ,MAAM,CAAC,IACjE,IAAI,OAAO,EAAE,YAAY,CAAC,KAAK;AACnC,YAAI,CAAC,IAAI,OAAO,UAAU,YAAY,KAAK,SAAS,CAAC,IACjD,CAAC,QACD;AACJ,eAAO;AAAA,MACT;AAAA,MACA,CAAC;AAAA,IACH;AAAA,IACA,YAAkB;AAAA,MAChB,kBAAkB,IAAI,aAAa;AAAA,MACnC,GAAG,EAAE;AAAA,MACL,GAAG,OAAO,QAAQ,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,KAAK,aAAa,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,CAAC;AAAA,IAC3F;AAAA,IACA,OAAkB,EAAE,SAAS,CAAC;AAAA,IAC9B,sBAAsB,IAAI,GAAG,KAAK,GAAG;AAAA,IACrC,UAAkB,EAAE;AAAA,IACpB,UAAkB,EAAE;AAAA,IACpB,SAAkB,EAAE;AAAA,IACpB,aAAkB,EAAE;AAAA,IACpB,QAAkB,EAAE;AAAA,IACpB,WAAkB,eAAe,EAAE,aAAa,EAAE,WAAW,OAAU,CAAC;AAAA,IACxE,YAAkB,CAAC;AAAA,IACnB,QAAkB,EAAE,SAAS,GAAG,cAAc,CAAC,EAAE;AAAA,IACjD,GAAG,eAAe,EAAE,KAAK;AAAA,EAC3B;AACF;AAEA,SAAS,IAAI,GAAG,KAAK,KAAK;AACxB,QAAM,IAAI,EAAE,wBAAwB,IAAI,aAAa,IAAI,sBAAsB,KAAK,IAAI;AACxF,MAAI,CAAC,KAAK,CAAC,cAAc,aAAa,WAAW,WAAW,gBAAgB,EAAE,SAAS,CAAC;AACtF,WAAO;AAET,QAAM,IAAI,MAAM,0BAA0B,IAAI,mBAAmB;AACnE;AAEA,SAAS,QAAQ,SAAS;AACxB,UAAQ,MAAM,KAAK,OAAO,IAAI,KAAK,KAAK,IAAI,KAAK,UAAU,KAAK,EAAE;AACpE;AAEA,SAAS,eAAe;AACtB,SAAO,MAAM,KAAK,KAAK,OAAO,IAAI;AACpC;AAEA,SAAS,eAAe,GAAG;AACzB,SAAO;AAAA,IACL,WAAW,EAAE;AAAA,IACb,QAAQ;AAAA,MACN,MAAM,OAAO,EAAE,WAAW,aAAa,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO;AAAA,MACvE,IAAI,EAAE,UAAU,EAAE,OAAO;AAAA,IAC3B;AAAA,IACA,OAAO;AAAA,MACL,MAAM,OAAO,EAAE,UAAU,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM;AAAA,MACnE,IAAI,EAAE,SAAS,EAAE,MAAM;AAAA,IACzB;AAAA,IACA,KAAK;AAAA,MACH,MAAM,OAAO,EAAE,QAAQ,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI;AAAA,MAC3D,IAAI,EAAE,OAAO,EAAE,IAAI;AAAA,IACrB;AAAA,EACF;AACF;AAEA,SAAS,SAAS,KAAK;AACrB,MAAI,CAAC,OAAO,OAAO,QAAQ;AACzB,WAAO,EAAE,KAAK,EAAE,cAAc,oBAAI,IAAI,EAAE,EAAE;AAE5C,MAAI,OAAO;AACX,SAAO,KAAK,MAAM,KAAK,QAAQ,KAAK,IAAI,CAAC,EAAE,MAAM,MAAM,EAAE,CAAC;AAC1D,SAAO,mBAAmB,KAAK,MAAM,KAAK,QAAQ,GAAG,IAAI,CAAC,CAAC;AAE3D,QAAM,SAAS,IAAI,IAAI,IAAI,QAAQ,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;AAE5D,SAAO;AAAA,IACL,KAAK;AAAA,MACH,UAAU,mBAAmB,OAAO,QAAQ;AAAA,MAC5C,UAAU,mBAAmB,OAAO,QAAQ;AAAA,MAC5C,MAAM,OAAO;AAAA,MACb,UAAU,OAAO;AAAA,MACjB,MAAM,OAAO;AAAA,MACb,UAAU,OAAO;AAAA,MACjB,cAAc,OAAO;AAAA,IACvB;AAAA,IACA,WAAW,KAAK,QAAQ,GAAG,IAAI,MAAM;AAAA,EACvC;AACF;AAEA,SAAS,aAAa;AACpB,MAAI;AACF,WAAO,GAAG,SAAS,EAAE;AAAA,EACvB,SAAS,GAAG;AACV,WAAO,QAAQ,IAAI,YAAY,QAAQ,IAAI,QAAQ,QAAQ,IAAI;AAAA,EACjE;AACF;;;AUrjBA;;;ACAA;AAsHO,IAAM,eAAN,cAOG,aAIV;EAKC,YACC,OACQ,SACA,SACR,UACC;AACD,UAAM;AAJE,SAAA,UAAA;AACA,SAAA,UAAA;AAIR,SAAK,SAAS,EAAE,OAAO,SAAS;EACjC;EAZA,QAA0B,UAAU,IAAY;EAExC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAyCR,MAAM,OAAkE;AACvE,SAAK,OAAO,QAAQ;AACpB,WAAO;EACR;EA0BA,UACC,SAA6B,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO,GAC1B;AACzC,SAAK,OAAO,YAAY,oBAA8B,MAAM;AAC5D,WAAO;EACR;;EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;EACjD;EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;EACR;;EAGA,SAAS,MAAsC;AAC9C,WAAO,OAAO,gBAAgB,wBAAwB,MAAM;AAC3D,aAAO,KAAK,QAAQ,aAIlB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,WAAW,MAAM,IAAI;IAC5E,CAAC;EACF;EAEA,QAAQ,MAAqC;AAC5C,WAAO,KAAK,SAAS,IAAI;EAC1B;EAEQ;;EAER,SAAS,OAAe;AACvB,SAAK,YAAY;AACjB,WAAO;EACR;EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,aAAO,KAAK,SAAS,EAAE,QAAQ,mBAAmB,KAAK,SAAS;IACjE,CAAC;EACF;EAEA,WAAkC;AACjC,WAAO;EACR;AACD;;;AC1PA;;;ACAA;;;ACAA;;;ACCA;AAIO,SAAS,YAAY,OAAe;AAC1C,QAAM,QAAQ,MACZ,QAAQ,cAAc,EAAE,EACxB,MAAM,yCAAyC,KAAK,CAAC;AAEvD,SAAO,MAAM,IAAI,CAAC,SAAS,KAAK,YAAY,CAAC,EAAE,KAAK,GAAG;AACxD;AAEO,SAAS,YAAY,OAAe;AAC1C,QAAM,QAAQ,MACZ,QAAQ,cAAc,EAAE,EACxB,MAAM,yCAAyC,KAAK,CAAC;AAEvD,SAAO,MAAM,OAAO,CAAC,KAAK,MAAM,MAAM;AACrC,UAAM,gBAAgB,MAAM,IAAI,KAAK,YAAY,IAAI,GAAG,KAAK,CAAC,EAAG,YAAY,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC;AAC9F,WAAO,MAAM;EACd,GAAG,EAAE;AACN;AAEA,SAAS,SAAS,OAAe;AAChC,SAAO;AACR;AAEO,IAAM,cAAN,MAAkB;EACxB,QAAiB,UAAU,IAAY;;EAGvC,QAAgC,CAAC;EACzB,eAAqC,CAAC;EACtC;EAER,YAAY,QAAiB;AAC5B,SAAK,UAAU,WAAW,eACvB,cACA,WAAW,cACX,cACA;EACJ;EAEA,gBAAgB,QAAwB;AACvC,QAAI,CAAC,OAAO;AAAW,aAAO,OAAO;AAErC,UAAM,SAAS,OAAO,MAAM,MAAM,OAAO,MAAM,KAAK;AACpD,UAAM,YAAY,OAAO,MAAM,MAAM,OAAO,YAAY;AACxD,UAAM,MAAM,GAAG,MAAM,IAAI,SAAS,IAAI,OAAO,IAAI;AAEjD,QAAI,CAAC,KAAK,MAAM,GAAG,GAAG;AACrB,WAAK,WAAW,OAAO,KAAK;IAC7B;AACA,WAAO,KAAK,MAAM,GAAG;EACtB;EAEQ,WAAW,OAAc;AAChC,UAAM,SAAS,MAAM,MAAM,OAAO,MAAM,KAAK;AAC7C,UAAM,YAAY,MAAM,MAAM,OAAO,YAAY;AACjD,UAAM,WAAW,GAAG,MAAM,IAAI,SAAS;AAEvC,QAAI,CAAC,KAAK,aAAa,QAAQ,GAAG;AACjC,iBAAW,UAAU,OAAO,OAAO,MAAM,MAAM,OAAO,OAAO,CAAC,GAAG;AAChE,cAAM,YAAY,GAAG,QAAQ,IAAI,OAAO,IAAI;AAC5C,aAAK,MAAM,SAAS,IAAI,KAAK,QAAQ,OAAO,IAAI;MACjD;AACA,WAAK,aAAa,QAAQ,IAAI;IAC/B;EACD;EAEA,aAAa;AACZ,SAAK,QAAQ,CAAC;AACd,SAAK,eAAe,CAAC;EACtB;AACD;;;AC3EA;AAGO,IAAe,aAAf,cAIG,KAAwC;EACjD,QAA0B,UAAU,IAAY;AAKjD;;;AFiDO,IAAM,YAAN,MAAgB;EACtB,QAAiB,UAAU,IAAY;;EAG9B;EAET,YAAY,QAA0B;AACrC,SAAK,SAAS,IAAI,YAAY,QAAQ,MAAM;EAC7C;EAEA,MAAM,QAAQ,YAA6B,SAAoB,QAAiD;AAC/G,UAAM,kBAAkB,OAAO,WAAW,WACvC,yBACA,OAAO,mBAAmB;AAC7B,UAAM,mBAAmB,OAAO,WAAW,WAAW,YAAY,OAAO,oBAAoB;AAC7F,UAAM,uBAAuB;gCACC,IAAI,WAAW,gBAAgB,CAAC,IAAI,IAAI,WAAW,eAAe,CAAC;;;;;;AAMjG,UAAM,QAAQ,QAAQ,kCAAkC,IAAI,WAAW,gBAAgB,CAAC,EAAE;AAC1F,UAAM,QAAQ,QAAQ,oBAAoB;AAE1C,UAAM,eAAe,MAAM,QAAQ;MAClC,uCAAuC,IAAI,WAAW,gBAAgB,CAAC,IACtE,IAAI,WAAW,eAAe,CAC/B;IACD;AAEA,UAAM,kBAAkB,aAAa,CAAC;AACtC,UAAM,QAAQ,YAAY,OAAO,OAAO;AACvC,uBAAiB,aAAa,YAAY;AACzC,YACC,CAAC,mBACE,OAAO,gBAAgB,UAAU,IAAI,UAAU,cACjD;AACD,qBAAW,QAAQ,UAAU,KAAK;AACjC,kBAAM,GAAG,QAAQ,IAAI,IAAI,IAAI,CAAC;UAC/B;AACA,gBAAM,GAAG;YACR,kBAAkB,IAAI,WAAW,gBAAgB,CAAC,IACjD,IAAI,WAAW,eAAe,CAC/B,kCAAkC,UAAU,IAAI,KAAK,UAAU,YAAY;UAC5E;QACD;MACD;IACD,CAAC;EACF;EAEA,WAAW,MAAsB;AAChC,WAAO,IAAI,IAAI;EAChB;EAEA,YAAY,KAAqB;AAChC,WAAO,IAAI,MAAM,CAAC;EACnB;EAEA,aAAa,KAAqB;AACjC,WAAO,IAAI,IAAI,QAAQ,MAAM,IAAI,CAAC;EACnC;EAEQ,aAAa,SAAkD;AACtE,QAAI,CAAC,SAAS;AAAQ,aAAO;AAE7B,UAAM,gBAAgB,CAAC,UAAU;AACjC,eAAW,CAAC,GAAG,CAAC,KAAK,QAAQ,QAAQ,GAAG;AACvC,oBAAc,KAAK,MAAM,IAAI,WAAW,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE,GAAG,GAAG;AACpE,UAAI,IAAI,QAAQ,SAAS,GAAG;AAC3B,sBAAc,KAAK,OAAO;MAC3B;IACD;AACA,kBAAc,KAAK,MAAM;AACzB,WAAO,IAAI,KAAK,aAAa;EAC9B;EAEA,iBAAiB,EAAE,OAAO,OAAO,WAAW,SAAS,GAAwB;AAC5E,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,eAAe,YAClB,iBAAiB,KAAK,eAAe,WAAW,EAAE,eAAe,KAAK,CAAC,CAAC,KACxE;AAEH,UAAM,WAAW,QAAQ,aAAa,KAAK,KAAK;AAEhD,WAAO,MAAM,OAAO,eAAe,KAAK,GAAG,QAAQ,GAAG,YAAY;EACnE;EAEA,eAAe,OAAgB,KAAqB;AACnD,UAAM,eAAe,MAAM,MAAM,OAAO,OAAO;AAE/C,UAAM,cAAc,OAAO,KAAK,YAAY,EAAE;MAAO,CAAC,YACrD,IAAI,OAAO,MAAM,UAAa,aAAa,OAAO,GAAG,eAAe;IACrE;AAEA,UAAM,UAAU,YAAY;AAC5B,WAAO,IAAI,KAAK,YAAY,QAAQ,CAAC,SAAS,MAAM;AACnD,YAAM,MAAM,aAAa,OAAO;AAEhC,YAAM,QAAQ,IAAI,OAAO,KAAK,IAAI,MAAM,IAAI,WAAY,GAAG,GAAG;AAC9D,YAAM,MAAM,MAAM,IAAI,WAAW,KAAK,OAAO,gBAAgB,GAAG,CAAC,CAAC,MAAM,KAAK;AAE7E,UAAI,IAAI,UAAU,GAAG;AACpB,eAAO,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC;MAC3B;AACA,aAAO,CAAC,GAAG;IACZ,CAAC,CAAC;EACH;EAEA,iBAAiB,EAAE,OAAO,KAAK,OAAO,WAAW,UAAU,MAAM,MAAM,GAAwB;AAC9F,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,YAAY,MAAM,QAAQ,OAAO,IAAI;AAC3C,UAAM,cAAc,MAAM,QAAQ,OAAO,MAAM;AAC/C,UAAM,gBAAgB,MAAM,QAAQ,OAAO,YAAY;AACvD,UAAM,QAAQ,cAAc,gBAAgB,SAAY;AACxD,UAAM,WAAW,MAAM,cAAc,MAAM,IAAI,WAAW,WAAW,CAAC,MAAM,MAAS,GACpF,IAAI,WAAW,aAAa,CAC7B,GAAG,SAAS,OAAO,IAAI,WAAW,KAAK,CAAC,EAAE;AAE1C,UAAM,SAAS,KAAK,eAAe,OAAO,GAAG;AAE7C,UAAM,UAAU,QAAQ,IAAI,KAAK,CAAC,IAAI,IAAI,QAAQ,GAAG,KAAK,eAAe,IAAI,CAAC,CAAC;AAE/E,UAAM,WAAW,KAAK,WAAW,KAAK;AAEtC,UAAM,eAAe,YAClB,iBAAiB,KAAK,eAAe,WAAW,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,KACzE;AAEH,UAAM,WAAW,QAAQ,aAAa,KAAK,KAAK;AAEhD,WAAO,MAAM,OAAO,UAAU,QAAQ,QAAQ,MAAM,GAAG,OAAO,GAAG,QAAQ,GAAG,QAAQ,GAAG,YAAY;EACpG;;;;;;;;;;;;EAaQ,eACP,QACA,EAAE,gBAAgB,MAAM,IAAiC,CAAC,GACpD;AACN,UAAM,aAAa,OAAO;AAE1B,UAAM,SAAS,OACb,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;AAC1B,YAAM,QAAoB,CAAC;AAE3B,UAAI,GAAG,OAAO,IAAI,OAAO,KAAK,MAAM,kBAAkB;AACrD,cAAM,KAAK,IAAI,WAAW,MAAM,UAAU,CAAC;MAC5C,WAAW,GAAG,OAAO,IAAI,OAAO,KAAK,GAAG,OAAO,GAAG,GAAG;AACpD,cAAM,QAAQ,GAAG,OAAO,IAAI,OAAO,IAAI,MAAM,MAAM;AAEnD,YAAI,eAAe;AAClB,gBAAM;YACL,IAAI;cACH,MAAM,YAAY,IAAI,CAAC,MAAM;AAC5B,oBAAI,GAAG,GAAG,QAAQ,GAAG;AACpB,yBAAO,IAAI,WAAW,KAAK,OAAO,gBAAgB,CAAC,CAAC;gBACrD;AACA,uBAAO;cACR,CAAC;YACF;UACD;QACD,OAAO;AACN,gBAAM,KAAK,KAAK;QACjB;AAEA,YAAI,GAAG,OAAO,IAAI,OAAO,GAAG;AAC3B,gBAAM,KAAK,UAAU,IAAI,WAAW,MAAM,UAAU,CAAC,EAAE;QACxD;MACD,WAAW,GAAG,OAAO,MAAM,GAAG;AAC7B,YAAI,eAAe;AAClB,gBAAM,KAAK,IAAI,WAAW,KAAK,OAAO,gBAAgB,KAAK,CAAC,CAAC;QAC9D,OAAO;AACN,gBAAM,KAAK,KAAK;QACjB;MACD;AAEA,UAAI,IAAI,aAAa,GAAG;AACvB,cAAM,KAAK,OAAO;MACnB;AAEA,aAAO;IACR,CAAC;AAEF,WAAO,IAAI,KAAK,MAAM;EACvB;EAEQ,WAAW,OAA0D;AAC5E,QAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AACjC,aAAO;IACR;AAEA,UAAM,aAAoB,CAAC;AAE3B,eAAW,CAAC,OAAO,QAAQ,KAAK,MAAM,QAAQ,GAAG;AAChD,UAAI,UAAU,GAAG;AAChB,mBAAW,KAAK,MAAM;MACvB;AACA,YAAM,QAAQ,SAAS;AACvB,YAAM,aAAa,SAAS,UAAU,gBAAgB;AAEtD,UAAI,GAAG,OAAO,OAAO,GAAG;AACvB,cAAM,YAAY,MAAM,QAAQ,OAAO,IAAI;AAC3C,cAAM,cAAc,MAAM,QAAQ,OAAO,MAAM;AAC/C,cAAM,gBAAgB,MAAM,QAAQ,OAAO,YAAY;AACvD,cAAM,QAAQ,cAAc,gBAAgB,SAAY,SAAS;AACjE,mBAAW;UACV,MAAM,IAAI,IAAI,SAAS,QAAQ,CAAC,QAAQ,UAAU,IACjD,cAAc,MAAM,IAAI,WAAW,WAAW,CAAC,MAAM,MACtD,GAAG,IAAI,WAAW,aAAa,CAAC,GAAG,SAAS,OAAO,IAAI,WAAW,KAAK,CAAC,EAAE,OAAO,SAAS,EAAE;QAC7F;MACD,WAAW,GAAG,OAAO,IAAI,GAAG;AAC3B,cAAM,WAAW,MAAM,cAAc,EAAE;AACvC,cAAM,aAAa,MAAM,cAAc,EAAE;AACzC,cAAM,eAAe,MAAM,cAAc,EAAE;AAC3C,cAAM,QAAQ,aAAa,eAAe,SAAY,SAAS;AAC/D,mBAAW;UACV,MAAM,IAAI,IAAI,SAAS,QAAQ,CAAC,QAAQ,UAAU,IACjD,aAAa,MAAM,IAAI,WAAW,UAAU,CAAC,MAAM,MACpD,GAAG,IAAI,WAAW,YAAY,CAAC,GAAG,SAAS,OAAO,IAAI,WAAW,KAAK,CAAC,EAAE,OAAO,SAAS,EAAE;QAC5F;MACD,OAAO;AACN,mBAAW;UACV,MAAM,IAAI,IAAI,SAAS,QAAQ,CAAC,QAAQ,UAAU,IAAI,KAAK,OAAO,SAAS,EAAE;QAC9E;MACD;AACA,UAAI,QAAQ,MAAM,SAAS,GAAG;AAC7B,mBAAW,KAAK,MAAM;MACvB;IACD;AAEA,WAAO,IAAI,KAAK,UAAU;EAC3B;EAEQ,eACP,OACoD;AACpD,QAAI,GAAG,OAAO,KAAK,KAAK,MAAM,MAAM,OAAO,YAAY,MAAM,MAAM,MAAM,OAAO,IAAI,GAAG;AACtF,UAAI,WAAW,MAAM,IAAI,WAAW,MAAM,MAAM,OAAO,YAAY,CAAC,CAAC;AACrE,UAAI,MAAM,MAAM,OAAO,MAAM,GAAG;AAC/B,mBAAW,MAAM,IAAI,WAAW,MAAM,MAAM,OAAO,MAAM,CAAE,CAAC,IAAI,QAAQ;MACzE;AACA,aAAO,MAAM,QAAQ,IAAI,IAAI,WAAW,MAAM,MAAM,OAAO,IAAI,CAAC,CAAC;IAClE;AAEA,WAAO;EACR;EAEA,iBACC;IACC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD,GACM;AACN,UAAM,aAAa,cAAc,oBAA8B,MAAM;AACrE,eAAW,KAAK,YAAY;AAC3B,UACC,GAAG,EAAE,OAAO,MAAM,KACf,aAAa,EAAE,MAAM,KAAK,OACvB,GAAG,OAAO,QAAQ,IACpB,MAAM,EAAE,QACR,GAAG,OAAO,UAAU,IACpB,MAAM,cAAc,EAAE,OACtB,GAAG,OAAO,GAAG,IACb,SACA,aAAa,KAAK,MACnB,EAAE,CAACC,WACL,OAAO;QAAK,CAAC,EAAE,MAAM,MACpB,WAAWA,OAAM,MAAM,OAAO,OAAO,IAAI,aAAaA,MAAK,IAAIA,OAAM,MAAM,OAAO,QAAQ;MAC3F,GAAG,EAAE,MAAM,KAAK,GAChB;AACD,cAAM,YAAY,aAAa,EAAE,MAAM,KAAK;AAC5C,cAAM,IAAI;UACT,SACC,EAAE,KAAK,KAAK,IAAI,CACjB,gCAAgC,SAAS,MAAM,EAAE,MAAM,IAAI,qBAAqB,SAAS;QAC1F;MACD;IACD;AAEA,UAAM,gBAAgB,CAAC,SAAS,MAAM,WAAW;AAEjD,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,QAAI;AACJ,QAAI,UAAU;AACb,oBAAc,aAAa,OAAO,iBAAiB,oBAAoB,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC;IACtG;AAEA,UAAM,YAAY,KAAK,eAAe,YAAY,EAAE,cAAc,CAAC;AAEnE,UAAM,WAAW,KAAK,eAAe,KAAK;AAE1C,UAAM,WAAW,KAAK,WAAW,KAAK;AAEtC,UAAM,WAAW,QAAQ,aAAa,KAAK,KAAK;AAEhD,UAAM,YAAY,SAAS,cAAc,MAAM,KAAK;AAEpD,QAAI;AACJ,QAAI,WAAW,QAAQ,SAAS,GAAG;AAClC,mBAAa,gBAAgB,IAAI,KAAK,SAAS,OAAO,CAAC;IACxD;AAEA,QAAI;AACJ,QAAI,WAAW,QAAQ,SAAS,GAAG;AAClC,mBAAa,gBAAgB,IAAI,KAAK,SAAS,OAAO,CAAC;IACxD;AAEA,UAAM,WAAW,OAAO,UAAU,YAAa,OAAO,UAAU,YAAY,SAAS,IAClF,aAAa,KAAK,KAClB;AAEH,UAAM,YAAY,SAAS,cAAc,MAAM,KAAK;AAEpD,UAAM,mBAAmB,IAAI,MAAM;AACnC,QAAI,eAAe;AAClB,YAAM,YAAY,WAAW,IAAI,IAAI,cAAc,QAAQ,CAAC;AAC5D,UAAI,cAAc,OAAO,IAAI;AAC5B,kBAAU;UACT,UACC,IAAI;YACH,MAAM,QAAQ,cAAc,OAAO,EAAE,IAAI,cAAc,OAAO,KAAK,CAAC,cAAc,OAAO,EAAE;YAC3F;UACD,CACD;QACD;MACD;AACA,UAAI,cAAc,OAAO,QAAQ;AAChC,kBAAU,OAAO,aAAa;MAC/B,WAAW,cAAc,OAAO,YAAY;AAC3C,kBAAU,OAAO,iBAAiB;MACnC;AACA,uBAAiB,OAAO,SAAS;IAClC;AACA,UAAM,aACL,MAAM,OAAO,SAAS,WAAW,IAAI,SAAS,SAAS,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,UAAU,GAAG,SAAS,GAAG,UAAU,GAAG,QAAQ,GAAG,SAAS,GAAG,gBAAgB;AAEtK,QAAI,aAAa,SAAS,GAAG;AAC5B,aAAO,KAAK,mBAAmB,YAAY,YAAY;IACxD;AAEA,WAAO;EACR;EAEA,mBAAmB,YAAiB,cAAmD;AACtF,UAAM,CAAC,aAAa,GAAG,IAAI,IAAI;AAE/B,QAAI,CAAC,aAAa;AACjB,YAAM,IAAI,MAAM,kDAAkD;IACnE;AAEA,QAAI,KAAK,WAAW,GAAG;AACtB,aAAO,KAAK,uBAAuB,EAAE,YAAY,YAAY,CAAC;IAC/D;AAGA,WAAO,KAAK;MACX,KAAK,uBAAuB,EAAE,YAAY,YAAY,CAAC;MACvD;IACD;EACD;EAEA,uBAAuB;IACtB;IACA,aAAa,EAAE,MAAM,OAAO,aAAa,OAAO,SAAS,OAAO;EACjE,GAAkF;AACjF,UAAM,YAAY,OAAO,WAAW,OAAO,CAAC;AAC5C,UAAM,aAAa,OAAO,YAAY,OAAO,CAAC;AAE9C,QAAI;AACJ,QAAI,WAAW,QAAQ,SAAS,GAAG;AAClC,YAAM,gBAAyC,CAAC;AAIhD,iBAAW,iBAAiB,SAAS;AACpC,YAAI,GAAG,eAAe,QAAQ,GAAG;AAChC,wBAAc,KAAK,IAAI,WAAW,cAAc,IAAI,CAAC;QACtD,WAAW,GAAG,eAAe,GAAG,GAAG;AAClC,mBAAS,IAAI,GAAG,IAAI,cAAc,YAAY,QAAQ,KAAK;AAC1D,kBAAM,QAAQ,cAAc,YAAY,CAAC;AAEzC,gBAAI,GAAG,OAAO,QAAQ,GAAG;AACxB,4BAAc,YAAY,CAAC,IAAI,IAAI,WAAW,MAAM,IAAI;YACzD;UACD;AAEA,wBAAc,KAAK,MAAM,aAAa,EAAE;QACzC,OAAO;AACN,wBAAc,KAAK,MAAM,aAAa,EAAE;QACzC;MACD;AAEA,mBAAa,gBAAgB,IAAI,KAAK,eAAe,OAAO,CAAC;IAC9D;AAEA,UAAM,WAAW,OAAO,UAAU,YAAa,OAAO,UAAU,YAAY,SAAS,IAClF,aAAa,KAAK,KAClB;AAEH,UAAM,gBAAgB,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,SAAS,EAAE,EAAE;AAE9D,UAAM,YAAY,SAAS,cAAc,MAAM,KAAK;AAEpD,WAAO,MAAM,SAAS,GAAG,aAAa,GAAG,UAAU,GAAG,UAAU,GAAG,QAAQ,GAAG,SAAS;EACxF;EAEA,iBACC,EAAE,OAAO,QAAQ,gBAAgB,YAAY,WAAW,UAAU,QAAAC,SAAQ,uBAAuB,GAC3F;AACN,UAAM,gBAA8C,CAAC;AACrD,UAAM,UAAoC,MAAM,MAAM,OAAO,OAAO;AAEpE,UAAM,aAAmC,OAAO,QAAQ,OAAO,EAAE,OAAO,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,oBAAoB,CAAC;AAEhH,UAAM,cAAc,WAAW;MAC9B,CAAC,CAAC,EAAE,MAAM,MAAM,IAAI,WAAW,KAAK,OAAO,gBAAgB,MAAM,CAAC;IACnE;AAEA,QAAIA,SAAQ;AACX,YAAMA,WAAS;AAEf,UAAI,GAAGA,UAAQ,GAAG,GAAG;AACpB,sBAAc,KAAKA,QAAM;MAC1B,OAAO;AACN,sBAAc,KAAKA,SAAO,OAAO,CAAC;MACnC;IACD,OAAO;AACN,YAAMC,UAAS;AACf,oBAAc,KAAK,IAAI,IAAI,SAAS,CAAC;AAErC,iBAAW,CAAC,YAAY,KAAK,KAAKA,QAAO,QAAQ,GAAG;AACnD,cAAM,YAAgC,CAAC;AACvC,mBAAW,CAAC,WAAW,GAAG,KAAK,YAAY;AAC1C,gBAAM,WAAW,MAAM,SAAS;AAChC,cAAI,aAAa,UAAc,GAAG,UAAU,KAAK,KAAK,SAAS,UAAU,QAAY;AAEpF,gBAAI,IAAI,cAAc,QAAW;AAChC,oBAAM,kBAAkB,IAAI,UAAU;AACtC,oBAAM,eAAe,GAAG,iBAAiB,GAAG,IAAI,kBAAkB,IAAI,MAAM,iBAAiB,GAAG;AAChG,wBAAU,KAAK,YAAY;YAE5B,WAAW,CAAC,IAAI,WAAW,IAAI,eAAe,QAAW;AACxD,oBAAM,mBAAmB,IAAI,WAAW;AACxC,oBAAM,WAAW,GAAG,kBAAkB,GAAG,IAAI,mBAAmB,IAAI,MAAM,kBAAkB,GAAG;AAC/F,wBAAU,KAAK,QAAQ;YACxB,OAAO;AACN,wBAAU,KAAK,YAAY;YAC5B;UACD,OAAO;AACN,sBAAU,KAAK,QAAQ;UACxB;QACD;AAEA,sBAAc,KAAK,SAAS;AAC5B,YAAI,aAAaA,QAAO,SAAS,GAAG;AACnC,wBAAc,KAAK,OAAO;QAC3B;MACD;IACD;AAEA,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,YAAY,IAAI,KAAK,aAAa;AAExC,UAAM,eAAe,YAClB,iBAAiB,KAAK,eAAe,WAAW,EAAE,eAAe,KAAK,CAAC,CAAC,KACxE;AAEH,UAAM,gBAAgB,aAAa,mBAAmB,UAAU,KAAK;AAErE,UAAM,gBAAgB,2BAA2B,OAAO,gCAAgC;AAExF,WAAO,MAAM,OAAO,eAAe,KAAK,IAAI,WAAW,IAAI,aAAa,GAAG,SAAS,GAAG,aAAa,GAAG,YAAY;EACpH;EAEA,kCACC,EAAE,MAAM,cAAc,WAAW,GAC3B;AACN,UAAM,kBAAkB,eAAe,qBAAqB;AAC5D,UAAM,gBAAgB,aAAa,qBAAqB;AAExD,WAAO,+BAA+B,eAAe,IAAI,IAAI,GAAG,aAAa;EAC9E;EAEA,cAAc,SAAkE;AAC/E,QAAI,GAAG,SAAS,OAAO,KAAK,GAAG,SAAS,MAAM,GAAG;AAChD,aAAO;IACR,WAAW,GAAG,SAAS,SAAS,GAAG;AAClC,aAAO;IACR,WAAW,GAAG,SAAS,MAAM,GAAG;AAC/B,aAAO;IACR,WAAW,GAAG,SAAS,WAAW,KAAK,GAAG,SAAS,iBAAiB,GAAG;AACtE,aAAO;IACR,WAAW,GAAG,SAAS,MAAM,KAAK,GAAG,SAAS,YAAY,GAAG;AAC5D,aAAO;IACR,WAAW,GAAG,SAAS,MAAM,GAAG;AAC/B,aAAO;IACR,OAAO;AACN,aAAO;IACR;EACD;EAEA,WAAWC,MAAU,cAAwD;AAC5E,WAAOA,KAAI,QAAQ;MAClB,QAAQ,KAAK;MACb,YAAY,KAAK;MACjB,aAAa,KAAK;MAClB,cAAc,KAAK;MACnB,eAAe,KAAK;MACpB;IACD,CAAC;EACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAohBA,8BAA8B;IAC7B;IACA;IACA;IACA;IACA;IACA,aAAa;IACb;IACA;IACA;EACD,GAUkD;AACjD,QAAI,YAAwE,CAAC;AAC7E,QAAI,OAAO,QAAQ,UAAkD,CAAC,GAAG;AACzE,UAAM,QAA8B,CAAC;AAErC,QAAI,WAAW,MAAM;AACpB,YAAM,mBAAmB,OAAO,QAAQ,YAAY,OAAO;AAC3D,kBAAY,iBAAiB,IAAI,CAChC,CAAC,KAAK,KAAK,OACN;QACL,OAAO,MAAM;QACb,OAAO;QACP,OAAO,mBAAmB,OAAmB,UAAU;QACvD,oBAAoB;QACpB,QAAQ;QACR,WAAW,CAAC;MACb,EAAE;IACH,OAAO;AACN,YAAM,iBAAiB,OAAO;QAC7B,OAAO,QAAQ,YAAY,OAAO,EAAE,IAAI,CACvC,CAAC,KAAK,KAAK,MACP,CAAC,KAAK,mBAAmB,OAAO,UAAU,CAAC,CAAC;MAClD;AAEA,UAAI,OAAO,OAAO;AACjB,cAAM,WAAW,OAAO,OAAO,UAAU,aACtC,OAAO,MAAM,gBAAgB,aAAa,CAAC,IAC3C,OAAO;AACV,gBAAQ,YAAY,uBAAuB,UAAU,UAAU;MAChE;AAEA,YAAM,kBAAsE,CAAC;AAC7E,UAAI,kBAA4B,CAAC;AAGjC,UAAI,OAAO,SAAS;AACnB,YAAI,gBAAgB;AAEpB,mBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,OAAO,OAAO,GAAG;AAC5D,cAAI,UAAU,QAAW;AACxB;UACD;AAEA,cAAI,SAAS,YAAY,SAAS;AACjC,gBAAI,CAAC,iBAAiB,UAAU,MAAM;AACrC,8BAAgB;YACjB;AACA,4BAAgB,KAAK,KAAK;UAC3B;QACD;AAEA,YAAI,gBAAgB,SAAS,GAAG;AAC/B,4BAAkB,gBACf,gBAAgB,OAAO,CAAC,MAAM,OAAO,UAAU,CAAC,MAAM,IAAI,IAC1D,OAAO,KAAK,YAAY,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,gBAAgB,SAAS,GAAG,CAAC;QACnF;MACD,OAAO;AAEN,0BAAkB,OAAO,KAAK,YAAY,OAAO;MAClD;AAEA,iBAAW,SAAS,iBAAiB;AACpC,cAAM,SAAS,YAAY,QAAQ,KAAK;AACxC,wBAAgB,KAAK,EAAE,OAAO,OAAO,OAAO,OAAO,CAAC;MACrD;AAEA,UAAI,oBAIE,CAAC;AAGP,UAAI,OAAO,MAAM;AAChB,4BAAoB,OAAO,QAAQ,OAAO,IAAI,EAC5C,OAAO,CAAC,UAAoE,CAAC,CAAC,MAAM,CAAC,CAAC,EACtF,IAAI,CAAC,CAAC,OAAO,WAAW,OAAO,EAAE,OAAO,aAAa,UAAU,YAAY,UAAU,KAAK,EAAG,EAAE;MAClG;AAEA,UAAI;AAGJ,UAAI,OAAO,QAAQ;AAClB,iBAAS,OAAO,OAAO,WAAW,aAC/B,OAAO,OAAO,gBAAgB,EAAE,IAAI,CAAC,IACrC,OAAO;AACV,mBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACpD,0BAAgB,KAAK;YACpB;YACA,OAAO,8BAA8B,OAAO,UAAU;UACvD,CAAC;QACF;MACD;AAIA,iBAAW,EAAE,OAAO,MAAM,KAAK,iBAAiB;AAC/C,kBAAU,KAAK;UACd,OAAO,GAAG,OAAO,IAAI,OAAO,IAAI,MAAM,aAAa,YAAY,QAAQ,KAAK,EAAG;UAC/E;UACA,OAAO,GAAG,OAAO,MAAM,IAAI,mBAAmB,OAAO,UAAU,IAAI;UACnE,oBAAoB;UACpB,QAAQ;UACR,WAAW,CAAC;QACb,CAAC;MACF;AAEA,UAAI,cAAc,OAAO,OAAO,YAAY,aACzC,OAAO,QAAQ,gBAAgB,oBAAoB,CAAC,IACpD,OAAO,WAAW,CAAC;AACtB,UAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAChC,sBAAc,CAAC,WAAW;MAC3B;AACA,gBAAU,YAAY,IAAI,CAAC,iBAAiB;AAC3C,YAAI,GAAG,cAAc,MAAM,GAAG;AAC7B,iBAAO,mBAAmB,cAAc,UAAU;QACnD;AACA,eAAO,uBAAuB,cAAc,UAAU;MACvD,CAAC;AAED,cAAQ,OAAO;AACf,eAAS,OAAO;AAGhB,iBACO;QACL,OAAO;QACP,aAAa;QACb;MACD,KAAK,mBACJ;AACD,cAAM,qBAAqB,kBAAkB,QAAQ,eAAe,QAAQ;AAC5E,cAAM,oBAAoB,mBAAmB,SAAS,eAAe;AACrE,cAAM,sBAAsB,cAAc,iBAAiB;AAC3D,cAAM,qBAAqB,GAAG,UAAU,IAAI,qBAAqB;AACjE,cAAMC,UAAS;UACd,GAAG,mBAAmB,OAAO;YAAI,CAACC,QAAO,MACxC;cACC,mBAAmB,mBAAmB,WAAW,CAAC,GAAI,kBAAkB;cACxE,mBAAmBA,QAAO,UAAU;YACrC;UACD;QACD;AACA,cAAM,gBAAgB,KAAK,8BAA8B;UACxD;UACA;UACA;UACA,OAAO,WAAW,mBAAmB;UACrC,aAAa,OAAO,mBAAmB;UACvC,aAAa,GAAG,UAAU,GAAG,IACzB,gCAAgC,OAChC,EAAE,OAAO,EAAE,IACX,EAAE,GAAG,6BAA6B,OAAO,EAAE,IAC5C;UACH,YAAY;UACZ,QAAAD;UACA,qBAAqB;QACtB,CAAC;AACD,cAAM,QAAQ,MAAM,IAAI,WAAW,kBAAkB,CAAC,IAAI,IAAI,WAAW,MAAM,CAAC,GAAG,GAAG,qBAAqB;AAC3G,cAAM,KAAK;UACV,IAAI;UACJ,OAAO,IAAI,SAAS,cAAc,KAAY,CAAC,GAAG,kBAAkB;UACpE,OAAO;UACP,UAAU;UACV,SAAS;QACV,CAAC;AACD,kBAAU,KAAK;UACd,OAAO;UACP,OAAO;UACP;UACA,oBAAoB;UACpB,QAAQ;UACR,WAAW,cAAc;QAC1B,CAAC;MACF;IACD;AAEA,QAAI,UAAU,WAAW,GAAG;AAC3B,YAAM,IAAI,aAAa,EAAE,SAAS,iCAAiC,YAAY,MAAM,OAAO,UAAU,KAAK,CAAC;IAC7G;AAEA,QAAI;AAEJ,YAAQ,IAAI,QAAQ,KAAK;AAEzB,QAAI,qBAAqB;AACxB,UAAI,QAAQ,uBACX,IAAI;QACH,UAAU;UAAI,CAAC,EAAE,OAAAC,QAAO,OAAO,OAAO,MACrC,SACG,MAAM,IAAI,WAAW,GAAG,UAAU,IAAI,KAAK,EAAE,CAAC,IAAI,IAAI,WAAW,MAAM,CAAC,KACxE,GAAGA,QAAO,IAAI,OAAO,IACrBA,OAAM,MACNA;QACJ;QACA;MACD,CACD;AACA,UAAI,GAAG,qBAAqB,IAAI,GAAG;AAClC,gBAAQ,wBAAwB,KAAK,GACpC,QAAQ,SAAS,IAAI,gBAAgB,IAAI,KAAK,SAAS,OAAO,CAAC,KAAK,MACrE;MAED;AACA,YAAM,kBAAkB,CAAC;QACxB,OAAO;QACP,OAAO;QACP,OAAO,MAAM,GAAG,MAAM;QACtB,QAAQ;QACR,oBAAoB,YAAY;QAChC;MACD,CAAC;AAED,YAAM,gBAAgB,UAAU,UAAa,WAAW,UAAa,QAAQ,SAAS;AAEtF,UAAI,eAAe;AAClB,iBAAS,KAAK,iBAAiB;UAC9B,OAAO,aAAa,OAAO,UAAU;UACrC,QAAQ,CAAC;UACT,YAAY,CAAC;YACZ,MAAM,CAAC;YACP,OAAO,IAAI,IAAI,GAAG;UACnB,CAAC;UACD;UACA;UACA;UACA;UACA,cAAc,CAAC;QAChB,CAAC;AAED,gBAAQ;AACR,gBAAQ;AACR,iBAAS;AACT,kBAAU,CAAC;MACZ,OAAO;AACN,iBAAS,aAAa,OAAO,UAAU;MACxC;AAEA,eAAS,KAAK,iBAAiB;QAC9B,OAAO,GAAG,QAAQ,OAAO,IAAI,SAAS,IAAI,SAAS,QAAQ,CAAC,GAAG,UAAU;QACzE,QAAQ,CAAC;QACT,YAAY,gBAAgB,IAAI,CAAC,EAAE,OAAAA,OAAM,OAAO;UAC/C,MAAM,CAAC;UACP,OAAO,GAAGA,QAAO,MAAM,IAAI,mBAAmBA,QAAO,UAAU,IAAIA;QACpE,EAAE;QACF;QACA;QACA;QACA;QACA;QACA,cAAc,CAAC;MAChB,CAAC;IACF,OAAO;AACN,eAAS,KAAK,iBAAiB;QAC9B,OAAO,aAAa,OAAO,UAAU;QACrC,QAAQ,CAAC;QACT,YAAY,UAAU,IAAI,CAAC,EAAE,MAAM,OAAO;UACzC,MAAM,CAAC;UACP,OAAO,GAAG,OAAO,MAAM,IAAI,mBAAmB,OAAO,UAAU,IAAI;QACpE,EAAE;QACF;QACA;QACA;QACA;QACA;QACA,cAAc,CAAC;MAChB,CAAC;IACF;AAEA,WAAO;MACN,YAAY,YAAY;MACxB,KAAK;MACL;IACD;EACD;AACD;;;AGj5CA;AAOO,IAAM,wBAAN,MAAM,uBAEb;EACC,QAAiB,UAAU,IAAY;EAE/B;EA8BR,YAAY,QAA4C;AACvD,SAAK,SAAS,EAAE,GAAG,OAAO;EAC3B;EAEA,IAAI,UAAa,MAA4B;AAC5C,QAAI,SAAS,KAAK;AACjB,aAAO;QACN,GAAG,SAAS,GAA4B;QACxC,gBAAgB,IAAI;UAClB,SAAsB,EAAE;UACzB;QACD;MACD;IACD;AAEA,QAAI,SAAS,gBAAgB;AAC5B,aAAO;QACN,GAAG,SAAS,cAAuC;QACnD,gBAAgB,IAAI;UAClB,SAAkB,cAAc,EAAE;UACnC;QACD;MACD;IACD;AAEA,QAAI,OAAO,SAAS,UAAU;AAC7B,aAAO,SAAS,IAA6B;IAC9C;AAEA,UAAM,UAAU,GAAG,UAAU,QAAQ,IAClC,SAAS,EAAE,iBACX,GAAG,UAAU,IAAI,IACjB,SAAS,cAAc,EAAE,iBACzB;AACH,UAAM,QAAiB,QAAQ,IAA4B;AAE3D,QAAI,GAAG,OAAO,IAAI,OAAO,GAAG;AAE3B,UAAI,KAAK,OAAO,uBAAuB,SAAS,CAAC,MAAM,kBAAkB;AACxE,eAAO,MAAM;MACd;AAEA,YAAM,WAAW,MAAM,MAAM;AAC7B,eAAS,mBAAmB;AAC5B,aAAO;IACR;AAEA,QAAI,GAAG,OAAO,GAAG,GAAG;AACnB,UAAI,KAAK,OAAO,gBAAgB,OAAO;AACtC,eAAO;MACR;AAEA,YAAM,IAAI;QACT,2BAA2B,IAAI;MAChC;IACD;AAEA,QAAI,GAAG,OAAO,MAAM,GAAG;AACtB,UAAI,KAAK,OAAO,OAAO;AACtB,eAAO,IAAI;UACV;UACA,IAAI;YACH,IAAI;cACH,MAAM;cACN,IAAI,uBAAuB,KAAK,OAAO,OAAO,KAAK,OAAO,uBAAuB,KAAK;YACvF;UACD;QACD;MACD;AACA,aAAO;IACR;AAEA,QAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAChD,aAAO;IACR;AAEA,WAAO,IAAI,MAAM,OAAO,IAAI,uBAAsB,KAAK,MAAM,CAAC;EAC/D;AACD;;;ACxHA;;;ACAA;AAGO,IAAe,oBAAf,MAAsF;EAC5F,QAAiB,UAAU,IAAY;;EAQvC,oBAAgC;AAC/B,WAAO,KAAK,EAAE;EACf;AAGD;;;ADgCO,IAAM,kBAAN,MAGL;EACD,QAAiB,UAAU,IAAY;EAE/B;EACA;EACA;EACA,WAAuB,CAAC;EACxB;EAIR,YACC,QASC;AACD,SAAK,SAAS,OAAO;AACrB,SAAK,UAAU,OAAO;AACtB,SAAK,UAAU,OAAO;AACtB,QAAI,OAAO,UAAU;AACpB,WAAK,WAAW,OAAO;IACxB;AACA,SAAK,WAAW,OAAO;EACxB;EAEQ;;EAER,SAAS,OAAe;AACvB,SAAK,YAAY;AACjB,WAAO;EACR;;;;;;;EAQA,KACC,QAMC;AACD,UAAM,kBAAkB,CAAC,CAAC,KAAK;AAE/B,QAAI;AACJ,QAAI,KAAK,QAAQ;AAChB,eAAS,KAAK;IACf,WAAW,GAAG,QAAQ,QAAQ,GAAG;AAEhC,eAAS,OAAO;QACf,OAAO,KAAK,OAAO,EAAE,cAAc,EAAE,IAAI,CACxC,QACI,CAAC,KAAK,OAAO,GAAqC,CAAsC,CAAC;MAC/F;IACD,WAAW,GAAG,QAAQ,UAAU,GAAG;AAClC,eAAS,OAAO,cAAc,EAAE;IACjC,WAAW,GAAG,QAAQ,GAAG,GAAG;AAC3B,eAAS,CAAC;IACX,OAAO;AACN,eAAS,gBAAyB,MAAM;IACzC;AAEA,WAAQ,KAAK,cAAc,SACxB,IAAI,aAAa;MAClB,OAAO;MACP;MACA;MACA,SAAS,KAAK;MACd,SAAS,KAAK;MACd,UAAU,KAAK;MACf,UAAU,KAAK;IAChB,CAAC,IACC,IAAI,aAAa;MAClB,OAAO;MACP;MACA;MACA,SAAS,KAAK;MACd,SAAS,KAAK;MACd,UAAU,KAAK;MACf,UAAU,KAAK;IAChB,CAAC,EAAE,SAAS,KAAK,SAAS;EAC5B;AACD;AAEO,IAAe,2BAAf,cAWG,kBAA4C;EACrD,QAA0B,UAAU,IAAY;EAE9B;EAaR;EACA;EACF;EACA;EACE;EACA;EAEV,YACC,EAAE,OAAO,QAAQ,iBAAiB,SAAS,SAAS,UAAU,SAAS,GAWtE;AACD,UAAM;AACN,SAAK,SAAS;MACb;MACA;MACA,QAAQ,EAAE,GAAG,OAAO;MACpB;MACA,cAAc,CAAC;IAChB;AACA,SAAK,kBAAkB;AACvB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,IAAI;MACR,gBAAgB;IACjB;AACA,SAAK,YAAY,iBAAiB,KAAK;AACvC,SAAK,sBAAsB,OAAO,KAAK,cAAc,WAAW,EAAE,CAAC,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC;EAC/F;EAEQ,WACP,UAC4C;AAC5C,WAAO,CACN,OACA,OACI;AACJ,YAAM,gBAAgB,KAAK;AAC3B,YAAM,YAAY,iBAAiB,KAAK;AAExC,UAAI,OAAO,cAAc,YAAY,KAAK,OAAO,OAAO,KAAK,CAAC,SAAS,KAAK,UAAU,SAAS,GAAG;AACjG,cAAM,IAAI,MAAM,UAAU,SAAS,iCAAiC;MACrE;AAEA,UAAI,CAAC,KAAK,iBAAiB;AAE1B,YAAI,OAAO,KAAK,KAAK,mBAAmB,EAAE,WAAW,KAAK,OAAO,kBAAkB,UAAU;AAC5F,eAAK,OAAO,SAAS;YACpB,CAAC,aAAa,GAAG,KAAK,OAAO;UAC9B;QACD;AACA,YAAI,OAAO,cAAc,YAAY,CAAC,GAAG,OAAO,GAAG,GAAG;AACrD,gBAAM,YAAY,GAAG,OAAO,QAAQ,IACjC,MAAM,EAAE,iBACR,GAAG,OAAO,IAAI,IACd,MAAM,cAAc,EAAE,iBACtB,MAAM,MAAM,OAAO,OAAO;AAC7B,eAAK,OAAO,OAAO,SAAS,IAAI;QACjC;MACD;AAEA,UAAI,OAAO,OAAO,YAAY;AAC7B,aAAK;UACJ,IAAI;YACH,KAAK,OAAO;YACZ,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;UAC5E;QACD;MACD;AAEA,UAAI,CAAC,KAAK,OAAO,OAAO;AACvB,aAAK,OAAO,QAAQ,CAAC;MACtB;AAEA,WAAK,OAAO,MAAM,KAAK,EAAE,IAAI,OAAO,UAAU,OAAO,UAAU,CAAC;AAEhE,UAAI,OAAO,cAAc,UAAU;AAClC,gBAAQ,UAAU;UACjB,KAAK,QAAQ;AACZ,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;UACA,KAAK,SAAS;AACb,iBAAK,sBAAsB,OAAO;cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;UACA,KAAK,SAAS;AACb,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;UACA,KAAK,QAAQ;AACZ,iBAAK,sBAAsB,OAAO;cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;QACD;MACD;AAEA,aAAO;IACR;EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BA,WAAW,KAAK,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BjC,YAAY,KAAK,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BnC,YAAY,KAAK,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BnC,WAAW,KAAK,WAAW,MAAM;EAEzB,kBACP,MACA,OAUC;AACD,WAAO,CAAC,mBAAmB;AAC1B,YAAM,cAAe,OAAO,mBAAmB,aAC5C,eAAe,kBAAkB,CAAC,IAClC;AAKH,UAAI,CAAC,aAAa,KAAK,kBAAkB,GAAG,YAAY,kBAAkB,CAAC,GAAG;AAC7E,cAAM,IAAI;UACT;QACD;MACD;AAEA,WAAK,OAAO,aAAa,KAAK,EAAE,MAAM,OAAO,YAAY,CAAC;AAC1D,aAAO;IACR;EACD;;;;;;;;;;;;;;;;;;;;;;;;;;EA2BA,QAAQ,KAAK,kBAAkB,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;EA2B7C,WAAW,KAAK,kBAAkB,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;EA2B/C,YAAY,KAAK,kBAAkB,aAAa,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA0CrD,eAAe,KAAK,kBAAkB,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;EA2BvD,SAAS,KAAK,kBAAkB,UAAU,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA0C/C,YAAY,KAAK,kBAAkB,UAAU,IAAI;;EAGjD,gBAAgB,cAKd;AACD,SAAK,OAAO,aAAa,KAAK,GAAG,YAAY;AAC7C,WAAO;EACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA,MACC,OAC2C;AAC3C,QAAI,OAAO,UAAU,YAAY;AAChC,cAAQ;QACP,IAAI;UACH,KAAK,OAAO;UACZ,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;QAC5E;MACD;IACD;AACA,SAAK,OAAO,QAAQ;AACpB,WAAO;EACR;;;;;;;;;;;;;;;;;;;;;;;EAwBA,OACC,QAC4C;AAC5C,QAAI,OAAO,WAAW,YAAY;AACjC,eAAS;QACR,IAAI;UACH,KAAK,OAAO;UACZ,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;QAC5E;MACD;IACD;AACA,SAAK,OAAO,SAAS;AACrB,WAAO;EACR;EAyBA,WACI,SAG0C;AAC7C,QAAI,OAAO,QAAQ,CAAC,MAAM,YAAY;AACrC,YAAM,UAAU,QAAQ,CAAC;QACxB,IAAI;UACH,KAAK,OAAO;UACZ,IAAI,sBAAsB,EAAE,oBAAoB,SAAS,aAAa,MAAM,CAAC;QAC9E;MACD;AACA,WAAK,OAAO,UAAU,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;IAClE,OAAO;AACN,WAAK,OAAO,UAAU;IACvB;AACA,WAAO;EACR;EA8BA,WACI,SAG0C;AAC7C,QAAI,OAAO,QAAQ,CAAC,MAAM,YAAY;AACrC,YAAM,UAAU,QAAQ,CAAC;QACxB,IAAI;UACH,KAAK,OAAO;UACZ,IAAI,sBAAsB,EAAE,oBAAoB,SAAS,aAAa,MAAM,CAAC;QAC9E;MACD;AAEA,YAAM,eAAe,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAEhE,UAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,aAAK,OAAO,aAAa,GAAG,EAAE,EAAG,UAAU;MAC5C,OAAO;AACN,aAAK,OAAO,UAAU;MACvB;IACD,OAAO;AACN,YAAM,eAAe;AAErB,UAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,aAAK,OAAO,aAAa,GAAG,EAAE,EAAG,UAAU;MAC5C,OAAO;AACN,aAAK,OAAO,UAAU;MACvB;IACD;AACA,WAAO;EACR;;;;;;;;;;;;;;;;;EAkBA,MAAM,OAAuE;AAC5E,QAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,WAAK,OAAO,aAAa,GAAG,EAAE,EAAG,QAAQ;IAC1C,OAAO;AACN,WAAK,OAAO,QAAQ;IACrB;AACA,WAAO;EACR;;;;;;;;;;;;;;;;;EAkBA,OAAO,QAAyE;AAC/E,QAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,WAAK,OAAO,aAAa,GAAG,EAAE,EAAG,SAAS;IAC3C,OAAO;AACN,WAAK,OAAO,SAAS;IACtB;AACA,WAAO;EACR;;;;;;;;;;;EAYA,IAAI,UAAwB,SAAqB,CAAC,GAA2C;AAC5F,SAAK,OAAO,gBAAgB,EAAE,UAAU,OAAO;AAC/C,WAAO;EACR;;EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;EACjD;EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;EACR;EAEA,GACC,OAC6D;AAC7D,WAAO,IAAI;MACV,IAAI,SAAS,KAAK,OAAO,GAAG,KAAK,OAAO,QAAQ,KAAK;MACrD,IAAI,sBAAsB,EAAE,OAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;IACvF;EACD;;EAGS,oBAAiD;AACzD,WAAO,IAAI;MACV,KAAK,OAAO;MACZ,IAAI,sBAAsB,EAAE,OAAO,KAAK,WAAW,oBAAoB,SAAS,aAAa,QAAQ,CAAC;IACvG;EACD;EAEA,WAAkC;AACjC,WAAO;EACR;AACD;AA4BO,IAAM,eAAN,cAUG,yBAU4C;EACrD,QAA0B,UAAU,IAAY;;EAGhD,SAAS,MAAsC;AAC9C,UAAM,EAAE,SAAS,QAAQ,SAAS,qBAAqB,UAAU,IAAI;AACrE,QAAI,CAAC,SAAS;AACb,YAAM,IAAI,MAAM,oFAAoF;IACrG;AACA,WAAO,OAAO,gBAAgB,wBAAwB,MAAM;AAC3D,YAAM,aAAa,oBAA8B,OAAO,MAAM;AAC9D,YAAM,QAAQ,QAAQ,aAEpB,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,YAAY,MAAM,IAAI;AAC3D,YAAM,sBAAsB;AAE5B,aAAO,cAAc,SAAY,QAAQ,MAAM,SAAS,SAAS;IAClE,CAAC;EACF;;;;;;;;EASA,QAAQ,MAAqC;AAC5C,WAAO,KAAK,SAAS,IAAI;EAC1B;EAEQ;;EAER,SAAS,OAAe;AACvB,SAAK,YAAY;AACjB,WAAO;EACR;EAEA,UAAkD,CAAC,sBAAsB;AACxE,WAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,aAAO,KAAK,SAAS,EAAE,QAAQ,mBAAmB,KAAK,SAAS;IACjE,CAAC;EACF;AACD;AAEA,YAAY,cAAc,CAAC,YAAY,CAAC;AAExC,SAAS,kBAAkB,MAAmB,OAAuC;AACpF,SAAO,CAAC,YAAY,gBAAgB,gBAAgB;AACnD,UAAM,eAAe,CAAC,aAAa,GAAG,WAAW,EAAE,IAAI,CAACC,aAAY;MACnE;MACA;MACA,aAAaA;IACd,EAAE;AAEF,eAAW,eAAe,cAAc;AACvC,UAAI,CAAC,aAAc,WAAmB,kBAAkB,GAAG,YAAY,YAAY,kBAAkB,CAAC,GAAG;AACxG,cAAM,IAAI;UACT;QACD;MACD;IACD;AAEA,WAAQ,WAA2B,gBAAgB,YAAY;EAChE;AACD;AAEA,IAAM,oBAAoB,OAAO;EAChC;EACA;EACA;EACA;EACA;EACA;AACD;AA2BO,IAAM,QAAQ,kBAAkB,SAAS,KAAK;AA2B9C,IAAM,WAAW,kBAAkB,SAAS,IAAI;AA2BhD,IAAM,YAAY,kBAAkB,aAAa,KAAK;AA0CtD,IAAM,eAAe,kBAAkB,aAAa,IAAI;AA2BxD,IAAM,SAAS,kBAAkB,UAAU,KAAK;AA0ChD,IAAM,YAAY,kBAAkB,UAAU,IAAI;;;ALnsClD,IAAM,eAAN,MAAmB;EACzB,QAAiB,UAAU,IAAY;EAE/B;EACA;EAER,YAAY,SAAuC;AAClD,SAAK,UAAU,GAAG,SAAS,SAAS,IAAI,UAAU;AAClD,SAAK,gBAAgB,GAAG,SAAS,SAAS,IAAI,SAAY;EAC3D;EAEA,MAA6B,OAAe;AAC3C,UAAM,eAAe;AAErB,WAAO;MACN,GACC,IACgD;AAChD,YAAI,OAAO,OAAO,YAAY;AAC7B,eAAK,GAAG,YAAY;QACrB;AAEA,eAAO,IAAI;UACV,IAAI,aAAa,GAAG,OAAO,GAAG,GAAG,kBAAkB,GAAqB,OAAO,IAAI;UACnF,IAAI,sBAAsB,EAAE,OAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;QACvF;MACD;IACD;EACD;EAEA,QAAQ,SAAyB;AAChC,UAAM,OAAO;AAIb,aAASC,QACR,QACgD;AAChD,aAAO,IAAI,gBAAgB;QAC1B,QAAQ,UAAU;QAClB,SAAS;QACT,SAAS,KAAK,WAAW;QACzB,UAAU;MACX,CAAC;IACF;AAIA,aAAS,eAAe,QAA4E;AACnG,aAAO,IAAI,gBAAgB;QAC1B,QAAQ,UAAU;QAClB,SAAS;QACT,SAAS,KAAK,WAAW;QACzB,UAAU;MACX,CAAC;IACF;AAOA,aAAS,iBACR,IACA,QACoD;AACpD,aAAO,IAAI,gBAAgB;QAC1B,QAAQ,UAAU;QAClB,SAAS;QACT,SAAS,KAAK,WAAW;QACzB,UAAU,EAAE,GAAG;MAChB,CAAC;IACF;AAEA,WAAO,EAAE,QAAAA,SAAQ,gBAAgB,iBAAiB;EACnD;EAIA,OAA0C,QAAoE;AAC7G,WAAO,IAAI,gBAAgB;MAC1B,QAAQ,UAAU;MAClB,SAAS;MACT,SAAS,KAAK,WAAW;IAC1B,CAAC;EACF;EAIA,eAAe,QAAsE;AACpF,WAAO,IAAI,gBAAgB;MAC1B,QAAQ,UAAU;MAClB,SAAS;MACT,SAAS,KAAK,WAAW;MACzB,UAAU;IACX,CAAC;EACF;EAOA,iBACC,IACA,QAC8C;AAC9C,WAAO,IAAI,gBAAgB;MAC1B,QAAQ,UAAU;MAClB,SAAS;MACT,SAAS,KAAK,WAAW;MACzB,UAAU,EAAE,GAAG;IAChB,CAAC;EACF;;EAGQ,aAAa;AACpB,QAAI,CAAC,KAAK,SAAS;AAClB,WAAK,UAAU,IAAI,UAAU,KAAK,aAAa;IAChD;AAEA,WAAO,KAAK;EACb;AACD;;;ADrFO,IAAM,kBAAN,MAIL;EAGD,YACS,OACA,SACA,SACA,UACA,wBACP;AALO,SAAA,QAAA;AACA,SAAA,UAAA;AACA,SAAA,UAAA;AACA,SAAA,WAAA;AACA,SAAA,yBAAA;EACN;EARH,QAAiB,UAAU,IAAY;EAU/B;;EAER,SAAS,OAAe;AACvB,SAAK,YAAY;AACjB,WAAO;EACR;EAEA,wBAAoG;AACnG,SAAK,yBAAyB;AAC9B,WAAO;EACR;EAIA,OACCC,SACqC;AACrC,IAAAA,UAAS,MAAM,QAAQA,OAAM,IAAIA,UAAS,CAACA,OAAM;AACjD,QAAIA,QAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,iDAAiD;IAClE;AACA,UAAM,eAAeA,QAAO,IAAI,CAAC,UAAU;AAC1C,YAAM,SAAsC,CAAC;AAC7C,YAAM,OAAO,KAAK,MAAM,MAAM,OAAO,OAAO;AAC5C,iBAAW,UAAU,OAAO,KAAK,KAAK,GAAG;AACxC,cAAM,WAAW,MAAM,MAA4B;AACnD,eAAO,MAAM,IAAI,GAAG,UAAU,GAAG,IAAI,WAAW,IAAI,MAAM,UAAU,KAAK,MAAM,CAAC;MACjF;AACA,aAAO;IACR,CAAC;AAED,WAAO,KAAK,cAAc,SACvB,IAAI;MACL,KAAK;MACL;MACA,KAAK;MACL,KAAK;MACL,KAAK;MACL;MACA,KAAK;IACN,IACE,IAAI;MACL,KAAK;MACL;MACA,KAAK;MACL,KAAK;MACL,KAAK;MACL;MACA,KAAK;IACN,EAAE,SAAS,KAAK,SAAS;EAC3B;EAMA,OACC,aAIqC;AACrC,UAAMC,UAAS,OAAO,gBAAgB,aAAa,YAAY,IAAI,aAAa,CAAC,IAAI;AAErF,QACC,CAAC,GAAGA,SAAQ,GAAG,KACZ,CAAC,aAAa,KAAK,MAAM,OAAO,GAAGA,QAAO,EAAE,cAAc,GAC5D;AACD,YAAM,IAAI;QACT;MACD;IACD;AAEA,WAAO,IAAI,aAAa,KAAK,OAAOA,SAAQ,KAAK,SAAS,KAAK,SAAS,KAAK,UAAU,IAAI;EAC5F;AACD;AAwFO,IAAM,eAAN,cAQG,aAIV;EAKC,YACC,OACAD,SACQ,SACA,SACR,UACAC,SACA,wBACC;AACD,UAAM;AANE,SAAA,UAAA;AACA,SAAA,UAAA;AAMR,SAAK,SAAS,EAAE,OAAO,QAAAD,SAAuB,UAAU,QAAAC,SAAQ,uBAAuB;EACxF;EAfA,QAA0B,UAAU,IAAY;EAExC;EAuCR,UACC,SAA6B,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO,GACb;AACtD,SAAK,OAAO,YAAY,oBAA8B,MAAM;AAC5D,WAAO;EACR;;;;;;;;;;;;;;;;;;;;;;;EAwBA,oBACC,SAAgE,CAAC,GACe;AAChF,QAAI,OAAO,WAAW,QAAW;AAChC,WAAK,OAAO,aAAa;IAC1B,OAAO;AACN,UAAI,eAAe;AACnB,qBAAe,MAAM,QAAQ,OAAO,MAAM,IACvC,OAAO,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,WAAW,KAAK,QAAQ,OAAO,gBAAgB,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,IACpG,KAAK,QAAQ,WAAW,KAAK,QAAQ,OAAO,gBAAgB,OAAO,MAAM,CAAC;AAE7E,YAAM,WAAW,OAAO,QAAQ,aAAa,OAAO,KAAK,KAAK;AAC9D,WAAK,OAAO,aAAa,OAAO,IAAI,IAAI,YAAY,CAAC,IAAI,QAAQ;IAClE;AACA,WAAO;EACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA,mBACC,QACgF;AAChF,QAAI,OAAO,UAAU,OAAO,eAAe,OAAO,WAAW;AAC5D,YAAM,IAAI;QACT;MACD;IACD;AACA,UAAM,WAAW,OAAO,QAAQ,aAAa,OAAO,KAAK,KAAK;AAC9D,UAAM,iBAAiB,OAAO,cAAc,aAAa,OAAO,WAAW,KAAK;AAChF,UAAM,cAAc,OAAO,WAAW,aAAa,OAAO,QAAQ,KAAK;AACvE,UAAM,SAAS,KAAK,QAAQ,eAAe,KAAK,OAAO,OAAO,aAAa,KAAK,OAAO,OAAO,OAAO,GAAG,CAAC;AACzG,QAAI,eAAe;AACnB,mBAAe,MAAM,QAAQ,OAAO,MAAM,IACvC,OAAO,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,WAAW,KAAK,QAAQ,OAAO,gBAAgB,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,IACpG,KAAK,QAAQ,WAAW,KAAK,QAAQ,OAAO,gBAAgB,OAAO,MAAM,CAAC;AAC7E,SAAK,OAAO,aAAa,OACxB,IAAI,IAAI,YAAY,CACrB,IAAI,cAAc,kBAAkB,MAAM,GAAG,QAAQ,GAAG,WAAW;AACnE,WAAO;EACR;;EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;EACjD;EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;EACR;;EAGA,SAAS,MAAsC;AAC9C,WAAO,OAAO,gBAAgB,wBAAwB,MAAM;AAC3D,aAAO,KAAK,QAAQ,aAIlB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,WAAW,MAAM,IAAI;IAC5E,CAAC;EACF;EAEA,QAAQ,MAAqC;AAC5C,WAAO,KAAK,SAAS,IAAI;EAC1B;EAEQ;;EAER,SAAS,OAAe;AACvB,SAAK,YAAY;AACjB,WAAO;EACR;EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,aAAO,KAAK,SAAS,EAAE,QAAQ,mBAAmB,KAAK,SAAS;IACjE,CAAC;EACF;EAEA,WAAkC;AACjC,WAAO;EACR;AACD;;;AQpaA;AA4BO,IAAM,4BAAN,cACE,aAET;EASC,YACC,MACQ,SACA,SACP;AACD,UAAM;AAHE,SAAA,UAAA;AACA,SAAA,UAAA;AAGR,SAAK,SAAS,EAAE,KAAK;EACtB;EAfA,QAA0B,UAAU,IAAY;EAExC;EAeR,eAAqB;AACpB,QAAI,KAAK,OAAO,eAAe,QAAW;AACzC,YAAM,IAAI,MAAM,iDAAiD;IAClE;AACA,SAAK,OAAO,eAAe;AAC3B,WAAO;EACR;EAEA,aAAmB;AAClB,QAAI,KAAK,OAAO,iBAAiB,QAAW;AAC3C,YAAM,IAAI,MAAM,iDAAiD;IAClE;AACA,SAAK,OAAO,aAAa;AACzB,WAAO;EACR;;EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,kCAAkC,KAAK,MAAM;EAClE;EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;EACR;;EAGA,SAAS,MAIP;AACD,WAAO,OAAO,gBAAgB,wBAAwB,MAAM;AAC3D,aAAO,KAAK,QAAQ,aAAa,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,QAAW,MAAM,IAAI;IAC/F,CAAC;EACF;EAEA,QAAQ,MAIN;AACD,WAAO,KAAK,SAAS,IAAI;EAC1B;EAEQ;;EAER,SAAS,OAAe;AACvB,SAAK,YAAY;AACjB,WAAO;EACR;EAEA,UAAkD,CAAC,sBAAsB;AACxE,WAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,aAAO,KAAK,SAAS,EAAE,QAAQ,mBAAmB,KAAK,SAAS;IACjE,CAAC;EACF;AACD;;;ACzGA;AAmDO,IAAM,kBAAN,MAAqF;EAO3F,YACS,OACA,SACA,SACA,UACP;AAJO,SAAA,QAAA;AACA,SAAA,UAAA;AACA,SAAA,UAAA;AACA,SAAA,WAAA;EACN;EAXH,QAAiB,UAAU,IAAY;EAa/B;EACR,SAAS,OAAe;AACvB,SAAK,YAAY;AACjB,WAAO;EACR;EAEA,IACCC,SACkH;AAClH,WAAO,KAAK,cAAc,SACvB,IAAI;MACL,KAAK;MACL,aAAa,KAAK,OAAOA,OAAM;MAC/B,KAAK;MACL,KAAK;MACL,KAAK;IACN,IACE,IAAI;MACL,KAAK;MACL,aAAa,KAAK,OAAOA,OAAM;MAC/B,KAAK;MACL,KAAK;MACL,KAAK;IACN,EAAE,SAAS,KAAK,SAAS;EAC3B;AACD;AAwNO,IAAM,eAAN,cAaG,aAIV;EAOC,YACC,OACA,KACQ,SACA,SACR,UACC;AACD,UAAM;AAJE,SAAA,UAAA;AACA,SAAA,UAAA;AAIR,SAAK,SAAS,EAAE,KAAK,OAAO,UAAU,OAAO,CAAC,EAAE;AAChD,SAAK,YAAY,iBAAiB,KAAK;AACvC,SAAK,sBAAsB,OAAO,KAAK,cAAc,WAAW,EAAE,CAAC,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC;EAC/F;EAjBA,QAA0B,UAAU,IAAY;EAExC;EACA;EACA;EAeR,KACC,QAC2C;AAC3C,UAAM,YAAY,iBAAiB,MAAM;AACzC,QAAI,OAAO,cAAc,UAAU;AAClC,WAAK,oBAAoB,SAAS,IAAI;IACvC;AACA,SAAK,OAAO,OAAO;AACnB,WAAO;EACR;EAEQ,mBAAmB,OAAiE;AAC3F,QAAI,GAAG,OAAO,OAAO,GAAG;AACvB,aAAO,MAAM,MAAM,OAAO,OAAO;IAClC,WAAW,GAAG,OAAO,QAAQ,GAAG;AAC/B,aAAO,MAAM,EAAE;IAChB;AACA,WAAO,MAAM,cAAc,EAAE;EAC9B;EAEQ,WACP,UAC4C;AAC5C,WAAQ,CACP,OACA,OACI;AACJ,YAAM,YAAY,iBAAiB,KAAK;AAExC,UAAI,OAAO,cAAc,YAAY,KAAK,OAAO,MAAM,KAAK,CAAC,SAAS,KAAK,UAAU,SAAS,GAAG;AAChG,cAAM,IAAI,MAAM,UAAU,SAAS,iCAAiC;MACrE;AAEA,UAAI,OAAO,OAAO,YAAY;AAC7B,cAAM,OAAO,KAAK,OAAO,QAAQ,CAAC,GAAG,KAAK,OAAO,MAAM,GAAG,IACvD,KAAK,mBAAmB,KAAK,OAAO,IAAI,IACxC;AACH,aAAK;UACJ,IAAI;YACH,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO;YACtC,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;UAC5E;UACA,QAAQ,IAAI;YACX;YACA,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;UAC5E;QACD;MACD;AAEA,WAAK,OAAO,MAAM,KAAK,EAAE,IAAI,OAAO,UAAU,OAAO,UAAU,CAAC;AAEhE,UAAI,OAAO,cAAc,UAAU;AAClC,gBAAQ,UAAU;UACjB,KAAK,QAAQ;AACZ,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;UACA,KAAK,SAAS;AACb,iBAAK,sBAAsB,OAAO;cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;UACA,KAAK,SAAS;AACb,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;UACA,KAAK,QAAQ;AACZ,iBAAK,sBAAsB,OAAO;cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;QACD;MACD;AAEA,aAAO;IACR;EACD;EAEA,WAAW,KAAK,WAAW,MAAM;EAEjC,YAAY,KAAK,WAAW,OAAO;EAEnC,YAAY,KAAK,WAAW,OAAO;EAEnC,WAAW,KAAK,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAmCjC,MAAM,OAAkE;AACvE,SAAK,OAAO,QAAQ;AACpB,WAAO;EACR;EA4BA,UACC,QACsD;AACtD,QAAI,CAAC,QAAQ;AACZ,eAAS,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO,CAAC;AAElE,UAAI,KAAK,OAAO,MAAM;AACrB,cAAM,YAAY,iBAAiB,KAAK,OAAO,IAAI;AAEnD,YAAI,OAAO,cAAc,YAAY,KAAK,OAAO,QAAQ,CAAC,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;AACpF,gBAAM,aAAa,KAAK,mBAAmB,KAAK,OAAO,IAAI;AAC3D,iBAAO,SAAS,IAAI;QACrB;AAEA,mBAAW,QAAQ,KAAK,OAAO,OAAO;AACrC,gBAAMC,aAAY,iBAAiB,KAAK,KAAK;AAE7C,cAAI,OAAOA,eAAc,YAAY,CAAC,GAAG,KAAK,OAAO,GAAG,GAAG;AAC1D,kBAAM,aAAa,KAAK,mBAAmB,KAAK,KAAK;AACrD,mBAAOA,UAAS,IAAI;UACrB;QACD;MACD;IACD;AAEA,SAAK,OAAO,YAAY,oBAA8B,MAAM;AAC5D,WAAO;EACR;;EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;EACjD;EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;EACR;;EAGA,SAAS,MAAsC;AAC9C,UAAM,QAAQ,KAAK,QAAQ,aAEzB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,WAAW,MAAM,IAAI;AAC3E,UAAM,sBAAsB,KAAK;AACjC,WAAO;EACR;EAEA,QAAQ,MAAqC;AAC5C,WAAO,KAAK,SAAS,IAAI;EAC1B;EAEQ;;EAER,SAAS,OAAe;AACvB,SAAK,YAAY;AACjB,WAAO;EACR;EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,KAAK,SAAS,EAAE,QAAQ,mBAAmB,KAAK,SAAS;EACjE;EAEA,WAAkC;AACjC,WAAO;EACR;AACD;;;ACpjBA;AAKO,IAAM,iBAAN,MAAM,wBAEH,IAAmD;EAuB5D,YACU,QAKR;AACD,UAAM,gBAAe,mBAAmB,OAAO,QAAQ,OAAO,OAAO,EAAE,WAAW;AANzE,SAAA,SAAA;AAQT,SAAK,QAAQ,MAAM;AAEnB,SAAK,UAAU,OAAO;AAEtB,SAAK,MAAM,gBAAe;MACzB,OAAO;MACP,OAAO;IACR;EACD;EAvCQ;EACA;EAER,QAA0B,UAAU,IAAI;EACxC,CAAC,OAAO,WAAW,IAAI;EAEf;EAER,OAAe,mBACd,QACA,SACc;AACd,WAAO,4BAAoC,MAAM,GAAG,IAAI,IAAI,SAAS,EAAE,GAAG,OAAO,CAAC,GAAG,OAAO;EAC7F;EAEA,OAAe,WACd,QACA,SACc;AACd,WAAO,oCAA4C,MAAM,GAAG,IAAI,IAAI,SAAS,EAAE,GAAG,OAAO,CAAC,GAAG,OAAO;EACrG;;EAsBA,SAAS,OAAe;AACvB,SAAK,QAAQ;EACd;EAEA,KACC,aACA,YAC+B;AAC/B,WAAO,QAAQ,QAAQ,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,KAAK,CAAC,EAC7D;MACA;MACA;IACD;EACF;EAEA,MACC,YACkB;AAClB,WAAO,KAAK,KAAK,QAAW,UAAU;EACvC;EAEA,QAAQ,WAA8D;AACrE,WAAO,KAAK;MACX,CAAC,UAAU;AACV,oBAAY;AACZ,eAAO;MACR;MACA,CAAC,WAAW;AACX,oBAAY;AACZ,cAAM;MACP;IACD;EACD;AACD;;;ACnFA;AAkBO,IAAM,yBAAN,MAA4G;EAGlH,YACS,YACA,QACA,eACA,OACA,aACA,SACA,SACP;AAPO,SAAA,aAAA;AACA,SAAA,SAAA;AACA,SAAA,gBAAA;AACA,SAAA,QAAA;AACA,SAAA,cAAA;AACA,SAAA,UAAA;AACA,SAAA,UAAA;EACN;EAVH,QAAiB,UAAU,IAAY;EAYvC,SACC,QACmE;AACnE,WAAO,IAAI;MACV,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,SAAU,SAAyC,CAAC;MACpD;IACD;EACD;EAEA,UACC,QACgF;AAChF,WAAO,IAAI;MACV,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,SAAS,EAAE,GAAI,QAAoD,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;MAC3F;IACD;EACD;AACD;AAEO,IAAM,oBAAN,cAAyC,aAEhD;EAQC,YACS,YACA,QACA,eACA,OACA,aACA,SACA,SACA,QACA,MACP;AACD,UAAM;AAVE,SAAA,aAAA;AACA,SAAA,SAAA;AACA,SAAA,gBAAA;AACA,SAAA,QAAA;AACA,SAAA,cAAA;AACA,SAAA,UAAA;AACA,SAAA,UAAA;AACA,SAAA,SAAA;AACA,SAAA,OAAA;EAGT;EAnBA,QAA0B,UAAU,IAAY;;EAsBhD,SAAS,MAA4E;AACpF,WAAO,OAAO,gBAAgB,wBAAwB,MAAM;AAC3D,YAAM,EAAE,OAAO,WAAW,IAAI,KAAK,OAAO;AAE1C,aAAO,KAAK,QAAQ;QACnB;QACA;QACA;QACA;QACA,CAAC,SAAS,mBAAmB;AAC5B,gBAAM,OAAO,QAAQ;YAAI,CAAC,QACzB,iBAAiB,KAAK,QAAQ,KAAK,aAAa,KAAK,MAAM,WAAW,cAAc;UACrF;AACA,cAAI,KAAK,SAAS,SAAS;AAC1B,mBAAO,KAAK,CAAC;UACd;AACA,iBAAO;QACR;MACD;IACD,CAAC;EACF;EAEA,QAAQ,MAA2E;AAClF,WAAO,KAAK,SAAS,IAAI;EAC1B;EAEQ,YAAY;AACnB,WAAO,KAAK,QAAQ,8BAA8B;MACjD,YAAY,KAAK;MACjB,QAAQ,KAAK;MACb,eAAe,KAAK;MACpB,OAAO,KAAK;MACZ,aAAa,KAAK;MAClB,aAAa,KAAK;MAClB,YAAY,KAAK,YAAY;IAC9B,CAAC;EACF;;EAGA,SAAc;AACb,WAAO,KAAK,UAAU,EAAE;EACzB;EAEQ,SAA8E;AACrF,UAAM,QAAQ,KAAK,UAAU;AAE7B,UAAM,aAAa,KAAK,QAAQ,WAAW,MAAM,GAAU;AAE3D,WAAO,EAAE,OAAO,WAAW;EAC5B;EAEA,QAAe;AACd,WAAO,KAAK,OAAO,EAAE;EACtB;EAEQ;;EAER,SAAS,OAAe;AACvB,SAAK,YAAY;AACjB,WAAO;EACR;EAES,UAA4B;AACpC,WAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,aAAO,KAAK,SAAS,EAAE,QAAQ,QAAW,KAAK,SAAS;IACzD,CAAC;EACF;AACD;;;AC5JA;AAQO,IAAM,QAAN,cAA6B,aAEpC;EAQC,YACQ,SACCC,MACA,OACA,gBACP;AACD,UAAM;AALC,SAAA,UAAA;AACC,SAAA,MAAAA;AACA,SAAA,QAAA;AACA,SAAA,iBAAA;EAGT;EAdA,QAA0B,UAAU,IAAY;;EAiBhD,SAAS;AACR,WAAO,KAAK;EACb;EAEA,WAAW;AACV,WAAO,KAAK;EACb;EAEA,UAAU,QAAiB,aAAuB;AACjD,WAAO,cAAc,KAAK,eAAe,MAAM,IAAI;EACpD;EAEA,WAA0B;AACzB,WAAO;EACR;;EAGA,wBAAwB;AACvB,WAAO;EACR;AACD;;;AddO,IAAM,aAAN,MAIL;EAgBD,YAEU,SAEA,SACT,QACC;AAJQ,SAAA,UAAA;AAEA,SAAA,UAAA;AAGT,SAAK,IAAI,SACN;MACD,QAAQ,OAAO;MACf,YAAY,OAAO;MACnB,eAAe,OAAO;MACtB;IACD,IACE;MACD,QAAQ;MACR,YAAY,CAAC;MACb,eAAe,CAAC;MAChB;IACD;AACD,SAAK,QAAQ,CAAC;AACd,QAAI,KAAK,EAAE,QAAQ;AAClB,iBAAW,CAAC,WAAW,OAAO,KAAK,OAAO,QAAQ,KAAK,EAAE,MAAM,GAAG;AAChE,aAAK,MAAiE,SAAS,IAAI,IAAI;UACvF,OAAQ;UACR,KAAK,EAAE;UACP,KAAK,EAAE;UACP,OAAQ,WAAW,SAAS;UAC5B;UACA;UACA;QACD;MACD;IACD;EACD;EAjDA,QAAiB,UAAU,IAAY;EASvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA0EA,MAA6B,OAAe;AAC3C,UAAM,OAAO;AACb,WAAO;MACN,GACC,IACgD;AAChD,YAAI,OAAO,OAAO,YAAY;AAC7B,eAAK,GAAG,IAAI,aAAa,KAAK,OAAO,CAAC;QACvC;AAEA,eAAO,IAAI;UACV,IAAI,aAAa,GAAG,OAAO,GAAG,GAAG,kBAAkB,GAAqB,OAAO,IAAI;UACnF,IAAI,sBAAsB,EAAE,OAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;QACvF;MACD;IACD;EACD;EAEA,OACC,QACA,SACC;AACD,WAAO,IAAI,eAAe,EAAE,QAAQ,SAAS,SAAS,KAAK,QAAQ,CAAC;EACrE;;;;;;;;;;;;;;;;;;;;EAqBA,QAAQ,SAAyB;AAChC,UAAM,OAAO;AAwCb,aAASC,QAAO,QAAsE;AACrF,aAAO,IAAI,gBAAgB;QAC1B,QAAQ,UAAU;QAClB,SAAS,KAAK;QACd,SAAS,KAAK;QACd,UAAU;MACX,CAAC;IACF;AA4BA,aAAS,eAAe,QAAsE;AAC7F,aAAO,IAAI,gBAAgB;QAC1B,QAAQ,UAAU;QAClB,SAAS,KAAK;QACd,SAAS,KAAK;QACd,UAAU;QACV,UAAU;MACX,CAAC;IACF;AAgCA,aAAS,iBACR,IACA,QAC8C;AAC9C,aAAO,IAAI,gBAAgB;QAC1B,QAAQ,UAAU;QAClB,SAAS,KAAK;QACd,SAAS,KAAK;QACd,UAAU;QACV,UAAU,EAAE,GAAG;MAChB,CAAC;IACF;AA6BA,aAAS,OAA+B,OAAsD;AAC7F,aAAO,IAAI,gBAAgB,OAAO,KAAK,SAAS,KAAK,SAAS,OAAO;IACtE;AA0BA,aAAS,OAA+B,OAAsD;AAC7F,aAAO,IAAI,gBAAgB,OAAO,KAAK,SAAS,KAAK,SAAS,OAAO;IACtE;AA0BA,aAAS,QAAgC,OAAmD;AAC3F,aAAO,IAAI,aAAa,OAAO,KAAK,SAAS,KAAK,SAAS,OAAO;IACnE;AAEA,WAAO,EAAE,QAAAA,SAAQ,gBAAgB,kBAAkB,QAAQ,QAAQ,QAAQ,QAAQ;EACpF;EAwCA,OAAO,QAAsE;AAC5E,WAAO,IAAI,gBAAgB;MAC1B,QAAQ,UAAU;MAClB,SAAS,KAAK;MACd,SAAS,KAAK;IACf,CAAC;EACF;EA4BA,eAAe,QAAsE;AACpF,WAAO,IAAI,gBAAgB;MAC1B,QAAQ,UAAU;MAClB,SAAS,KAAK;MACd,SAAS,KAAK;MACd,UAAU;IACX,CAAC;EACF;EAgCA,iBACC,IACA,QAC8C;AAC9C,WAAO,IAAI,gBAAgB;MAC1B,QAAQ,UAAU;MAClB,SAAS,KAAK;MACd,SAAS,KAAK;MACd,UAAU,EAAE,GAAG;IAChB,CAAC;EACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BA,OAA+B,OAAsD;AACpF,WAAO,IAAI,gBAAgB,OAAO,KAAK,SAAS,KAAK,OAAO;EAC7D;;;;;;;;;;;;;;;;;;;;;;;;;EA0BA,OAA+B,OAAsD;AACpF,WAAO,IAAI,gBAAgB,OAAO,KAAK,SAAS,KAAK,OAAO;EAC7D;;;;;;;;;;;;;;;;;;;;;;;;;EA0BA,OAA+B,OAAmD;AACjF,WAAO,IAAI,aAAa,OAAO,KAAK,SAAS,KAAK,OAAO;EAC1D;EAEA,wBAA0D,MAAsD;AAC/G,WAAO,IAAI,0BAA0B,MAAM,KAAK,SAAS,KAAK,OAAO;EACtE;EAEU;EAEV,QACC,OAC+C;AAC/C,UAAM,SAAS,OAAO,UAAU,WAAW,IAAI,IAAI,KAAK,IAAI,MAAM,OAAO;AACzE,UAAM,aAAa,KAAK,QAAQ,WAAW,MAAM;AACjD,UAAM,WAAW,KAAK,QAAQ;MAG7B;MACA;MACA;MACA;IACD;AACA,WAAO,IAAI;MACV,MAAM,SAAS,QAAQ,QAAW,KAAK,SAAS;MAChD;MACA;MACA,CAAC,WAAW,SAAS,UAAU,QAAQ,IAAI;IAC5C;EACD;EAEA,YACC,aACA,QACa;AACb,WAAO,KAAK,QAAQ,YAAY,aAAa,MAAM;EACpD;AACD;;;AennBA;;;ACDA;AAgBO,IAAe,kBAAf,MAAuF;EAC7F,YAAsB,OAAc;AAAd,SAAA,QAAA;EAAe;EAE3B;EAEV,WAAkB;AACjB,WAAO,KAAK;EACb;EAEA,UAAU,UAAmB,cAAiC;AAC7D,WAAO;EACR;;EAGA,SAAS,OAAgB;AACxB,SAAK,YAAY;AACjB,WAAO;EACR;EAEA,QAAiB,UAAU,IAAY;;EAGvC;AAaD;AAQO,IAAe,YAAf,MAIL;EAGD,YAAsB,SAAoB;AAApB,SAAA,UAAA;EAAqB;EAF3C,QAAiB,UAAU,IAAY;;EAgBvC,QAAW,OAAY,OAA4B;AAClD,WAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,YAAM,WAAW,OAAO,gBAAgB,wBAAwB,MAAM;AACrE,eAAO,KAAK;UACX,KAAK,QAAQ,WAAW,KAAK;UAC7B;UACA;UACA;QACD;MACD,CAAC;AAED,aAAO,SAAS,SAAS,KAAK,EAAE,QAAQ,QAAW,KAAK;IACzD,CAAC;EACF;EAEA,IAAiB,OAA0B;AAC1C,WAAO,KAAK;MACX,KAAK,QAAQ,WAAW,KAAK;MAC7B;MACA;MACA;IACD,EAAE,IAAI;EACP;;EAMA,MAAM,MAAMC,MAAU,OAAiC;AACtD,UAAM,MAAM,MAAM,KAAK,QAA6BA,MAAK,KAAK;AAE9D,WAAO;MACN,IAAI,CAAC,EAAE,OAAO;IACf;EACD;AAMD;AAEO,IAAe,gBAAf,cAIG,WAA+C;EAGxD,YACC,SACA,SACU,QAKS,cAAc,GAChC;AACD,UAAM,SAAS,SAAS,MAAM;AAPpB,SAAA,SAAA;AAKS,SAAA,cAAA;EAGpB;EAbA,QAA0B,UAAU,IAAY;EAehD,WAAkB;AACjB,UAAM,IAAI,yBAAyB;EACpC;;EAGA,wBAAwB,QAAkC;AACzD,UAAM,SAAmB,CAAC;AAC1B,QAAI,OAAO,gBAAgB;AAC1B,aAAO,KAAK,mBAAmB,OAAO,cAAc,EAAE;IACvD;AACA,QAAI,OAAO,YAAY;AACtB,aAAO,KAAK,OAAO,UAAU;IAC9B;AACA,QAAI,OAAO,OAAO,eAAe,WAAW;AAC3C,aAAO,KAAK,OAAO,aAAa,eAAe,gBAAgB;IAChE;AACA,WAAO,IAAI,IAAI,OAAO,KAAK,GAAG,CAAC;EAChC;EAEA,eAAe,QAA4C;AAC1D,WAAO,KAAK,QAAQ,QAAQ,sBAAsB,KAAK,wBAAwB,MAAM,CAAC,EAAE;EACzF;AAKD;;;AD1JO,IAAM,0BAAN,cAAqE,gBAAmB;EAG9F,YACS,QACA,aACA,QACA,QACA,QACA,wBACA,oBACP;AACD,UAAM,EAAE,KAAK,aAAa,OAAO,CAAC;AAR1B,SAAA,SAAA;AACA,SAAA,cAAA;AACA,SAAA,SAAA;AACA,SAAA,SAAA;AACA,SAAA,SAAA;AACA,SAAA,yBAAA;AACA,SAAA,qBAAA;EAGT;EAZA,QAA0B,UAAU,IAAY;EAchD,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,WAAO,OAAO,gBAAgB,mBAAmB,OAAO,SAAS;AAChE,YAAM,SAAS,iBAAiB,KAAK,QAAQ,iBAAiB;AAE9D,YAAM,cAAc;QACnB,sBAAsB,KAAK;QAC3B,wBAAwB,KAAK,UAAU,MAAM;MAC9C,CAAC;AAED,WAAK,OAAO,SAAS,KAAK,aAAa,MAAM;AAE7C,YAAM,EAAE,QAAQ,aAAa,OAAO,QAAQ,qBAAqB,mBAAmB,IAAI;AACxF,UAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,eAAO,OAAO,gBAAgB,0BAA0B,MAAM;AAC7D,iBAAO,OAAO,OAAO,OAAO,MAAe;QAC5C,CAAC;MACF;AAEA,YAAM,OAAO,MAAM,OAAO,gBAAgB,0BAA0B,MAAM;AACzE,cAAM,cAAc;UACnB,sBAAsB;UACtB,wBAAwB,KAAK,UAAU,MAAM;QAC9C,CAAC;AAED,eAAO,OAAO,OAAO,OAAO,MAAe,EAAE,OAAO;MACrD,CAAC;AAED,aAAO,OAAO,gBAAgB,uBAAuB,MAAM;AAC1D,eAAO,qBACJ,mBAAmB,IAAI,IACvB,KAAK,IAAI,CAAC,QAAQ,aAA2B,QAAS,KAAK,mBAAmB,CAAC;MACnF,CAAC;IACF,CAAC;EACF;EAEA,IAAI,oBAAyD,CAAC,GAAsB;AACnF,WAAO,OAAO,gBAAgB,mBAAmB,OAAO,SAAS;AAChE,YAAM,SAAS,iBAAiB,KAAK,QAAQ,iBAAiB;AAC9D,YAAM,cAAc;QACnB,sBAAsB,KAAK;QAC3B,wBAAwB,KAAK,UAAU,MAAM;MAC9C,CAAC;AACD,WAAK,OAAO,SAAS,KAAK,aAAa,MAAM;AAC7C,aAAO,OAAO,gBAAgB,0BAA0B,MAAM;AAC7D,cAAM,cAAc;UACnB,sBAAsB,KAAK;UAC3B,wBAAwB,KAAK,UAAU,MAAM;QAC9C,CAAC;AACD,eAAO,KAAK,OAAO,OAAO,KAAK,aAAa,MAAe;MAC5D,CAAC;IACF,CAAC;EACF;;EAGA,wBAAiC;AAChC,WAAO,KAAK;EACb;AACD;AAMO,IAAM,oBAAN,MAAM,2BAIH,UAA0D;EAKnE,YACQ,QACP,SACQ,QAEC,UAAoC,CAAC,GAC7C;AACD,UAAM,OAAO;AANN,SAAA,SAAA;AAEC,SAAA,SAAA;AAEC,SAAA,UAAA;AAGT,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;EAChD;EAbA,QAA0B,UAAU,IAAY;EAEhD;EAaA,aACC,OACA,QACA,MACA,uBACA,oBACqB;AACrB,WAAO,IAAI;MACV,KAAK;MACL,MAAM;MACN,MAAM;MACN,KAAK;MACL;MACA;MACA;IACD;EACD;EAEA,MAAM,OAAe,QAA4C;AAChE,SAAK,OAAO,SAAS,OAAO,MAAM;AAClC,WAAO,KAAK,OAAO,OAAO,OAAO,MAAe,EAAE,OAAO;EAC1D;EAEA,aACC,OACA,QACwB;AACxB,WAAO,KAAK,OAAO,OAAO,OAAO,MAAe;EACjD;EAES,YACR,aACA,QACa;AACb,WAAO,KAAK,OAAO,MAAM,OAAO,WAAW;AAC1C,YAAM,UAAU,IAAI;QACnB;QACA,KAAK;QACL,KAAK;QACL,KAAK;MACN;AACA,YAAM,KAAK,IAAI,sBAAsB,KAAK,SAAS,SAAS,KAAK,MAAM;AACvE,UAAI,QAAQ;AACX,cAAM,GAAG,eAAe,MAAM;MAC/B;AACA,aAAO,YAAY,EAAE;IACtB,CAAC;EACF;AACD;AAEO,IAAM,wBAAN,MAAM,+BAGH,cAA8D;EAGvE,YACC,SAEkB,SAClB,QACA,cAAc,GACb;AACD,UAAM,SAAS,SAAS,QAAQ,WAAW;AAJzB,SAAA,UAAA;EAKnB;EAVA,QAA0B,UAAU,IAAY;EAYvC,YACR,aACa;AACb,WAAO,KAAK,QAAQ,OAAO,UAAU,CAAC,WAAW;AAChD,YAAM,UAAU,IAAI;QACnB;QACA,KAAK;QACL,KAAK;QACL,KAAK,QAAQ;MACd;AACA,YAAM,KAAK,IAAI,uBAA4C,KAAK,SAAS,SAAS,KAAK,MAAM;AAC7F,aAAO,YAAY,EAAE;IACtB,CAAC;EACF;AACD;;;A1BjLO,IAAM,qBAAN,cAEG,WAA8C;EACvD,QAA0B,UAAU,IAAY;AACjD;AAEA,SAAS,UACR,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,oBAAoB,CAAC,QAAa;AAGxC,aAAW,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,MAAM,GAAG;AACpD,WAAO,QAAQ,QAAQ,IAAW,IAAI;AACtC,WAAO,QAAQ,YAAY,IAAW,IAAI;EAC3C;AACA,SAAO,QAAQ,YAAY,KAAK,IAAI;AACpC,SAAO,QAAQ,YAAY,MAAM,IAAI;AAErC,QAAM,UAAU,IAAI,UAAU,EAAE,QAAQ,OAAO,OAAO,CAAC;AACvD,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;MACpB,OAAO;MACP;IACD;AACA,aAAS;MACR,YAAY,OAAO;MACnB,QAAQ,aAAa;MACrB,eAAe,aAAa;IAC7B;EACD;AAEA,QAAM,UAAU,IAAI,kBAAkB,QAAQ,SAAS,QAAQ,EAAE,OAAO,CAAC;AACzE,QAAMC,MAAK,IAAI,mBAAmB,SAAS,SAAS,MAAa;AAC1D,EAAAA,IAAI,UAAU;AAErB,SAAOA;AACR;AAEO,SAAS,WAIZ,QAiBF;AACD,MAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AAClC,UAAM,WAAW,YAAS,OAAO,CAAC,CAAW;AAE7C,WAAO,UAAU,UAAU,OAAO,CAAC,CAAC;EACrC;AAEA,MAAI,SAAS,OAAO,CAAC,CAAC,GAAG;AACxB,UAAM,EAAE,YAAAC,aAAY,QAAQ,GAAG,cAAc,IAAI,OAAO,CAAC;AAKzD,QAAI;AAAQ,aAAO,UAAU,QAAQ,aAAa;AAElD,QAAI,OAAOA,gBAAe,YAAYA,YAAW,QAAQ,QAAW;AACnE,YAAM,EAAE,KAAK,GAAG,OAAO,IAAIA;AAE3B,YAAMC,YAAW,YAAS,KAAK,MAAM;AACrC,aAAO,UAAUA,WAAU,aAAa;IACzC;AAEA,UAAM,WAAW,YAASD,WAAU;AACpC,WAAO,UAAU,UAAU,aAAa;EACzC;AAEA,SAAO,UAAU,OAAO,CAAC,GAAc,OAAO,CAAC,CAAuC;AACvF;CAEO,CAAUE,aAAV;AACC,WAAS,KACf,QAGC;AACD,WAAO,UAAU,CAAC,GAAU,MAAM;EACnC;AANOA,WAAS,OAAA;AAAA,GADA,YAAA,UAAA,CAAA,EAAA;;;A4BnHjB;AAEA,IAAI,YAAY;AAET,SAAS,kBAAkB;AAChC,MAAI,aAAa,OAAO,WAAW,aAAa;AAC9C;AAAA,EACF;AAEA,MAAI;AAEF,mBAAkB,OAAO,EAAE,MAAM,OAAO,CAAC;AACzC,gBAAY;AACZ,YAAQ,IAAI,WAAW;AAAA,EACzB,SAAS,OAAO;AAEd,QAAI;AACF,qBAAkB,OAAO,EAAE,MAAM,aAAa,CAAC;AAC/C,kBAAY;AACZ,cAAQ,IAAI,wBAAwB;AAAA,IACtC,SAAS,QAAQ;AACf,cAAQ,KAAK,yBAAyB;AAAA,IACxC;AAAA,EACF;AACF;AAEO,SAAS,eAAe,KAAqB;AAClD,kBAAgB;AAEhB,QAAM,QAAQ,QAAQ,IAAI,GAAG;AAC7B,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,QAAQ,GAAG,MAAM;AAAA,EACnC;AACA,SAAO;AACT;AAEO,SAAS,eAAe,KAAa,cAA2C;AACrF,kBAAgB;AAEhB,SAAO,QAAQ,IAAI,GAAG,KAAK;AAC7B;;;A7BlCA,gBAAgB;AAGhB,IAAM,mBAAmB,eAAe,cAAc;AAEtD,IAAI;AAEJ,IAAI,kBAAkB;AAEpB,QAAM,SAAS,YAAS,kBAAkB;AAAA,IACxC,KAAK;AAAA,IACL,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,KAAK;AAAA,IACL,SAAS;AAAA,EACX,CAAC;AAGD,OAAK,QAAQ,QAAQ,EAAE,uBAAO,CAAC;AACjC,OAAO;AAEL,QAAM,aAAa,CAAC;AACpB,OAAK,QAAQ,YAAY,EAAE,uBAAO,CAAC;AACrC;", "names": ["fs", "os", "crypto", "parse", "path", "b", "types", "b", "types", "inferType", "arraySerializer", "<PERSON><PERSON><PERSON><PERSON>", "x", "index", "parsers", "backoff", "connection", "query", "error", "types", "length", "x", "b", "chunk", "rows", "buffer", "timer", "noop", "postgres", "connection", "sql", "slot", "stream", "state", "x", "b", "parsers", "Stream", "sql", "size", "b", "sql", "handler", "options", "query", "name", "result", "queries", "connection", "fn", "timer", "a", "table", "select", "values", "sql", "joinOn", "field", "select", "select", "values", "select", "values", "tableName", "sql", "select", "sql", "db", "connection", "instance", "drizzle"]}