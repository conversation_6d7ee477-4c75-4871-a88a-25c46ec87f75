{"version": 3, "sources": ["../../../src/lib/db/schema.ts"], "sourcesContent": ["import { pgTable, text, timestamp, integer, boolean, jsonb, uuid } from 'drizzle-orm/pg-core';\n\n// Users table\nexport const users = pgTable('users', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  email: text('email').notNull().unique(),\n  name: text('name'),\n  avatar: text('avatar'),\n  bio: text('bio'),\n  age: integer('age'),\n  gender: text('gender'),\n  location: text('location'),\n  interests: jsonb('interests').$type<string[]>().default([]),\n  personalityTraits: jsonb('personality_traits').$type<Record<string, any>>(),\n  personalitySummary: text('personality_summary'),\n  isActive: boolean('is_active').default(true),\n  createdAt: timestamp('created_at').defaultNow(),\n  updatedAt: timestamp('updated_at').defaultNow(),\n});\n\n// User profiles table for additional profile information\nexport const userProfiles = pgTable('user_profiles', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  userId: uuid('user_id').references(() => users.id).notNull(),\n  selfDescription: text('self_description'),\n  lookingFor: text('looking_for'),\n  relationshipGoals: text('relationship_goals'),\n  lifestyle: jsonb('lifestyle').$type<Record<string, any>>(),\n  values: jsonb('values').$type<string[]>().default([]),\n  photos: jsonb('photos').$type<string[]>().default([]),\n  preferences: jsonb('preferences').$type<Record<string, any>>(),\n  createdAt: timestamp('created_at').defaultNow(),\n  updatedAt: timestamp('updated_at').defaultNow(),\n});\n\n// Matches table\nexport const matches = pgTable('matches', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  user1Id: uuid('user1_id').references(() => users.id).notNull(),\n  user2Id: uuid('user2_id').references(() => users.id).notNull(),\n  compatibilityScore: integer('compatibility_score'),\n  aiAnalysis: jsonb('ai_analysis').$type<Record<string, any>>(),\n  conversationSimulation: jsonb('conversation_simulation').$type<Record<string, any>>(),\n  status: text('status').default('pending'), // pending, mutual_like, rejected\n  user1Liked: boolean('user1_liked').default(false),\n  user2Liked: boolean('user2_liked').default(false),\n  user1Viewed: boolean('user1_viewed').default(false),\n  user2Viewed: boolean('user2_viewed').default(false),\n  createdAt: timestamp('created_at').defaultNow(),\n  updatedAt: timestamp('updated_at').defaultNow(),\n});\n\n// AI Agent Feedback table\nexport const aiAgentFeedback = pgTable('ai_agent_feedback', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  matchId: uuid('match_id').references(() => matches.id).notNull(),\n  userId: uuid('user_id').references(() => users.id).notNull(),\n  feedbackType: text('feedback_type').notNull(), // 'like', 'dislike', 'accurate', 'inaccurate'\n  feedbackText: text('feedback_text'),\n  rating: integer('rating'), // 1-5 scale\n  aspectRated: text('aspect_rated'), // 'personality', 'conversation', 'compatibility'\n  createdAt: timestamp('created_at').defaultNow(),\n});\n\n// Conversations table for storing chat messages\nexport const conversations = pgTable('conversations', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  matchId: uuid('match_id').references(() => matches.id).notNull(),\n  isActive: boolean('is_active').default(true),\n  createdAt: timestamp('created_at').defaultNow(),\n  updatedAt: timestamp('updated_at').defaultNow(),\n});\n\n// Messages table\nexport const messages = pgTable('messages', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  conversationId: uuid('conversation_id').references(() => conversations.id).notNull(),\n  senderId: uuid('sender_id').references(() => users.id).notNull(),\n  content: text('content').notNull(),\n  messageType: text('message_type').default('text'), // text, image, etc.\n  isRead: boolean('is_read').default(false),\n  createdAt: timestamp('created_at').defaultNow(),\n});\n\n// User sessions for tracking activity\nexport const userSessions = pgTable('user_sessions', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  userId: uuid('user_id').references(() => users.id).notNull(),\n  sessionToken: text('session_token').notNull().unique(),\n  expiresAt: timestamp('expires_at').notNull(),\n  createdAt: timestamp('created_at').defaultNow(),\n});\n\n// ===== V2.0 新增表 =====\n\n// 1. 任务队列，用于异步处理匹配请求\nexport const matchQueue = pgTable('match_queue', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  matchRequestId: uuid('match_request_id').notNull().unique(),\n  requesterId: uuid('requester_id').references(() => users.id).notNull(),\n  status: text('status').default('pending'), // pending, processing, completed, failed\n  attempts: integer('attempts').default(0),\n  createdAt: timestamp('created_at').defaultNow(),\n});\n\n// 2. 匹配请求的总记录\nexport const matchRequests = pgTable('match_requests', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  requesterId: uuid('requester_id').references(() => users.id).notNull(),\n  status: text('status').default('processing'), // processing, completed, failed\n  errorMessage: text('error_message'),\n  createdAt: timestamp('created_at').defaultNow(),\n});\n\n// 3. 用于记录用户对每个候选人的决策和详细分析\nexport const matchCandidates = pgTable('match_candidates', {\n  id: uuid('id').primaryKey().defaultRandom(),\n  requestId: uuid('request_id').references(() => matchRequests.id).notNull(),\n  candidateId: uuid('candidate_id').references(() => users.id).notNull(),\n  rank: integer('rank').notNull(), // 1 to 5 (1 = 首席推荐)\n\n  // 兼容性分析数据\n  compatibilityScore: integer('compatibility_score').notNull().default(0), // 0-100 分数\n  reasoning: text('reasoning'), // 兼容性推理\n  highlights: jsonb('highlights'), // 关系亮点数组\n  challenges: jsonb('challenges'), // 潜在挑战数组\n  personalitySummary: text('personality_summary'), // 候选人人格摘要\n\n  // 首席推荐的额外数据 (rank = 1 时才有)\n  relationshipInsight: jsonb('relationship_insight'), // 关系洞察\n  conversationSimulation: jsonb('conversation_simulation'), // 对话模拟\n  datePlan: jsonb('date_plan'), // 约会计划\n\n  // 用户决策\n  userDecision: text('user_decision').default('pending'), // pending, liked, skipped, mutual_liked\n  createdAt: timestamp('created_at').defaultNow(),\n});\n\n// Note: Zod schemas removed due to drizzle-zod compatibility issues\n// You can add manual Zod schemas if needed for validation\n\n// Types\nexport type User = typeof users.$inferSelect;\nexport type NewUser = typeof users.$inferInsert;\nexport type UserProfile = typeof userProfiles.$inferSelect;\nexport type NewUserProfile = typeof userProfiles.$inferInsert;\nexport type Match = typeof matches.$inferSelect;\nexport type NewMatch = typeof matches.$inferInsert;\nexport type AiAgentFeedback = typeof aiAgentFeedback.$inferSelect;\nexport type NewAiAgentFeedback = typeof aiAgentFeedback.$inferInsert;\nexport type Conversation = typeof conversations.$inferSelect;\nexport type NewConversation = typeof conversations.$inferInsert;\nexport type Message = typeof messages.$inferSelect;\nexport type NewMessage = typeof messages.$inferInsert;\n\n// V2.0 新增类型\nexport type MatchQueue = typeof matchQueue.$inferSelect;\nexport type NewMatchQueue = typeof matchQueue.$inferInsert;\nexport type MatchRequest = typeof matchRequests.$inferSelect;\nexport type NewMatchRequest = typeof matchRequests.$inferInsert;\nexport type MatchCandidate = typeof matchCandidates.$inferSelect;\nexport type NewMatchCandidate = typeof matchCandidates.$inferInsert;\n"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGO,IAAM,QAAQ,QAAQ,SAAS;AAAA,EACpC,IAAI,KAAK,IAAI,EAAE,WAAW,EAAE,cAAc;AAAA,EAC1C,OAAO,KAAK,OAAO,EAAE,QAAQ,EAAE,OAAO;AAAA,EACtC,MAAM,KAAK,MAAM;AAAA,EACjB,QAAQ,KAAK,QAAQ;AAAA,EACrB,KAAK,KAAK,KAAK;AAAA,EACf,KAAK,QAAQ,KAAK;AAAA,EAClB,QAAQ,KAAK,QAAQ;AAAA,EACrB,UAAU,KAAK,UAAU;AAAA,EACzB,WAAW,MAAM,WAAW,EAAE,MAAgB,EAAE,QAAQ,CAAC,CAAC;AAAA,EAC1D,mBAAmB,MAAM,oBAAoB,EAAE,MAA2B;AAAA,EAC1E,oBAAoB,KAAK,qBAAqB;AAAA,EAC9C,UAAU,QAAQ,WAAW,EAAE,QAAQ,IAAI;AAAA,EAC3C,WAAW,UAAU,YAAY,EAAE,WAAW;AAAA,EAC9C,WAAW,UAAU,YAAY,EAAE,WAAW;AAChD,CAAC;AAGM,IAAM,eAAe,QAAQ,iBAAiB;AAAA,EACnD,IAAI,KAAK,IAAI,EAAE,WAAW,EAAE,cAAc;AAAA,EAC1C,QAAQ,KAAK,SAAS,EAAE,WAAW,MAAM,MAAM,EAAE,EAAE,QAAQ;AAAA,EAC3D,iBAAiB,KAAK,kBAAkB;AAAA,EACxC,YAAY,KAAK,aAAa;AAAA,EAC9B,mBAAmB,KAAK,oBAAoB;AAAA,EAC5C,WAAW,MAAM,WAAW,EAAE,MAA2B;AAAA,EACzD,QAAQ,MAAM,QAAQ,EAAE,MAAgB,EAAE,QAAQ,CAAC,CAAC;AAAA,EACpD,QAAQ,MAAM,QAAQ,EAAE,MAAgB,EAAE,QAAQ,CAAC,CAAC;AAAA,EACpD,aAAa,MAAM,aAAa,EAAE,MAA2B;AAAA,EAC7D,WAAW,UAAU,YAAY,EAAE,WAAW;AAAA,EAC9C,WAAW,UAAU,YAAY,EAAE,WAAW;AAChD,CAAC;AAGM,IAAM,UAAU,QAAQ,WAAW;AAAA,EACxC,IAAI,KAAK,IAAI,EAAE,WAAW,EAAE,cAAc;AAAA,EAC1C,SAAS,KAAK,UAAU,EAAE,WAAW,MAAM,MAAM,EAAE,EAAE,QAAQ;AAAA,EAC7D,SAAS,KAAK,UAAU,EAAE,WAAW,MAAM,MAAM,EAAE,EAAE,QAAQ;AAAA,EAC7D,oBAAoB,QAAQ,qBAAqB;AAAA,EACjD,YAAY,MAAM,aAAa,EAAE,MAA2B;AAAA,EAC5D,wBAAwB,MAAM,yBAAyB,EAAE,MAA2B;AAAA,EACpF,QAAQ,KAAK,QAAQ,EAAE,QAAQ,SAAS;AAAA;AAAA,EACxC,YAAY,QAAQ,aAAa,EAAE,QAAQ,KAAK;AAAA,EAChD,YAAY,QAAQ,aAAa,EAAE,QAAQ,KAAK;AAAA,EAChD,aAAa,QAAQ,cAAc,EAAE,QAAQ,KAAK;AAAA,EAClD,aAAa,QAAQ,cAAc,EAAE,QAAQ,KAAK;AAAA,EAClD,WAAW,UAAU,YAAY,EAAE,WAAW;AAAA,EAC9C,WAAW,UAAU,YAAY,EAAE,WAAW;AAChD,CAAC;AAGM,IAAM,kBAAkB,QAAQ,qBAAqB;AAAA,EAC1D,IAAI,KAAK,IAAI,EAAE,WAAW,EAAE,cAAc;AAAA,EAC1C,SAAS,KAAK,UAAU,EAAE,WAAW,MAAM,QAAQ,EAAE,EAAE,QAAQ;AAAA,EAC/D,QAAQ,KAAK,SAAS,EAAE,WAAW,MAAM,MAAM,EAAE,EAAE,QAAQ;AAAA,EAC3D,cAAc,KAAK,eAAe,EAAE,QAAQ;AAAA;AAAA,EAC5C,cAAc,KAAK,eAAe;AAAA,EAClC,QAAQ,QAAQ,QAAQ;AAAA;AAAA,EACxB,aAAa,KAAK,cAAc;AAAA;AAAA,EAChC,WAAW,UAAU,YAAY,EAAE,WAAW;AAChD,CAAC;AAGM,IAAM,gBAAgB,QAAQ,iBAAiB;AAAA,EACpD,IAAI,KAAK,IAAI,EAAE,WAAW,EAAE,cAAc;AAAA,EAC1C,SAAS,KAAK,UAAU,EAAE,WAAW,MAAM,QAAQ,EAAE,EAAE,QAAQ;AAAA,EAC/D,UAAU,QAAQ,WAAW,EAAE,QAAQ,IAAI;AAAA,EAC3C,WAAW,UAAU,YAAY,EAAE,WAAW;AAAA,EAC9C,WAAW,UAAU,YAAY,EAAE,WAAW;AAChD,CAAC;AAGM,IAAM,WAAW,QAAQ,YAAY;AAAA,EAC1C,IAAI,KAAK,IAAI,EAAE,WAAW,EAAE,cAAc;AAAA,EAC1C,gBAAgB,KAAK,iBAAiB,EAAE,WAAW,MAAM,cAAc,EAAE,EAAE,QAAQ;AAAA,EACnF,UAAU,KAAK,WAAW,EAAE,WAAW,MAAM,MAAM,EAAE,EAAE,QAAQ;AAAA,EAC/D,SAAS,KAAK,SAAS,EAAE,QAAQ;AAAA,EACjC,aAAa,KAAK,cAAc,EAAE,QAAQ,MAAM;AAAA;AAAA,EAChD,QAAQ,QAAQ,SAAS,EAAE,QAAQ,KAAK;AAAA,EACxC,WAAW,UAAU,YAAY,EAAE,WAAW;AAChD,CAAC;AAGM,IAAM,eAAe,QAAQ,iBAAiB;AAAA,EACnD,IAAI,KAAK,IAAI,EAAE,WAAW,EAAE,cAAc;AAAA,EAC1C,QAAQ,KAAK,SAAS,EAAE,WAAW,MAAM,MAAM,EAAE,EAAE,QAAQ;AAAA,EAC3D,cAAc,KAAK,eAAe,EAAE,QAAQ,EAAE,OAAO;AAAA,EACrD,WAAW,UAAU,YAAY,EAAE,QAAQ;AAAA,EAC3C,WAAW,UAAU,YAAY,EAAE,WAAW;AAChD,CAAC;AAKM,IAAM,aAAa,QAAQ,eAAe;AAAA,EAC/C,IAAI,KAAK,IAAI,EAAE,WAAW,EAAE,cAAc;AAAA,EAC1C,gBAAgB,KAAK,kBAAkB,EAAE,QAAQ,EAAE,OAAO;AAAA,EAC1D,aAAa,KAAK,cAAc,EAAE,WAAW,MAAM,MAAM,EAAE,EAAE,QAAQ;AAAA,EACrE,QAAQ,KAAK,QAAQ,EAAE,QAAQ,SAAS;AAAA;AAAA,EACxC,UAAU,QAAQ,UAAU,EAAE,QAAQ,CAAC;AAAA,EACvC,WAAW,UAAU,YAAY,EAAE,WAAW;AAChD,CAAC;AAGM,IAAM,gBAAgB,QAAQ,kBAAkB;AAAA,EACrD,IAAI,KAAK,IAAI,EAAE,WAAW,EAAE,cAAc;AAAA,EAC1C,aAAa,KAAK,cAAc,EAAE,WAAW,MAAM,MAAM,EAAE,EAAE,QAAQ;AAAA,EACrE,QAAQ,KAAK,QAAQ,EAAE,QAAQ,YAAY;AAAA;AAAA,EAC3C,cAAc,KAAK,eAAe;AAAA,EAClC,WAAW,UAAU,YAAY,EAAE,WAAW;AAChD,CAAC;AAGM,IAAM,kBAAkB,QAAQ,oBAAoB;AAAA,EACzD,IAAI,KAAK,IAAI,EAAE,WAAW,EAAE,cAAc;AAAA,EAC1C,WAAW,KAAK,YAAY,EAAE,WAAW,MAAM,cAAc,EAAE,EAAE,QAAQ;AAAA,EACzE,aAAa,KAAK,cAAc,EAAE,WAAW,MAAM,MAAM,EAAE,EAAE,QAAQ;AAAA,EACrE,MAAM,QAAQ,MAAM,EAAE,QAAQ;AAAA;AAAA;AAAA,EAG9B,oBAAoB,QAAQ,qBAAqB,EAAE,QAAQ,EAAE,QAAQ,CAAC;AAAA;AAAA,EACtE,WAAW,KAAK,WAAW;AAAA;AAAA,EAC3B,YAAY,MAAM,YAAY;AAAA;AAAA,EAC9B,YAAY,MAAM,YAAY;AAAA;AAAA,EAC9B,oBAAoB,KAAK,qBAAqB;AAAA;AAAA;AAAA,EAG9C,qBAAqB,MAAM,sBAAsB;AAAA;AAAA,EACjD,wBAAwB,MAAM,yBAAyB;AAAA;AAAA,EACvD,UAAU,MAAM,WAAW;AAAA;AAAA;AAAA,EAG3B,cAAc,KAAK,eAAe,EAAE,QAAQ,SAAS;AAAA;AAAA,EACrD,WAAW,UAAU,YAAY,EAAE,WAAW;AAChD,CAAC;", "names": []}