import {
  <PERSON><PERSON>e<PERSON><PERSON><PERSON>,
  DevUsageManager,
  ExecutorToWorkerMessageCatalog,
  InternalError,
  OtelTaskLogger,
  PreciseWallClock,
  SemanticInternalAttributes,
  SpanStatusCode,
  StandardMetadataManager,
  StandardRunTimelineMetricsManager,
  StandardTaskCatalog,
  StandardWaitUntilManager,
  TaskExecutor,
  TaskRunErrorCodes,
  TracingSDK,
  UsageTimeoutManager,
  WorkerManifest,
  WorkerToExecutorMessageCatalog,
  ZodSchemaParsedError,
  apiClientManager,
  clock,
  context,
  getEnvVar,
  getNumberEnvVar,
  init_esm as init_esm2,
  logLevels,
  logger,
  normalizeImportPath,
  o,
  propagation,
  recordSpanException,
  require_source_map_support,
  require_src,
  runMetadata,
  runTimelineMetrics,
  runtime,
  taskCatalog,
  taskContext,
  timeout,
  trace,
  usage,
  waitUntil,
  z
} from "../../../../../../../../chunk-RVZAMPQI.mjs";
import {
  __toESM,
  init_esm
} from "../../../../../../../../chunk-CYQ63LDX.mjs";

// ../../../.npm/_npx/f51a09bd0abf5f10/node_modules/trigger.dev/dist/esm/entryPoints/dev-run-worker.js
init_esm();

// ../../../.npm/_npx/f51a09bd0abf5f10/node_modules/@trigger.dev/core/dist/esm/v3/tracer.js
init_esm();
init_esm2();
var import_api_logs = __toESM(require_src(), 1);
var TriggerTracer = class {
  _config;
  constructor(_config) {
    this._config = _config;
  }
  _tracer;
  get tracer() {
    if (!this._tracer) {
      if ("tracer" in this._config)
        return this._config.tracer;
      this._tracer = trace.getTracer(this._config.name, this._config.version);
    }
    return this._tracer;
  }
  _logger;
  get logger() {
    if (!this._logger) {
      if ("logger" in this._config)
        return this._config.logger;
      this._logger = import_api_logs.logs.getLogger(this._config.name, this._config.version);
    }
    return this._logger;
  }
  extractContext(traceContext) {
    return propagation.extract(context.active(), traceContext ?? {});
  }
  startActiveSpan(name, fn, options, ctx, signal) {
    const parentContext = ctx ?? context.active();
    const attributes = options?.attributes ?? {};
    let spanEnded = false;
    return this.tracer.startActiveSpan(name, {
      ...options,
      attributes,
      startTime: clock.preciseNow()
    }, parentContext, async (span) => {
      signal?.addEventListener("abort", () => {
        if (!spanEnded) {
          spanEnded = true;
          recordSpanException(span, signal.reason);
          span.end();
        }
      });
      if (taskContext.ctx) {
        const partialSpan = this.tracer.startSpan(name, {
          ...options,
          attributes: {
            ...attributes,
            [SemanticInternalAttributes.SPAN_PARTIAL]: true,
            [SemanticInternalAttributes.SPAN_ID]: span.spanContext().spanId
          }
        }, parentContext);
        if (options?.events) {
          for (const event of options.events) {
            partialSpan.addEvent(event.name, event.attributes, event.startTime);
          }
        }
        partialSpan.end();
      }
      if (options?.events) {
        for (const event of options.events) {
          span.addEvent(event.name, event.attributes, event.startTime);
        }
      }
      const usageMeasurement = usage.start();
      try {
        return await fn(span);
      } catch (e) {
        if (!spanEnded) {
          if (typeof e === "string" || e instanceof Error) {
            span.recordException(e);
          }
          span.setStatus({ code: SpanStatusCode.ERROR });
        }
        throw e;
      } finally {
        if (!spanEnded) {
          spanEnded = true;
          if (taskContext.ctx) {
            const usageSample = usage.stop(usageMeasurement);
            const machine = taskContext.ctx.machine;
            span.setAttributes({
              [SemanticInternalAttributes.USAGE_DURATION_MS]: usageSample.cpuTime,
              [SemanticInternalAttributes.USAGE_COST_IN_CENTS]: machine?.centsPerMs ? usageSample.cpuTime * machine.centsPerMs : 0
            });
          }
          span.end(clock.preciseNow());
        }
      }
    });
  }
  startSpan(name, options, ctx) {
    const parentContext = ctx ?? context.active();
    const attributes = options?.attributes ?? {};
    const span = this.tracer.startSpan(name, options, ctx);
    this.tracer.startSpan(name, {
      ...options,
      attributes: {
        ...attributes,
        [SemanticInternalAttributes.SPAN_PARTIAL]: true,
        [SemanticInternalAttributes.SPAN_ID]: span.spanContext().spanId
      }
    }, parentContext).end();
    return span;
  }
};

// ../../../.npm/_npx/f51a09bd0abf5f10/node_modules/@trigger.dev/core/dist/esm/v3/dev/index.js
init_esm();

// ../../../.npm/_npx/f51a09bd0abf5f10/node_modules/@trigger.dev/core/dist/esm/v3/runtime/devRuntimeManager.js
init_esm();

// ../../../.npm/_npx/f51a09bd0abf5f10/node_modules/@trigger.dev/core/dist/esm/v3/utils/timers.js
init_esm();
import { setInterval, setTimeout as setTimeout2 } from "node:timers/promises";
async function unboundedTimeout(delay = 0, value, options) {
  const maxDelay = 2147483647;
  const fullTimeouts = Math.floor(delay / maxDelay);
  const remainingDelay = delay % maxDelay;
  let lastTimeoutResult = await setTimeout2(remainingDelay, value, options);
  for (let i = 0; i < fullTimeouts; i++) {
    lastTimeoutResult = await setTimeout2(maxDelay, value, options);
  }
  return lastTimeoutResult;
}

// ../../../.npm/_npx/f51a09bd0abf5f10/node_modules/@trigger.dev/core/dist/esm/v3/runtime/preventMultipleWaits.js
init_esm();
var concurrentWaitErrorMessage = "Parallel waits are not supported, e.g. using Promise.all() around our wait functions.";
function preventMultipleWaits() {
  let isExecutingWait = false;
  return async (cb) => {
    if (isExecutingWait) {
      console.error(concurrentWaitErrorMessage);
      throw new InternalError({
        code: TaskRunErrorCodes.TASK_DID_CONCURRENT_WAIT,
        message: concurrentWaitErrorMessage,
        skipRetrying: true,
        showStackTrace: false
      });
    }
    isExecutingWait = true;
    try {
      await Promise.resolve();
      return await cb();
    } finally {
      isExecutingWait = false;
    }
  };
}

// ../../../.npm/_npx/f51a09bd0abf5f10/node_modules/@trigger.dev/core/dist/esm/v3/runtime/devRuntimeManager.js
var DevRuntimeManager = class {
  _taskWaits = /* @__PURE__ */ new Map();
  _batchWaits = /* @__PURE__ */ new Map();
  _pendingCompletionNotifications = /* @__PURE__ */ new Map();
  _preventMultipleWaits = preventMultipleWaits();
  disable() {
  }
  async waitForDuration(ms) {
    await this._preventMultipleWaits(() => unboundedTimeout(ms));
  }
  async waitUntil(date) {
    return this.waitForDuration(date.getTime() - Date.now());
  }
  async waitForTask(params) {
    return this._preventMultipleWaits(async () => {
      const pendingCompletion = this._pendingCompletionNotifications.get(params.id);
      if (pendingCompletion) {
        this._pendingCompletionNotifications.delete(params.id);
        return pendingCompletion;
      }
      const promise = new Promise((resolve) => {
        this._taskWaits.set(params.id, { resolve });
      });
      await this.#tryFlushMetadata();
      return await promise;
    });
  }
  async waitForBatch(params) {
    return this._preventMultipleWaits(async () => {
      if (!params.runs.length) {
        return Promise.resolve({ id: params.id, items: [] });
      }
      const promise = Promise.all(params.runs.map((runId) => {
        return new Promise((resolve, reject) => {
          const pendingCompletion = this._pendingCompletionNotifications.get(runId);
          if (pendingCompletion) {
            this._pendingCompletionNotifications.delete(runId);
            resolve(pendingCompletion);
            return;
          }
          this._taskWaits.set(runId, { resolve });
        });
      }));
      await this.#tryFlushMetadata();
      const results = await promise;
      return {
        id: params.id,
        items: results
      };
    });
  }
  resumeTask(completion, runId) {
    const wait = this._taskWaits.get(runId);
    if (!wait) {
      this._pendingCompletionNotifications.set(runId, completion);
      return;
    }
    wait.resolve(completion);
    this._taskWaits.delete(runId);
  }
  async #tryFlushMetadata() {
    try {
      await runMetadata.flush();
    } catch (err) {
    }
  }
};

// ../../../.npm/_npx/f51a09bd0abf5f10/node_modules/@trigger.dev/core/dist/esm/v3/zodIpc.js
init_esm();
import { randomUUID } from "crypto";
var messageSchema = z.object({
  version: z.literal("v1").default("v1"),
  type: z.string(),
  payload: z.unknown()
});
var ZodIpcMessageHandler = class {
  #schema;
  #handlers;
  #sender;
  constructor(options) {
    this.#schema = options.schema;
    this.#handlers = options.handlers;
    this.#sender = options.sender;
  }
  async handleMessage(message) {
    const parsedMessage = this.parseMessage(message);
    if (!this.#handlers) {
      throw new Error("No handlers provided");
    }
    const handler = this.#handlers[parsedMessage.type];
    if (!handler) {
      return;
    }
    const ack = await handler(parsedMessage.payload, this.#sender);
    return ack;
  }
  parseMessage(message) {
    const parsedMessage = messageSchema.safeParse(message);
    if (!parsedMessage.success) {
      throw new Error(`Failed to parse message: ${JSON.stringify(parsedMessage.error)}`);
    }
    const schema = this.#schema[parsedMessage.data.type]?.["message"];
    if (!schema) {
      throw new Error(`Unknown message type: ${parsedMessage.data.type}`);
    }
    const parsedPayload = schema.safeParse(parsedMessage.data.payload);
    if (!parsedPayload.success) {
      throw new Error(`Failed to parse message payload: ${JSON.stringify(parsedPayload.error)}`);
    }
    return {
      type: parsedMessage.data.type,
      payload: parsedPayload.data
    };
  }
};
var Packet = z.discriminatedUnion("type", [
  z.object({
    type: z.literal("CONNECT"),
    sessionId: z.string().optional()
  }),
  z.object({
    type: z.literal("ACK"),
    message: z.any(),
    id: z.number()
  }),
  z.object({
    type: z.literal("EVENT"),
    message: z.any(),
    id: z.number().optional()
  })
]);
var ZodIpcConnection = class {
  opts;
  #sessionId;
  #messageCounter = 0;
  #handler;
  #acks = /* @__PURE__ */ new Map();
  constructor(opts) {
    this.opts = opts;
    this.#handler = new ZodIpcMessageHandler({
      schema: opts.listenSchema,
      handlers: opts.handlers,
      sender: {
        send: this.send.bind(this),
        sendWithAck: this.sendWithAck.bind(this)
      }
    });
    this.#registerHandlers();
  }
  async #registerHandlers() {
    if (!this.opts.process.on) {
      return;
    }
    this.opts.process.on("message", async (message) => {
      this.#handlePacket(message);
    });
  }
  async connect() {
    this.#sendPacket({ type: "CONNECT" });
  }
  async #handlePacket(packet) {
    const parsedPacket = Packet.safeParse(packet);
    if (!parsedPacket.success) {
      return;
    }
    switch (parsedPacket.data.type) {
      case "ACK": {
        const ack = this.#acks.get(parsedPacket.data.id);
        if (!ack) {
          return;
        }
        clearTimeout(ack.timeout);
        ack.resolve(parsedPacket.data.message);
        break;
      }
      case "CONNECT": {
        if (!parsedPacket.data.sessionId) {
          const id = randomUUID();
          await this.#sendPacket({ type: "CONNECT", sessionId: id });
          return;
        }
        if (this.#sessionId) {
          return;
        }
        this.#sessionId = parsedPacket.data.sessionId;
        break;
      }
      case "EVENT": {
        const result = await this.#handler.handleMessage(parsedPacket.data.message);
        if (typeof parsedPacket.data.id === "undefined") {
          return;
        }
        await this.#sendPacket({
          type: "ACK",
          id: parsedPacket.data.id,
          message: result
        });
        break;
      }
      default: {
        break;
      }
    }
  }
  async #sendPacket(packet) {
    await this.opts.process.send?.(packet);
  }
  async send(type, payload) {
    const schema = this.opts.emitSchema[type]?.["message"];
    if (!schema) {
      throw new Error(`Unknown message type: ${type}`);
    }
    const parsedPayload = schema.safeParse(payload);
    if (!parsedPayload.success) {
      throw new ZodSchemaParsedError(parsedPayload.error, payload);
    }
    await this.#sendPacket({
      type: "EVENT",
      message: {
        type,
        payload,
        version: "v1"
      }
    });
  }
  async sendWithAck(type, payload, timeoutInMs) {
    const currentId = this.#messageCounter++;
    return new Promise(async (resolve, reject) => {
      const defaultTimeoutInMs = 2e3;
      const timeout2 = setTimeout(() => {
        reject(JSON.stringify({
          reason: "sendWithAck() timeout",
          timeoutInMs: timeoutInMs ?? defaultTimeoutInMs,
          type,
          payload
        }));
      }, timeoutInMs ?? defaultTimeoutInMs);
      this.#acks.set(currentId, { resolve, reject, timeout: timeout2 });
      const schema = this.opts.emitSchema[type]?.["message"];
      if (!schema) {
        clearTimeout(timeout2);
        return reject(`Unknown message type: ${type}`);
      }
      const parsedPayload = schema.safeParse(payload);
      if (!parsedPayload.success) {
        clearTimeout(timeout2);
        return reject(`Failed to parse message payload: ${JSON.stringify(parsedPayload.error)}`);
      }
      await this.#sendPacket({
        type: "EVENT",
        message: {
          type,
          payload,
          version: "v1"
        },
        id: currentId
      });
    });
  }
};

// ../../../.npm/_npx/f51a09bd0abf5f10/node_modules/trigger.dev/dist/esm/entryPoints/dev-run-worker.js
var import_source_map_support = __toESM(require_source_map_support(), 1);
import { readFile } from "node:fs/promises";

// ../../../.npm/_npx/f51a09bd0abf5f10/node_modules/trigger.dev/dist/esm/version.js
init_esm();
var VERSION = "3.3.17";

// ../../../.npm/_npx/f51a09bd0abf5f10/node_modules/trigger.dev/dist/esm/entryPoints/dev-run-worker.js
import_source_map_support.default.install({
  handleUncaughtExceptions: false,
  environment: "node",
  hookRequire: false
});
process.on("uncaughtException", function(error, origin) {
  if (error instanceof Error) {
    process.send && process.send({
      type: "UNCAUGHT_EXCEPTION",
      payload: {
        error: { name: error.name, message: error.message, stack: error.stack },
        origin
      },
      version: "v1"
    });
  } else {
    process.send && process.send({
      type: "UNCAUGHT_EXCEPTION",
      payload: {
        error: {
          name: "Error",
          message: typeof error === "string" ? error : JSON.stringify(error)
        },
        origin
      },
      version: "v1"
    });
  }
});
var standardRunTimelineMetricsManager = new StandardRunTimelineMetricsManager();
runTimelineMetrics.setGlobalManager(standardRunTimelineMetricsManager);
taskCatalog.setGlobalTaskCatalog(new StandardTaskCatalog());
var durableClock = new PreciseWallClock();
clock.setGlobalClock(durableClock);
var devUsageManager = new DevUsageManager();
usage.setGlobalUsageManager(devUsageManager);
var devRuntimeManager = new DevRuntimeManager();
runtime.setGlobalRuntimeManager(devRuntimeManager);
timeout.setGlobalManager(new UsageTimeoutManager(devUsageManager));
var runMetadataManager = new StandardMetadataManager(apiClientManager.clientOrThrow(), getEnvVar("TRIGGER_STREAM_URL", getEnvVar("TRIGGER_API_URL")) ?? "https://api.trigger.dev", getEnvVar("TRIGGER_REALTIME_STREAM_VERSION") ?? "v1");
runMetadata.setGlobalManager(runMetadataManager);
var waitUntilManager = new StandardWaitUntilManager();
waitUntil.setGlobalManager(waitUntilManager);
waitUntil.register({
  requiresResolving: () => runMetadataManager.hasActiveStreams(),
  promise: () => runMetadataManager.waitForAllStreams()
});
var triggerLogLevel = getEnvVar("TRIGGER_LOG_LEVEL");
standardRunTimelineMetricsManager.seedMetricsFromEnvironment();
async function importConfig(configPath) {
  const configModule = await import(normalizeImportPath(configPath));
  const config = configModule?.default ?? configModule?.config;
  return {
    config,
    handleError: configModule?.handleError
  };
}
async function loadWorkerManifest() {
  const manifestContents = await readFile(o.TRIGGER_WORKER_MANIFEST_PATH, "utf-8");
  const raw = JSON.parse(manifestContents);
  return WorkerManifest.parse(raw);
}
async function bootstrap() {
  const workerManifest = await loadWorkerManifest();
  const { config, handleError } = await importConfig(workerManifest.configPath);
  const tracingSDK = new TracingSDK({
    url: o.OTEL_EXPORTER_OTLP_ENDPOINT ?? "http://0.0.0.0:4318",
    instrumentations: config.telemetry?.instrumentations ?? config.instrumentations ?? [],
    exporters: config.telemetry?.exporters ?? [],
    diagLogLevel: o.OTEL_LOG_LEVEL ?? "none",
    forceFlushTimeoutMillis: 3e4
  });
  const otelTracer = tracingSDK.getTracer("trigger-dev-worker", VERSION);
  const otelLogger = tracingSDK.getLogger("trigger-dev-worker", VERSION);
  const tracer = new TriggerTracer({ tracer: otelTracer, logger: otelLogger });
  const consoleInterceptor = new ConsoleInterceptor(otelLogger, typeof config.enableConsoleLogging === "boolean" ? config.enableConsoleLogging : true);
  const configLogLevel = triggerLogLevel ?? config.logLevel ?? "info";
  const otelTaskLogger = new OtelTaskLogger({
    logger: otelLogger,
    tracer,
    level: logLevels.includes(configLogLevel) ? configLogLevel : "info"
  });
  logger.setGlobalTaskLogger(otelTaskLogger);
  for (const task of workerManifest.tasks) {
    taskCatalog.registerTaskFileMetadata(task.id, {
      exportName: task.exportName,
      filePath: task.filePath,
      entryPoint: task.entryPoint
    });
  }
  return {
    tracer,
    tracingSDK,
    consoleInterceptor,
    config,
    handleErrorFn: handleError,
    workerManifest
  };
}
var _execution;
var _isRunning = false;
var _tracingSDK;
var zodIpc = new ZodIpcConnection({
  listenSchema: WorkerToExecutorMessageCatalog,
  emitSchema: ExecutorToWorkerMessageCatalog,
  process,
  handlers: {
    EXECUTE_TASK_RUN: async ({ execution, traceContext, metadata, metrics }, sender) => {
      standardRunTimelineMetricsManager.registerMetricsFromExecution(metrics);
      if (_isRunning) {
        console.error("Worker is already running a task");
        await sender.send("TASK_RUN_COMPLETED", {
          execution,
          result: {
            ok: false,
            id: execution.run.id,
            error: {
              type: "INTERNAL_ERROR",
              code: TaskRunErrorCodes.TASK_ALREADY_RUNNING
            },
            usage: {
              durationMs: 0
            },
            taskIdentifier: execution.task.id
          }
        });
        return;
      }
      const { tracer, tracingSDK, consoleInterceptor, config, handleErrorFn, workerManifest } = await bootstrap();
      _tracingSDK = tracingSDK;
      const taskManifest = workerManifest.tasks.find((t) => t.id === execution.task.id);
      if (!taskManifest) {
        console.error(`Could not find task ${execution.task.id}`);
        await sender.send("TASK_RUN_COMPLETED", {
          execution,
          result: {
            ok: false,
            id: execution.run.id,
            error: {
              type: "INTERNAL_ERROR",
              code: TaskRunErrorCodes.COULD_NOT_FIND_TASK
            },
            usage: {
              durationMs: 0
            },
            taskIdentifier: execution.task.id
          }
        });
        return;
      }
      try {
        await runTimelineMetrics.measureMetric("trigger.dev/start", "import", {
          entryPoint: taskManifest.entryPoint
        }, async () => {
          await import(normalizeImportPath(taskManifest.entryPoint));
        });
      } catch (err) {
        console.error(`Failed to import task ${execution.task.id}`, err);
        await sender.send("TASK_RUN_COMPLETED", {
          execution,
          result: {
            ok: false,
            id: execution.run.id,
            error: {
              type: "INTERNAL_ERROR",
              code: TaskRunErrorCodes.COULD_NOT_IMPORT_TASK,
              message: err instanceof Error ? err.message : String(err),
              stackTrace: err instanceof Error ? err.stack : void 0
            },
            usage: {
              durationMs: 0
            },
            taskIdentifier: execution.task.id
          }
        });
        return;
      }
      process.title = `trigger-dev-worker: ${execution.task.id} ${execution.run.id}`;
      const task = taskCatalog.getTask(execution.task.id);
      if (!task) {
        console.error(`Could not find task ${execution.task.id}`);
        await sender.send("TASK_RUN_COMPLETED", {
          execution,
          result: {
            ok: false,
            id: execution.run.id,
            error: {
              type: "INTERNAL_ERROR",
              code: TaskRunErrorCodes.COULD_NOT_FIND_EXECUTOR
            },
            usage: {
              durationMs: 0
            },
            taskIdentifier: execution.task.id
          }
        });
        return;
      }
      const executor = new TaskExecutor(task, {
        tracer,
        tracingSDK,
        consoleInterceptor,
        config,
        handleErrorFn
      });
      try {
        _execution = execution;
        _isRunning = true;
        runMetadataManager.runId = execution.run.id;
        runMetadataManager.startPeriodicFlush(getNumberEnvVar("TRIGGER_RUN_METADATA_FLUSH_INTERVAL", 1e3));
        const measurement = usage.start();
        const signal = execution.run.maxDuration ? timeout.abortAfterTimeout(execution.run.maxDuration) : void 0;
        signal?.addEventListener("abort", async (e) => {
          if (_isRunning) {
            _isRunning = false;
            _execution = void 0;
            const usageSample2 = usage.stop(measurement);
            await sender.send("TASK_RUN_COMPLETED", {
              execution,
              result: {
                ok: false,
                id: execution.run.id,
                error: {
                  type: "INTERNAL_ERROR",
                  code: TaskRunErrorCodes.MAX_DURATION_EXCEEDED,
                  message: signal.reason instanceof Error ? signal.reason.message : String(signal.reason)
                },
                usage: {
                  durationMs: usageSample2.cpuTime
                },
                taskIdentifier: execution.task.id,
                metadata: runMetadataManager.stopAndReturnLastFlush()
              }
            });
          }
        });
        const { result } = await executor.execute(execution, metadata, traceContext, measurement, signal);
        const usageSample = usage.stop(measurement);
        if (_isRunning) {
          return sender.send("TASK_RUN_COMPLETED", {
            execution,
            result: {
              ...result,
              usage: {
                durationMs: usageSample.cpuTime
              },
              taskIdentifier: execution.task.id,
              metadata: runMetadataManager.stopAndReturnLastFlush()
            }
          });
        }
      } finally {
        _execution = void 0;
        _isRunning = false;
      }
    },
    TASK_RUN_COMPLETED_NOTIFICATION: async (payload) => {
      switch (payload.version) {
        case "v1": {
          devRuntimeManager.resumeTask(payload.completion, payload.execution.run.id);
          break;
        }
        case "v2": {
          devRuntimeManager.resumeTask(payload.completion, payload.completion.id);
          break;
        }
      }
    },
    FLUSH: async ({ timeoutInMs }, sender) => {
      await Promise.allSettled([_tracingSDK?.flush(), runMetadataManager.flush()]);
    }
  }
});
process.title = "trigger-dev-worker";
async function asyncHeartbeat(initialDelayInSeconds = 30, intervalInSeconds = 30) {
  async function _doHeartbeat() {
    while (true) {
      if (_isRunning && _execution) {
        try {
          await zodIpc.send("TASK_HEARTBEAT", { id: _execution.attempt.id });
        } catch (err) {
          console.error("Failed to send HEARTBEAT message", err);
        }
      }
      await new Promise((resolve) => setTimeout(resolve, 1e3 * intervalInSeconds));
    }
  }
  await new Promise((resolve) => setTimeout(resolve, 1e3 * initialDelayInSeconds));
  return _doHeartbeat();
}
await asyncHeartbeat();
//# sourceMappingURL=dev-run-worker.mjs.map
