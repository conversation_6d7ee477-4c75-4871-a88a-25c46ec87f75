{"version": 3, "sources": ["../../../../../../trigger.config.ts"], "sourcesContent": ["import { defineConfig } from \"@trigger.dev/sdk/v3\";\n\nexport default defineConfig({\n  // Your project ref (you can see it on the Project settings page in the dashboard)\n  project: \"proj_ysmmahpuzkhdywrrwegt\",\n\n  //The paths for your trigger folders\n  dirs: [\"./src/trigger\"],\n\n  retries: {\n    //If you want to retry a task in dev mode (when using the CLI)\n    enabledInDev: false,\n    //the default retry settings. Used if you don't specify on a task.\n    default: {\n      maxAttempts: 3,\n      minTimeoutInMs: 1000,\n      maxTimeoutInMs: 10000,\n      factor: 2,\n      randomize: true,\n    },\n  },\n\n  maxDuration: 300,\n  build: {}\n});\nexport const resolveEnvVars = undefined;"], "mappings": ";;;;;;;;AAAA;AAEA,IAAO,yBAAQ,aAAa;AAAA;AAAA,EAE1B,SAAS;AAAA;AAAA,EAGT,MAAM,CAAC,eAAe;AAAA,EAEtB,SAAS;AAAA;AAAA,IAEP,cAAc;AAAA;AAAA,IAEd,SAAS;AAAA,MACP,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EAEA,aAAa;AAAA,EACb,OAAO,CAAC;AACV,CAAC;AACM,IAAM,iBAAiB;", "names": []}