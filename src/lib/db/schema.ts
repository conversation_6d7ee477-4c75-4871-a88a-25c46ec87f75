import { pgTable, text, timestamp, integer, boolean, jsonb, uuid } from 'drizzle-orm/pg-core';

// Users table
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: text('email').notNull().unique(),
  name: text('name'),
  avatar: text('avatar'),
  bio: text('bio'),
  age: integer('age'),
  gender: text('gender'),
  location: text('location'),
  interests: jsonb('interests').$type<string[]>().default([]),
  personalityTraits: jsonb('personality_traits').$type<Record<string, any>>(),
  personalitySummary: text('personality_summary'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// User profiles table for additional profile information
export const userProfiles = pgTable('user_profiles', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  selfDescription: text('self_description'),
  lookingFor: text('looking_for'),
  relationshipGoals: text('relationship_goals'),
  lifestyle: jsonb('lifestyle').$type<Record<string, any>>(),
  values: jsonb('values').$type<string[]>().default([]),
  photos: jsonb('photos').$type<string[]>().default([]),
  preferences: jsonb('preferences').$type<Record<string, any>>(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Matches table
export const matches = pgTable('matches', {
  id: uuid('id').primaryKey().defaultRandom(),
  user1Id: uuid('user1_id').references(() => users.id).notNull(),
  user2Id: uuid('user2_id').references(() => users.id).notNull(),
  compatibilityScore: integer('compatibility_score'),
  aiAnalysis: jsonb('ai_analysis').$type<Record<string, any>>(),
  conversationSimulation: jsonb('conversation_simulation').$type<Record<string, any>>(),
  status: text('status').default('pending'), // pending, mutual_like, rejected
  user1Liked: boolean('user1_liked').default(false),
  user2Liked: boolean('user2_liked').default(false),
  user1Viewed: boolean('user1_viewed').default(false),
  user2Viewed: boolean('user2_viewed').default(false),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// AI Agent Feedback table
export const aiAgentFeedback = pgTable('ai_agent_feedback', {
  id: uuid('id').primaryKey().defaultRandom(),
  matchId: uuid('match_id').references(() => matches.id).notNull(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  feedbackType: text('feedback_type').notNull(), // 'like', 'dislike', 'accurate', 'inaccurate'
  feedbackText: text('feedback_text'),
  rating: integer('rating'), // 1-5 scale
  aspectRated: text('aspect_rated'), // 'personality', 'conversation', 'compatibility'
  createdAt: timestamp('created_at').defaultNow(),
});

// Conversations table for storing chat messages
export const conversations = pgTable('conversations', {
  id: uuid('id').primaryKey().defaultRandom(),
  matchId: uuid('match_id').references(() => matches.id).notNull(),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Messages table
export const messages = pgTable('messages', {
  id: uuid('id').primaryKey().defaultRandom(),
  conversationId: uuid('conversation_id').references(() => conversations.id).notNull(),
  senderId: uuid('sender_id').references(() => users.id).notNull(),
  content: text('content').notNull(),
  messageType: text('message_type').default('text'), // text, image, etc.
  isRead: boolean('is_read').default(false),
  createdAt: timestamp('created_at').defaultNow(),
});

// User sessions for tracking activity
export const userSessions = pgTable('user_sessions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  sessionToken: text('session_token').notNull().unique(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
});

// ===== V2.0 新增表 =====

// 1. 任务队列，用于异步处理匹配请求
export const matchQueue = pgTable('match_queue', {
  id: uuid('id').primaryKey().defaultRandom(),
  matchRequestId: uuid('match_request_id').notNull().unique(),
  requesterId: uuid('requester_id').references(() => users.id).notNull(),
  status: text('status').default('pending'), // pending, processing, completed, failed
  attempts: integer('attempts').default(0),
  createdAt: timestamp('created_at').defaultNow(),
});

// 2. 匹配请求的总记录
export const matchRequests = pgTable('match_requests', {
  id: uuid('id').primaryKey().defaultRandom(),
  requesterId: uuid('requester_id').references(() => users.id).notNull(),
  status: text('status').default('processing'), // processing, completed, failed
  errorMessage: text('error_message'),
  createdAt: timestamp('created_at').defaultNow(),
});

// 3. 用于记录用户对每个候选人的决策和详细分析
export const matchCandidates = pgTable('match_candidates', {
  id: uuid('id').primaryKey().defaultRandom(),
  requestId: uuid('request_id').references(() => matchRequests.id).notNull(),
  candidateId: uuid('candidate_id').references(() => users.id).notNull(),
  rank: integer('rank').notNull(), // 1 to 5 (1 = 首席推荐)

  // 兼容性分析数据
  compatibilityScore: integer('compatibility_score').notNull().default(0), // 0-100 分数
  reasoning: text('reasoning'), // 兼容性推理
  highlights: jsonb('highlights'), // 关系亮点数组
  challenges: jsonb('challenges'), // 潜在挑战数组
  personalitySummary: text('personality_summary'), // 候选人人格摘要

  // 首席推荐的额外数据 (rank = 1 时才有)
  relationshipInsight: jsonb('relationship_insight'), // 关系洞察
  conversationSimulation: jsonb('conversation_simulation'), // 对话模拟
  datePlan: jsonb('date_plan'), // 约会计划

  // 用户决策
  userDecision: text('user_decision').default('pending'), // pending, liked, skipped, mutual_liked
  createdAt: timestamp('created_at').defaultNow(),
});

// 4. Agent执行日志表 - 记录ARAG-Soul框架每个Agent的执行状态和结果
export const agentExecutionLogs = pgTable('agent_execution_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  matchRequestId: uuid('match_request_id').references(() => matchRequests.id).notNull(),

  // Agent信息
  agentName: text('agent_name').notNull(), // retrieveCandidates, generateUserSoulProfile, runCompatibilityInference, rankAndFinalize, generateFullReport
  stepOrder: integer('step_order').notNull(), // 执行顺序 1,2,3,4,5

  // 执行状态
  status: text('status').default('pending'), // pending, running, completed, failed

  // 时间信息
  startedAt: timestamp('started_at'),
  completedAt: timestamp('completed_at'),

  // 结果数据
  resultData: jsonb('result_data'), // 执行结果的JSON数据
  resultSummary: text('result_summary'), // 结果摘要，用于前端显示
  errorMessage: text('error_message'), // 错误信息

  // 性能指标
  executionTimeMs: integer('execution_time_ms'), // 执行耗时（毫秒）

  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Note: Zod schemas removed due to drizzle-zod compatibility issues
// You can add manual Zod schemas if needed for validation

// Types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type UserProfile = typeof userProfiles.$inferSelect;
export type NewUserProfile = typeof userProfiles.$inferInsert;
export type Match = typeof matches.$inferSelect;
export type NewMatch = typeof matches.$inferInsert;
export type AiAgentFeedback = typeof aiAgentFeedback.$inferSelect;
export type NewAiAgentFeedback = typeof aiAgentFeedback.$inferInsert;
export type Conversation = typeof conversations.$inferSelect;
export type NewConversation = typeof conversations.$inferInsert;
export type Message = typeof messages.$inferSelect;
export type NewMessage = typeof messages.$inferInsert;

// V2.0 新增类型
export type MatchQueue = typeof matchQueue.$inferSelect;
export type NewMatchQueue = typeof matchQueue.$inferInsert;
export type MatchRequest = typeof matchRequests.$inferSelect;
export type NewMatchRequest = typeof matchRequests.$inferInsert;
export type MatchCandidate = typeof matchCandidates.$inferSelect;
export type NewMatchCandidate = typeof matchCandidates.$inferInsert;
export type AgentExecutionLog = typeof agentExecutionLogs.$inferSelect;
export type NewAgentExecutionLog = typeof agentExecutionLogs.$inferInsert;
