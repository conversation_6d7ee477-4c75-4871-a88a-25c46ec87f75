// Agent执行日志记录服务

import { db } from '@/lib/db';
import { agentExecutionLogs } from '@/lib/db/schema';
import type { NewAgentExecutionLog } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// Agent名称枚举
export const AGENT_NAMES = {
  RETRIEVE_CANDIDATES: 'retrieveCandidates',
  GENERATE_USER_SOUL_PROFILE: 'generateUserSoulProfile', 
  RUN_COMPATIBILITY_INFERENCE: 'runCompatibilityInference',
  RANK_AND_FINALIZE: 'rankAndFinalize',
  GENERATE_FULL_REPORT: 'generateFullReport'
} as const;

// Agent步骤顺序
export const AGENT_STEP_ORDER = {
  [AGENT_NAMES.RETRIEVE_CANDIDATES]: 1,
  [AGENT_NAMES.GENERATE_USER_SOUL_PROFILE]: 2,
  [AGENT_NAMES.RUN_COMPATIBILITY_INFERENCE]: 3,
  [AGENT_NAMES.RANK_AND_FINALIZE]: 4,
  [AGENT_NAMES.GENERATE_FULL_REPORT]: 5
} as const;

// Agent友好名称
export const AGENT_DISPLAY_NAMES = {
  [AGENT_NAMES.RETRIEVE_CANDIDATES]: '检索候选人',
  [AGENT_NAMES.GENERATE_USER_SOUL_PROFILE]: '分析用户人格',
  [AGENT_NAMES.RUN_COMPATIBILITY_INFERENCE]: '兼容性推理',
  [AGENT_NAMES.RANK_AND_FINALIZE]: '排序决策',
  [AGENT_NAMES.GENERATE_FULL_REPORT]: '生成完整报告'
} as const;

// 执行状态
export type AgentStatus = 'pending' | 'running' | 'completed' | 'failed';

// Agent日志记录器类
export class AgentLogger {
  private matchRequestId: string;

  constructor(matchRequestId: string) {
    this.matchRequestId = matchRequestId;
  }

  // 初始化所有Agent日志记录
  async initializeAgentLogs(): Promise<void> {
    try {
      const logs: NewAgentExecutionLog[] = Object.values(AGENT_NAMES).map(agentName => ({
        matchRequestId: this.matchRequestId,
        agentName,
        stepOrder: AGENT_STEP_ORDER[agentName],
        status: 'pending' as AgentStatus,
        resultSummary: `等待执行 ${AGENT_DISPLAY_NAMES[agentName]}...`
      }));

      await db.insert(agentExecutionLogs).values(logs);
      console.log(`✅ 初始化了 ${logs.length} 个Agent日志记录`);
    } catch (error) {
      console.error('❌ 初始化Agent日志失败:', error);
      throw error;
    }
  }

  // 开始执行Agent
  async startAgent(agentName: keyof typeof AGENT_DISPLAY_NAMES): Promise<void> {
    try {
      const startTime = new Date();
      
      await db
        .update(agentExecutionLogs)
        .set({
          status: 'running',
          startedAt: startTime,
          resultSummary: `正在执行 ${AGENT_DISPLAY_NAMES[agentName]}...`,
          updatedAt: startTime
        })
        .where(
          and(
            eq(agentExecutionLogs.matchRequestId, this.matchRequestId),
            eq(agentExecutionLogs.agentName, agentName)
          )
        );

      console.log(`🚀 开始执行 ${agentName}`);
    } catch (error) {
      console.error(`❌ 开始执行 ${agentName} 失败:`, error);
      throw error;
    }
  }

  // 完成Agent执行
  async completeAgent(
    agentName: keyof typeof AGENT_DISPLAY_NAMES,
    resultData: any,
    resultSummary: string
  ): Promise<void> {
    try {
      const completedTime = new Date();
      
      // 获取开始时间以计算执行耗时
      const existingLog = await db
        .select()
        .from(agentExecutionLogs)
        .where(
          and(
            eq(agentExecutionLogs.matchRequestId, this.matchRequestId),
            eq(agentExecutionLogs.agentName, agentName)
          )
        )
        .limit(1);

      const executionTimeMs = existingLog[0]?.startedAt 
        ? completedTime.getTime() - existingLog[0].startedAt.getTime()
        : null;

      await db
        .update(agentExecutionLogs)
        .set({
          status: 'completed',
          completedAt: completedTime,
          resultData,
          resultSummary,
          executionTimeMs,
          updatedAt: completedTime
        })
        .where(
          and(
            eq(agentExecutionLogs.matchRequestId, this.matchRequestId),
            eq(agentExecutionLogs.agentName, agentName)
          )
        );

      console.log(`✅ 完成执行 ${agentName}, 耗时: ${executionTimeMs}ms`);
    } catch (error) {
      console.error(`❌ 完成执行 ${agentName} 失败:`, error);
      throw error;
    }
  }

  // Agent执行失败
  async failAgent(
    agentName: keyof typeof AGENT_DISPLAY_NAMES,
    errorMessage: string
  ): Promise<void> {
    try {
      const failedTime = new Date();
      
      // 获取开始时间以计算执行耗时
      const existingLog = await db
        .select()
        .from(agentExecutionLogs)
        .where(
          and(
            eq(agentExecutionLogs.matchRequestId, this.matchRequestId),
            eq(agentExecutionLogs.agentName, agentName)
          )
        )
        .limit(1);

      const executionTimeMs = existingLog[0]?.startedAt 
        ? failedTime.getTime() - existingLog[0].startedAt.getTime()
        : null;

      await db
        .update(agentExecutionLogs)
        .set({
          status: 'failed',
          completedAt: failedTime,
          errorMessage,
          resultSummary: `${AGENT_DISPLAY_NAMES[agentName]} 执行失败`,
          executionTimeMs,
          updatedAt: failedTime
        })
        .where(
          and(
            eq(agentExecutionLogs.matchRequestId, this.matchRequestId),
            eq(agentExecutionLogs.agentName, agentName)
          )
        );

      console.log(`❌ ${agentName} 执行失败: ${errorMessage}`);
    } catch (error) {
      console.error(`❌ 记录 ${agentName} 失败状态失败:`, error);
      throw error;
    }
  }

  // 获取所有Agent日志
  async getAllAgentLogs() {
    try {
      return await db
        .select()
        .from(agentExecutionLogs)
        .where(eq(agentExecutionLogs.matchRequestId, this.matchRequestId))
        .orderBy(agentExecutionLogs.stepOrder);
    } catch (error) {
      console.error('❌ 获取Agent日志失败:', error);
      throw error;
    }
  }
}

// 工具函数：生成结果摘要
export const generateResultSummary = {
  [AGENT_NAMES.RETRIEVE_CANDIDATES]: (data: any) => {
    const count = data?.candidatePoolIds?.length || 0;
    return `找到 ${count} 个候选人`;
  },
  
  [AGENT_NAMES.GENERATE_USER_SOUL_PROFILE]: (data: any) => {
    const traits = data?.userSoulProfile?.personalityTraits;
    const traitCount = traits ? Object.keys(traits).length : 0;
    return `分析了 ${traitCount} 个人格特质`;
  },
  
  [AGENT_NAMES.RUN_COMPATIBILITY_INFERENCE]: (data: any) => {
    const count = data?.candidatesWithAnalysis?.length || 0;
    return `完成 ${count} 个候选人的兼容性分析`;
  },
  
  [AGENT_NAMES.RANK_AND_FINALIZE]: (data: any) => {
    const count = data?.rankedCandidates?.length || 0;
    return `排序完成，选出前 ${count} 名候选人`;
  },
  
  [AGENT_NAMES.GENERATE_FULL_REPORT]: (data: any) => {
    const hasTopMatch = !!data?.finalMatrix?.topMatch;
    const potentialCount = data?.finalMatrix?.potentialMatches?.length || 0;
    return `生成完整报告：1个首席推荐 + ${potentialCount}个潜力候选人`;
  }
};
