// ARAG-Soul 框架的 Agent 实现

import OpenAI from 'openai';
import { PROMPTS, SYSTEM_PROMPTS } from './prompts';
import { ensureEnvLoaded, getRequiredEnv } from '../../utils/env';
import { AgentLogger, AGENT_NAMES, generateResultSummary } from './agent-logger';
import type {
  UserSoulProfile,
  CandidateAnalysis,
  RelationshipInsight,
  ConversationSimulation,
  DatePlan
} from './types';

// 状态类型定义（避免循环导入）
type AragSoulState = {
  requesterId: string;
  userProfile?: any;
  candidatePoolIds?: string[];
  userSoulProfile?: UserSoulProfile;
  candidatesWithAnalysis?: CandidateAnalysis[];
  rankedCandidates?: CandidateAnalysis[];
  finalMatrix?: any;
  error?: string;
  step?: string;
  logger?: AgentLogger; // 添加logger
};

// 模型配置
const MODEL_NAME = 'google/gemini-2.5-flash-preview-05-20';

// 创建 AI 调用函数
const createModelCall = async (prompt: string, systemPrompt: string = SYSTEM_PROMPTS.default): Promise<string> => {
  // 确保环境变量被加载并获取 API 密钥
  ensureEnvLoaded();
  const apiKey = getRequiredEnv('OPENROUTER_API_KEY');

  // 初始化 OpenRouter 客户端
  const openrouter = new OpenAI({
    baseURL: 'https://openrouter.ai/api/v1',
    apiKey: apiKey,
  });

  try {
    const completion = await openrouter.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 2048,
      response_format: { type: 'json_object' },
    });

    return completion.choices[0]?.message?.content || '';
  } catch (error) {
    console.error('OpenRouter API 调用失败:', error);
    throw error;
  }
};

// 人格洞察 Agent
export async function generateUserSoulProfileNode(state: AragSoulState): Promise<Partial<AragSoulState>> {
  const agentName = AGENT_NAMES.GENERATE_USER_SOUL_PROFILE;

  try {
    console.log('🧠 执行人格洞察 Agent...');

    // 记录开始执行
    if (state.logger) {
      await state.logger.startAgent(agentName);
    }

    const { userProfile } = state;
    if (!userProfile) {
      throw new Error('用户资料不存在');
    }

    const prompt = PROMPTS.personalityInsight
      .replace('{name}', userProfile.name || '未知')
      .replace('{age}', userProfile.age?.toString() || '未知')
      .replace('{gender}', userProfile.gender || '未知')
      .replace('{selfDescription}', userProfile.selfDescription || '')
      .replace('{interests}', JSON.stringify(userProfile.interests || []))
      .replace('{values}', JSON.stringify(userProfile.values || []))
      .replace('{lifestyle}', JSON.stringify(userProfile.lifestyle || {}))
      .replace('{relationshipGoals}', userProfile.relationshipGoals || '');

    const responseContent = await createModelCall(prompt, SYSTEM_PROMPTS.personality);

    console.log('🔍 AI 返回的原始内容:', responseContent);

    let result;
    try {
      result = JSON.parse(responseContent);
    } catch (parseError) {
      console.error('❌ JSON 解析失败:', parseError);
      console.error('原始内容:', responseContent);
      throw new Error(`人格洞察 JSON 解析失败: ${parseError}`);
    }

    console.log('🔍 解析后的结果:', result);

    // 添加安全检查和默认值
    const userSoulProfile: UserSoulProfile = {
      userId: userProfile.userId,
      personalityTraits: result.personalityTraits || {},
      coreValues: Array.isArray(result.coreValues) ? result.coreValues : [],
      communicationStyle: result.communicationStyle || '未知',
      relationshipGoals: userProfile.relationshipGoals || '寻找合适的伴侣',
      lifestyle: userProfile.lifestyle || {},
      summary: result.summary || '正在分析中...'
    };

    console.log('🔍 生成的用户灵魂档案:', {
      userId: userSoulProfile.userId,
      coreValuesCount: userSoulProfile.coreValues.length,
      hasSummary: !!userSoulProfile.summary
    });

    console.log('✅ 人格洞察完成');

    // 记录完成执行
    if (state.logger) {
      const summary = generateResultSummary[agentName](userSoulProfile);
      await state.logger.completeAgent(agentName, userSoulProfile, summary);
    }

    return {
      userSoulProfile,
      step: 'personality_insight_completed'
    };
  } catch (error) {
    console.error('❌ 人格洞察失败:', error);

    // 记录失败状态
    if (state.logger) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      await state.logger.failAgent(agentName, errorMessage);
    }

    return {
      error: `人格洞察失败: ${error instanceof Error ? error.message : '未知错误'}`,
      step: 'personality_insight_failed'
    };
  }
}

// 深度兼容性推理 Agent
export async function runCompatibilityInferenceNode(state: AragSoulState): Promise<Partial<AragSoulState>> {
  const agentName = AGENT_NAMES.RUN_COMPATIBILITY_INFERENCE;

  try {
    console.log('🔍 执行兼容性推理 Agent...');

    // 记录开始执行
    if (state.logger) {
      await state.logger.startAgent(agentName);
    }

    const { userSoulProfile, candidatePoolIds } = state;
    if (!userSoulProfile || !candidatePoolIds) {
      throw new Error('缺少必要的状态数据');
    }

    // 这里需要从数据库获取候选人资料
    // 为了演示，我们先创建一个模拟的候选人分析函数
    const candidatesWithAnalysis: CandidateAnalysis[] = [];
    
    // 并行处理所有候选人
    const analysisPromises = candidatePoolIds.map(async (candidateId: string) => {
      // TODO: 从数据库获取候选人详细资料
      const candidateProfile = await getCandidateProfile(candidateId);

      const prompt = PROMPTS.compatibilityInference
        .replace('{userSoulProfile}', JSON.stringify(userSoulProfile))
        .replace('{candidateName}', candidateProfile.name || '未知')
        .replace('{candidateAge}', candidateProfile.age?.toString() || '未知')
        .replace('{candidateSelfDescription}', candidateProfile.selfDescription || '')
        .replace('{candidateInterests}', JSON.stringify(candidateProfile.interests || []))
        .replace('{candidateValues}', JSON.stringify(candidateProfile.values || []))
        .replace('{candidateLifestyle}', JSON.stringify(candidateProfile.lifestyle || {}));

      const responseContent = await createModelCall(prompt, SYSTEM_PROMPTS.compatibility);
      const result = JSON.parse(responseContent);

      return {
        candidateId,
        compatibilityScore: result.compatibilityScore || 0,
        reasoning: result.reasoning || '分析中...',
        highlights: Array.isArray(result.highlights) ? result.highlights : [],
        challenges: Array.isArray(result.challenges) ? result.challenges : [],
        personalitySummary: result.personalitySummary || '候选人分析中...'
      } as CandidateAnalysis;
    });

    const analysisResults = await Promise.all(analysisPromises);
    candidatesWithAnalysis.push(...analysisResults);

    console.log(`✅ 兼容性推理完成，分析了 ${candidatesWithAnalysis.length} 个候选人`);

    // 记录完成执行
    if (state.logger) {
      const summary = generateResultSummary[agentName]({ candidatesWithAnalysis });
      await state.logger.completeAgent(agentName, { candidatesWithAnalysis }, summary);
    }

    return {
      candidatesWithAnalysis,
      step: 'compatibility_inference_completed'
    };
  } catch (error) {
    console.error('❌ 兼容性推理失败:', error);

    // 记录失败状态
    if (state.logger) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      await state.logger.failAgent(agentName, errorMessage);
    }

    return {
      error: `兼容性推理失败: ${error instanceof Error ? error.message : '未知错误'}`,
      step: 'compatibility_inference_failed'
    };
  }
}

// 从数据库获取候选人资料
async function getCandidateProfile(candidateId: string) {
  const { db } = await import('@/lib/db');
  const { users, userProfiles } = await import('@/lib/db/schema');
  const { eq } = await import('drizzle-orm');

  const candidateWithProfile = await db
    .select({
      user: users,
      profile: userProfiles
    })
    .from(users)
    .leftJoin(userProfiles, eq(users.id, userProfiles.userId))
    .where(eq(users.id, candidateId))
    .limit(1);

  if (candidateWithProfile.length === 0) {
    throw new Error(`候选人 ${candidateId} 不存在`);
  }

  const candidate = candidateWithProfile[0];
  return {
    userId: candidate.user.id,
    name: candidate.user.name || '未知',
    age: candidate.user.age || 0,
    selfDescription: candidate.profile?.selfDescription || '',
    interests: candidate.user.interests || [],
    values: candidate.profile?.values || [],
    lifestyle: candidate.profile?.lifestyle || {}
  };
}

// 排序和最终决策 Agent
export async function rankAndFinalizeNode(state: AragSoulState): Promise<Partial<AragSoulState>> {
  const agentName = AGENT_NAMES.RANK_AND_FINALIZE;

  try {
    console.log('🏆 执行排序和最终决策 Agent...');

    // 记录开始执行
    if (state.logger) {
      await state.logger.startAgent(agentName);
    }

    const { candidatesWithAnalysis } = state;
    if (!candidatesWithAnalysis || candidatesWithAnalysis.length === 0) {
      throw new Error('没有候选人分析数据');
    }

    // 按兼容性分数排序
    const rankedCandidates = candidatesWithAnalysis
      .sort((a, b) => b.compatibilityScore - a.compatibilityScore)
      .slice(0, 5); // 取前5名

    console.log(`✅ 排序完成，选出前 ${rankedCandidates.length} 名候选人`);

    // 记录完成执行
    if (state.logger) {
      const summary = generateResultSummary[agentName]({ rankedCandidates });
      await state.logger.completeAgent(agentName, { rankedCandidates }, summary);
    }

    return {
      rankedCandidates,
      step: 'ranking_completed'
    };
  } catch (error) {
    console.error('❌ 排序失败:', error);

    // 记录失败状态
    if (state.logger) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      await state.logger.failAgent(agentName, errorMessage);
    }

    return {
      error: `排序失败: ${error instanceof Error ? error.message : '未知错误'}`,
      step: 'ranking_failed'
    };
  }
}

// 生成完整报告 Agent
export async function generateFullReportNode(state: AragSoulState): Promise<Partial<AragSoulState>> {
  const agentName = AGENT_NAMES.GENERATE_FULL_REPORT;

  try {
    console.log('📝 生成完整报告...');

    // 记录开始执行
    if (state.logger) {
      await state.logger.startAgent(agentName);
    }

    const { rankedCandidates, userSoulProfile } = state;
    if (!rankedCandidates || !userSoulProfile) {
      throw new Error('缺少必要数据');
    }

    // 为首席推荐生成完整报告
    const topCandidate = rankedCandidates[0];
    
    // 生成关系洞察、对话模拟和约会计划
    const [relationshipInsight, conversationSimulation, datePlan] = await Promise.all([
      generateRelationshipInsight(userSoulProfile, topCandidate),
      generateConversationSimulation(userSoulProfile, topCandidate),
      generateDatePlan(userSoulProfile, topCandidate)
    ]);

    const finalMatrix = {
      topMatch: {
        candidate: topCandidate,
        relationshipInsight,
        conversationSimulation,
        datePlan
      },
      potentialMatches: rankedCandidates.slice(1, 5).map(candidate => ({
        candidate,
        highlights: candidate.highlights,
        compatibilityReason: candidate.reasoning
      })),
      generatedAt: new Date(),
      requestId: state.requesterId
    };

    console.log('✅ 完整报告生成完成');

    // 记录完成执行
    if (state.logger) {
      const summary = generateResultSummary[agentName]({ finalMatrix });
      await state.logger.completeAgent(agentName, { finalMatrix }, summary);
    }

    return {
      finalMatrix,
      step: 'report_generation_completed'
    };
  } catch (error) {
    console.error('❌ 报告生成失败:', error);

    // 记录失败状态
    if (state.logger) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      await state.logger.failAgent(agentName, errorMessage);
    }

    return {
      error: `报告生成失败: ${error instanceof Error ? error.message : '未知错误'}`,
      step: 'report_generation_failed'
    };
  }
}

// 辅助函数：生成关系洞察
async function generateRelationshipInsight(userProfile: UserSoulProfile, candidate: CandidateAnalysis): Promise<RelationshipInsight> {
  const prompt = PROMPTS.relationshipHighlight
    .replace('{userSoulProfile}', JSON.stringify(userProfile))
    .replace('{candidateAnalysis}', JSON.stringify(candidate));

  const responseContent = await createModelCall(prompt, SYSTEM_PROMPTS.compatibility);
  return JSON.parse(responseContent);
}

// 辅助函数：生成对话模拟
async function generateConversationSimulation(userProfile: UserSoulProfile, candidate: CandidateAnalysis): Promise<ConversationSimulation> {
  const prompt = PROMPTS.conversationSimulation
    .replace('{userProfile}', JSON.stringify(userProfile))
    .replace('{candidateProfile}', JSON.stringify(candidate))
    .replace('{scenario}', '咖啡厅初次见面');

  const responseContent = await createModelCall(prompt, SYSTEM_PROMPTS.conversation);
  return JSON.parse(responseContent);
}

// 辅助函数：生成约会计划
async function generateDatePlan(userProfile: UserSoulProfile, candidate: CandidateAnalysis): Promise<DatePlan> {
  // 安全地找出共同兴趣
  const userCoreValues = userProfile.coreValues || [];
  const candidateHighlights = candidate.highlights || [];

  const commonInterests = userCoreValues.filter(value =>
    candidateHighlights.some(highlight => highlight.includes(value))
  );

  const prompt = PROMPTS.datePlanGeneration
    .replace('{userProfile}', JSON.stringify(userProfile))
    .replace('{candidateProfile}', JSON.stringify(candidate))
    .replace('{commonInterests}', JSON.stringify(commonInterests));

  const responseContent = await createModelCall(prompt, SYSTEM_PROMPTS.dating);
  return JSON.parse(responseContent);
}
