'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  Loader2, 
  ChevronDown, 
  ChevronUp,
  Search,
  Brain,
  BarChart3,
  Trophy,
  FileText
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Agent执行日志类型
interface AgentExecutionLog {
  id: string;
  matchRequestId: string;
  agentName: string;
  stepOrder: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt?: string;
  completedAt?: string;
  resultData?: any;
  resultSummary?: string;
  errorMessage?: string;
  executionTimeMs?: number;
  createdAt: string;
  updatedAt: string;
}

// Agent显示配置
const AGENT_CONFIG = {
  retrieveCandidates: {
    name: '检索候选人',
    icon: Search,
    description: '从用户池中寻找潜在匹配对象',
    color: 'blue'
  },
  generateUserSoulProfile: {
    name: '分析用户人格',
    icon: Brain,
    description: '深度分析用户的性格特质和价值观',
    color: 'purple'
  },
  runCompatibilityInference: {
    name: '兼容性推理',
    icon: BarChart3,
    description: '计算与候选人的匹配度',
    color: 'green'
  },
  rankAndFinalize: {
    name: '排序决策',
    icon: Trophy,
    description: '对候选人进行智能排序',
    color: 'yellow'
  },
  generateFullReport: {
    name: '生成完整报告',
    icon: FileText,
    description: '生成详细的匹配分析报告',
    color: 'red'
  }
} as const;

interface AgentProgressTrackerProps {
  matchRequestId: string;
  onComplete?: () => void;
}

export function AgentProgressTracker({ matchRequestId, onComplete }: AgentProgressTrackerProps) {
  const [logs, setLogs] = useState<AgentExecutionLog[]>([]);
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const supabase = createClient();
    
    // 初始加载数据
    const fetchLogs = async () => {
      try {
        const { data, error } = await supabase
          .from('agent_execution_logs')
          .select('*')
          .eq('match_request_id', matchRequestId)
          .order('step_order', { ascending: true });

        if (error) throw error;
        setLogs(data || []);
      } catch (error) {
        console.error('获取Agent日志失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLogs();

    // 设置实时监听
    const channel = supabase
      .channel(`agent_logs_${matchRequestId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'agent_execution_logs',
          filter: `match_request_id=eq.${matchRequestId}`
        },
        (payload) => {
          console.log('Agent日志更新:', payload);
          
          if (payload.eventType === 'INSERT') {
            setLogs(prev => [...prev, payload.new as AgentExecutionLog].sort((a, b) => a.stepOrder - b.stepOrder));
          } else if (payload.eventType === 'UPDATE') {
            setLogs(prev => prev.map(log => 
              log.id === payload.new.id ? payload.new as AgentExecutionLog : log
            ));
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [matchRequestId]);

  // 检查是否所有步骤都完成
  useEffect(() => {
    const allCompleted = logs.length === 5 && logs.every(log => log.status === 'completed');
    if (allCompleted && onComplete) {
      onComplete();
    }
  }, [logs, onComplete]);

  const toggleExpanded = (stepId: string) => {
    setExpandedSteps(prev => {
      const newSet = new Set(prev);
      if (newSet.has(stepId)) {
        newSet.delete(stepId);
      } else {
        newSet.add(stepId);
      }
      return newSet;
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'running':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'secondary',
      running: 'default',
      completed: 'default',
      failed: 'destructive'
    } as const;

    const labels = {
      pending: '等待中',
      running: '执行中',
      completed: '已完成',
      failed: '失败'
    };

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {labels[status as keyof typeof labels] || status}
      </Badge>
    );
  };

  const calculateProgress = () => {
    const completedCount = logs.filter(log => log.status === 'completed').length;
    return (completedCount / 5) * 100;
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          <span>加载执行进度...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="w-5 h-5" />
          AI红娘工作进度
        </CardTitle>
        <div className="space-y-2">
          <Progress value={calculateProgress()} className="w-full" />
          <p className="text-sm text-gray-600">
            {logs.filter(log => log.status === 'completed').length} / 5 步骤已完成
          </p>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {logs.map((log) => {
          const config = AGENT_CONFIG[log.agentName as keyof typeof AGENT_CONFIG];
          const Icon = config?.icon || Clock;
          const isExpanded = expandedSteps.has(log.id);
          
          return (
            <div key={log.id} className="border rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(log.status)}
                    <Icon className={cn("w-4 h-4", `text-${config?.color}-500`)} />
                  </div>
                  <div>
                    <h4 className="font-medium">{config?.name || log.agentName}</h4>
                    <p className="text-sm text-gray-600">{config?.description}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(log.status)}
                  {(log.resultSummary || log.errorMessage) && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleExpanded(log.id)}
                    >
                      {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                    </Button>
                  )}
                </div>
              </div>
              
              {/* 执行时间 */}
              {log.executionTimeMs && (
                <div className="mt-2 text-xs text-gray-500">
                  执行耗时: {log.executionTimeMs}ms
                </div>
              )}
              
              {/* 展开的详细信息 */}
              {isExpanded && (
                <div className="mt-3 p-3 bg-gray-50 rounded border-l-4 border-blue-200">
                  {log.status === 'failed' && log.errorMessage ? (
                    <div className="text-red-600">
                      <strong>错误信息:</strong> {log.errorMessage}
                    </div>
                  ) : log.resultSummary ? (
                    <div className="text-green-700">
                      <strong>执行结果:</strong> {log.resultSummary}
                    </div>
                  ) : null}
                  
                  {log.resultData && (
                    <details className="mt-2">
                      <summary className="cursor-pointer text-sm text-gray-600">
                        查看详细数据
                      </summary>
                      <pre className="mt-2 text-xs bg-white p-2 rounded border overflow-auto max-h-40">
                        {JSON.stringify(log.resultData, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
}
