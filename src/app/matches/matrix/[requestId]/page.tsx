'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { CandidateMatrix } from '@/components/CandidateMatrix';
import { AgentProgressTracker } from '@/components/AgentProgressTracker';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, ArrowLeft, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import type { CandidateMatrix as CandidateMatrixType } from '@/lib/services/arag-soul/types';

interface MatrixResponse {
  status: 'processing' | 'completed' | 'failed';
  requestId: string;
  matrix?: CandidateMatrixType;
  message?: string;
  generatedAt?: string;
}

export default function MatrixPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const requestId = params.requestId as string;

  const [matrixData, setMatrixData] = useState<CandidateMatrixType | null>(null);
  const [status, setStatus] = useState<'loading' | 'processing' | 'completed' | 'failed'>('loading');
  const [message, setMessage] = useState<string>('');
  const [pollCount, setPollCount] = useState(0);

  // 轮询获取结果
  useEffect(() => {
    if (!requestId) return;

    const pollResult = async () => {
      try {
        const response = await fetch(`/api/matches/matrix?requestId=${requestId}`);
        const data: MatrixResponse = await response.json();

        setStatus(data.status);
        setMessage(data.message || '');

        if (data.status === 'completed' && data.matrix) {
          setMatrixData(data.matrix);
        } else if (data.status === 'failed') {
          toast({
            title: "生成失败",
            description: data.message || "匹配矩阵生成失败，请重试",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('获取结果失败:', error);
        setStatus('failed');
        setMessage('网络错误，请检查连接');
      }
    };

    // 立即执行一次
    pollResult();

    // 如果还在处理中，设置轮询
    const interval = setInterval(() => {
      if (status === 'processing' || status === 'loading') {
        pollResult();
        setPollCount(prev => prev + 1);
      }
    }, 3000); // 每3秒轮询一次

    // 最多轮询40次（2分钟）
    if (pollCount > 40) {
      clearInterval(interval);
      setStatus('failed');
      setMessage('生成超时，请重试');
    }

    return () => clearInterval(interval);
  }, [requestId, status, pollCount, toast]);

  // 处理候选人决策
  const handleCandidateDecision = async (candidateId: string, decision: 'liked' | 'skipped') => {
    try {
      const response = await fetch(
        `/api/matches/matrix/${requestId}/candidates/${candidateId}`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ decision }),
        }
      );

      const result = await response.json();

      if (result.success) {
        toast({
          title: decision === 'liked' ? "已喜欢" : "已跳过",
          description: result.message,
          variant: result.mutualMatch ? "default" : "default",
        });

        // 如果是互相匹配，可以显示特殊提示
        if (result.mutualMatch) {
          toast({
            title: "🎉 恭喜！",
            description: "你们互相喜欢了！可以开始聊天了。",
            variant: "default",
          });
        }
      } else {
        throw new Error(result.message || '操作失败');
      }
    } catch (error) {
      console.error('决策失败:', error);
      toast({
        title: "操作失败",
        description: error instanceof Error ? error.message : "请重试",
        variant: "destructive",
      });
    }
  };

  // 重新生成
  const handleRegenerate = async () => {
    try {
      const response = await fetch('/api/matches/matrix', {
        method: 'POST',
      });

      const result = await response.json();

      if (result.success) {
        router.push(`/matches/matrix/${result.requestId}`);
      } else {
        throw new Error(result.message || '重新生成失败');
      }
    } catch (error) {
      console.error('重新生成失败:', error);
      toast({
        title: "重新生成失败",
        description: error instanceof Error ? error.message : "请重试",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* 导航栏 */}
      <div className="flex items-center justify-between mb-6">
        <Button
          variant="ghost"
          onClick={() => router.push('/dashboard')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          返回主页
        </Button>

        {status === 'completed' && (
          <Button
            variant="outline"
            onClick={handleRegenerate}
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            重新生成
          </Button>
        )}
      </div>

      {/* 内容区域 */}
      {status === 'loading' || status === 'processing' ? (
        <ProcessingView message={message} pollCount={pollCount} />
      ) : status === 'failed' ? (
        <FailedView message={message} onRetry={handleRegenerate} />
      ) : matrixData ? (
        <CandidateMatrix
          matrix={matrixData}
          requestId={requestId}
          onCandidateDecision={handleCandidateDecision}
        />
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500">没有找到匹配数据</p>
        </div>
      )}
    </div>
  );
}

// 处理中视图
function ProcessingView({ requestId, onComplete }: { requestId: string; onComplete?: () => void }) {
  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">AI红娘正在为您工作</h2>
        <p className="text-gray-600">
          正在运行ARAG-Soul框架，为您生成专属的候选人矩阵
        </p>
      </div>

      <AgentProgressTracker
        matchRequestId={requestId}
        onComplete={onComplete}
      />

      <div className="text-center text-sm text-gray-500">
        <p>请保持页面打开，我们会实时更新进度</p>
      </div>
    </div>
  );
}

// 失败视图
function FailedView({ message, onRetry }: { message: string; onRetry: () => void }) {
  return (
    <Card className="max-w-md mx-auto">
      <CardContent className="text-center py-12">
        <div className="text-red-500 text-6xl mb-4">😔</div>
        <h2 className="text-xl font-semibold mb-2">生成失败</h2>
        <p className="text-gray-600 mb-6">
          {message || '匹配矩阵生成失败，请重试'}
        </p>
        <Button onClick={onRetry} className="w-full">
          重新生成
        </Button>
      </CardContent>
    </Card>
  );
}
