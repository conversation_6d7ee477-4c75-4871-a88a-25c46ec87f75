CREATE TABLE "agent_execution_logs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"match_request_id" uuid NOT NULL,
	"agent_name" text NOT NULL,
	"step_order" integer NOT NULL,
	"status" text DEFAULT 'pending',
	"started_at" timestamp,
	"completed_at" timestamp,
	"result_data" jsonb,
	"result_summary" text,
	"error_message" text,
	"execution_time_ms" integer,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "agent_execution_logs" ADD CONSTRAINT "agent_execution_logs_match_request_id_match_requests_id_fk" FOREIGN KEY ("match_request_id") REFERENCES "public"."match_requests"("id") ON DELETE no action ON UPDATE no action;