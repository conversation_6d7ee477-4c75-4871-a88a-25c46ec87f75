-- 创建 agent_execution_logs 表
CREATE TABLE IF NOT EXISTS agent_execution_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    match_request_id UUID REFERENCES match_requests(id) NOT NULL,
    
    -- Agent信息
    agent_name TEXT NOT NULL,
    step_order INTEGER NOT NULL,
    
    -- 执行状态
    status TEXT DEFAULT 'pending',
    
    -- 时间信息
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- 结果数据
    result_data JSONB,
    result_summary TEXT,
    error_message TEXT,
    
    -- 性能指标
    execution_time_ms INTEGER,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_agent_execution_logs_match_request_id ON agent_execution_logs(match_request_id);
CREATE INDEX IF NOT EXISTS idx_agent_execution_logs_agent_name ON agent_execution_logs(agent_name);
CREATE INDEX IF NOT EXISTS idx_agent_execution_logs_status ON agent_execution_logs(status);
CREATE INDEX IF NOT EXISTS idx_agent_execution_logs_step_order ON agent_execution_logs(step_order);
CREATE INDEX IF NOT EXISTS idx_agent_execution_logs_created_at ON agent_execution_logs(created_at);

-- 启用 Row Level Security (RLS)
ALTER TABLE agent_execution_logs ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略
-- 用户只能查看自己的匹配请求相关的Agent日志
CREATE POLICY "Users can view their own agent logs" ON agent_execution_logs
    FOR SELECT USING (
        match_request_id IN (
            SELECT id FROM match_requests WHERE requester_id = auth.uid()
        )
    );

-- 系统可以插入和更新Agent日志（通过service role）
CREATE POLICY "Service role can manage agent logs" ON agent_execution_logs
    FOR ALL USING (auth.role() = 'service_role');

-- 添加约束
ALTER TABLE agent_execution_logs 
ADD CONSTRAINT check_agent_name 
CHECK (agent_name IN ('retrieveCandidates', 'generateUserSoulProfile', 'runCompatibilityInference', 'rankAndFinalize', 'generateFullReport'));

ALTER TABLE agent_execution_logs 
ADD CONSTRAINT check_status 
CHECK (status IN ('pending', 'running', 'completed', 'failed'));

ALTER TABLE agent_execution_logs 
ADD CONSTRAINT check_step_order 
CHECK (step_order >= 1 AND step_order <= 5);

-- 添加唯一约束，确保每个匹配请求的每个Agent只有一条记录
ALTER TABLE agent_execution_logs 
ADD CONSTRAINT unique_match_request_agent 
UNIQUE (match_request_id, agent_name);

-- 创建触发器自动更新 updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_agent_execution_logs_updated_at 
    BEFORE UPDATE ON agent_execution_logs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 添加注释
COMMENT ON TABLE agent_execution_logs IS 'ARAG-Soul框架Agent执行日志表';
COMMENT ON COLUMN agent_execution_logs.match_request_id IS '关联的匹配请求ID';
COMMENT ON COLUMN agent_execution_logs.agent_name IS 'Agent名称';
COMMENT ON COLUMN agent_execution_logs.step_order IS 'Agent执行顺序';
COMMENT ON COLUMN agent_execution_logs.status IS 'Agent执行状态';
COMMENT ON COLUMN agent_execution_logs.result_data IS 'Agent执行结果的JSON数据';
COMMENT ON COLUMN agent_execution_logs.result_summary IS 'Agent执行结果摘要';
COMMENT ON COLUMN agent_execution_logs.execution_time_ms IS 'Agent执行耗时（毫秒）';
