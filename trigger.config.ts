import { defineConfig } from "@trigger.dev/sdk/v3";
import { config } from "dotenv";

config();

export default defineConfig({
  // Your project ref (you can see it on the Project settings page in the dashboard)
  project: process.env.TRIGGER_PROJECT_REF!,
  //The paths for your trigger folders
  dirs: ["./src/trigger"],
  retries: {
    //If you want to retry a task in dev mode (when using the CLI)
    enabledInDev: false,
    //the default retry settings. Used if you don't specify on a task.
    default: {
      maxAttempts: 3,
      minTimeoutInMs: 1000,
      maxTimeoutInMs: 10000,
      factor: 2,
      randomize: true,
    },
  },
  maxDuration: 300
});