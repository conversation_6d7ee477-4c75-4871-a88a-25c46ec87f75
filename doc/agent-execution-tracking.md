# ARAG-Soul Agent执行步骤追踪功能

## 功能概述

这个功能为ARAG-Soul框架添加了详细的执行步骤记录和实时进度追踪，让用户在等待匹配时能够看到AI处理的详细进度。

## 核心组件

### 1. 数据库表：agent_execution_logs

记录每个Agent的执行状态和结果：

```sql
CREATE TABLE agent_execution_logs (
    id UUID PRIMARY KEY,
    match_request_id UUID REFERENCES match_requests(id),
    agent_name TEXT NOT NULL,
    step_order INTEGER NOT NULL,
    status TEXT DEFAULT 'pending',
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    result_data JSONB,
    result_summary TEXT,
    error_message TEXT,
    execution_time_ms INTEGER,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 2. AgentLogger服务

负责记录Agent执行日志的核心服务类：

```typescript
import { AgentLogger } from '@/lib/services/arag-soul/agent-logger';

// 创建logger实例
const logger = new AgentLogger(matchRequestId);

// 初始化所有Agent日志
await logger.initializeAgentLogs();

// 记录Agent开始执行
await logger.startAgent('retrieveCandidates');

// 记录Agent完成执行
await logger.completeAgent('retrieveCandidates', resultData, '找到 5 个候选人');

// 记录Agent执行失败
await logger.failAgent('retrieveCandidates', '网络连接失败');
```

### 3. AgentProgressTracker组件

前端实时进度追踪组件：

```tsx
import { AgentProgressTracker } from '@/components/AgentProgressTracker';

<AgentProgressTracker 
  matchRequestId={requestId} 
  onComplete={() => {
    // 所有Agent执行完成后的回调
    console.log('匹配完成！');
  }}
/>
```

## 5个Agent步骤

1. **检索候选人** (retrieveCandidates)
   - 从用户池中寻找潜在匹配对象
   - 显示找到的候选人数量

2. **分析用户人格** (generateUserSoulProfile)
   - 深度分析用户的性格特质和价值观
   - 显示分析的人格特质数量

3. **兼容性推理** (runCompatibilityInference)
   - 计算与候选人的匹配度
   - 显示完成分析的候选人数量

4. **排序决策** (rankAndFinalize)
   - 对候选人进行智能排序
   - 显示最终选出的候选人数量

5. **生成完整报告** (generateFullReport)
   - 生成详细的匹配分析报告
   - 显示报告生成状态

## 实时更新机制

使用Supabase Realtime功能实现实时更新：

```typescript
const channel = supabase
  .channel(`agent_logs_${matchRequestId}`)
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'agent_execution_logs',
      filter: `match_request_id=eq.${matchRequestId}`
    },
    (payload) => {
      // 处理实时更新
      if (payload.eventType === 'UPDATE') {
        updateAgentStatus(payload.new);
      }
    }
  )
  .subscribe();
```

## 用户体验

### 进度显示
- 整体进度条显示完成百分比
- 每个步骤显示状态图标（等待、执行中、完成、失败）
- 实时更新执行时间和结果摘要

### 详细信息
- 点击展开可查看每个步骤的详细结果
- 显示执行耗时
- 错误时显示具体错误信息
- 支持查看原始JSON数据

### 状态图标
- ⏰ 等待中 (pending)
- 🔄 执行中 (running) - 带旋转动画
- ✅ 已完成 (completed)
- ❌ 失败 (failed)

## 部署步骤

### 1. 数据库迁移

在Supabase SQL编辑器中执行：

```sql
-- 执行 supabase-agent-logs-migration.sql 中的SQL
```

### 2. 启用Realtime

在Supabase Dashboard中：
1. 进入 Database > Replication
2. 为 `agent_execution_logs` 表启用Realtime

### 3. 配置RLS策略

确保用户只能查看自己的Agent日志：

```sql
-- 已在迁移脚本中包含
CREATE POLICY "Users can view their own agent logs" ON agent_execution_logs
    FOR SELECT USING (
        match_request_id IN (
            SELECT id FROM match_requests WHERE requester_id = auth.uid()
        )
    );
```

## 性能考虑

### 数据清理
- 建议定期清理旧的Agent日志记录
- 可以设置自动清理策略，保留最近30天的记录

### 实时连接
- 前端组件会自动处理连接断开和重连
- 使用合理的过滤条件避免接收无关数据

### 错误处理
- Agent执行失败时会记录详细错误信息
- 前端有降级机制，Realtime失败时可切换到轮询

## 监控和调试

### 日志查询
```sql
-- 查看特定匹配请求的所有Agent日志
SELECT * FROM agent_execution_logs 
WHERE match_request_id = 'your-request-id' 
ORDER BY step_order;

-- 查看失败的Agent执行
SELECT * FROM agent_execution_logs 
WHERE status = 'failed' 
ORDER BY created_at DESC;

-- 查看平均执行时间
SELECT agent_name, AVG(execution_time_ms) as avg_time_ms
FROM agent_execution_logs 
WHERE status = 'completed'
GROUP BY agent_name;
```

### 性能指标
- 每个Agent的平均执行时间
- 失败率统计
- 用户等待时间分析

## 未来扩展

1. **更细粒度的进度追踪**
   - 在单个Agent内部添加子步骤
   - 显示更详细的处理进度

2. **历史记录查看**
   - 用户可以查看之前匹配的执行历史
   - 对比不同时间的执行性能

3. **智能预估**
   - 基于历史数据预估剩余时间
   - 动态调整进度显示

4. **错误恢复**
   - 自动重试失败的Agent
   - 部分失败时的优雅降级
